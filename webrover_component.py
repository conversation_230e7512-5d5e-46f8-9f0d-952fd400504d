#!/usr/bin/env python3
"""
WebRover Component
==================

Autonomous web navigation agent with intelligent crawling and data extraction capabilities.
Provides advanced web automation with AI-powered decision making.
"""

import asyncio
import json
import logging
import os
import time
import re
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass, asdict
from urllib.parse import urljoin, urlparse
import aiohttp
from bs4 import BeautifulSoup

logger = logging.getLogger(__name__)

@dataclass
class WebPage:
    url: str
    title: str
    content: str
    links: List[str]
    forms: List[Dict[str, Any]]
    images: List[str]
    metadata: Dict[str, Any]
    timestamp: str
    status_code: int = 200
    error: Optional[str] = None

@dataclass
class NavigationAction:
    action_type: str  # navigate, click, fill, submit, extract, wait
    target: str
    value: Optional[str] = None
    selector: Optional[str] = None
    wait_time: float = 1.0

@dataclass
class CrawlSession:
    session_id: str
    start_url: str
    max_pages: int
    max_depth: int
    current_depth: int = 0
    visited_urls: List[str] = None
    pages_crawled: List[WebPage] = None
    status: str = "pending"  # pending, running, completed, failed
    created_at: str = ""
    completed_at: Optional[str] = None
    
    def __post_init__(self):
        if self.visited_urls is None:
            self.visited_urls = []
        if self.pages_crawled is None:
            self.pages_crawled = []

class WebRoverComponent:
    """WebRover autonomous web navigation agent"""
    
    def __init__(self):
        self.session = None
        self.crawl_sessions: Dict[str, CrawlSession] = {}
        self.config = {
            'user_agent': 'WebRover/1.0 (Autonomous Web Agent)',
            'timeout': 30,
            'max_concurrent_requests': 5,
            'respect_robots_txt': True,
            'delay_between_requests': 1.0,
            'max_retries': 3
        }
        
        # AI-powered decision making patterns
        self.navigation_patterns = {
            'login_forms': [
                r'login', r'signin', r'sign-in', r'log-in',
                r'username', r'email', r'password'
            ],
            'search_forms': [
                r'search', r'query', r'find', r'lookup'
            ],
            'navigation_links': [
                r'home', r'about', r'contact', r'services',
                r'products', r'blog', r'news'
            ],
            'data_extraction_targets': [
                r'article', r'content', r'main', r'post',
                r'product', r'item', r'listing'
            ]
        }
        
    async def initialize(self):
        """Initialize WebRover with HTTP session"""
        connector = aiohttp.TCPConnector(limit=self.config['max_concurrent_requests'])
        timeout = aiohttp.ClientTimeout(total=self.config['timeout'])
        
        self.session = aiohttp.ClientSession(
            connector=connector,
            timeout=timeout,
            headers={'User-Agent': self.config['user_agent']}
        )
        
        logger.info("WebRover initialized successfully")
        
    async def cleanup(self):
        """Cleanup resources"""
        if self.session:
            await self.session.close()
            
    async def navigate_to(self, url: str) -> WebPage:
        """Navigate to a specific URL and extract page information"""
        try:
            logger.info(f"Navigating to: {url}")
            
            async with self.session.get(url) as response:
                content = await response.text()
                
                # Parse the page
                soup = BeautifulSoup(content, 'html.parser')
                
                # Extract page information
                page = WebPage(
                    url=url,
                    title=soup.title.string if soup.title else "No Title",
                    content=soup.get_text(strip=True)[:5000],  # Limit content size
                    links=self._extract_links(soup, url),
                    forms=self._extract_forms(soup),
                    images=self._extract_images(soup, url),
                    metadata=self._extract_metadata(soup),
                    timestamp=datetime.now().isoformat(),
                    status_code=response.status
                )
                
                logger.info(f"Successfully navigated to {url}")
                return page
                
        except Exception as e:
            logger.error(f"Error navigating to {url}: {e}")
            return WebPage(
                url=url,
                title="Error",
                content="",
                links=[],
                forms=[],
                images=[],
                metadata={},
                timestamp=datetime.now().isoformat(),
                status_code=0,
                error=str(e)
            )
            
    def _extract_links(self, soup: BeautifulSoup, base_url: str) -> List[str]:
        """Extract all links from the page"""
        links = []
        for link in soup.find_all('a', href=True):
            href = link['href']
            full_url = urljoin(base_url, href)
            if self._is_valid_url(full_url):
                links.append(full_url)
        return list(set(links))  # Remove duplicates
        
    def _extract_forms(self, soup: BeautifulSoup) -> List[Dict[str, Any]]:
        """Extract form information from the page"""
        forms = []
        for form in soup.find_all('form'):
            form_data = {
                'action': form.get('action', ''),
                'method': form.get('method', 'GET').upper(),
                'fields': []
            }
            
            # Extract form fields
            for field in form.find_all(['input', 'textarea', 'select']):
                field_data = {
                    'type': field.get('type', 'text'),
                    'name': field.get('name', ''),
                    'id': field.get('id', ''),
                    'placeholder': field.get('placeholder', ''),
                    'required': field.has_attr('required')
                }
                form_data['fields'].append(field_data)
                
            forms.append(form_data)
        return forms
        
    def _extract_images(self, soup: BeautifulSoup, base_url: str) -> List[str]:
        """Extract image URLs from the page"""
        images = []
        for img in soup.find_all('img', src=True):
            src = img['src']
            full_url = urljoin(base_url, src)
            if self._is_valid_url(full_url):
                images.append(full_url)
        return list(set(images))
        
    def _extract_metadata(self, soup: BeautifulSoup) -> Dict[str, Any]:
        """Extract metadata from the page"""
        metadata = {}
        
        # Meta tags
        for meta in soup.find_all('meta'):
            name = meta.get('name') or meta.get('property')
            content = meta.get('content')
            if name and content:
                metadata[name] = content
                
        # Structured data
        for script in soup.find_all('script', type='application/ld+json'):
            try:
                structured_data = json.loads(script.string)
                metadata['structured_data'] = structured_data
                break
            except (json.JSONDecodeError, AttributeError):
                continue
                
        return metadata
        
    def _is_valid_url(self, url: str) -> bool:
        """Check if URL is valid and should be processed"""
        try:
            parsed = urlparse(url)
            return parsed.scheme in ['http', 'https'] and parsed.netloc
        except:
            return False
            
    async def intelligent_crawl(self, start_url: str, max_pages: int = 10, max_depth: int = 3) -> CrawlSession:
        """Perform intelligent web crawling with AI-powered navigation"""
        session_id = f"crawl_{int(time.time())}"
        
        crawl_session = CrawlSession(
            session_id=session_id,
            start_url=start_url,
            max_pages=max_pages,
            max_depth=max_depth,
            created_at=datetime.now().isoformat()
        )
        
        self.crawl_sessions[session_id] = crawl_session
        crawl_session.status = "running"
        
        try:
            await self._crawl_recursive(crawl_session, start_url, 0)
            crawl_session.status = "completed"
            
        except Exception as e:
            logger.error(f"Crawl session {session_id} failed: {e}")
            crawl_session.status = "failed"
            
        finally:
            crawl_session.completed_at = datetime.now().isoformat()
            
        return crawl_session
        
    async def _crawl_recursive(self, session: CrawlSession, url: str, depth: int):
        """Recursively crawl pages with intelligent navigation"""
        if (depth > session.max_depth or 
            len(session.pages_crawled) >= session.max_pages or
            url in session.visited_urls):
            return
            
        session.visited_urls.append(url)
        
        # Navigate to the page
        page = await self.navigate_to(url)
        session.pages_crawled.append(page)
        
        if page.error:
            logger.warning(f"Skipping {url} due to error: {page.error}")
            return
            
        # Intelligent link selection
        priority_links = self._prioritize_links(page.links, page.content)
        
        # Crawl priority links
        for link in priority_links[:5]:  # Limit concurrent crawls
            if len(session.pages_crawled) >= session.max_pages:
                break
                
            await asyncio.sleep(self.config['delay_between_requests'])
            await self._crawl_recursive(session, link, depth + 1)
            
    def _prioritize_links(self, links: List[str], page_content: str) -> List[str]:
        """Use AI-powered heuristics to prioritize links for crawling"""
        scored_links = []
        
        for link in links:
            score = 0
            link_lower = link.lower()
            
            # Score based on navigation patterns
            for pattern_type, patterns in self.navigation_patterns.items():
                for pattern in patterns:
                    if re.search(pattern, link_lower):
                        if pattern_type == 'navigation_links':
                            score += 10
                        elif pattern_type == 'data_extraction_targets':
                            score += 8
                        elif pattern_type == 'search_forms':
                            score += 6
                        else:
                            score += 4
                            
            # Avoid common non-content links
            if any(avoid in link_lower for avoid in ['logout', 'admin', 'login', 'register']):
                score -= 5
                
            # Prefer shorter, cleaner URLs
            if len(link) < 100:
                score += 2
                
            scored_links.append((link, score))
            
        # Sort by score (descending) and return URLs
        scored_links.sort(key=lambda x: x[1], reverse=True)
        return [link for link, score in scored_links]
        
    async def extract_data(self, url: str, extraction_rules: Dict[str, Any]) -> Dict[str, Any]:
        """Extract specific data from a webpage using extraction rules"""
        page = await self.navigate_to(url)
        
        if page.error:
            return {'error': page.error}
            
        soup = BeautifulSoup(page.content, 'html.parser')
        extracted_data = {}
        
        for field_name, rule in extraction_rules.items():
            try:
                if rule['type'] == 'css_selector':
                    elements = soup.select(rule['selector'])
                    if rule.get('multiple', False):
                        extracted_data[field_name] = [elem.get_text(strip=True) for elem in elements]
                    else:
                        extracted_data[field_name] = elements[0].get_text(strip=True) if elements else None
                        
                elif rule['type'] == 'regex':
                    matches = re.findall(rule['pattern'], page.content)
                    extracted_data[field_name] = matches if rule.get('multiple', False) else (matches[0] if matches else None)
                    
                elif rule['type'] == 'attribute':
                    elements = soup.select(rule['selector'])
                    if elements:
                        extracted_data[field_name] = elements[0].get(rule['attribute'])
                        
            except Exception as e:
                logger.error(f"Error extracting {field_name}: {e}")
                extracted_data[field_name] = None
                
        return {
            'url': url,
            'extracted_data': extracted_data,
            'timestamp': datetime.now().isoformat()
        }
        
    async def perform_actions(self, url: str, actions: List[NavigationAction]) -> Dict[str, Any]:
        """Perform a sequence of navigation actions on a webpage"""
        results = []
        current_url = url
        
        for action in actions:
            try:
                if action.action_type == 'navigate':
                    page = await self.navigate_to(action.target)
                    current_url = action.target
                    results.append({
                        'action': 'navigate',
                        'target': action.target,
                        'success': not page.error,
                        'result': {'title': page.title, 'url': page.url}
                    })
                    
                elif action.action_type == 'extract':
                    page = await self.navigate_to(current_url)
                    soup = BeautifulSoup(page.content, 'html.parser')
                    
                    if action.selector:
                        elements = soup.select(action.selector)
                        extracted_text = [elem.get_text(strip=True) for elem in elements]
                    else:
                        extracted_text = page.content[:1000]  # First 1000 chars
                        
                    results.append({
                        'action': 'extract',
                        'selector': action.selector,
                        'success': True,
                        'result': extracted_text
                    })
                    
                elif action.action_type == 'wait':
                    await asyncio.sleep(action.wait_time)
                    results.append({
                        'action': 'wait',
                        'duration': action.wait_time,
                        'success': True
                    })
                    
                # Note: For full browser automation (click, fill, submit),
                # integration with Playwright or Selenium would be needed
                    
            except Exception as e:
                logger.error(f"Error performing action {action.action_type}: {e}")
                results.append({
                    'action': action.action_type,
                    'success': False,
                    'error': str(e)
                })
                
        return {
            'url': url,
            'actions_performed': len(results),
            'results': results,
            'timestamp': datetime.now().isoformat()
        }
        
    def get_crawl_session(self, session_id: str) -> Optional[CrawlSession]:
        """Get a crawl session by ID"""
        return self.crawl_sessions.get(session_id)
        
    def get_all_sessions(self) -> List[CrawlSession]:
        """Get all crawl sessions"""
        return list(self.crawl_sessions.values())

# Example usage and testing
if __name__ == "__main__":
    async def test_webrover():
        rover = WebRoverComponent()
        await rover.initialize()
        
        try:
            # Test basic navigation
            page = await rover.navigate_to("https://httpbin.org/html")
            print(f"Navigated to: {page.title}")
            print(f"Found {len(page.links)} links")
            
            # Test intelligent crawling
            session = await rover.intelligent_crawl("https://httpbin.org", max_pages=3, max_depth=2)
            print(f"Crawl session {session.session_id} completed with {len(session.pages_crawled)} pages")
            
        finally:
            await rover.cleanup()
            
    asyncio.run(test_webrover())
