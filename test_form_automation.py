import asyncio
import logging
from quote_form_filler import QuoteFormFiller
from datetime import datetime

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def test_medicare_quote():
    """Test Medicare Supplement quote form filling"""
    
    # Medicare client data
    medicare_client = {
        "name": "Test Medicare",
        "dob": "1955-06-15",  # Age 68
        "height": "5'10\"",
        "weight": "175",
        "gender": "M",
        "phone": "************",
        "email": "<EMAIL>",
        "address": "123 Senior Way, Port St. Lucie, FL 34952",
        "ssn": "***********",
        
        "medications": [
            {
                "drug_name": "Metformin",
                "dosage": "500mg",
                "frequency": "twice daily"
            }
        ],
        
        "tobacco_use": False,
        "product_type": "Medicare Supplement",
        "plan_type": "Plan G",  # Common Medicare Supplement plan
        "effective_date": "2025-06-01"
    }
    
    logger.info("\n=== Testing Medicare Quote Form Automation ===")
    
    filler = QuoteFormFiller()
    try:
        # Fill MOO Medicare quote form
        logger.info("\nFilling Mutual of Omaha Medicare Supplement quote form...")
        success = filler.fill_moo_quote_form(medicare_client)
        
        if success:
            logger.info("Successfully filled MOO Medicare quote form")
            input("Press Enter after verifying Medicare quote...")
        else:
            logger.error("Failed to fill MOO Medicare quote form")
            
    finally:
        filler.close()

async def test_life_quote():
    """Test Term Life quote form filling"""
    
    # Life insurance client data
    life_client = {
        "name": "Test Life",
        "dob": "1980-08-20",
        "height": "5'11\"",
        "weight": "180",
        "gender": "M",
        "phone": "************",
        "email": "<EMAIL>",
        "address": "456 Insurance Blvd, Port St. Lucie, FL 34952",
        "ssn": "***********",
        
        "tobacco_use": False,
        "product_type": "Term Life",
        "desired_coverage": "250000",
        "term_length": "20",  # 20-year term
        "health_class": "Preferred"  # Assuming good health
    }
    
    logger.info("\n=== Testing Life Insurance Quote Form Automation ===")
    
    filler = QuoteFormFiller()
    try:
        # Fill FFL life quote form
        logger.info("\nFilling FFL Trident Life quote form...")
        success = filler.fill_ffl_quote_form(life_client)
        
        if success:
            logger.info("Successfully filled FFL life quote form")
            input("Press Enter after verifying life quote...")
        else:
            logger.error("Failed to fill FFL life quote form")
            
    finally:
        filler.close()

async def main():
    """Run form automation tests"""
    try:
        # Test Medicare quote
        await test_medicare_quote()
        
        # Test life insurance quote
        await test_life_quote()
        
        logger.info("\nForm automation testing completed!")
        
    except Exception as e:
        logger.error(f"Error during testing: {str(e)}")
        raise

if __name__ == "__main__":
    asyncio.run(main())