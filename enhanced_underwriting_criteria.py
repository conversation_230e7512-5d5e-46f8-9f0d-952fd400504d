"""
Enhanced Underwriting Criteria

This module provides detailed health underwriting criteria for insurance carriers
to more accurately determine client eligibility and rating classes.
"""

from typing import Dict, List, Tuple, Optional

# Detailed health condition definitions with severity levels
HEALTH_CONDITIONS_DETAILED = {
    "diabetes": {
        "type_1": {
            "description": "Type 1 diabetes (insulin-dependent)",
            "severity_levels": {
                "controlled": "A1C < 7.0, no complications",
                "moderate": "A1C 7.0-8.5, minor complications",
                "severe": "A1C > 8.5, major complications"
            },
            "typical_rating": {
                "controlled": "Table 2-4",
                "moderate": "Table 4-6",
                "severe": "Decline"
            }
        },
        "type_2": {
            "description": "Type 2 diabetes (may or may not require insulin)",
            "severity_levels": {
                "controlled": "A1C < 7.0, no complications, diagnosed after age 50",
                "moderate": "A1C 7.0-8.0, minor complications or diagnosed age 40-50",
                "severe": "A1C > 8.0, complications or diagnosed before age 40"
            },
            "typical_rating": {
                "controlled": "Standard to Table 2",
                "moderate": "Table 2-4",
                "severe": "Table 4-8 or Decline"
            }
        },
        "pre_diabetes": {
            "description": "Elevated blood glucose, not yet diabetic",
            "severity_levels": {
                "mild": "A1C 5.7-6.0, no other risk factors",
                "moderate": "A1C 6.0-6.4, with other risk factors"
            },
            "typical_rating": {
                "mild": "Standard",
                "moderate": "Standard to Table 2"
            }
        }
    },
    "cardiovascular": {
        "hypertension": {
            "description": "High blood pressure",
            "severity_levels": {
                "controlled": "BP consistently <140/90 with medication",
                "moderate": "BP 140-159/90-99 with medication",
                "severe": "BP >160/100 despite medication"
            },
            "typical_rating": {
                "controlled": "Preferred to Standard",
                "moderate": "Standard to Table 2",
                "severe": "Table 2-4 or Decline"
            }
        },
        "heart_attack": {
            "description": "Myocardial infarction",
            "severity_levels": {
                "single_remote": "Single event >5 years ago, good recovery",
                "single_recent": "Single event <5 years ago",
                "multiple": "Multiple events"
            },
            "typical_rating": {
                "single_remote": "Table 2-4",
                "single_recent": "Table 4-8",
                "multiple": "Table 8 or Decline"
            },
            "time_factors": {
                "<6 months": "Postpone",
                "6-12 months": "Table 8 or Decline",
                "1-2 years": "Table 4-8",
                "2-5 years": "Table 2-6",
                ">5 years": "Table 2-4"
            }
        },
        "stroke": {
            "description": "Cerebrovascular accident",
            "severity_levels": {
                "tia_single": "Single TIA (mini-stroke) with full recovery",
                "tia_multiple": "Multiple TIAs",
                "full_stroke": "Full stroke with recovery",
                "with_residuals": "Stroke with residual effects"
            },
            "typical_rating": {
                "tia_single": "Table 2-4",
                "tia_multiple": "Table 4-6",
                "full_stroke": "Table 4-8",
                "with_residuals": "Table 8 or Decline"
            },
            "time_factors": {
                "<6 months": "Postpone",
                "6-12 months": "Table 8 or Decline",
                "1-2 years": "Table 4-8",
                "2-5 years": "Table 2-6",
                ">5 years": "Table 2-4"
            }
        },
        "afib": {
            "description": "Atrial fibrillation",
            "severity_levels": {
                "paroxysmal": "Occasional episodes, controlled",
                "persistent": "Frequent episodes, controlled with medication",
                "permanent": "Continuous, requiring ongoing treatment"
            },
            "typical_rating": {
                "paroxysmal": "Standard to Table 2",
                "persistent": "Table 2-4",
                "permanent": "Table 4-6"
            }
        },
        "coronary_artery_disease": {
            "description": "Coronary artery disease",
            "severity_levels": {
                "mild": "Single vessel, <50% blockage",
                "moderate": "Multiple vessels, 50-70% blockage",
                "severe": ">70% blockage or left main disease"
            },
            "typical_rating": {
                "mild": "Standard to Table 2",
                "moderate": "Table 2-4",
                "severe": "Table 4-8"
            }
        }
    },
    "cancer": {
        "skin_cancer": {
            "description": "Skin cancer",
            "severity_levels": {
                "basal_cell": "Basal cell carcinoma",
                "squamous_cell": "Squamous cell carcinoma",
                "melanoma_in_situ": "Melanoma in situ",
                "melanoma_invasive": "Invasive melanoma"
            },
            "typical_rating": {
                "basal_cell": "Preferred to Standard",
                "squamous_cell": "Standard",
                "melanoma_in_situ": "Standard to Table 2",
                "melanoma_invasive": "Table 2-8 depending on stage and time since treatment"
            }
        },
        "breast_cancer": {
            "description": "Breast cancer",
            "severity_levels": {
                "stage_0": "Stage 0 (DCIS/LCIS)",
                "stage_1": "Stage 1",
                "stage_2": "Stage 2",
                "stage_3": "Stage 3",
                "stage_4": "Stage 4 (metastatic)"
            },
            "typical_rating": {
                "stage_0": "Standard to Table 2 after 2 years",
                "stage_1": "Table 2-4 after 3-5 years",
                "stage_2": "Table 4-6 after 5 years",
                "stage_3": "Table 6-8 after 7-10 years",
                "stage_4": "Decline"
            },
            "time_factors": {
                "<2 years": "Postpone to Table 8",
                "2-5 years": "Table 2-6 depending on stage",
                "5-10 years": "Standard to Table 4 depending on stage",
                ">10 years": "Standard possible for early stages"
            }
        },
        "prostate_cancer": {
            "description": "Prostate cancer",
            "severity_levels": {
                "low_grade": "Gleason score ≤6, PSA <10",
                "intermediate": "Gleason 7 or PSA 10-20",
                "high_grade": "Gleason 8-10 or PSA >20"
            },
            "typical_rating": {
                "low_grade": "Standard to Table 2 after 1 year",
                "intermediate": "Table 2-4 after 2-3 years",
                "high_grade": "Table 4-8 after 5+ years"
            }
        },
        "colon_cancer": {
            "description": "Colon cancer",
            "severity_levels": {
                "stage_0": "Stage 0 (in situ)",
                "stage_1": "Stage 1",
                "stage_2": "Stage 2",
                "stage_3": "Stage 3",
                "stage_4": "Stage 4 (metastatic)"
            },
            "typical_rating": {
                "stage_0": "Standard after 1 year",
                "stage_1": "Standard to Table 2 after 3 years",
                "stage_2": "Table 2-4 after 5 years",
                "stage_3": "Table 4-8 after 7 years",
                "stage_4": "Decline"
            }
        }
    },
    "respiratory": {
        "asthma": {
            "description": "Asthma",
            "severity_levels": {
                "mild": "Intermittent, rare attacks, no hospitalizations",
                "moderate": "Daily medication, occasional exacerbations",
                "severe": "Frequent exacerbations, hospitalizations"
            },
            "typical_rating": {
                "mild": "Preferred to Standard",
                "moderate": "Standard to Table 2",
                "severe": "Table 2-4"
            }
        },
        "copd": {
            "description": "Chronic Obstructive Pulmonary Disease",
            "severity_levels": {
                "mild": "FEV1 >80% predicted, minimal symptoms",
                "moderate": "FEV1 50-80% predicted, moderate symptoms",
                "severe": "FEV1 30-50% predicted, significant symptoms",
                "very_severe": "FEV1 <30% predicted, severe symptoms"
            },
            "typical_rating": {
                "mild": "Standard to Table 2",
                "moderate": "Table 2-4",
                "severe": "Table 4-8",
                "very_severe": "Decline"
            }
        },
        "sleep_apnea": {
            "description": "Sleep apnea",
            "severity_levels": {
                "mild": "AHI 5-15, treated or untreated",
                "moderate": "AHI 15-30, treated with CPAP",
                "severe": "AHI >30, treated with CPAP",
                "severe_untreated": "AHI >30, untreated"
            },
            "typical_rating": {
                "mild": "Preferred to Standard",
                "moderate": "Standard with documented compliance",
                "severe": "Standard to Table 2 with documented compliance",
                "severe_untreated": "Table 4 or Decline"
            }
        }
    },
    "bmi": {
        "description": "Body Mass Index",
        "ranges": {
            "underweight": "BMI <18.5",
            "normal": "BMI 18.5-24.9",
            "overweight": "BMI 25-29.9",
            "obese_class_1": "BMI 30-34.9",
            "obese_class_2": "BMI 35-39.9",
            "obese_class_3": "BMI ≥40"
        },
        "typical_rating": {
            "underweight": "Standard to Table 2",
            "normal": "Preferred Plus possible",
            "overweight": "Preferred possible",
            "obese_class_1": "Standard to Table 2",
            "obese_class_2": "Table 2-4",
            "obese_class_3": "Table 4-8 or Decline"
        }
    }
}

# Carrier-specific underwriting guidelines
CARRIER_UNDERWRITING = {
    "mutual_of_omaha": {
        "preferred_plus_criteria": {
            "bmi_range": (18.5, 27.5),
            "blood_pressure": "≤135/85, no medication",
            "cholesterol": "≤220, ratio ≤5.0, no medication",
            "family_history": "No cardiovascular disease or cancer in parents/siblings before age 60",
            "tobacco_use": "No tobacco/nicotine in 5 years",
            "driving_history": "No DUI/reckless driving in 5 years, ≤2 moving violations in 3 years",
            "substance_abuse": "No history of substance abuse ever",
            "aviation": "No private aviation except as fare-paying passenger"
        },
        "preferred_criteria": {
            "bmi_range": (18.5, 29.0),
            "blood_pressure": "≤140/90, with or without medication",
            "cholesterol": "≤240, ratio ≤5.5, with or without medication",
            "family_history": "No cardiovascular death in parents before age 60",
            "tobacco_use": "No tobacco/nicotine in 3 years",
            "driving_history": "No DUI/reckless driving in 5 years, ≤3 moving violations in 3 years",
            "substance_abuse": "No history in 10 years",
            "aviation": "Available with flat extra or exclusion"
        },
        "standard_plus_criteria": {
            "bmi_range": (18.5, 32.0),
            "blood_pressure": "≤145/90, with or without medication",
            "cholesterol": "≤260, ratio ≤6.5, with or without medication",
            "family_history": "No cardiovascular death in parents before age 60",
            "tobacco_use": "No tobacco/nicotine in 1 year",
            "driving_history": "No DUI/reckless driving in 3 years",
            "substance_abuse": "No history in 7 years"
        },
        "special_considerations": {
            "diabetes": "Type 2, well-controlled may qualify for Standard",
            "asthma": "Mild, well-controlled may qualify for Preferred",
            "sleep_apnea": "Treated with documented compliance may qualify for Standard Plus",
            "mental_health": "Mild anxiety/depression may qualify for Preferred"
        }
    },
    "american_general": {
        "preferred_plus_criteria": {
            "bmi_range": (18.5, 27.0),
            "blood_pressure": "≤130/80, no medication",
            "cholesterol": "≤210, ratio ≤4.5, no medication",
            "family_history": "No cardiovascular disease or cancer in parents/siblings before age 65",
            "tobacco_use": "No tobacco/nicotine in 5 years",
            "driving_history": "No DUI/reckless driving in 7 years, ≤2 moving violations in 3 years",
            "substance_abuse": "No history of substance abuse ever",
            "aviation": "No private aviation except as fare-paying passenger"
        },
        "preferred_criteria": {
            "bmi_range": (18.5, 30.0),
            "blood_pressure": "≤140/85, with or without medication",
            "cholesterol": "≤250, ratio ≤5.0, with or without medication",
            "family_history": "No cardiovascular death in parents before age 60",
            "tobacco_use": "No tobacco/nicotine in 3 years",
            "driving_history": "No DUI/reckless driving in 5 years, ≤3 moving violations in 3 years",
            "substance_abuse": "No history in 10 years",
            "aviation": "Available with flat extra or exclusion"
        },
        "special_considerations": {
            "cancer": "More favorable consideration for certain cancer types after 5 years",
            "cardiovascular": "More favorable on well-controlled hypertension",
            "build": "More flexible on build/BMI than some carriers"
        }
    },
    "pacific_life": {
        "preferred_plus_criteria": {
            "bmi_range": (18.5, 28.0),
            "blood_pressure": "≤130/80, no medication",
            "cholesterol": "≤220, ratio ≤5.0, no medication",
            "family_history": "No cardiovascular disease in parents/siblings before age 65",
            "tobacco_use": "No tobacco/nicotine in 5 years",
            "driving_history": "No DUI/reckless driving in 7 years, ≤2 moving violations in 3 years"
        },
        "preferred_criteria": {
            "bmi_range": (18.5, 30.0),
            "blood_pressure": "≤140/90, with or without medication",
            "cholesterol": "≤250, ratio ≤5.5, with or without medication",
            "family_history": "No cardiovascular death in parents before age 60",
            "tobacco_use": "No tobacco/nicotine in 3 years",
            "driving_history": "No DUI/reckless driving in 5 years"
        },
        "special_considerations": {
            "foreign_travel": "More liberal on foreign travel than other carriers",
            "mental_health": "More favorable on anxiety/depression",
            "recreational_activities": "More flexible on hazardous activities"
        }
    }
}

def get_health_condition_details(condition: str, severity: str = None) -> Dict:
    """
    Get detailed information about a health condition
    
    Args:
        condition: Health condition name
        severity: Optional severity level
        
    Returns:
        Dictionary with condition details
    """
    # Parse condition into category and specific condition
    parts = condition.split('_')
    if len(parts) > 1:
        category = parts[0]
        specific = '_'.join(parts[1:])
    else:
        # Try to find in any category
        for cat, conditions in HEALTH_CONDITIONS_DETAILED.items():
            if condition in conditions:
                return conditions[condition]
        return {}
    
    # Look for the condition in the specified category
    if category in HEALTH_CONDITIONS_DETAILED:
        category_conditions = HEALTH_CONDITIONS_DETAILED[category]
        if specific in category_conditions:
            condition_info = category_conditions[specific]
            if severity and severity in condition_info.get("severity_levels", {}):
                # Return info for specific severity
                result = {
                    "description": condition_info["description"],
                    "severity": severity,
                    "severity_description": condition_info["severity_levels"][severity],
                    "typical_rating": condition_info["typical_rating"][severity]
                }
                if "time_factors" in condition_info:
                    result["time_factors"] = condition_info["time_factors"]
                return result
            else:
                # Return general condition info
                return condition_info
    
    return {}

def get_carrier_underwriting_guidelines(carrier: str, rating_class: str = None) -> Dict:
    """
    Get underwriting guidelines for a specific carrier
    
    Args:
        carrier: Carrier name
        rating_class: Optional rating class (preferred_plus, preferred, etc.)
        
    Returns:
        Dictionary with underwriting guidelines
    """
    carrier_lower = carrier.lower().replace(" ", "_")
    
    if carrier_lower in CARRIER_UNDERWRITING:
        if rating_class and rating_class in CARRIER_UNDERWRITING[carrier_lower]:
            return CARRIER_UNDERWRITING[carrier_lower][rating_class]
        else:
            return CARRIER_UNDERWRITING[carrier_lower]
    
    return {}

def evaluate_client_health_rating(client_data: Dict, carrier: str) -> Dict:
    """
    Evaluate a client's likely health rating based on their health profile
    
    Args:
        client_data: Dictionary with client health information
        carrier: Carrier to evaluate for
        
    Returns:
        Dictionary with likely rating and explanations
    """
    carrier_lower = carrier.lower().replace(" ", "_")
    
    if carrier_lower not in CARRIER_UNDERWRITING:
        return {
            "likely_rating": "Unknown",
            "explanation": f"No underwriting data available for {carrier}"
        }
    
    # Extract client health data
    age = client_data.get("age", 35)
    bmi = client_data.get("bmi")
    tobacco_use = client_data.get("tobacco_use", False)
    tobacco_last_use = client_data.get("tobacco_last_use", 0)  # Years since last use
    blood_pressure = client_data.get("blood_pressure", {})
    bp_systolic = blood_pressure.get("systolic", 120)
    bp_diastolic = blood_pressure.get("diastolic", 80)
    bp_medication = blood_pressure.get("medication", False)
    cholesterol = client_data.get("cholesterol", {})
    total_cholesterol = cholesterol.get("total", 200)
    hdl = cholesterol.get("hdl", 50)
    cholesterol_ratio = total_cholesterol / hdl if hdl > 0 else 4.0
    cholesterol_medication = cholesterol.get("medication", False)
    family_history = client_data.get("family_history", [])
    health_conditions = client_data.get("health_conditions", [])
    medications = client_data.get("medications", [])
    driving_history = client_data.get("driving_history", {})
    
    # Start with best rating
    rating_classes = ["preferred_plus", "preferred", "standard_plus", "standard", "table_rating"]
    likely_rating = "preferred_plus"
    rating_explanations = []
    
    # Check BMI
    if bmi:
        for rating in rating_classes:
            if rating in CARRIER_UNDERWRITING[carrier_lower] and "bmi_range" in CARRIER_UNDERWRITING[carrier_lower][rating]:
                min_bmi, max_bmi = CARRIER_UNDERWRITING[carrier_lower][rating]["bmi_range"]
                if min_bmi <= bmi <= max_bmi:
                    if rating_classes.index(rating) > rating_classes.index(likely_rating):
                        likely_rating = rating
                        rating_explanations.append(f"BMI of {bmi} qualifies for {rating.replace('_', ' ').title()}")
                    break
    
    # Check tobacco use
    if tobacco_use:
        likely_rating = "standard"  # Current tobacco use typically disqualifies from preferred
        rating_explanations.append("Current tobacco use typically limits to Standard rates")
    elif tobacco_last_use > 0:
        # Check years since last use against carrier criteria
        for rating in rating_classes:
            if rating in CARRIER_UNDERWRITING[carrier_lower] and "tobacco_use" in CARRIER_UNDERWRITING[carrier_lower][rating]:
                tobacco_criteria = CARRIER_UNDERWRITING[carrier_lower][rating]["tobacco_use"]
                if "no tobacco/nicotine in" in tobacco_criteria.lower():
                    years_required = int(tobacco_criteria.lower().split("in ")[1].split(" ")[0])
                    if tobacco_last_use < years_required:
                        if rating_classes.index(rating) > rating_classes.index(likely_rating):
                            likely_rating = rating
                            rating_explanations.append(f"Tobacco use {tobacco_last_use} years ago qualifies for {rating.replace('_', ' ').title()}")
                        break
    
    # Check blood pressure
    if bp_systolic and bp_diastolic:
        for rating in rating_classes:
            if rating in CARRIER_UNDERWRITING[carrier_lower] and "blood_pressure" in CARRIER_UNDERWRITING[carrier_lower][rating]:
                bp_criteria = CARRIER_UNDERWRITING[carrier_lower][rating]["blood_pressure"]
                max_systolic = int(bp_criteria.split("≤")[1].split("/")[0])
                max_diastolic = int(bp_criteria.split("/")[1].split(",")[0])
                
                if bp_medication and "no medication" in bp_criteria.lower():
                    if rating_classes.index(rating) > rating_classes.index(likely_rating):
                        likely_rating = rating
                        rating_explanations.append(f"Blood pressure medication use disqualifies from {rating.replace('_', ' ').title()}")
                    continue
                
                if bp_systolic > max_systolic or bp_diastolic > max_diastolic:
                    if rating_classes.index(rating) > rating_classes.index(likely_rating):
                        likely_rating = rating
                        rating_explanations.append(f"Blood pressure {bp_systolic}/{bp_diastolic} exceeds {rating.replace('_', ' ').title()} criteria")
                    break
    
    # Check health conditions
    for condition in health_conditions:
        condition_details = get_health_condition_details(condition)
        if condition_details and "typical_rating" in condition_details:
            # This is a simplified approach - real underwriting would be more complex
            worst_rating = min(condition_details["typical_rating"].values(), key=lambda x: 
                              "Decline" if "Decline" in x else
                              int(x.split("Table ")[1].split("-")[0]) if "Table" in x else
                              rating_classes.index(x.lower().replace(" ", "_")) if x.lower().replace(" ", "_") in rating_classes else
                              999)
            
            if "Decline" in worst_rating:
                likely_rating = "decline"
                rating_explanations.append(f"{condition_details['description']} may result in decline")
                break
            elif "Table" in worst_rating:
                likely_rating = "table_rating"
                table_rating = worst_rating.split("Table ")[1].split("-")[0]
                rating_explanations.append(f"{condition_details['description']} may result in Table {table_rating} rating")
            elif worst_rating.lower().replace(" ", "_") in rating_classes:
                worst_rating_index = rating_classes.index(worst_rating.lower().replace(" ", "_"))
                if worst_rating_index > rating_classes.index(likely_rating):
                    likely_rating = rating_classes[worst_rating_index]
                    rating_explanations.append(f"{condition_details['description']} limits to {worst_rating} rating")
    
    # Special considerations
    if "special_considerations" in CARRIER_UNDERWRITING[carrier_lower]:
        for condition, consideration in CARRIER_UNDERWRITING[carrier_lower]["special_considerations"].items():
            if any(condition.lower() in c.lower() for c in health_conditions):
                rating_explanations.append(f"Special consideration: {consideration}")
    
    return {
        "likely_rating": likely_rating.replace("_", " ").title(),
        "explanations": rating_explanations,
        "carrier": carrier
    }

# Example usage
if __name__ == "__main__":
    # Example client
    test_client = {
        "age": 32,
        "gender": "Male",
        "tobacco_use": False,
        "bmi": 24.5,
        "blood_pressure": {
            "systolic": 125,
            "diastolic": 80,
            "medication": False
        },
        "cholesterol": {
            "total": 190,
            "hdl": 55,
            "medication": False
        },
        "health_conditions": [],
        "medications": [],
        "family_history": []
    }
    
    # Evaluate for American General
    rating = evaluate_client_health_rating(test_client, "American General")
    
    print(f"Likely rating with {rating['carrier']}: {rating['likely_rating']}")
    if rating['explanations']:
        print("Explanations:")
        for explanation in rating['explanations']:
            print(f"- {explanation}")
    
    # Get details about a specific health condition
    condition_details = get_health_condition_details("diabetes_type_2", "controlled")
    
    if condition_details:
        print(f"\nCondition: {condition_details['description']}")
        print(f"Severity: {condition_details['severity_description']}")
        print(f"Typical rating: {condition_details['typical_rating']}")
