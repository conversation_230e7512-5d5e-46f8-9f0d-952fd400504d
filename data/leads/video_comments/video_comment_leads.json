[{"id": "youtube_1745975789_e0b4a88b", "platform": "youtube", "source": "video_comment", "video_id": "mock_video_4898", "video_title": "How to Use IUL for Tax-Free Retirement", "comment_id": "mock_comment_29411", "author_name": "User_981", "author_channel": "channel_504", "comment": "Need info on final expense coverage. How much would it cost for a 75-year-old?", "detected_interest": {"is_interested": false, "confidence_score": 0.6, "general_interest": true, "specific_product": "final_expense", "matched_keywords": ["final expense"]}, "published_at": "2025-04-29T21:16:29.168140", "processed_at": "2025-04-29T21:16:29.168143", "responded": true, "response_template_used": "specific", "status": "exported", "exported_at": "2025-04-29T21:16:29.169602"}, {"id": "youtube_1745975789_104fe934", "platform": "youtube", "source": "video_comment", "video_id": "mock_video_9069", "video_title": "How to Use IUL for Tax-Free Retirement", "comment_id": "mock_comment_38205", "author_name": "User_657", "author_channel": "channel_467", "comment": "I've been looking for a good retirement strategy. How can I get a quote?", "detected_interest": {"is_interested": false, "confidence_score": 0.6, "general_interest": true, "specific_product": "medicare", "matched_keywords": ["retirement"]}, "published_at": "2025-04-29T21:16:29.168165", "processed_at": "2025-04-29T21:16:29.168166", "responded": true, "response_template_used": "specific", "status": "exported", "exported_at": "2025-04-29T21:16:29.169604"}, {"id": "youtube_1745975789_b4fe3d96", "platform": "youtube", "source": "video_comment", "video_id": "mock_video_8565", "video_title": "How to Use IUL for Tax-Free Retirement", "comment_id": "mock_comment_68694", "author_name": "User_227", "author_channel": "channel_265", "comment": "I'm interested in life insurance. How do I get more info?", "detected_interest": {"is_interested": false, "confidence_score": 0.6, "general_interest": true, "specific_product": "life_insurance", "matched_keywords": ["life insurance"]}, "published_at": "2025-04-29T21:16:29.168184", "processed_at": "2025-04-29T21:16:29.168185", "responded": true, "response_template_used": "specific", "status": "exported", "exported_at": "2025-04-29T21:16:29.169605"}, {"id": "youtube_1745975789_f37145af", "platform": "youtube", "source": "video_comment", "video_id": "mock_video_2847", "video_title": "How to Use IUL for Tax-Free Retirement", "comment_id": "mock_comment_63843", "author_name": "User_940", "author_channel": "channel_415", "comment": "I've been looking for a good retirement strategy. How can I get a quote?", "detected_interest": {"is_interested": false, "confidence_score": 0.6, "general_interest": true, "specific_product": "medicare", "matched_keywords": ["retirement"]}, "published_at": "2025-04-29T21:16:29.168216", "processed_at": "2025-04-29T21:16:29.168219", "responded": true, "response_template_used": "specific", "status": "exported", "exported_at": "2025-04-29T21:16:29.169606"}, {"id": "tiktok_1745975789_cf676561", "platform": "tiktok", "source": "video_comment", "video_id": "mock_tiktok_2760", "comment_id": "mock_comment_23702", "author_name": "tiktok_user_701", "comment": "How much would term life insurance cost for a 35 year old?", "detected_interest": {"is_interested": false, "confidence_score": 0.6, "general_interest": true, "specific_product": "life_insurance", "matched_keywords": ["life insurance", "term life"]}, "published_at": "2025-04-29T21:16:29.168642", "processed_at": "2025-04-29T21:16:29.168645", "responded": true, "response_template_used": "specific", "status": "exported", "exported_at": "2025-04-29T21:16:29.169607"}, {"id": "instagram_1745975789_42ff22e6", "platform": "instagram", "source": "post_comment", "post_id": "mock_post_8381", "comment_id": "mock_comment_68645", "author_name": "ig_user_383", "comment": "Very interesting! How do I sign up for a quote?", "detected_interest": {"is_interested": false, "confidence_score": 0.0, "general_interest": false, "specific_product": null, "matched_keywords": []}, "published_at": "2025-04-29T21:16:29.168889", "processed_at": "2025-04-29T21:16:29.168891", "responded": true, "response_template_used": "specific", "status": "exported", "exported_at": "2025-04-29T21:16:29.169608"}, {"id": "instagram_1745975789_411d054f", "platform": "instagram", "source": "post_comment", "post_id": "mock_post_6782", "comment_id": "mock_comment_12989", "author_name": "ig_user_545", "comment": "What's the minimum investment for this IUL strategy?", "detected_interest": {"is_interested": false, "confidence_score": 0.2, "general_interest": false, "specific_product": "life_insurance", "matched_keywords": ["iul"]}, "published_at": "2025-04-29T21:16:29.168908", "processed_at": "2025-04-29T21:16:29.168909", "responded": true, "response_template_used": "specific", "status": "exported", "exported_at": "2025-04-29T21:16:29.169609"}, {"id": "facebook_1745975789_7dcb5944", "platform": "facebook", "source": "post_comment", "post_id": "mock_post_2541", "comment_id": "mock_comment_80783", "author_name": "fb_user_396", "comment": "My parents need final expense insurance. What's the process?", "detected_interest": {"is_interested": false, "confidence_score": 0.2, "general_interest": false, "specific_product": "final_expense", "matched_keywords": ["final expense"]}, "published_at": "2025-04-29T21:16:29.169179", "processed_at": "2025-04-29T21:16:29.169181", "responded": true, "response_template_used": "specific", "status": "exported", "exported_at": "2025-04-29T21:16:29.169610"}, {"id": "facebook_1745975789_89a02e17", "platform": "facebook", "source": "post_comment", "post_id": "mock_post_8789", "comment_id": "mock_comment_68900", "author_name": "fb_user_395", "comment": "This IUL strategy sounds perfect for my retirement. Can you contact me?", "detected_interest": {"is_interested": false, "confidence_score": 0.6, "general_interest": true, "specific_product": "life_insurance", "matched_keywords": ["iul"]}, "published_at": "2025-04-29T21:16:29.169196", "processed_at": "2025-04-29T21:16:29.169197", "responded": true, "response_template_used": "specific", "status": "exported", "exported_at": "2025-04-29T21:16:29.169610"}, {"id": "facebook_1745975789_5e420fe1", "platform": "facebook", "source": "post_comment", "post_id": "mock_post_2222", "comment_id": "mock_comment_22322", "author_name": "fb_user_187", "comment": "I've been looking for a financial strategy like this. How do I learn more?", "detected_interest": {"is_interested": false, "confidence_score": 0.6, "general_interest": true, "specific_product": null, "matched_keywords": []}, "published_at": "2025-04-29T21:16:29.169212", "processed_at": "2025-04-29T21:16:29.169213", "responded": true, "response_template_used": "specific", "status": "exported", "exported_at": "2025-04-29T21:16:29.169611"}, {"id": "facebook_1745975789_947e6c53", "platform": "facebook", "source": "post_comment", "post_id": "mock_post_5223", "comment_id": "mock_comment_63138", "author_name": "fb_user_862", "comment": "I've been looking for a financial strategy like this. How do I learn more?", "detected_interest": {"is_interested": false, "confidence_score": 0.6, "general_interest": true, "specific_product": null, "matched_keywords": []}, "published_at": "2025-04-29T21:16:29.169225", "processed_at": "2025-04-29T21:16:29.169226", "responded": true, "response_template_used": "specific", "status": "exported", "exported_at": "2025-04-29T21:16:29.169612"}, {"id": "facebook_1745975789_d0ca18c7", "platform": "facebook", "source": "post_comment", "post_id": "mock_post_3568", "comment_id": "mock_comment_93631", "author_name": "fb_user_208", "comment": "My parents need final expense insurance. What's the process?", "detected_interest": {"is_interested": false, "confidence_score": 0.2, "general_interest": false, "specific_product": "final_expense", "matched_keywords": ["final expense"]}, "published_at": "2025-04-29T21:16:29.169239", "processed_at": "2025-04-29T21:16:29.169240", "responded": true, "response_template_used": "specific", "status": "exported", "exported_at": "2025-04-29T21:16:29.169613"}, {"id": "facebook_1745975789_6a5f6448", "platform": "facebook", "source": "post_comment", "post_id": "mock_post_7751", "comment_id": "mock_comment_69014", "author_name": "fb_user_191", "comment": "My parents need final expense insurance. What's the process?", "detected_interest": {"is_interested": false, "confidence_score": 0.2, "general_interest": false, "specific_product": "final_expense", "matched_keywords": ["final expense"]}, "published_at": "2025-04-29T21:16:29.169251", "processed_at": "2025-04-29T21:16:29.169252", "responded": true, "response_template_used": "specific", "status": "exported", "exported_at": "2025-04-29T21:16:29.169614"}]