#!/usr/bin/env python3
"""
Test Twilio Connection and Message Delivery

This script provides comprehensive testing for Twilio connectivity and message delivery
to verify that the 400 Bad Request error has been resolved.
"""

import os
import sys
import time
import json
import requests
from dotenv import load_dotenv
from datetime import datetime
from twilio_config import (
    validate_twilio_credentials,
    get_account_info,
    is_trial_account,
    get_verified_numbers,
    format_error_message,
    get_from_number,
    mask_credential
)

# Load environment variables
load_dotenv()

# Twilio Configuration
TWILIO_ACCOUNT_SID = os.getenv("TWILIO_ACCOUNT_SID")
TWILIO_AUTH_TOKEN = os.getenv("TWILIO_AUTH_TOKEN")
TWILIO_PHONE_NUMBER = os.getenv("TWILIO_PHONE_NUMBER")

def check_account_status():
    """Check Twilio account status and configuration"""
    print("\n" + "=" * 80)
    print("CHECKING TWILIO ACCOUNT STATUS")
    print("=" * 80)
    
    # Validate credentials
    is_valid, error_message = validate_twilio_credentials()
    
    if not is_valid:
        print(f"❌ Twilio credential validation failed: {error_message}")
        print("\nPlease check your .env file and ensure it contains:")
        print("TWILIO_ACCOUNT_SID=your_account_sid")
        print("TWILIO_AUTH_TOKEN=your_auth_token")
        print("TWILIO_PHONE_NUMBER=your_phone_number")
        return False
    
    # Get account information
    account_info = get_account_info()
    
    if not account_info:
        print("❌ Failed to retrieve account information")
        return False
    
    # Display account information
    print(f"Account SID: {mask_credential(account_info.get('sid', 'Unknown'), 7)}")
    print(f"Account Name: {account_info.get('friendly_name', 'Unknown')}")
    print(f"Account Type: {account_info.get('type', 'Unknown')}")
    print(f"Account Status: {account_info.get('status', 'Unknown')}")
    
    # Check if account is active
    if account_info.get('status', '').lower() != 'active':
        print(f"\n❌ Account is not active: {account_info.get('status')}")
        print("Please check your Twilio account status in the console:")
        print("https://console.twilio.com/")
        return False
    
    print("\n✓ Account active")
    
    # Check if we're using a trial account
    if is_trial_account():
        print("\n⚠️ This is a TRIAL account")
        print("Trial accounts have restrictions:")
        print("- Can only send messages to verified numbers")
        print("- Limited phone call functionality")
        
        # Check verified numbers
        verified_numbers = get_verified_numbers()
        print(f"\nVerified Numbers ({len(verified_numbers)}):")
        
        for number in verified_numbers:
            print(f"- {number}")
        
        # Check if our test number is verified
        test_number = TWILIO_PHONE_NUMBER
        if test_number in verified_numbers:
            print(f"\n✓ Test number {test_number} is verified")
        else:
            print(f"\n⚠️ Test number {test_number} is NOT verified")
            print("Please verify this number in your Twilio console:")
            print("https://console.twilio.com/phone-numbers/verified")
    else:
        print("\n✓ This is a full Twilio account (not a trial)")
    
    return True

def send_test_sms():
    """Send a test SMS to verify message delivery"""
    print("\n" + "=" * 80)
    print("SENDING TEST SMS MESSAGE")
    print("=" * 80)
    
    # The phone number we'll be texting
    to_number = TWILIO_PHONE_NUMBER
    
    # Create test message with timestamp for uniqueness
    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    message = f"Test message from test_twilio_connection.py at {timestamp}. Please reply with YES to confirm receipt."
    
    # Determine which number to use as the 'From' number
    from_number = get_from_number(to_number)
    
    print(f"From Number: {from_number}")
    print(f"To Number: {to_number}")
    print(f"Message: {message}")
    
    # Twilio API endpoint for sending messages
    url = f"https://api.twilio.com/2010-04-01/Accounts/{TWILIO_ACCOUNT_SID}/Messages.json"
    
    # Prepare the request data
    data = {
        "To": to_number,
        "From": from_number,
        "Body": message
    }
    
    try:
        print("\nSending message...")
        
        # Make the request
        response = requests.post(url, auth=(TWILIO_ACCOUNT_SID, TWILIO_AUTH_TOKEN), data=data)
        
        # Log the request and response for debugging
        print(f"\nRequest URL: {url}")
        print(f"Request Data: To={to_number}, From={from_number}, Body='{message[:20]}...'")
        print(f"HTTP Status: {response.status_code}")
        print(f"Response Headers: {json.dumps(dict(response.headers), indent=2)}")
        
        # Check if the request was successful
        if response.status_code == 201:
            response_data = response.json()
            print(f"\n✓ Message created successfully!")
            print(f"Message SID: {response_data.get('sid')}")
            print(f"Status: {response_data.get('status')}")
            print(f"Direction: {response_data.get('direction')}")
            
            # Provide link to view message in console
            message_sid = response_data.get('sid')
            console_url = f"https://console.twilio.com/us1/develop/sms/logs/{message_sid}"
            print(f"\nView message status in Twilio console: {console_url}")
            
            # Instructions for verification
            print("\nVerification steps:")
            print("1. Check your phone for the test message")
            print("2. Reply YES to confirm receipt")
            print("3. Check Twilio console for message status")
            
            return True
        else:
            print(f"\n❌ Error creating message: HTTP {response.status_code}")
            print(format_error_message(response))
            
            # Special handling for common errors
            if response.status_code == 400:
                try:
                    error_data = response.json()
                    error_code = error_data.get('code')
                    
                    if error_code == 21211:
                        print("\nThis error often means the destination number is not verified in your trial account.")
                        print("Please verify the number in your Twilio console:")
                        print("https://console.twilio.com/phone-numbers/verified")
                    elif error_code == 21606:
                        print("\nThis error means the 'From' number is not valid or not owned by your account.")
                        print("Please check your phone number in the Twilio console.")
                except:
                    pass
                    
            print("\nTroubleshooting steps:")
            print("1. Verify your credentials are correct")
            print("2. Confirm the phone number is verified (for trial accounts)")
            print("3. Check your account balance (for paid accounts)")
            
            return False
    except Exception as e:
        print(f"\n❌ Exception sending message: {str(e)}")
        return False

def test_email_notification():
    """Test email notification (simulated)"""
    print("\n" + "=" * 80)
    print("TESTING EMAIL NOTIFICATION")
    print("=" * 80)
    
    email = "<EMAIL>"
    print(f"Sending test email notification to {email}...")
    
    # This is a placeholder for actual email implementation
    # In a real implementation, we would use something like smtplib or an email service API
    
    print("\n⚠️ Note: Email functionality simulation only")
    print("Actual email integration should be implemented separately")
    print("Recommended options:")
    print("- Gmail API")
    print("- SendGrid")
    print("- SMTP server")
    
    # Example code for future email implementation
    print("\nExample email implementation code:")
    print("""
    import smtplib
    from email.mime.text import MIMEText
    from email.mime.multipart import MIMEMultipart

    # Email settings
    sender_email = "<EMAIL>"
    receiver_email = "<EMAIL>"
    password = "your_app_password"  # Use App Password for Gmail

    # Create message
    message = MIMEMultipart()
    message["From"] = sender_email
    message["To"] = receiver_email
    message["Subject"] = "Test Notification"
    
    body = "This is a test notification. Please reply to confirm receipt."
    message.attach(MIMEText(body, "plain"))
    
    # Send email
    with smtplib.SMTP_SSL("smtp.gmail.com", 465) as server:
        server.login(sender_email, password)
        server.send_message(message)
    """)
    
    return True

def main():
    """Main function to test Twilio connectivity and message delivery"""
    print("=" * 80)
    print("TWILIO CONNECTION AND MESSAGE DELIVERY TEST")
    print("=" * 80)
    print("This script verifies that the 400 Bad Request error has been resolved")
    print("by testing Twilio connectivity and message delivery.")
    
    # Check account status
    account_status_ok = check_account_status()
    
    if not account_status_ok:
        print("\n❌ Account status check failed. Please fix the issues and try again.")
        sys.exit(1)
    
    # Send test SMS
    print("\nProceed with sending test message? (y/n): ", end="")
    proceed = input().lower()
    
    if proceed == 'y':
        sms_success = send_test_sms()
        
        if sms_success:
            print("\n✓ Test SMS sent successfully!")
            print("Please check your phone for the message and reply YES to confirm receipt.")
            print("This proves that the 400 Bad Request error has been resolved.")
        else:
            print("\n❌ Failed to send test SMS.")
            print("Please check the error messages above for troubleshooting.")
    
    print("\nProceed with testing email notification? (y/n): ", end="")
    proceed = input().lower()
    
    if proceed == 'y':
        email_success = test_email_notification()
        
        if email_success:
            print("\n⚠️ Email notification test simulation completed.")
            print("Note: This was just a simulation. Actual email implementation needed.")
        else:
            print("\n❌ Failed to simulate email notification test.")
    
    print("\n" + "=" * 80)
    print("TEST SUMMARY")
    print("=" * 80)
    
    if account_status_ok:
        print("✓ Account Status: Active and properly configured")
    else:
        print("❌ Account Status: Issues detected (see above)")
    
    print("\nNext steps:")
    print("1. If the test SMS was delivered successfully, the 400 Bad Request error is resolved")
    print("2. If not, follow the troubleshooting steps provided above")
    print("3. For email functionality, implement a proper email integration")
    
    print("\n" + "=" * 80)
    print("For direct API testing via curl:")
    print("=" * 80)
    
    # Provide curl command for direct API testing
    print("curl -X POST 'https://api.twilio.com/2010-04-01/Accounts/$TWILIO_ACCOUNT_SID/Messages.json' \\")
    print("--data-urlencode 'To=+***********' \\")
    print("--data-urlencode 'From=+***********' \\")
    print("--data-urlencode 'Body=API Test Message' \\")
    print("-u '$TWILIO_ACCOUNT_SID:$TWILIO_AUTH_TOKEN'")
    

if __name__ == "__main__":
    main()
