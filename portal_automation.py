"""
Insurance Portal Automation

This module provides automation capabilities for insurance portals and email accounts
using legitimate authentication methods and browser automation.

It integrates with the credential_manager.py to securely access credentials.
"""

import os
import time
import json
import logging
import random
from typing import Dict, Any, List, Tuple, Optional
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import WebDriver<PERSON>ait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, NoSuchElementException

# Import our credential manager
from credential_manager import SecureCredentialManager, InsurancePortalManager, EmailAccountManager, setup_credential_system

# Configure logging
logging.basicConfig(level=logging.INFO,
                    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class BrowserSession:
    """Manages browser sessions for portal automation"""
    
    def __init__(self, headless=False):
        """Initialize browser session"""
        self.headless = headless
        self.driver = None
        
    def start(self):
        """Start browser session"""
        options = webdriver.ChromeOptions()
        if self.headless:
            options.add_argument('--headless')
        options.add_argument('--start-maximized')
        options.add_argument('--disable-extensions')
        options.add_argument('--disable-popup-blocking')
        
        try:
            self.driver = webdriver.Chrome(options=options)
            logger.info("Browser session started")
        except Exception as e:
            logger.error(f"Failed to start browser session: {e}")
            raise
            
    def stop(self):
        """Stop browser session"""
        if self.driver:
            self.driver.quit()
            self.driver = None
            logger.info("Browser session stopped")
            
    def navigate(self, url):
        """Navigate to URL"""
        if not self.driver:
            self.start()
            
        try:
            self.driver.get(url)
            logger.info(f"Navigated to {url}")
            # Add a small random delay to avoid detection
            time.sleep(random.uniform(1.0, 2.0))
        except Exception as e:
            logger.error(f"Failed to navigate to {url}: {e}")
            raise
            
    def wait_for_element(self, selector, by=By.CSS_SELECTOR, timeout=10):
        """Wait for element to be present"""
        try:
            element = WebDriverWait(self.driver, timeout).until(
                EC.presence_of_element_located((by, selector))
            )
            return element
        except TimeoutException:
            logger.warning(f"Timeout waiting for element: {selector}")
            return None
            
    def wait_for_clickable(self, selector, by=By.CSS_SELECTOR, timeout=10):
        """Wait for element to be clickable"""
        try:
            element = WebDriverWait(self.driver, timeout).until(
                EC.element_to_be_clickable((by, selector))
            )
            return element
        except TimeoutException:
            logger.warning(f"Timeout waiting for clickable element: {selector}")
            return None
            
    def fill_form(self, form_data):
        """Fill form fields with data"""
        for selector, value in form_data.items():
            try:
                # Try CSS selector first
                element = self.wait_for_element(selector)
                if not element:
                    # Try XPath
                    element = self.wait_for_element(selector, by=By.XPATH)
                    
                if element:
                    element.clear()
                    element.send_keys(value)
                    # Add a small random delay between inputs
                    time.sleep(random.uniform(0.3, 0.7))
                    logger.info(f"Filled form field {selector}")
                else:
                    logger.warning(f"Could not find form field: {selector}")
            except Exception as e:
                logger.error(f"Error filling form field {selector}: {e}")
                
    def click_element(self, selector, by=By.CSS_SELECTOR):
        """Click on element"""
        try:
            element = self.wait_for_clickable(selector, by)
            if element:
                element.click()
                # Add a small random delay after clicking
                time.sleep(random.uniform(0.5, 1.5))
                logger.info(f"Clicked on element: {selector}")
                return True
            else:
                logger.warning(f"Could not click on element: {selector}")
                return False
        except Exception as e:
            logger.error(f"Error clicking on element {selector}: {e}")
            return False
            
    def get_text(self, selector, by=By.CSS_SELECTOR):
        """Get text from element"""
        try:
            element = self.wait_for_element(selector, by)
            if element:
                return element.text
            else:
                return None
        except Exception as e:
            logger.error(f"Error getting text from element {selector}: {e}")
            return None
            
    def take_screenshot(self, filename):
        """Take screenshot"""
        try:
            self.driver.save_screenshot(filename)
            logger.info(f"Screenshot saved to {filename}")
            return True
        except Exception as e:
            logger.error(f"Error taking screenshot: {e}")
            return False


class PortalAutomator:
    """Automates interactions with insurance portals"""
    
    def __init__(self, credential_manager, portal_manager):
        """Initialize portal automator"""
        self.credential_manager = credential_manager
        self.portal_manager = portal_manager
        self.browser = None
        
        # Portal-specific selectors
        self.portal_selectors = {
            "aetna": {
                "login": {
                    "username": "#loginId",
                    "password": "#password",
                    "submit": "#loginButton"
                },
                "quote": {
                    "new_quote": "//a[contains(text(), 'New Quote')]",
                    "zip_code": "#zipCode",
                    "continue": "//button[contains(text(), 'Continue')]"
                }
            },
            "united_healthcare": {
                "login": {
                    "username": "#username",
                    "password": "#password",
                    "submit": "//button[@type='submit']"
                },
                "quote": {
                    "new_quote": "//a[contains(text(), 'Get a Quote')]",
                    "zip_code": "#zipCode",
                    "continue": "#continueButton"
                }
            },
            "healthsherpa": {
                "login": {
                    "username": "#user_email",
                    "password": "#user_password",
                    "submit": "input[type='submit']"
                },
                "quote": {
                    "new_quote": "//a[contains(text(), 'New Application')]",
                    "zip_code": "#zip_code",
                    "continue": ".btn-primary"
                }
            }
        }
        
    def start_browser(self, headless=False):
        """Start browser session"""
        self.browser = BrowserSession(headless=headless)
        self.browser.start()
        
    def stop_browser(self):
        """Stop browser session"""
        if self.browser:
            self.browser.stop()
            self.browser = None
            
    def login_to_portal(self, portal_name):
        """
        Log into an insurance portal
        
        Args:
            portal_name: Name of the portal (e.g., "aetna", "united_healthcare")
            
        Returns:
            True if successful, False otherwise
        """
        if not self.browser:
            self.start_browser()
            
        # Get portal access details
        portal_access = self.portal_manager.get_portal_access(portal_name)
        if "error" in portal_access:
            logger.error(f"Error accessing portal {portal_name}: {portal_access['error']}")
            return False
            
        credentials = portal_access["credentials"]
        url = portal_access["url"]
        
        # Navigate to portal
        try:
            self.browser.navigate(url)
            
            # Check if portal is in selectors
            if portal_name not in self.portal_selectors:
                logger.warning(f"No selectors defined for portal: {portal_name}")
                return False
                
            # Get login selectors
            selectors = self.portal_selectors[portal_name]["login"]
            
            # Fill login form
            form_data = {
                selectors["username"]: credentials["username"],
                selectors["password"]: credentials["password"]
            }
            self.browser.fill_form(form_data)
            
            # Submit form
            if not self.browser.click_element(selectors["submit"]):
                logger.error(f"Failed to click submit button for {portal_name}")
                return False
                
            # Take screenshot after login
            self.browser.take_screenshot(f"{portal_name}_login.png")
            
            # Add delay for login to complete
            time.sleep(3)
            
            # Check for login success (this would need to be customized per portal)
            # For now, just return True
            logger.info(f"Successfully logged into {portal_name}")
            return True
            
        except Exception as e:
            logger.error(f"Error logging into {portal_name}: {e}")
            return False
            
    def start_quote(self, portal_name, zip_code):
        """
        Start a new insurance quote
        
        Args:
            portal_name: Name of the portal
            zip_code: ZIP code for the quote
            
        Returns:
            True if successful, False otherwise
        """
        if not self.browser:
            logger.error("Browser session not started")
            return False
            
        # Check if portal is in selectors
        if portal_name not in self.portal_selectors:
            logger.warning(f"No selectors defined for portal: {portal_name}")
            return False
            
        try:
            # Get quote selectors
            selectors = self.portal_selectors[portal_name]["quote"]
            
            # Click on new quote button
            if not self.browser.click_element(selectors["new_quote"], by=By.XPATH):
                logger.error(f"Failed to click new quote button for {portal_name}")
                return False
                
            # Enter ZIP code
            form_data = {
                selectors["zip_code"]: zip_code
            }
            self.browser.fill_form(form_data)
            
            # Click continue
            if not self.browser.click_element(selectors["continue"], 
                                              by=By.XPATH if "//" in selectors["continue"] else By.CSS_SELECTOR):
                logger.error(f"Failed to click continue button for {portal_name}")
                return False
                
            # Take screenshot after starting quote
            self.browser.take_screenshot(f"{portal_name}_quote.png")
            
            logger.info(f"Successfully started quote on {portal_name}")
            return True
            
        except Exception as e:
            logger.error(f"Error starting quote on {portal_name}: {e}")
            return False


class EmailAutomator:
    """Automates interactions with email accounts"""
    
    def __init__(self, credential_manager, email_manager):
        """Initialize email automator"""
        self.credential_manager = credential_manager
        self.email_manager = email_manager
        self.browser = None
        
        # Email provider selectors
        self.email_selectors = {
            "gmail": {
                "login": {
                    "email": "input[type='email']",
                    "next_button": "#identifierNext",
                    "password": "input[type='password']",
                    "submit": "#passwordNext"
                },
                "compose": {
                    "compose_button": "//div[text()='Compose']",
                    "to": "//input[@role='combobox']",
                    "subject": "//input[@name='subjectbox']",
                    "body": "//div[@role='textbox']",
                    "send": "//div[text()='Send']"
                }
            }
        }
        
    def start_browser(self, headless=False):
        """Start browser session"""
        self.browser = BrowserSession(headless=headless)
        self.browser.start()
        
    def stop_browser(self):
        """Stop browser session"""
        if self.browser:
            self.browser.stop()
            self.browser = None
            
    def login_to_email(self, email_address):
        """
        Log into an email account
        
        Args:
            email_address: Email address to log into
            
        Returns:
            True if successful, False otherwise
        """
        if not self.browser:
            self.start_browser()
            
        # Get email access details
        email_access = self.email_manager.get_email_access(email_address)
        if "error" in email_access:
            logger.error(f"Error accessing email {email_address}: {email_access['error']}")
            return False
            
        credentials = email_access["credentials"]
        email_type = credentials.get("type", "gmail")
        
        # Check if email type is supported
        if email_type not in self.email_selectors:
            logger.warning(f"No selectors defined for email type: {email_type}")
            return False
            
        # Navigate to Gmail
        try:
            self.browser.navigate("https://mail.google.com")
            
            # Get login selectors
            selectors = self.email_selectors[email_type]["login"]
            
            # Enter email
            self.browser.wait_for_element(selectors["email"])
            form_data = {
                selectors["email"]: email_address
            }
            self.browser.fill_form(form_data)
            
            # Click next
            if not self.browser.click_element(selectors["next_button"]):
                logger.error(f"Failed to click next button for {email_address}")
                return False
                
            # Wait for password field
            time.sleep(2)
            
            # Enter password
            self.browser.wait_for_element(selectors["password"])
            form_data = {
                selectors["password"]: credentials["password"]
            }
            self.browser.fill_form(form_data)
            
            # Click submit
            if not self.browser.click_element(selectors["submit"]):
                logger.error(f"Failed to click submit button for {email_address}")
                return False
                
            # Take screenshot after login
            self.browser.take_screenshot(f"{email_address.split('@')[0]}_login.png")
            
            # Add delay for login to complete
            time.sleep(5)
            
            logger.info(f"Successfully logged into {email_address}")
            return True
            
        except Exception as e:
            logger.error(f"Error logging into {email_address}: {e}")
            return False
            
    def send_email(self, from_email, to_email, subject, body):
        """
        Send an email
        
        Args:
            from_email: Sender's email address
            to_email: Recipient's email address
            subject: Email subject
            body: Email body
            
        Returns:
            True if successful, False otherwise
        """
        if not self.browser:
            logger.error("Browser session not started")
            return False
            
        # Get email type
        email_access = self.email_manager.get_email_access(from_email)
        if "error" in email_access:
            logger.error(f"Error accessing email {from_email}: {email_access['error']}")
            return False
            
        credentials = email_access["credentials"]
        email_type = credentials.get("type", "gmail")
        
        # Check if email type is supported
        if email_type not in self.email_selectors:
            logger.warning(f"No selectors defined for email type: {email_type}")
            return False
            
        try:
            # Get compose selectors
            selectors = self.email_selectors[email_type]["compose"]
            
            # Click compose button
            if not self.browser.click_element(selectors["compose_button"], by=By.XPATH):
                logger.error(f"Failed to click compose button for {from_email}")
                return False
                
            # Wait for compose form
            time.sleep(2)
            
            # Fill compose form
            self.browser.wait_for_element(selectors["to"], by=By.XPATH)
            
            form_data = {
                selectors["to"]: to_email,
                selectors["subject"]: subject,
                selectors["body"]: body
            }
            
            # Fill each field separately with appropriate waits
            for selector, value in form_data.items():
                element = self.browser.wait_for_element(selector, by=By.XPATH)
                if element:
                    element.clear()
                    element.send_keys(value)
                    time.sleep(0.5)
                    
            # Click send
            if not self.browser.click_element(selectors["send"], by=By.XPATH):
                logger.error(f"Failed to click send button for {from_email}")
                return False
                
            # Take screenshot after sending
            self.browser.take_screenshot(f"{from_email.split('@')[0]}_send_email.png")
            
            logger.info(f"Successfully sent email from {from_email} to {to_email}")
            return True
            
        except Exception as e:
            logger.error(f"Error sending email from {from_email}: {e}")
            return False


# Main example function
def run_automation_example():
    """Run an example of portal and email automation"""
    # Set up credential system
    managers = setup_credential_system()
    credential_manager = managers["credential_manager"]
    portal_manager = managers["portal_manager"]
    email_manager = managers["email_manager"]
    
    # Create automation instances
    portal_automator = PortalAutomator(credential_manager, portal_manager)
    email_automator = EmailAutomator(credential_manager, email_manager)
    
    # Example: Log into HealthSherpa
    try:
        logger.info("Attempting to log into HealthSherpa...")
        portal_automator.login_to_portal("healthsherpa")
        
        # Start a quote
        portal_automator.start_quote("healthsherpa", "33401")
        
        # Log out and clean up
        portal_automator.stop_browser()
    except Exception as e:
        logger.error(f"Error in portal automation: {e}")
        if portal_automator.browser:
            portal_automator.stop_browser()
    
    # Example: Log into Gmail and send an email
    try:
        logger.info("Attempting to log into Gmail...")
        
        # Log into the main insurance email
        email_address = "<EMAIL>"
        email_automator.login_to_email(email_address)
        
        # Send a test email
        email_automator.send_email(
            from_email=email_address,
            to_email="<EMAIL>",
            subject="Test Email from Automation",
            body="This is a test email sent by the automation system."
        )
        
        # Log out and clean up
        email_automator.stop_browser()
    except Exception as e:
        logger.error(f"Error in email automation: {e}")
        if email_automator.browser:
            email_automator.stop_browser()


# Execute if run directly
if __name__ == "__main__":
    try:
        run_automation_example()
        logger.info("Automation example completed")
    except Exception as e:
        logger.error(f"Error running automation example: {e}")