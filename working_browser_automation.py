#!/usr/bin/env python3
"""
Working Browser Automation System
=================================

A lightweight browser automation system that works without requiring
large browser downloads. Uses system Chrome with webdriver-manager.
"""

import asyncio
import json
import time
import traceback
from dataclasses import dataclass, asdict
from typing import List, Dict, Any, Optional
from pathlib import Path

# Browser automation imports
try:
    from selenium import webdriver
    from selenium.webdriver.chrome.options import Options
    from selenium.webdriver.common.by import By
    from selenium.webdriver.support.ui import WebDriverWait
    from selenium.webdriver.support import expected_conditions as EC
    from selenium.webdriver.common.action_chains import ActionChains
    from webdriver_manager.chrome import ChromeDriverManager
    from selenium.webdriver.chrome.service import Service
    SELENIUM_AVAILABLE = True
except ImportError:
    SELENIUM_AVAILABLE = False

# Web server imports
try:
    from aiohttp import web, WSMsgType
    import aiohttp_cors
    AIOHTTP_AVAILABLE = True
except ImportError:
    AIOHTTP_AVAILABLE = False

@dataclass
class BrowserTask:
    """Browser automation task"""
    id: str
    name: str
    url: str
    actions: List[Dict[str, Any]]
    status: str = "pending"
    result: Optional[Dict[str, Any]] = None
    error: Optional[str] = None
    created_at: float = None
    completed_at: Optional[float] = None
    
    def __post_init__(self):
        if self.created_at is None:
            self.created_at = time.time()

class WorkingBrowserAutomation:
    """Working browser automation system"""
    
    def __init__(self, headless: bool = True):
        self.headless = headless
        self.tasks: Dict[str, BrowserTask] = {}
        self.driver = None
        self.is_running = False
        
    def setup_chrome_driver(self):
        """Setup Chrome driver with webdriver-manager"""
        try:
            print("🔧 Setting up Chrome driver...")
            
            # Chrome options
            chrome_options = Options()
            if self.headless:
                chrome_options.add_argument("--headless")
            chrome_options.add_argument("--no-sandbox")
            chrome_options.add_argument("--disable-dev-shm-usage")
            chrome_options.add_argument("--disable-gpu")
            chrome_options.add_argument("--window-size=1920,1080")
            chrome_options.add_argument("--user-agent=Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36")
            
            # Use webdriver-manager to get Chrome driver
            service = Service(ChromeDriverManager().install())
            
            # Create driver
            self.driver = webdriver.Chrome(service=service, options=chrome_options)
            self.driver.implicitly_wait(10)
            
            print("✅ Chrome driver setup successful!")
            return True
            
        except Exception as e:
            print(f"❌ Chrome driver setup failed: {e}")
            return False
    
    def execute_action(self, action: Dict[str, Any]) -> Dict[str, Any]:
        """Execute a single browser action"""
        action_type = action.get("type")
        target = action.get("target", "")
        value = action.get("value", "")
        
        try:
            if action_type == "navigate":
                self.driver.get(target)
                return {"success": True, "message": f"Navigated to {target}"}
                
            elif action_type == "click":
                element = self.driver.find_element(By.CSS_SELECTOR, target)
                element.click()
                return {"success": True, "message": f"Clicked {target}"}
                
            elif action_type == "type":
                element = self.driver.find_element(By.CSS_SELECTOR, target)
                element.clear()
                element.send_keys(value)
                return {"success": True, "message": f"Typed '{value}' into {target}"}
                
            elif action_type == "wait":
                time.sleep(float(value))
                return {"success": True, "message": f"Waited {value} seconds"}
                
            elif action_type == "screenshot":
                filename = f"screenshot_{int(time.time())}.png"
                self.driver.save_screenshot(filename)
                return {"success": True, "message": f"Screenshot saved as {filename}"}
                
            elif action_type == "get_text":
                element = self.driver.find_element(By.CSS_SELECTOR, target)
                text = element.text
                return {"success": True, "message": f"Got text: {text}", "data": text}
                
            elif action_type == "get_title":
                title = self.driver.title
                return {"success": True, "message": f"Got title: {title}", "data": title}
                
            elif action_type == "get_url":
                url = self.driver.current_url
                return {"success": True, "message": f"Current URL: {url}", "data": url}
                
            else:
                return {"success": False, "message": f"Unknown action type: {action_type}"}
                
        except Exception as e:
            return {"success": False, "message": f"Action failed: {str(e)}"}
    
    def execute_task(self, task: BrowserTask) -> Dict[str, Any]:
        """Execute a complete browser task"""
        print(f"🚀 Executing task: {task.name}")
        
        if not self.driver:
            if not self.setup_chrome_driver():
                task.status = "failed"
                task.error = "Failed to setup Chrome driver"
                return {"success": False, "error": task.error}
        
        task.status = "running"
        results = []
        
        try:
            for i, action in enumerate(task.actions):
                print(f"  📝 Action {i+1}/{len(task.actions)}: {action.get('type', 'unknown')}")
                result = self.execute_action(action)
                results.append(result)
                
                if not result.get("success", False):
                    print(f"  ❌ Action failed: {result.get('message', 'Unknown error')}")
                    break
                else:
                    print(f"  ✅ Action succeeded: {result.get('message', 'Success')}")
            
            task.status = "completed"
            task.completed_at = time.time()
            task.result = {
                "success": True,
                "actions_executed": len(results),
                "results": results,
                "execution_time": task.completed_at - task.created_at
            }
            
            print(f"✅ Task completed: {task.name}")
            return task.result
            
        except Exception as e:
            task.status = "failed"
            task.error = str(e)
            task.result = {
                "success": False,
                "error": task.error,
                "results": results
            }
            print(f"❌ Task failed: {task.name} - {task.error}")
            return task.result
    
    def create_task(self, name: str, url: str, actions: List[Dict[str, Any]]) -> str:
        """Create a new browser task"""
        task_id = f"task_{int(time.time())}_{len(self.tasks)}"
        task = BrowserTask(
            id=task_id,
            name=name,
            url=url,
            actions=actions
        )
        self.tasks[task_id] = task
        print(f"📝 Created task: {name} ({task_id})")
        return task_id
    
    def get_task(self, task_id: str) -> Optional[BrowserTask]:
        """Get a task by ID"""
        return self.tasks.get(task_id)
    
    def list_tasks(self) -> List[Dict[str, Any]]:
        """List all tasks"""
        return [asdict(task) for task in self.tasks.values()]
    
    def cleanup(self):
        """Cleanup resources"""
        if self.driver:
            try:
                self.driver.quit()
                print("🧹 Browser driver cleaned up")
            except:
                pass
            self.driver = None

# Web interface for browser automation
class BrowserAutomationServer:
    """Web server for browser automation"""
    
    def __init__(self, host: str = "localhost", port: int = 8080):
        self.host = host
        self.port = port
        self.app = web.Application()
        self.automation = WorkingBrowserAutomation()
        self.setup_routes()
    
    def setup_routes(self):
        """Setup web routes"""
        self.app.router.add_get('/', self.index)
        self.app.router.add_get('/api/status', self.status)
        self.app.router.add_get('/api/tasks', self.get_tasks)
        self.app.router.add_post('/api/tasks', self.create_task)
        self.app.router.add_get('/api/tasks/{task_id}', self.get_task)
        self.app.router.add_post('/api/tasks/{task_id}/execute', self.execute_task)
        
        # Enable CORS
        if AIOHTTP_AVAILABLE:
            cors = aiohttp_cors.setup(self.app, defaults={
                "*": aiohttp_cors.ResourceOptions(
                    allow_credentials=True,
                    expose_headers="*",
                    allow_headers="*",
                    allow_methods="*"
                )
            })
            for route in list(self.app.router.routes()):
                cors.add(route)
    
    async def index(self, request):
        """Serve main page"""
        html = """
        <!DOCTYPE html>
        <html>
        <head>
            <title>Working Browser Automation</title>
            <style>
                body { font-family: Arial, sans-serif; margin: 20px; }
                .container { max-width: 1200px; margin: 0 auto; }
                .section { margin: 20px 0; padding: 20px; border: 1px solid #ddd; border-radius: 5px; }
                .button { background: #007bff; color: white; padding: 10px 20px; border: none; border-radius: 3px; cursor: pointer; }
                .button:hover { background: #0056b3; }
                .task { margin: 10px 0; padding: 10px; background: #f8f9fa; border-radius: 3px; }
                .status-pending { color: #ffc107; }
                .status-running { color: #17a2b8; }
                .status-completed { color: #28a745; }
                .status-failed { color: #dc3545; }
                textarea { width: 100%; height: 100px; }
                input[type="text"] { width: 100%; padding: 5px; }
            </style>
        </head>
        <body>
            <div class="container">
                <h1>🤖 Working Browser Automation</h1>
                
                <div class="section">
                    <h2>System Status</h2>
                    <div id="status">Loading...</div>
                    <button class="button" onclick="checkStatus()">Refresh Status</button>
                </div>
                
                <div class="section">
                    <h2>Create New Task</h2>
                    <div>
                        <label>Task Name:</label><br>
                        <input type="text" id="taskName" placeholder="My Browser Task">
                    </div>
                    <div>
                        <label>URL:</label><br>
                        <input type="text" id="taskUrl" placeholder="https://example.com">
                    </div>
                    <div>
                        <label>Actions (JSON):</label><br>
                        <textarea id="taskActions" placeholder='[{"type": "navigate", "target": "https://example.com"}, {"type": "get_title"}]'></textarea>
                    </div>
                    <button class="button" onclick="createTask()">Create Task</button>
                </div>
                
                <div class="section">
                    <h2>Tasks</h2>
                    <div id="tasks">Loading...</div>
                    <button class="button" onclick="loadTasks()">Refresh Tasks</button>
                </div>
            </div>
            
            <script>
                async function checkStatus() {
                    try {
                        const response = await fetch('/api/status');
                        const data = await response.json();
                        document.getElementById('status').innerHTML = `
                            <strong>Status:</strong> ${data.status}<br>
                            <strong>Selenium Available:</strong> ${data.selenium_available}<br>
                            <strong>Tasks:</strong> ${data.task_count}<br>
                            <strong>Uptime:</strong> ${data.uptime}s
                        `;
                    } catch (error) {
                        document.getElementById('status').innerHTML = 'Error loading status';
                    }
                }
                
                async function createTask() {
                    const name = document.getElementById('taskName').value;
                    const url = document.getElementById('taskUrl').value;
                    const actionsText = document.getElementById('taskActions').value;
                    
                    try {
                        const actions = JSON.parse(actionsText);
                        const response = await fetch('/api/tasks', {
                            method: 'POST',
                            headers: {'Content-Type': 'application/json'},
                            body: JSON.stringify({name, url, actions})
                        });
                        const data = await response.json();
                        alert('Task created: ' + data.task_id);
                        loadTasks();
                    } catch (error) {
                        alert('Error creating task: ' + error.message);
                    }
                }
                
                async function loadTasks() {
                    try {
                        const response = await fetch('/api/tasks');
                        const tasks = await response.json();
                        const tasksDiv = document.getElementById('tasks');
                        tasksDiv.innerHTML = tasks.map(task => `
                            <div class="task">
                                <strong>${task.name}</strong> (${task.id})<br>
                                <span class="status-${task.status}">Status: ${task.status}</span><br>
                                URL: ${task.url}<br>
                                Actions: ${task.actions.length}<br>
                                ${task.status === 'pending' ? `<button class="button" onclick="executeTask('${task.id}')">Execute</button>` : ''}
                                ${task.result ? `<br>Result: ${JSON.stringify(task.result, null, 2)}` : ''}
                            </div>
                        `).join('');
                    } catch (error) {
                        document.getElementById('tasks').innerHTML = 'Error loading tasks';
                    }
                }
                
                async function executeTask(taskId) {
                    try {
                        const response = await fetch(`/api/tasks/${taskId}/execute`, {method: 'POST'});
                        const data = await response.json();
                        alert('Task execution result: ' + JSON.stringify(data, null, 2));
                        loadTasks();
                    } catch (error) {
                        alert('Error executing task: ' + error.message);
                    }
                }
                
                // Load initial data
                checkStatus();
                loadTasks();
                
                // Auto-refresh every 5 seconds
                setInterval(() => {
                    checkStatus();
                    loadTasks();
                }, 5000);
            </script>
        </body>
        </html>
        """
        return web.Response(text=html, content_type='text/html')
    
    async def status(self, request):
        """Get system status"""
        return web.json_response({
            "status": "operational",
            "selenium_available": SELENIUM_AVAILABLE,
            "task_count": len(self.automation.tasks),
            "uptime": time.time() - getattr(self, 'start_time', time.time())
        })
    
    async def get_tasks(self, request):
        """Get all tasks"""
        return web.json_response(self.automation.list_tasks())
    
    async def create_task(self, request):
        """Create a new task"""
        try:
            data = await request.json()
            task_id = self.automation.create_task(
                name=data['name'],
                url=data['url'],
                actions=data['actions']
            )
            return web.json_response({"success": True, "task_id": task_id})
        except Exception as e:
            return web.json_response({"success": False, "error": str(e)}, status=400)
    
    async def get_task(self, request):
        """Get a specific task"""
        task_id = request.match_info['task_id']
        task = self.automation.get_task(task_id)
        if task:
            return web.json_response(asdict(task))
        else:
            return web.json_response({"error": "Task not found"}, status=404)
    
    async def execute_task(self, request):
        """Execute a task"""
        task_id = request.match_info['task_id']
        task = self.automation.get_task(task_id)
        if not task:
            return web.json_response({"error": "Task not found"}, status=404)
        
        try:
            result = self.automation.execute_task(task)
            return web.json_response(result)
        except Exception as e:
            return web.json_response({"success": False, "error": str(e)}, status=500)
    
    async def start_server(self):
        """Start the web server"""
        self.start_time = time.time()
        runner = web.AppRunner(self.app)
        await runner.setup()
        site = web.TCPSite(runner, self.host, self.port)
        await site.start()
        print(f"🌐 Browser Automation Server running at http://{self.host}:{self.port}")
        return runner

def main():
    """Main function"""
    print("🤖 Starting Working Browser Automation System...")
    
    if not SELENIUM_AVAILABLE:
        print("❌ Selenium not available. Please install: pip install selenium webdriver-manager")
        return
    
    if not AIOHTTP_AVAILABLE:
        print("❌ aiohttp not available. Please install: pip install aiohttp aiohttp-cors")
        return
    
    # Test basic functionality
    automation = WorkingBrowserAutomation()
    
    # Create a test task
    task_id = automation.create_task(
        name="Test Google Search",
        url="https://www.google.com",
        actions=[
            {"type": "navigate", "target": "https://www.google.com"},
            {"type": "get_title"},
            {"type": "get_url"}
        ]
    )
    
    print(f"📝 Created test task: {task_id}")
    
    # Execute the task
    task = automation.get_task(task_id)
    if task:
        result = automation.execute_task(task)
        print(f"✅ Task result: {result}")
    
    automation.cleanup()
    
    # Start web server
    async def run_server():
        server = BrowserAutomationServer()
        runner = await server.start_server()
        try:
            while True:
                await asyncio.sleep(1)
        except KeyboardInterrupt:
            print("\n🛑 Shutting down...")
        finally:
            await runner.cleanup()
            server.automation.cleanup()
    
    asyncio.run(run_server())

if __name__ == "__main__":
    main()
