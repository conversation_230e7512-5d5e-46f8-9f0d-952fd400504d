#!/usr/bin/env python3
"""
Phone Number Formatting Utility for Paul <PERSON> AI

This module provides utilities for formatting phone numbers in various formats
commonly used across the system.
"""

import re


def clean_phone_number(phone):
    """
    Clean a phone number by removing all non-digit characters.
    Returns only the digits.
    """
    if not phone:
        return ""
    
    # Remove all non-digit characters
    digits_only = re.sub(r'\D', '', str(phone))
    return digits_only


def format_phone_number(phone, format_type=1):
    """
    Format a phone number according to the specified format type.
    
    Args:
        phone (str): The phone number to format (can contain non-digit characters)
        format_type (int): The format type to use (1-4)
            1: (************* 
            2: ************
            3: ************
            4: 5551234567 (no formatting)
    
    Returns:
        str: The formatted phone number, or the original input if invalid
    """
    # Clean the phone number first
    digits = clean_phone_number(phone)
    
    # Check if we have a valid phone number (at least 10 digits)
    if len(digits) < 10:
        return phone  # Return original if not enough digits
    
    # Extract the parts of the phone number
    # If more than 10 digits, treat first digits as country code
    if len(digits) > 10:
        country_code = digits[:-10]
        area_code = digits[-10:-7]
        prefix = digits[-7:-4]
        suffix = digits[-4:]
    else:
        country_code = ""
        area_code = digits[-10:-7]
        prefix = digits[-7:-4]
        suffix = digits[-4:]
    
    # Format based on the format type
    if format_type == 1:
        # Format: (*************
        formatted = f"({area_code}) {prefix}-{suffix}"
        if country_code:
            formatted = f"+{country_code} {formatted}"
    elif format_type == 2:
        # Format: ************
        formatted = f"{area_code}-{prefix}-{suffix}"
        if country_code:
            formatted = f"+{country_code}-{formatted}"
    elif format_type == 3:
        # Format: ************
        formatted = f"{area_code}.{prefix}.{suffix}"
        if country_code:
            formatted = f"+{country_code}.{formatted}"
    else:  # format_type == 4 or any other value
        # Format: 5551234567 (no formatting)
        if country_code:
            formatted = f"+{country_code}{area_code}{prefix}{suffix}"
        else:
            formatted = f"{area_code}{prefix}{suffix}"
    
    return formatted


def get_phone_format_options():
    """
    Returns a list of available phone number format options with descriptions.
    
    Returns:
        list: A list of tuples containing (format_id, description, example)
    """
    return [
        (1, "Parentheses Format", "(*************"),
        (2, "Dash Format", "************"),
        (3, "Dot Format", "************"),
        (4, "No Format", "5551234567")
    ]


if __name__ == "__main__":
    # Example usage and testing
    test_numbers = [
        "5551234567",         # Basic 10-digit
        "(*************",     # Already formatted
        "************",       # Dash format
        "+1 (*************",  # With country code
        "15551234567",        # With country code no separator
        "************",       # Dot format
        "************",       # Space format
        "ABC1234567",         # With letters (should be removed)
        "55512345",           # Too few digits (invalid)
    ]
    
    print("Phone Number Formatting Examples:")
    print("=" * 50)
    
    for number in test_numbers:
        print(f"Original: {number}")
        for format_id, desc, _ in get_phone_format_options():
            formatted = format_phone_number(number, format_id)
            print(f"  Format {format_id} ({desc}): {formatted}")
        print("-" * 30)