#!/usr/bin/env python3
"""
UI-TARS-1.5-7B Model Server

This script runs the ByteDance-Seed/UI-TARS-1.5-7B model using vLLM.
"""

import os
import sys
import argparse
import subprocess
import time
import signal
import atexit
from pathlib import Path

def check_vllm_installed():
    """Check if vLLM is installed."""
    try:
        import vllm
        print(f"✅ vLLM is installed (version {vllm.__version__})")
        return True
    except ImportError:
        print("❌ vLLM is not installed. Please run install_vllm.py first.")
        return False

def download_model(model_name, model_dir):
    """Download the model if not already downloaded."""
    model_path = Path(model_dir) / model_name.split('/')[-1]
    
    if model_path.exists():
        print(f"✅ Model already downloaded at {model_path}")
        return str(model_path)
    
    print(f"📥 Downloading model {model_name}...")
    
    try:
        from huggingface_hub import snapshot_download
        
        # Download the model
        local_path = snapshot_download(
            repo_id=model_name,
            local_dir=str(model_path),
            local_dir_use_symlinks=False
        )
        
        print(f"✅ Model downloaded to {local_path}")
        return local_path
    except Exception as e:
        print(f"❌ Failed to download model: {e}")
        print("⚠️ Will try to use the model directly from Hugging Face")
        return model_name

def start_server(model_name, model_dir, port, tensor_parallel_size=1, gpu_memory_utilization=0.9):
    """Start the vLLM server with the specified model."""
    if not check_vllm_installed():
        return None
    
    # Prepare the model path
    model_path = download_model(model_name, model_dir)
    
    # Build the command
    cmd = [
        sys.executable, "-m", "vllm.entrypoints.openai.api_server",
        "--served-model-name", model_name.split('/')[-1],
        "--model", model_path,
        "--port", str(port),
        "--tensor-parallel-size", str(tensor_parallel_size),
        "--gpu-memory-utilization", str(gpu_memory_utilization)
    ]
    
    print(f"🚀 Starting vLLM server with command: {' '.join(cmd)}")
    
    # Start the server
    server_process = subprocess.Popen(cmd)
    
    # Register cleanup function
    def cleanup():
        if server_process.poll() is None:
            print("🛑 Stopping vLLM server...")
            server_process.send_signal(signal.SIGINT)
            server_process.wait()
    
    atexit.register(cleanup)
    
    # Wait for server to start
    print(f"⏳ Waiting for server to start on port {port}...")
    time.sleep(5)
    
    return server_process

def main():
    parser = argparse.ArgumentParser(description="Run UI-TARS-1.5-7B model with vLLM")
    parser.add_argument("--model", default="ByteDance-Seed/UI-TARS-1.5-7B", help="Model name or path")
    parser.add_argument("--model-dir", default="./models", help="Directory to store downloaded models")
    parser.add_argument("--port", type=int, default=8000, help="Port to run the server on")
    parser.add_argument("--tensor-parallel-size", type=int, default=1, help="Tensor parallel size")
    parser.add_argument("--gpu-memory-utilization", type=float, default=0.9, help="GPU memory utilization")
    args = parser.parse_args()
    
    # Create model directory if it doesn't exist
    os.makedirs(args.model_dir, exist_ok=True)
    
    # Start the server
    server_process = start_server(
        args.model,
        args.model_dir,
        args.port,
        args.tensor_parallel_size,
        args.gpu_memory_utilization
    )
    
    if server_process:
        print(f"✅ Server started on http://localhost:{args.port}")
        print("📝 Example curl command to test the server:")
        print(f"""curl -X POST "http://localhost:{args.port}/v1/chat/completions" \\
    -H "Content-Type: application/json" \\
    --data '{{
        "model": "{args.model.split('/')[-1]}",
        "messages": [
            {{
                "role": "user",
                "content": [
                    {{
                        "type": "text",
                        "text": "Describe this image in one sentence."
                    }},
                    {{
                        "type": "image_url",
                        "image_url": {{
                            "url": "https://cdn.britannica.com/61/93061-050-99147DCE/Statue-of-Liberty-Island-New-York-Bay.jpg"
                        }}
                    }}
                ]
            }}
        ]
    }}'""")
        
        try:
            # Keep the script running until Ctrl+C
            server_process.wait()
        except KeyboardInterrupt:
            print("\n🛑 Stopping server...")
    
if __name__ == "__main__":
    main()
