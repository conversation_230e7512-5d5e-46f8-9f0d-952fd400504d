#!/bin/bash

# Send SMS to <PERSON> using curl and Twilio API

# Twilio credentials
TWILIO_ACCOUNT_SID="AC187c871afa232bbbc978caf33f3e25d9"
TWILIO_AUTH_TOKEN="CqpVewwter1BEMdFIFHrN2XmUyt22wBP"

# <PERSON> contact information
PAUL_PHONE="+***********"  # Primary number
PAUL_ALT_PHONE="+***********"  # Secondary number

# Message content
MESSAGE="Hi <PERSON>, this is <PERSON> with Flo Faction Insurance. I help people create tax-free retirement income without stock market risk. I've prepared a personalized analysis for you. Would you be interested in seeing this analysis? Visit https://www.flofaction.com for more information. Thanks!"

echo "========================================================================"
echo "SENDING SMS TO PAUL EDWARDS USING CURL"
echo "========================================================================"

# Send to primary number
echo
echo "SENDING TO PRIMARY NUMBER: $PAUL_PHONE"
echo "------------------------------------------------------------------------"

curl -X POST https://api.twilio.com/2010-04-01/Accounts/$TWILIO_ACCOUNT_SID/Messages.json \
  --data-urlencode "To=$PAUL_PHONE" \
  --data-urlencode "From=+***********" \
  --data-urlencode "Body=$MESSAGE" \
  -u "$TWILIO_ACCOUNT_SID:$TWILIO_AUTH_TOKEN"

echo
echo

# Send to alternative number
echo "SENDING TO ALTERNATIVE NUMBER: $PAUL_ALT_PHONE"
echo "------------------------------------------------------------------------"

curl -X POST https://api.twilio.com/2010-04-01/Accounts/$TWILIO_ACCOUNT_SID/Messages.json \
  --data-urlencode "To=$PAUL_ALT_PHONE" \
  --data-urlencode "From=+***********" \
  --data-urlencode "Body=$MESSAGE" \
  -u "$TWILIO_ACCOUNT_SID:$TWILIO_AUTH_TOKEN"

echo
echo "========================================================================"
echo "SMS SENDING COMPLETE"
echo "========================================================================"
