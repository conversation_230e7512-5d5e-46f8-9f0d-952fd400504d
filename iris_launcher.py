#!/usr/bin/env python3
"""
IRIS System Launcher
===================

Quick launcher for the IRIS system.
"""

import sys
import asyncio
from pathlib import Path

# Add current directory to path
sys.path.append(str(Path(__file__).parent))

def main():
    """Main launcher function"""
    print("🚀 IRIS System Launcher")
    print("=" * 30)
    print("1. Interactive CLI")
    print("2. Dashboard Mode")
    print("3. System Test")
    print("4. Help")
    print("5. Exit")
    
    while True:
        try:
            choice = input("\nSelect option (1-5): ").strip()
            
            if choice == "1":
                print("Starting IRIS Interactive CLI...")
                import iris_cli
                asyncio.run(iris_cli.main())
                break
            elif choice == "2":
                print("Starting IRIS Dashboard...")
                import iris_dashboard
                asyncio.run(iris_dashboard.main())
                break
            elif choice == "3":
                print("Running IRIS System Test...")
                import iris_system_test
                asyncio.run(iris_system_test.main())
                break
            elif choice == "4":
                print("\n📖 IRIS Help:")
                print("• Option 1: Interactive CLI for conversational interface")
                print("• Option 2: Dashboard mode for programmatic access")
                print("• Option 3: System test to verify all components")
                print("• See IRIS_USER_GUIDE.md for detailed instructions")
            elif choice == "5":
                print("👋 Goodbye!")
                break
            else:
                print("❌ Invalid option. Please select 1-5.")
                
        except KeyboardInterrupt:
            print("\n👋 Goodbye!")
            break
        except Exception as e:
            print(f"❌ Error: {e}")

if __name__ == "__main__":
    main()
