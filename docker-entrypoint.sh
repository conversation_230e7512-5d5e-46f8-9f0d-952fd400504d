#!/bin/bash
set -e

# Function to wait for a service to be ready
wait_for() {
    local host="$1"
    local port="$2"
    local service="$3"
    local timeout="${4:-30}"
    
    echo "Waiting for $service to be ready..."
    for i in $(seq 1 $timeout); do
        if nc -z "$host" "$port"; then
            echo "$service is ready!"
            return 0
        fi
        echo "Waiting for $service... ($i/$timeout)"
        sleep 1
    done
    echo >&2 "$service is not available"
    return 1
}

# Function to check configuration
check_config() {
    if [ ! -f "config/config.yml" ]; then
        echo "No configuration file found. Running setup..."
        ./manage.sh init
    fi
}

# Function to check data directories
check_directories() {
    dirs=("data" "config" "plugins" "logs")
    for dir in "${dirs[@]}"; do
        if [ ! -d "$dir" ]; then
            echo "Creating directory: $dir"
            mkdir -p "$dir"
        fi
    done
}

# Function to initialize database
init_database() {
    if [ "$INIT_DB" = "true" ]; then
        echo "Initializing database..."
        python -m alembic upgrade head
    fi
}

# Function to wait for dependencies
wait_for_dependencies() {
    # Wait for PostgreSQL
    if [ -n "$POSTGRES_HOST" ]; then
        wait_for "$POSTGRES_HOST" "${POSTGRES_PORT:-5432}" "PostgreSQL" 60
    fi
    
    # Wait for Redis
    if [ -n "$REDIS_HOST" ]; then
        wait_for "$REDIS_HOST" "${REDIS_PORT:-6379}" "Redis" 30
    fi
    
    # Wait for other services if needed
    if [ -n "$MESSAGE_BROKER_HOST" ]; then
        wait_for "$MESSAGE_BROKER_HOST" "${MESSAGE_BROKER_PORT:-5672}" "Message Broker" 30
    fi
}

# Main initialization sequence
main() {
    # Check system directories
    check_directories
    
    # Check configuration
    check_config
    
    # Wait for dependencies
    wait_for_dependencies
    
    # Initialize database if needed
    init_database
    
    # Start the system
    if [ "$1" = "start" ]; then
        echo "Starting agent system..."
        exec ./manage.sh start
    else
        # Execute passed command
        exec "$@"
    fi
}

# Execute main function with all arguments
main "$@"