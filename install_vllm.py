#!/usr/bin/env python3
"""
UI-TARS-1.5-7B Model Installation Script

This script installs vLLM and its dependencies for running the ByteDance-Seed/UI-TARS-1.5-7B model.
"""

import os
import sys
import subprocess
import platform
import argparse
from pathlib import Path

def check_cuda():
    """Check if CUDA is available and return the CUDA version."""
    try:
        # Try to import torch to check CUDA availability
        import torch
        if torch.cuda.is_available():
            cuda_version = torch.version.cuda
            print(f"✅ CUDA is available (version {cuda_version})")
            # Convert CUDA version to format used by PyTorch (e.g., 12.1 -> cu121)
            cuda_version = "".join(cuda_version.split('.')[:2])
            return f"cu{cuda_version}"
        else:
            print("⚠️ CUDA is not available. vLLM will run in CPU mode, which is very slow.")
            return None
    except ImportError:
        print("⚠️ PyTorch is not installed. Will install it with vLLM.")
        return None

def install_vllm(cuda_version=None, vllm_version="0.6.6"):
    """Install vLLM with the specified CUDA version."""
    print(f"📦 Installing vLLM version {vllm_version}...")
    
    # Install transformers first
    subprocess.check_call([sys.executable, "-m", "pip", "install", "-U", "transformers"])
    
    # Install vLLM with CUDA support if available
    if cuda_version:
        install_cmd = [
            sys.executable, "-m", "pip", "install", 
            f"vllm=={vllm_version}", 
            "--extra-index-url", f"https://download.pytorch.org/whl/{cuda_version}"
        ]
    else:
        install_cmd = [sys.executable, "-m", "pip", "install", f"vllm=={vllm_version}"]
    
    subprocess.check_call(install_cmd)
    print("✅ vLLM installation completed successfully!")

def install_dependencies():
    """Install other required dependencies."""
    print("📦 Installing additional dependencies...")
    dependencies = [
        "requests",
        "pillow",
        "pyyaml",
        "python-dotenv",
        "fastapi",
        "uvicorn"
    ]
    subprocess.check_call([sys.executable, "-m", "pip", "install"] + dependencies)
    print("✅ Dependencies installed successfully!")

def main():
    parser = argparse.ArgumentParser(description="Install vLLM for UI-TARS-1.5-7B model")
    parser.add_argument("--vllm-version", default="0.6.6", help="vLLM version to install")
    parser.add_argument("--skip-cuda-check", action="store_true", help="Skip CUDA check and install CPU version")
    args = parser.parse_args()
    
    print("🚀 Starting vLLM installation for UI-TARS-1.5-7B model...")
    
    # Check CUDA availability
    cuda_version = None if args.skip_cuda_check else check_cuda()
    
    # Install vLLM
    install_vllm(cuda_version, args.vllm_version)
    
    # Install other dependencies
    install_dependencies()
    
    print("\n🎉 Installation complete! You can now run the UI-TARS-1.5-7B model with vLLM.")
    print("📝 Example command to start the server:")
    print("python -m vllm.entrypoints.openai.api_server --served-model-name UI-TARS-1.5-7B --model ByteDance-Seed/UI-TARS-1.5-7B")

if __name__ == "__main__":
    main()
