"""
Complete Contact Solution for <PERSON>

This script provides a complete solution for contacting <PERSON> using:
1. <PERSON><PERSON><PERSON> for SMS and calls
2. Eleven Labs for voice synthesis
3. Email for written communication

It uses your verified Twilio number (+***********) as the sender.
"""

import os
import requests
import base64
import json
import time
import smtplib
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
import ssl
from dotenv import load_dotenv
import getpass
from datetime import datetime

# Load environment variables
load_dotenv()

# <PERSON> contact information
PAUL_EDWARDS = {
    "first_name": "<PERSON>",
    "last_name": "<PERSON>",
    "email": "<EMAIL>",
    "primary_phone": "+***********",
    "secondary_phone": "+***********"  # This is also your verified Twilio number
}

# Twilio credentials - using the ones from your instructions
TWILIO_ACCOUNT_SID = "AC187c871afa232bbbc978caf33f3e25d9"
TWILIO_AUTH_TOKEN = "CqpVewwter1BEMdFIFHrN2XmUyt22wBP"
TWILIO_PHONE_NUMBER = "+***********"  # Your verified Twilio number

# Eleven Labs credentials
ELEVEN_LABS_API_KEY = "sk_531ff39d6099a4191b57e4935654c909d6196b59d9339015"
ELEVEN_LABS_VOICE_ID = "21m00Tcm4TlvDq8ikWAM"  # Rachel voice ID (female voice)

# Agent information
AGENT_INFO = {
    "name": "Sandra Smith",
    "agency": "Flo Faction Insurance",
    "email": "<EMAIL>",
    "website": "https://www.flofaction.com/insurance",
}

# Gmail credentials - For security, prefer environment variables
GMAIL_EMAIL = "<EMAIL>"  # Replace with your actual Gmail
GMAIL_APP_PASSWORD = os.environ.get("GMAIL_APP_PASSWORD")  # Set this environment variable

def generate_eleven_labs_audio(text):
    """Generate audio using Eleven Labs API"""
    print("Generating audio with Eleven Labs...")
    
    url = f"https://api.elevenlabs.io/v1/text-to-speech/{ELEVEN_LABS_VOICE_ID}"
    
    headers = {
        "Accept": "audio/mpeg",
        "Content-Type": "application/json",
        "xi-api-key": ELEVEN_LABS_API_KEY
    }
    
    data = {
        "text": text,
        "model_id": "eleven_monolingual_v1",
        "voice_settings": {
            "stability": 0.5,
            "similarity_boost": 0.5
        }
    }
    
    try:
        response = requests.post(url, json=data, headers=headers)
        
        if response.status_code == 200:
            # Save the audio file
            with open("paul_edwards_message.mp3", "wb") as f:
                f.write(response.content)
            
            print("Audio generated successfully and saved as paul_edwards_message.mp3")
            return "paul_edwards_message.mp3"
        else:
            print(f"Error generating audio: {response.status_code} - {response.text}")
            return None
    except Exception as e:
        print(f"Exception generating audio: {str(e)}")
        return None

def send_sms_with_twilio():
    """Send an SMS to Paul Edwards using Twilio"""
    print("=" * 80)
    print("SENDING SMS TO PAUL EDWARDS USING TWILIO")
    print("=" * 80)
    
    # Create message content
    message = f"""
Hi {PAUL_EDWARDS['first_name']}, this is {AGENT_INFO['name']} with {AGENT_INFO['agency']}. I help people create tax-free retirement income without stock market risk. I've prepared a personalized analysis for you. Would you be interested in seeing this analysis? Visit {AGENT_INFO['website']} for more information. Thanks!
    """.strip()
    
    # Twilio API endpoint for sending messages
    url = f"https://api.twilio.com/2010-04-01/Accounts/{TWILIO_ACCOUNT_SID}/Messages.json"
    
    # Create the authentication header
    auth = base64.b64encode(f"{TWILIO_ACCOUNT_SID}:{TWILIO_AUTH_TOKEN}".encode()).decode()
    headers = {
        "Authorization": f"Basic {auth}",
        "Content-Type": "application/x-www-form-urlencoded"
    }
    
    # Prepare the request data
    data = {
        "To": PAUL_EDWARDS["primary_phone"],
        "From": TWILIO_PHONE_NUMBER,
        "Body": message
    }
    
    try:
        # Make the request
        response = requests.post(url, headers=headers, data=data)
        
        # Check if the request was successful
        if response.status_code >= 200 and response.status_code < 300:
            response_data = response.json()
            print(f"SMS sent successfully with SID: {response_data.get('sid')}")
            print(f"Status: {response_data.get('status')}")
            return True
        else:
            print(f"Error sending SMS: {response.status_code} - {response.text}")
            return False
    except Exception as e:
        print(f"Exception sending SMS: {str(e)}")
        return False

def make_call_with_twilio():
    """Make a phone call to Paul Edwards using Twilio"""
    print("=" * 80)
    print("MAKING PHONE CALL TO PAUL EDWARDS USING TWILIO")
    print("=" * 80)
    
    # Create TwiML for the call
    twiml = f"""
    <Response>
        <Say voice="woman">
            Hello {PAUL_EDWARDS['first_name']}, this is {AGENT_INFO['name']} with {AGENT_INFO['agency']}. How are you doing today?
            
            I'm reaching out because I help people like you create tax-free retirement income without the risk of the stock market. Based on your profile, I've found that you might qualify for a strategy that could provide around $5,000 per month in tax-free retirement income.
            
            I'd love to share a personalized analysis I've prepared for you. Please give me a call back when you have a moment, or visit our website at {AGENT_INFO['website']} for more information.
            
            Thank you and have a great day!
        </Say>
    </Response>
    """
    
    # Twilio API endpoint for making calls
    url = f"https://api.twilio.com/2010-04-01/Accounts/{TWILIO_ACCOUNT_SID}/Calls.json"
    
    # Create the authentication header
    auth = base64.b64encode(f"{TWILIO_ACCOUNT_SID}:{TWILIO_AUTH_TOKEN}".encode()).decode()
    headers = {
        "Authorization": f"Basic {auth}",
        "Content-Type": "application/x-www-form-urlencoded"
    }
    
    # Prepare the request data
    data = {
        "To": PAUL_EDWARDS["primary_phone"],
        "From": TWILIO_PHONE_NUMBER,
        "Twiml": twiml
    }
    
    try:
        # Make the request
        response = requests.post(url, headers=headers, data=data)
        
        # Check if the request was successful
        if response.status_code >= 200 and response.status_code < 300:
            response_data = response.json()
            print(f"Call initiated successfully with SID: {response_data.get('sid')}")
            print(f"Status: {response_data.get('status')}")
            return True
        else:
            print(f"Error initiating call: {response.status_code} - {response.text}")
            return False
    except Exception as e:
        print(f"Exception making call: {str(e)}")
        return False

def leave_voicemail_with_twilio():
    """Leave a voicemail for Paul Edwards using Twilio and Eleven Labs"""
    print("=" * 80)
    print("LEAVING VOICEMAIL FOR PAUL EDWARDS USING TWILIO AND ELEVEN LABS")
    print("=" * 80)
    
    # Create voicemail script
    voicemail_text = f"""
    Hi {PAUL_EDWARDS['first_name']}, this is {AGENT_INFO['name']} with {AGENT_INFO['agency']}. I'm calling because I help people like you create tax-free retirement income without the risk of the stock market.
    
    Based on your profile, I've found that you might qualify for a strategy that could provide significant tax-free retirement income while also protecting your family with a death benefit.
    
    I'd love to share a personalized analysis I've prepared for you. Please give me a call back when you have a moment, or visit our website at {AGENT_INFO['website']} for more information.
    
    Thank you and have a great day!
    """
    
    # Generate audio with Eleven Labs
    audio_file = generate_eleven_labs_audio(voicemail_text)
    
    if not audio_file:
        print("Failed to generate audio. Using Twilio's text-to-speech instead.")
        
        # Create TwiML for the voicemail using Twilio's text-to-speech
        twiml = f"""
        <Response>
            <Pause length="2"/>
            <Say voice="woman">
                {voicemail_text}
            </Say>
            <Hangup/>
        </Response>
        """
        
        # Twilio API endpoint for making calls
        url = f"https://api.twilio.com/2010-04-01/Accounts/{TWILIO_ACCOUNT_SID}/Calls.json"
        
        # Create the authentication header
        auth = base64.b64encode(f"{TWILIO_ACCOUNT_SID}:{TWILIO_AUTH_TOKEN}".encode()).decode()
        headers = {
            "Authorization": f"Basic {auth}",
            "Content-Type": "application/x-www-form-urlencoded"
        }
        
        # Prepare the request data
        data = {
            "To": PAUL_EDWARDS["primary_phone"],
            "From": TWILIO_PHONE_NUMBER,
            "Twiml": twiml,
            "SendDigits": "1"  # This might help to skip to voicemail on some systems
        }
    else:
        # TODO: Upload the audio file to a publicly accessible URL
        # For now, we'll use Twilio's text-to-speech as a fallback
        print("Audio file generated, but we need to upload it to a publicly accessible URL.")
        print("Using Twilio's text-to-speech as a fallback.")
        
        # Create TwiML for the voicemail using Twilio's text-to-speech
        twiml = f"""
        <Response>
            <Pause length="2"/>
            <Say voice="woman">
                {voicemail_text}
            </Say>
            <Hangup/>
        </Response>
        """
        
        # Twilio API endpoint for making calls
        url = f"https://api.twilio.com/2010-04-01/Accounts/{TWILIO_ACCOUNT_SID}/Calls.json"
        
        # Create the authentication header
        auth = base64.b64encode(f"{TWILIO_ACCOUNT_SID}:{TWILIO_AUTH_TOKEN}".encode()).decode()
        headers = {
            "Authorization": f"Basic {auth}",
            "Content-Type": "application/x-www-form-urlencoded"
        }
        
        # Prepare the request data
        data = {
            "To": PAUL_EDWARDS["primary_phone"],
            "From": TWILIO_PHONE_NUMBER,
            "Twiml": twiml,
            "SendDigits": "1"  # This might help to skip to voicemail on some systems
        }
    
    try:
        # Make the request
        response = requests.post(url, headers=headers, data=data)
        
        # Check if the request was successful
        if response.status_code >= 200 and response.status_code < 300:
            response_data = response.json()
            print(f"Voicemail call initiated successfully with SID: {response_data.get('sid')}")
            print(f"Status: {response_data.get('status')}")
            return True
        else:
            print(f"Error initiating voicemail call: {response.status_code} - {response.text}")
            return False
    except Exception as e:
        print(f"Exception leaving voicemail: {str(e)}")
        return False

def send_direct_email(subject, message_text, recipient="<EMAIL>"):
    """
    Send a simple email directly to Paul Edwards or other recipient
    
    Args:
        subject: Email subject line
        message_text: Plain text email body
        recipient: Email recipient (default: Paul Edwards)
        
    Returns:
        tuple: (success_boolean, message)
    """
    if not GMAIL_APP_PASSWORD:
        return False, "ERROR: GMAIL_APP_PASSWORD environment variable not set"

    try:
        # Create message
        message = MIMEMultipart("alternative")
        message["Subject"] = subject
        message["From"] = f"Flo Faction <{GMAIL_EMAIL}>"
        message["To"] = recipient
        
        # Create both plain text and simple HTML versions
        text_part = MIMEText(message_text, "plain")
        html_part = MIMEText(f"<html><body><p>{message_text.replace(chr(10), '<br>')}</p></body></html>", "html")
        
        message.attach(text_part)
        message.attach(html_part)
        
        # Send email using SSL for security
        context = ssl.create_default_context()
        with smtplib.SMTP_SSL("smtp.gmail.com", 465, context=context) as server:
            server.login(GMAIL_EMAIL, GMAIL_APP_PASSWORD)
            server.send_message(message)
        
        return True, f"Email successfully sent to {recipient}"
        
    except Exception as e:
        return False, f"Error sending email: {str(e)}"

def test_email_to_paul():
    """Send a test email to Paul Edwards to confirm the system works"""
    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    
    subject = f"System Test Email - {timestamp}"
    message = f"""Hello Paul Edwards,

This is a test email from your AI Agent system at {timestamp}.

This email confirms that the direct email functionality in your AI Agent system is working correctly.

If you're receiving this message, it means:
1. The email credentials are valid
2. The SMTP connection is working
3. The AI Agent can send notifications to you

Please reply to this email to confirm receipt.

Best regards,
Your AI Agent System
"""
    
    success, message = send_direct_email(subject, message)
    print(message)
    return success

def setup_email_instructions():
    """Print instructions for setting up the email integration"""
    print("\n" + "="*80)
    print("EMAIL SETUP INSTRUCTIONS")
    print("="*80)
    
    print("""
To set up this email system:

1. Generate a Gmail App Password:
   - Go to your Google Account settings: https://myaccount.google.com/
   - Click on "Security" in the left sidebar
   - Under "Signing in to Google," click on "2-Step Verification" (must be enabled)
   - Scroll to the bottom and click on "App passwords"
   - Select "Mail" as the app and "Other" as the device
   - Enter "AI Agent System" as the name
   - Click "Generate"
   - Copy the 16-character password shown

2. Set the App Password as an environment variable:
   - On macOS/Linux, run in terminal:
     export GMAIL_APP_PASSWORD="your_16_character_app_password"
   
   - To make it permanent, add to your ~/.bash_profile or ~/.zshrc:
     echo 'export GMAIL_APP_PASSWORD="your_16_character_app_password"' >> ~/.zshrc
     
   - On Windows, use System Properties > Environment Variables
     or run in Command Prompt:
     setx GMAIL_APP_PASSWORD "your_16_character_app_password"

3. Update the email address if needed:
   - Open this script and modify the GMAIL_EMAIL variable
   - Ensure you have access to this Gmail account

4. Test the email system:
   - Run this script to verify it works
   - Check that Paul Edwards receives the test email
""")
    print("="*80 + "\n")

def main():
    """Main function to contact Paul Edwards"""
    print("=" * 80)
    print("COMPLETE CONTACT SOLUTION FOR PAUL EDWARDS")
    print("=" * 80)
    
    print("\nThis script provides a complete solution for contacting Paul Edwards using:")
    print("1. Twilio for SMS and calls")
    print("2. Eleven Labs for voice synthesis")
    print("3. Email for written communication")
    
    print(f"\nPaul Edwards contact information:")
    print(f"Name: {PAUL_EDWARDS['first_name']} {PAUL_EDWARDS['last_name']}")
    print(f"Email: {PAUL_EDWARDS['email']}")
    print(f"Primary Phone: {PAUL_EDWARDS['primary_phone']}")
    print(f"Secondary Phone: {PAUL_EDWARDS['secondary_phone']}")
    
    print("\nWhat would you like to do?")
    print("1. Send SMS")
    print("2. Make phone call")
    print("3. Leave voicemail")
    print("4. Send email")
    print("5. All of the above")
    
    choice = input("\nEnter your choice (1-5): ")
    
    if choice == "1":
        send_sms_with_twilio()
    elif choice == "2":
        make_call_with_twilio()
    elif choice == "3":
        leave_voicemail_with_twilio()
    elif choice == "4":
        print("Sending test email to Paul Edwards...")
        if test_email_to_paul():
            print("Email system is working correctly!")
        else:
            print("Email system is not working. Please check the setup instructions.")
            setup_email_instructions()
    elif choice == "5":
        print("\nExecuting all contact methods...")
        
        # Send SMS
        sms_success = send_sms_with_twilio()
        
        # Wait a bit between communications
        if sms_success:
            print("Waiting 10 seconds before next communication...")
            time.sleep(10)
        
        # Make phone call
        call_success = make_call_with_twilio()
        
        # Wait a bit between communications
        if call_success:
            print("Waiting 30 seconds before next communication...")
            time.sleep(30)
        
        # Leave voicemail
        voicemail_success = leave_voicemail_with_twilio()
        
        # Wait a bit between communications
        if voicemail_success:
            print("Waiting 10 seconds before next communication...")
            time.sleep(10)
        
        # Send email
        print("Sending test email to Paul Edwards...")
        email_success = test_email_to_paul()
        
        # Print summary
        print("\n" + "=" * 80)
        print("CONTACT SUMMARY")
        print("=" * 80)
        
        print(f"SMS: {'Success' if sms_success else 'Failed'}")
        print(f"Phone Call: {'Success' if call_success else 'Failed'}")
        print(f"Voicemail: {'Success' if voicemail_success else 'Failed'}")
        print(f"Email: {'Success' if email_success else 'Failed'}")
    else:
        print("Invalid choice. Please run the script again.")
    
    print("\n" + "=" * 80)
    print("CONTACT OPERATIONS COMPLETED")
    print("=" * 80)

if __name__ == "__main__":
    # Check if Gmail app password is set
    if not GMAIL_APP_PASSWORD:
        print("WARNING: GMAIL_APP_PASSWORD environment variable not set!")
        print("Email functionality will not work without setting this variable.")
        setup_email_instructions()
        choice = input("Do you want to set up the Gmail App Password now? (y/n): ")
        
        if choice.lower() == 'y':
            password = input("Enter your Gmail App Password (will not be displayed): ")
            os.environ["GMAIL_APP_PASSWORD"] = password
            print("Password set temporarily for this session.")
        else:
            print("Exiting without sending email.")
            exit(0)
    
    main()
