"""
Twilio Environment Variables Test

This script tests Twilio authentication using environment variables.
"""

import os
from twilio.rest import Client

def test_twilio_env():
    """Test Twilio authentication using environment variables"""
    print("=" * 80)
    print("TESTING TWILIO ENVIRONMENT VARIABLES AUTHENTICATION")
    print("=" * 80)
    
    # Set environment variables
    os.environ["TWILIO_ACCOUNT_SID"] = "AC187c871afa232bbbc978caf33f3e25d9"
    os.environ["TWILIO_AUTH_TOKEN"] = "CqpVewwter1BEMdFIFHrN2XmUyt22wBP"
    
    try:
        # Initialize Twilio client using environment variables
        client = Client()
        
        # Try to fetch account information
        account = client.api.accounts(os.environ["TWILIO_ACCOUNT_SID"]).fetch()
        
        print(f"Authentication successful!")
        print(f"Account SID: {account.sid}")
        print(f"Account Name: {account.friendly_name}")
        print(f"Account Status: {account.status}")
        
        return True
    except Exception as e:
        print(f"Authentication failed: {str(e)}")
        return False

if __name__ == "__main__":
    test_twilio_env()
