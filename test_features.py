import os
from content_creation_agent import ContentCreationAgent
import logging
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_voice_and_instagram():
    """Test voice generation and Instagram posting."""
    try:
        # Initialize agent with credentials
        config = {
            "elevenlabs_api_key": os.getenv("ELEVENLABS_API_KEY"),
            "instagram_username": os.getenv("INSTAGRAM_USERNAME"),
            "instagram_password": os.getenv("INSTAGRAM_PASSWORD")
        }
        
        agent = ContentCreationAgent("FloFaction Agent", config=config)
        
        # 1. Test voice generation
        logger.info("Testing voice generation...")
        voice_result = agent.execute_task(
            "generate_voice",
            text="Welcome to FloFaction Insurance. We're here to protect what matters most to you. Contact us today to learn about our comprehensive coverage options."
        )
        
        if voice_result:
            logger.info("Voice generation successful!")
            
            # 2. Test Instagram posting with the generated voice content
            logger.info("Testing Instagram posting...")
            post_result = agent.execute_task(
                "post_to_instagram",
                media_path=voice_result,
                caption="🎯 Protecting What Matters Most!\n\n🏢 FloFaction Insurance - Your trusted partner in protection.\n\n#FloFaction #Insurance #Protection #Business #SecurityFirst"
            )
            
            if post_result:
                logger.info(f"Instagram post successful! Post ID: {post_result}")
            else:
                logger.error("Instagram posting failed")
        else:
            logger.error("Voice generation failed")
            
    except Exception as e:
        logger.error(f"Test failed with error: {str(e)}")
        raise

if __name__ == "__main__":
    logger.info("Starting FloFaction feature tests...")
    test_voice_and_instagram()
    logger.info("Testing completed!")