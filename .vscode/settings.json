{"files.autoSave": "onWindowChange", "altimate.onboardedMcpServer": true, "cline.modelSettings.o3Mini.reasoningEffort": "high", "github.copilot.chat.agent.thinkingTool": true, "github.copilot.chat.codesearch.enabled": true, "githubPullRequests.experimental.useQuickChat": true, "githubPullRequests.experimental.chat": true, "githubPullRequests.experimental.notificationsView": true, "CodeGPT.apiKey": "CodeGPT Plus Beta", "python-envs.defaultEnvManager": "ms-python.python:system", "python-envs.pythonProjects": [], "locale": "en", "github.copilot.lang": "en", "github.copilot.chat.localePreference": "en-US", "github.copilot.editor.enableAutoCompletions": true}