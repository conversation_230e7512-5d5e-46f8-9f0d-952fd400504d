{"version": "2.0.0", "tasks": [{"type": "docker-build", "label": "docker-build", "platform": "python", "dockerBuild": {"tag": "pauledward<PERSON>:latest", "dockerfile": "${workspaceFolder}/Dockerfile", "context": "${workspaceFolder}", "pull": true}}, {"type": "docker-run", "label": "docker-run: debug", "dependsOn": ["docker-build"], "python": {"file": "venv/lib/python3.13/site-packages/google_auth_httplib2.py"}}, {"label": "Activate MCP Servers", "type": "shell", "command": "python ${workspaceFolder}/start_mcp_servers.py", "problemMatcher": [], "presentation": {"reveal": "always", "panel": "new"}, "runOptions": {"runOn": "folderOpen"}, "group": {"kind": "build", "isDefault": true}}, {"label": "Check MCP Status", "type": "shell", "command": "python -c \"import asyncio; from start_mcp_servers import MCPServerActivator; activator = MCPServerActivator(); asyncio.run(activator.activate_all_servers()); activator.print_server_status()\"", "problemMatcher": [], "presentation": {"reveal": "always", "panel": "new"}}]}