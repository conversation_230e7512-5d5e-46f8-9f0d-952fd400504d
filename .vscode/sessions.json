{"$schema": "https://cdn.statically.io/gh/nguyenngoclongdev/cdn/main/schema/v10/terminal-keeper.json", "theme": "tribe", "active": "default", "activateOnStartup": true, "keepExistingTerminals": false, "sessions": {"default": [{"name": "hello", "autoExecuteCommands": true, "icon": "person", "color": "terminal.ansiGreen", "commands": ["echo hello"]}, [{"name": "docker:ros", "commands": [""]}, {"name": "docker:k8s", "commands": [""]}], [{"name": "docker:nats", "commands": [""]}, {"name": "docker:fleet", "commands": [""]}]], "saved-session": [{"name": "connect", "commands": [""]}]}}