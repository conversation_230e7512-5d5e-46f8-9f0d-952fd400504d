"""
Twilio Webhook Server

This script creates a simple Flask server that handles Twilio webhooks for
incoming calls, voicemails, and text messages. It can be deployed to a service
like Heroku, AWS, or Vercel to provide a public endpoint for Twilio.
"""

from flask import Flask, request, Response
import os
import json
from twilio.twiml.voice_response import VoiceResponse
from twilio.twiml.messaging_response import MessagingResponse
from twilio.rest import Client
import requests

app = Flask(__name__)

# Twilio credentials
TWILIO_ACCOUNT_SID = os.environ.get("TWILIO_ACCOUNT_SID", "AC187c871afa232bbbc978caf33f3e25d9")
TWILIO_AUTH_TOKEN = os.environ.get("TWILIO_AUTH_TOKEN", "CqpVewwter1BEMdFIFHrN2XmUyt22wBP")
TWILIO_PHONE_NUMBER = os.environ.get("TWILIO_PHONE_NUMBER", "+***********")

# Wix website webhook (to notify the website of incoming communications)
WIX_WEBHOOK_URL = os.environ.get("WIX_WEBHOOK_URL", "https://www.flofaction.com/_functions/twilioWebhook")

# Initialize Twilio client
client = Client(TWILIO_ACCOUNT_SID, TWILIO_AUTH_TOKEN)

# ======================== VOICE WEBHOOKS ========================
@app.route("/voice", methods=["POST"])
def voice_webhook():
    """Handle incoming voice calls"""
    # Get call information
    from_number = request.values.get("From", "")
    to_number = request.values.get("To", "")
    call_sid = request.values.get("CallSid", "")
    
    # Log the incoming call
    print(f"Incoming call from {from_number} to {to_number} (SID: {call_sid})")
    
    # Create TwiML response
    response = VoiceResponse()
    
    # Add a greeting
    response.say(
        "Thank you for calling Flo Faction Insurance. "
        "Our agents are currently assisting other clients. "
        "Please leave a message after the tone, and we'll get back to you as soon as possible."
    )
    
    # Record a voicemail
    response.record(
        max_length=60,
        play_beep=True,
        timeout=5,
        transcribe=True,
        transcribe_callback="/transcription"
    )
    
    # Thank the caller
    response.say("Thank you for your message. We'll get back to you shortly.")
    
    # Notify the Wix website about the incoming call
    try:
        requests.post(
            WIX_WEBHOOK_URL,
            json={
                "event_type": "incoming_call",
                "from_number": from_number,
                "to_number": to_number,
                "call_sid": call_sid
            }
        )
    except Exception as e:
        print(f"Error notifying Wix website: {str(e)}")
    
    return Response(str(response), mimetype="text/xml")

@app.route("/transcription", methods=["POST"])
def transcription_webhook():
    """Handle voicemail transcriptions"""
    # Get transcription information
    recording_url = request.values.get("RecordingUrl", "")
    recording_sid = request.values.get("RecordingSid", "")
    from_number = request.values.get("From", "")
    transcription_text = request.values.get("TranscriptionText", "")
    
    # Log the transcription
    print(f"Transcription from {from_number}: {transcription_text}")
    
    # Notify the Wix website about the transcription
    try:
        requests.post(
            WIX_WEBHOOK_URL,
            json={
                "event_type": "voicemail_transcription",
                "from_number": from_number,
                "recording_url": recording_url,
                "recording_sid": recording_sid,
                "transcription_text": transcription_text
            }
        )
    except Exception as e:
        print(f"Error notifying Wix website: {str(e)}")
    
    return "OK"

# ======================== SMS WEBHOOKS ========================
@app.route("/sms", methods=["POST"])
def sms_webhook():
    """Handle incoming SMS messages"""
    # Get message information
    from_number = request.values.get("From", "")
    to_number = request.values.get("To", "")
    body = request.values.get("Body", "")
    message_sid = request.values.get("MessageSid", "")
    
    # Log the incoming message
    print(f"Incoming message from {from_number} to {to_number}: {body}")
    
    # Create TwiML response
    response = MessagingResponse()
    
    # Check for keywords in the message
    body_lower = body.lower()
    
    if "quote" in body_lower or "insurance" in body_lower:
        # Insurance quote request
        response.message(
            "Thank you for your interest in an insurance quote! "
            "An agent will contact you shortly to gather more information. "
            "In the meantime, you can visit our website at https://www.flofaction.com"
        )
    elif "appointment" in body_lower or "schedule" in body_lower:
        # Appointment request
        response.message(
            "Thank you for requesting an appointment! "
            "An agent will contact you shortly to schedule a time. "
            "You can also schedule online at https://www.flofaction.com/schedule"
        )
    elif "stop" in body_lower:
        # Opt-out request
        response.message(
            "You have been unsubscribed from Flo Faction Insurance messages. "
            "You will no longer receive messages from this number."
        )
    else:
        # General inquiry
        response.message(
            "Thank you for contacting Flo Faction Insurance! "
            "An agent will respond to your message shortly. "
            "For immediate assistance, please call us at 555-123-4567."
        )
    
    # Notify the Wix website about the incoming message
    try:
        requests.post(
            WIX_WEBHOOK_URL,
            json={
                "event_type": "incoming_sms",
                "from_number": from_number,
                "to_number": to_number,
                "body": body,
                "message_sid": message_sid
            }
        )
    except Exception as e:
        print(f"Error notifying Wix website: {str(e)}")
    
    return Response(str(response), mimetype="text/xml")

# ======================== AUDIO FILE HOSTING ========================
@app.route("/audio/<filename>", methods=["GET"])
def serve_audio(filename):
    """Serve audio files for Twilio to play"""
    # This assumes audio files are stored in an "audio" directory
    audio_path = os.path.join("audio", filename)
    
    if os.path.exists(audio_path):
        with open(audio_path, "rb") as f:
            audio_data = f.read()
        
        return Response(audio_data, mimetype="audio/mpeg")
    else:
        return "Audio file not found", 404

# ======================== MAIN FUNCTION ========================
if __name__ == "__main__":
    # Get port from environment variable or use 5000 as default
    port = int(os.environ.get("PORT", 5000))
    
    # Run the Flask app
    app.run(host="0.0.0.0", port=port)

# ======================== DEPLOYMENT INSTRUCTIONS ========================
"""
To deploy this webhook server:

1. Install the required packages:
   pip install flask twilio requests gunicorn

2. Create a requirements.txt file:
   flask==2.0.1
   twilio==7.0.0
   requests==2.26.0
   gunicorn==20.1.0

3. Deploy to Heroku:
   a. Create a Heroku account at heroku.com
   b. Install the Heroku CLI
   c. Create a new Heroku app:
      heroku create flo-faction-twilio-webhook
   d. Set environment variables:
      heroku config:set TWILIO_ACCOUNT_SID=AC187c871afa232bbbc978caf33f3e25d9
      heroku config:set TWILIO_AUTH_TOKEN=CqpVewwter1BEMdFIFHrN2XmUyt22wBP
      heroku config:set TWILIO_PHONE_NUMBER=+***********
      heroku config:set WIX_WEBHOOK_URL=https://www.flofaction.com/_functions/twilioWebhook
   e. Deploy the app:
      git push heroku main

4. Configure Twilio to use your webhook URLs:
   a. Go to the Twilio console
   b. Navigate to Phone Numbers > Manage > Active Numbers
   c. Click on your phone number
   d. Under "Voice & Fax", set "A CALL COMES IN" to "Webhook" and enter:
      https://flo-faction-twilio-webhook.herokuapp.com/voice
   e. Under "Messaging", set "A MESSAGE COMES IN" to "Webhook" and enter:
      https://flo-faction-twilio-webhook.herokuapp.com/sms

5. Test your webhook server:
   a. Send a text message to your Twilio number
   b. Make a call to your Twilio number
   c. Check the Heroku logs to see the incoming requests:
      heroku logs --tail
"""
