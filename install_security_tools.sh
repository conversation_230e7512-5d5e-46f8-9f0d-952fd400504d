#!/bin/bash

# Ethical Hacking Tools Installation Script for Ubuntu
# This script installs a comprehensive set of open-source security and penetration testing tools
# Created: April 29, 2025

# Text colors
GREEN='\033[0;32m'
BLUE='\033[0;34m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Function to display a banner
display_banner() {
    clear
    echo -e "${BLUE}"
    echo "╔═══════════════════════════════════════════════════════════════╗"
    echo "║                                                               ║"
    echo "║            ETHICAL HACKING TOOLS INSTALLATION                 ║"
    echo "║                                                               ║"
    echo "╚═══════════════════════════════════════════════════════════════╝"
    echo -e "${NC}"
}

# Function to display section headers
section_header() {
    echo -e "${YELLOW}\n════════════════════════════════════════════════════════════════════"
    echo "  $1"
    echo -e "════════════════════════════════════════════════════════════════════${NC}\n"
}

# Function to check if a command was successful
check_success() {
    if [ $? -eq 0 ]; then
        echo -e "${GREEN}✓ Success: $1${NC}"
    else
        echo -e "${RED}✗ Error: Failed to $1${NC}"
        exit 1
    fi
}

# Function to install a specific tool
install_tool() {
    echo -e "${BLUE}→ Installing $1...${NC}"
    apt-get install -y $2 >/dev/null 2>&1
    check_success "install $1"
}

# Check if script is run as root
if [ "$(id -u)" != "0" ]; then
    echo -e "${RED}This script must be run as root${NC}"
    echo -e "Please run: ${YELLOW}sudo $0${NC}"
    exit 1
fi

# Display banner
display_banner

# System update and essential packages
section_header "SYSTEM UPDATE AND DEPENDENCIES"
echo "→ Updating package repositories..."
apt-get update >/dev/null 2>&1
check_success "update repositories"

echo "→ Upgrading existing packages..."
apt-get upgrade -y >/dev/null 2>&1
check_success "upgrade packages"

echo "→ Installing essential dependencies..."
apt-get install -y build-essential libssl-dev libffi-dev python3-dev python3-pip \
    git curl wget net-tools >/dev/null 2>&1
check_success "install dependencies"

# Reconnaissance Tools
section_header "RECONNAISSANCE TOOLS"
install_tool "Nmap" "nmap"
install_tool "theHarvester" "theharvester"

# Vulnerability Scanning Tools
section_header "VULNERABILITY SCANNING TOOLS"
install_tool "Nikto" "nikto"

echo -e "${BLUE}→ Installing OpenVAS...${NC}"
add-apt-repository ppa:mrazavi/openvas -y >/dev/null 2>&1
apt-get update >/dev/null 2>&1
apt-get install -y openvas >/dev/null 2>&1
check_success "install OpenVAS"

# Exploitation Tools
section_header "EXPLOITATION TOOLS"
install_tool "Metasploit Framework" "metasploit-framework"

# Password Cracking Tools
section_header "PASSWORD CRACKING TOOLS"
install_tool "John the Ripper" "john"
install_tool "Hashcat" "hashcat"

# Wireless Testing Tools
section_header "WIRELESS TESTING TOOLS"
install_tool "Aircrack-ng" "aircrack-ng"
install_tool "Kismet" "kismet"

# Web Application Testing Tools
section_header "WEB APPLICATION TESTING TOOLS"
install_tool "OWASP ZAP" "zaproxy"
install_tool "sqlmap" "sqlmap"

# Social Engineering Tools
section_header "SOCIAL ENGINEERING TOOLS"
echo -e "${BLUE}→ Installing Social Engineer Toolkit (SET)...${NC}"
git clone https://github.com/trustedsec/social-engineer-toolkit.git /opt/set >/dev/null 2>&1
cd /opt/set
pip3 install -r requirements.txt >/dev/null 2>&1
python3 setup.py install >/dev/null 2>&1
check_success "install Social Engineer Toolkit"

# Forensics Tools
section_header "FORENSICS TOOLS"
install_tool "Autopsy & Sleuth Kit" "autopsy sleuthkit"

# Additional useful tools
section_header "ADDITIONAL TOOLS"
install_tool "Wireshark" "wireshark"
install_tool "Hydra" "hydra"
install_tool "Netcat" "netcat"
install_tool "Tcpdump" "tcpdump"
install_tool "Ettercap" "ettercap-graphical"

# Final setup
section_header "FINALIZATION"
echo "→ Cleaning up..."
apt-get autoremove -y >/dev/null 2>&1
apt-get clean >/dev/null 2>&1
check_success "clean up"

echo -e "\n${GREEN}═════════════════════════════════════════════════════════════════════"
echo "                   INSTALLATION COMPLETED!                        "
echo -e "═════════════════════════════════════════════════════════════════════${NC}"
echo -e "\nEthical hacking tools have been successfully installed."
echo -e "\n${YELLOW}IMPORTANT:${NC} Remember to use these tools responsibly and ethically."
echo -e "Only use these tools on systems you own or have explicit permission to test."
echo -e "Unauthorized access to computer systems is illegal and unethical.\n"

# Add some help for the user
echo -e "${BLUE}Tools installed:${NC}"
echo "  • Reconnaissance: Nmap, theHarvester"
echo "  • Vulnerability Scanning: Nikto, OpenVAS"
echo "  • Exploitation: Metasploit Framework"
echo "  • Password Cracking: John the Ripper, Hashcat"
echo "  • Wireless Testing: Aircrack-ng, Kismet"
echo "  • Web Application Testing: OWASP ZAP, sqlmap"
echo "  • Social Engineering: Social-Engineer Toolkit (SET)"
echo "  • Forensics: Autopsy, Sleuth Kit"
echo "  • Additional: Wireshark, Hydra, Netcat, Tcpdump, Ettercap"
echo -e "\n${BLUE}To open specific tools, use the following commands:${NC}"
echo "  • Nmap: nmap"
echo "  • OWASP ZAP: zaproxy"
echo "  • Wireshark: wireshark"
echo "  • Metasploit: msfconsole"
echo "  • Social Engineer Toolkit: setoolkit"
echo -e "\n${GREEN}Thank you for using this installation script!${NC}\n"