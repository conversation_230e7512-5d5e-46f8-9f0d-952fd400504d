from secure_credentials import SecureCredentialsManager
from cryptography.fernet import Fernet
import re

def check_password_strength(password):
    """Check if password meets complexity requirements"""
    length_ok = len(password) >= 8
    has_upper = any(c.isupper() for c in password)
    has_lower = any(c.islower() for c in password)
    has_digit = any(c.isdigit() for c in password)
    has_special = bool(re.search(r'[!@#$%^&*(),.?":{}|<>]', password))
    
    return {
        'length_ok': length_ok,
        'has_upper': has_upper,
        'has_lower': has_lower,
        'has_digit': has_digit,
        'has_special': has_special,
        'is_strong': all([length_ok, has_upper, has_lower, has_digit, has_special])
    }

def test_credentials():
    """Test all of <PERSON>'s credentials"""
    manager = SecureCredentialsManager()
    sandra_creds = manager.get_agent_credentials("sandra")
    
    results = {}
    
    for carrier, creds in sandra_creds['carriers'].items():
        password = creds['password']
        results[carrier] = {
            'password': password,
            'strength': check_password_strength(password),
            'length': len(password)
        }
    
    return results

if __name__ == "__main__":
    print("Testing <PERSON>'s carrier credentials...\n")
    results = test_credentials()
    
    for carrier, data in results.items():
        print(f"=== {carrier.upper()} ===")
        print(f"Password: {data['password']}")
        print(f"Length: {data['length']} characters")
        print("Strength tests:")
        for test, passed in data['strength'].items():
            print(f"  {test}: {'✓' if passed else '✗'}")
        print("\n")