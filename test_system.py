import asyncio
import logging
from datetime import datetime
import json
from agent_coordinator import AgentCoordinator
from rag_agent_system import InsuranceMainAgent, ContentMainAgent, EmailMainAgent
from agent_learning_modules import SpecializedLearners
from knowledge_management import KnowledgeManager

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def test_insurance_workflow():
    """Test insurance-related tasks and learning"""
    coordinator = AgentCoordinator()
    
    # Test 1: Insurance Quote Generation
    quote_task = {
        "id": "quote_001",
        "type": "insurance_quote",
        "sub_types": ["quote", "risk_assessment"],
        "data": {
            "customer": "<PERSON>",
            "insurance_type": "auto",
            "vehicle_info": {
                "make": "Tesla",
                "model": "Model 3",
                "year": 2023
            },
            "driver_info": {
                "age": 35,
                "driving_history": "clean",
                "location": "New York"
            }
        }
    }
    
    logger.info("Processing insurance quote request...")
    quote_result = await coordinator.process_task(quote_task)
    logger.info(f"Quote result: {json.dumps(quote_result, indent=2)}")

async def test_content_creation():
    """Test content creation and optimization"""
    coordinator = AgentCoordinator()
    
    # Test 2: Create Marketing Content
    content_task = {
        "id": "content_001",
        "type": "content_creation",
        "sub_types": ["writer", "editor", "seo"],
        "data": {
            "topic": "Electric Vehicle Insurance Benefits",
            "target_audience": "Tesla owners",
            "content_type": "blog_post",
            "requirements": {
                "word_count": 1200,
                "tone": "professional",
                "key_points": [
                    "Specialized EV coverage",
                    "Battery protection",
                    "Charging station coverage"
                ]
            }
        }
    }
    
    logger.info("Creating marketing content...")
    content_result = await coordinator.process_task(content_task)
    logger.info(f"Content result: {json.dumps(content_result, indent=2)}")

async def test_email_processing():
    """Test email processing and response generation"""
    coordinator = AgentCoordinator()
    
    # Test 3: Process Customer Email
    email_task = {
        "id": "email_001",
        "type": "email_processing",
        "sub_types": ["classifier", "responder"],
        "data": {
            "from": "<EMAIL>",
            "subject": "Question about EV insurance coverage",
            "body": """
            Hi,
            I recently purchased a Tesla Model Y and I'm interested in your 
            specialized EV insurance coverage. Could you tell me more about 
            the battery protection and charging station coverage options?
            Best regards,
            John
            """,
            "priority": "high",
            "customer_status": "prospect"
        }
    }
    
    logger.info("Processing customer email...")
    email_result = await coordinator.process_task(email_task)
    logger.info(f"Email processing result: {json.dumps(email_result, indent=2)}")

async def test_learning_and_improvement():
    """Test system learning and improvement over multiple interactions"""
    coordinator = AgentCoordinator()
    
    # Test 4: Multiple iterations with feedback
    for i in range(5):
        # Create a quote request with varying details
        quote_task = {
            "id": f"quote_{i+1}",
            "type": "insurance_quote",
            "sub_types": ["quote", "risk_assessment"],
            "data": {
                "customer": f"Customer_{i+1}",
                "insurance_type": "auto",
                "vehicle_info": {
                    "make": "Tesla",
                    "model": f"Model {['3', 'Y', 'S', 'X'][i % 4]}",
                    "year": 2023 - (i % 3)
                },
                "driver_info": {
                    "age": 30 + (i * 5),
                    "driving_history": ["clean", "minor_violation", "clean", "clean", "accident"][i],
                    "location": ["New York", "California", "Texas", "Florida", "Washington"][i]
                }
            }
        }
        
        logger.info(f"\nIteration {i+1}: Processing quote request...")
        result = await coordinator.process_task(quote_task)
        
        # Simulate customer feedback
        feedback = {
            "task_id": quote_task["id"],
            "satisfaction": 0.7 + (i * 0.05),  # Improving satisfaction
            "comments": "Good response, faster than before",
            "accepted_quote": i > 2  # Later quotes more likely to be accepted
        }
        
        # Let system learn from feedback
        await coordinator.learn_from_interaction(
            domain="insurance",
            task=quote_task,
            result={**result, "feedback": feedback}
        )
        
        logger.info(f"Iteration {i+1} complete. Feedback: {json.dumps(feedback, indent=2)}")
        
        # Short pause to simulate real-world timing
        await asyncio.sleep(1)
    
    # Check performance metrics after learning
    metrics = coordinator.main_agents['insurance']['performance_metrics']
    logger.info(f"\nFinal performance metrics: {json.dumps(metrics, indent=2)}")

async def main():
    """Run all tests"""
    logger.info("Starting system tests...")
    
    try:
        # Run insurance workflow test
        logger.info("\n=== Testing Insurance Workflow ===")
        await test_insurance_workflow()
        
        # Run content creation test
        logger.info("\n=== Testing Content Creation ===")
        await test_content_creation()
        
        # Run email processing test
        logger.info("\n=== Testing Email Processing ===")
        await test_email_processing()
        
        # Run learning and improvement test
        logger.info("\n=== Testing Learning and Improvement ===")
        await test_learning_and_improvement()
        
        logger.info("\nAll tests completed successfully!")
        
    except Exception as e:
        logger.error(f"Error during testing: {str(e)}")
        raise

if __name__ == "__main__":
    asyncio.run(main())