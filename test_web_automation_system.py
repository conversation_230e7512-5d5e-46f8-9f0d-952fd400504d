#!/usr/bin/env python3
"""
Comprehensive Test Suite for Web Automation System
==================================================

Tests all components of the unified web automation dashboard:
- Web-UI Component
- WebRover Component  
- MidScene Integration
- Unified Dashboard
- Deployment Scripts
"""

import asyncio
import json
import pytest
import pytest_asyncio
import tempfile
import time
from pathlib import Path
from unittest.mock import Mock, patch, AsyncMock
from aiohttp.test_utils import AioHTTPTestCase
from aiohttp import web

# Import components to test
try:
    from web_ui_component import WebUIComponent, AutomationTask
    from webrover_component import WebRoverComponent, WebPage, NavigationAction
    from midscene_integration import MidSceneIntegration, MidSceneAction
    from unified_web_automation_dashboard import UnifiedWebAutomationDashboard, UnifiedTask
    from deploy_to_gcloud import GoogleCloudDeployer
except ImportError as e:
    pytest.skip(f"Components not available: {e}", allow_module_level=True)

class TestWebUIComponent:
    """Test Web-UI Component functionality"""

    @pytest_asyncio.fixture
    async def web_ui(self):
        """Create Web-UI component for testing"""
        component = WebUIComponent(port=3002)  # Use different port for testing
        yield component
        if hasattr(component, 'cleanup'):
            await component.cleanup()
        
    @pytest.mark.asyncio
    async def test_task_creation(self, web_ui):
        """Test creating automation tasks"""
        task = AutomationTask(
            id="test_task_1",
            name="Test Task",
            url="https://httpbin.org/html",
            actions=["navigate", "screenshot"]
        )
        
        web_ui.tasks[task.id] = task
        
        assert task.id in web_ui.tasks
        assert web_ui.tasks[task.id].name == "Test Task"
        assert web_ui.tasks[task.id].status == "pending"
        
    @pytest.mark.asyncio
    async def test_task_execution(self, web_ui):
        """Test task execution workflow"""
        task = AutomationTask(
            id="test_task_2",
            name="Execution Test",
            url="https://httpbin.org/html",
            actions=["navigate"]
        )
        
        web_ui.tasks[task.id] = task
        
        # Mock the execution
        with patch.object(web_ui, 'execute_task') as mock_execute:
            mock_execute.return_value = {"success": True, "result": "Task completed"}
            
            result = await web_ui.execute_task(task)
            
            assert result["success"] is True
            mock_execute.assert_called_once_with(task)
            
    def test_task_serialization(self):
        """Test task serialization to JSON"""
        task = AutomationTask(
            id="test_task_3",
            name="Serialization Test",
            url="https://example.com",
            actions=["navigate", "click"]
        )
        
        task_dict = task.__dict__
        
        assert task_dict["id"] == "test_task_3"
        assert task_dict["name"] == "Serialization Test"
        assert task_dict["url"] == "https://example.com"
        assert "navigate" in task_dict["actions"]

class TestWebRoverComponent:
    """Test WebRover Component functionality"""

    @pytest_asyncio.fixture
    async def webrover(self):
        """Create WebRover component for testing"""
        component = WebRoverComponent()
        await component.initialize()
        yield component
        await component.cleanup()
        
    @pytest.mark.asyncio
    async def test_url_validation(self, webrover):
        """Test URL validation"""
        valid_urls = [
            "https://example.com",
            "http://test.org/path",
            "https://subdomain.example.com/page?param=value"
        ]
        
        invalid_urls = [
            "not-a-url",
            "ftp://example.com",
            "javascript:alert('xss')",
            ""
        ]
        
        for url in valid_urls:
            assert webrover._is_valid_url(url) is True
            
        for url in invalid_urls:
            assert webrover._is_valid_url(url) is False
            
    @pytest.mark.asyncio
    async def test_link_prioritization(self, webrover):
        """Test intelligent link prioritization"""
        links = [
            "https://example.com/admin/login",
            "https://example.com/about",
            "https://example.com/products",
            "https://example.com/logout",
            "https://example.com/contact"
        ]
        
        prioritized = webrover._prioritize_links(links, "test content")
        
        # About, products, contact should be prioritized over admin/logout
        high_priority = ["about", "products", "contact"]
        low_priority = ["admin", "logout"]
        
        # Check that high priority links come first
        first_few = prioritized[:3]
        assert any(hp in link.lower() for link in first_few for hp in high_priority)
        
    @pytest.mark.asyncio
    async def test_page_navigation(self, webrover):
        """Test page navigation with mocked response"""
        with patch('aiohttp.ClientSession.get') as mock_get:
            # Mock successful response
            mock_response = Mock()
            mock_response.status = 200
            mock_response.text = AsyncMock(return_value='''
                <html>
                    <head><title>Test Page</title></head>
                    <body>
                        <h1>Test Content</h1>
                        <a href="/link1">Link 1</a>
                        <a href="/link2">Link 2</a>
                        <form action="/submit" method="post">
                            <input type="text" name="field1">
                        </form>
                    </body>
                </html>
            ''')
            
            mock_get.return_value.__aenter__.return_value = mock_response
            
            page = await webrover.navigate_to("https://example.com")
            
            assert page.title == "Test Page"
            assert page.status_code == 200
            assert len(page.links) >= 2
            assert len(page.forms) >= 1
            assert page.error is None

class TestMidSceneIntegration:
    """Test MidScene Integration functionality"""
    
    @pytest.fixture
    def midscene(self):
        """Create MidScene integration for testing"""
        integration = MidSceneIntegration(headless=True)
        yield integration
        integration.cleanup()
        
    def test_action_creation(self, midscene):
        """Test MidScene action creation"""
        action = MidSceneAction(
            action_type="click",
            target="#submit-button",
            options={"timeout": 5000}
        )
        
        assert action.action_type == "click"
        assert action.target == "#submit-button"
        assert action.options["timeout"] == 5000
        
    def test_config_file_creation(self, midscene):
        """Test configuration file creation"""
        assert midscene.config_file.exists()
        
        with open(midscene.config_file, 'r') as f:
            config = json.load(f)
            
        assert config["browser"]["headless"] is True
        assert config["browser"]["viewport"]["width"] == 1280
        assert config["ai"]["enabled"] is True
        
    def test_script_file_creation(self, midscene):
        """Test JavaScript script file creation"""
        assert midscene.script_file.exists()
        
        with open(midscene.script_file, 'r') as f:
            script_content = f.read()
            
        assert "MidSceneAutomation" in script_content
        assert "playwright" in script_content
        assert "async function main()" in script_content
        
    @pytest.mark.asyncio
    async def test_action_execution_mock(self, midscene):
        """Test action execution with mocked subprocess"""
        actions = [
            MidSceneAction("navigate", "https://example.com"),
            MidSceneAction("click", "#button")
        ]
        
        with patch('asyncio.create_subprocess_exec') as mock_subprocess:
            # Mock successful execution
            mock_process = Mock()
            mock_process.returncode = 0
            mock_process.communicate = AsyncMock(return_value=(b"Success", b""))
            mock_subprocess.return_value = mock_process
            
            # Mock results file
            results_data = [
                {
                    "success": True,
                    "action": "navigate",
                    "target": "https://example.com",
                    "execution_time": 1000,
                    "timestamp": "2024-01-01T00:00:00Z"
                }
            ]
            
            with patch('pathlib.Path.exists', return_value=True):
                with patch('builtins.open', mock_open_json(results_data)):
                    results = await midscene.execute_actions(actions)
                    
            assert len(results) == 1
            assert results[0].success is True
            assert results[0].action == "navigate"

class TestUnifiedDashboard(AioHTTPTestCase):
    """Test Unified Web Automation Dashboard"""
    
    async def get_application(self):
        """Create test application"""
        dashboard = UnifiedWebAutomationDashboard(host='localhost', port=8001)
        await dashboard.initialize()
        return dashboard.app
        
    async def test_system_status_endpoint(self):
        """Test system status API endpoint"""
        resp = await self.client.request("GET", "/api/status")

        assert resp.status == 200

        data = await resp.json()
        assert "status" in data
        assert "components" in data
        assert "tasks" in data

    async def test_task_creation_endpoint(self):
        """Test task creation API endpoint"""
        task_data = {
            "name": "Test API Task",
            "type": "webrover",
            "component_data": {
                "url": "https://example.com",
                "action": "crawl"
            }
        }

        resp = await self.client.request("POST", "/api/tasks",
                                       json=task_data)

        assert resp.status == 200

        data = await resp.json()
        assert data["success"] is True
        assert "task" in data
        assert data["task"]["name"] == "Test API Task"

    async def test_get_all_tasks_endpoint(self):
        """Test get all tasks API endpoint"""
        resp = await self.client.request("GET", "/api/tasks")

        assert resp.status == 200

        data = await resp.json()
        assert "tasks" in data
        assert isinstance(data["tasks"], list)

class TestGoogleCloudDeployer:
    """Test Google Cloud deployment functionality"""
    
    @pytest.fixture
    def deployer(self):
        """Create deployer for testing"""
        return GoogleCloudDeployer("test-project", "us-central1")
        
    def test_deployer_initialization(self, deployer):
        """Test deployer initialization"""
        assert deployer.project_id == "test-project"
        assert deployer.region == "us-central1"
        assert deployer.service_name == "web-automation-dashboard"
        
    def test_dockerfile_creation(self, deployer):
        """Test Dockerfile creation"""
        with tempfile.TemporaryDirectory() as temp_dir:
            original_cwd = Path.cwd()
            try:
                import os
                os.chdir(temp_dir)
                
                deployer.create_dockerfile()
                
                dockerfile_path = Path("Dockerfile")
                assert dockerfile_path.exists()
                
                with open(dockerfile_path, 'r') as f:
                    content = f.read()
                    
                assert "FROM python:3.9-slim" in content
                assert "google-chrome-stable" in content
                assert "playwright install" in content
                
            finally:
                os.chdir(original_cwd)
                
    def test_package_json_creation(self, deployer):
        """Test package.json creation"""
        with tempfile.TemporaryDirectory() as temp_dir:
            original_cwd = Path.cwd()
            try:
                import os
                os.chdir(temp_dir)
                
                deployer.create_package_json()
                
                package_path = Path("package.json")
                assert package_path.exists()
                
                with open(package_path, 'r') as f:
                    package_data = json.load(f)
                    
                assert package_data["name"] == "web-automation-dashboard"
                assert "playwright" in package_data["dependencies"]
                
            finally:
                os.chdir(original_cwd)
                
    @patch('subprocess.run')
    def test_prerequisites_check(self, mock_subprocess, deployer):
        """Test prerequisites checking"""
        # Mock successful command executions
        mock_subprocess.return_value.returncode = 0
        mock_subprocess.return_value.stdout = "ACTIVE account"
        
        result = deployer.check_prerequisites()
        
        # Should call gcloud and docker commands
        assert mock_subprocess.call_count >= 3

class TestIntegration:
    """Integration tests for the complete system"""

    @pytest.mark.asyncio
    async def test_component_integration(self):
        """Test integration between components"""
        # Create dashboard with mocked static directory
        with patch('os.makedirs'):
            dashboard = UnifiedWebAutomationDashboard()

            # Mock component initialization
            with patch.object(dashboard, 'initialize') as mock_init:
                mock_init.return_value = None
                await dashboard.initialize()

            # Test task creation and execution flow
            task = UnifiedTask(
                id="integration_test",
                name="Integration Test Task",
                type="webrover"
            )

            dashboard.tasks[task.id] = task

            assert task.id in dashboard.tasks
            assert dashboard.tasks[task.id].status == "pending"

    @pytest.mark.asyncio
    async def test_end_to_end_workflow(self):
        """Test complete end-to-end workflow"""
        # This would test the complete workflow from task creation
        # through execution to completion

        # Mock external dependencies
        with patch('aiohttp.ClientSession'):
            with patch('subprocess.run'):
                with patch('os.makedirs'):
                    # Create components
                    dashboard = UnifiedWebAutomationDashboard()

                    # Create and execute task
                    task_data = {
                        "name": "E2E Test",
                        "type": "webrover",
                        "component_data": {"url": "https://httpbin.org"}
                    }

                    # This would normally go through the full execution pipeline
                    assert task_data["name"] == "E2E Test"

# Helper functions
def mock_open_json(data):
    """Mock open function that returns JSON data"""
    import json
    from unittest.mock import mock_open
    return mock_open(read_data=json.dumps(data))

# Pytest configuration
pytest_plugins = ('pytest_asyncio',)

if __name__ == "__main__":
    # Run tests
    pytest.main([__file__, "-v", "--tb=short"])
