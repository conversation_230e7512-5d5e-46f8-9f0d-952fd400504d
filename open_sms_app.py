"""
Open SMS App to Send Text Message to <PERSON>

This script opens the default SMS app with a pre-populated text message to <PERSON>.
"""

import webbrowser
import urllib.parse
import platform
import os

# <PERSON> contact information
PAUL_PHONE = "+17722089646"
PAUL_NAME = "<PERSON>"

def open_sms_app():
    """Open the default SMS app with a pre-populated text message to <PERSON>"""
    print("=" * 80)
    print("OPENING SMS APP TO SEND TEXT MESSAGE TO PAUL EDWARDS")
    print("=" * 80)
    
    # Create message content
    message = f"""
Hi {PAUL_NAME.split()[0]}, this is <PERSON> with Flo Faction Insurance. I help people create tax-free retirement income without stock market risk. I've prepared a personalized analysis for you. Would you be interested in seeing this analysis? Visit https://www.flofaction.com/insurance for more information. Thanks!
    """.strip()
    
    # URL encode the message
    message_encoded = urllib.parse.quote(message)
    
    # Create SMS URL based on platform
    if platform.system() == "Darwin":  # macOS
        # For macOS, use the sms: protocol
        sms_url = f"sms:{PAUL_PHONE}&body={message_encoded}"
    elif platform.system() == "Windows":
        # For Windows, use the sms: protocol
        sms_url = f"sms:{PAUL_PHONE}?body={message_encoded}"
    elif platform.system() == "Linux":
        # For Linux, try the sms: protocol
        sms_url = f"sms:{PAUL_PHONE}?body={message_encoded}"
    else:
        print(f"Unsupported platform: {platform.system()}")
        return False
    
    try:
        # Open the default SMS app
        webbrowser.open(sms_url)
        print(f"Default SMS app opened with message to {PAUL_PHONE}")
        print("Please complete the text message sending process in your SMS app.")
        return True
    except Exception as e:
        print(f"Error opening default SMS app: {str(e)}")
        return False

if __name__ == "__main__":
    print("This script will open your default SMS app with a pre-populated text message to Paul Edwards.")
    print(f"Recipient: {PAUL_PHONE}")
    
    proceed = input("Do you want to proceed? (yes/no): ")
    
    if proceed.lower() == "yes":
        open_sms_app()
    else:
        print("Operation cancelled.")
