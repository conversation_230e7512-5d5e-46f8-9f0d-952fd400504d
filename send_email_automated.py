"""
Send Email to <PERSON> - Automated Version

This script sends an email to <PERSON> using environment variables for credentials.
No interactive input required.
"""

import os
import smtplib
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
import ssl
from dotenv import load_dotenv
import subprocess
import platform
import webbrowser
import urllib.parse

# Load environment variables from .env file
load_dotenv()

# <PERSON> contact information
PAUL_EDWARDS = {
    "first_name": os.getenv("PAUL_FIRST_NAME", "<PERSON>"),
    "last_name": os.getenv("PAUL_LAST_NAME", "<PERSON>"),
    "email": os.getenv("PAUL_EMAIL", "<EMAIL>"),
    "primary_phone": os.getenv("PAUL_PRIMARY_PHONE", "+17722089646"),
    "secondary_phone": os.getenv("PAUL_SECONDARY_PHONE", "+17725395908")
}

# Agent information
AGENT_INFO = {
    "name": os.getenv("AGENT_NAME", "<PERSON>"),
    "agency": os.getenv("AGENT_AGENCY", "Flo Faction Insurance"),
    "email": os.getenv("AGENT_EMAIL", "<EMAIL>"),
    "website": os.getenv("AGENT_WEBSITE", "https://www.flofaction.com/insurance"),
}

def create_email_content():
    """Create email subject and body"""
    subject = "Creating Tax-Free Retirement Income Without Market Risk"
    body = f"""
Dear {PAUL_EDWARDS['first_name']},

I hope this email finds you well. My name is {AGENT_INFO['name']} with {AGENT_INFO['agency']}, and I specialize in helping people like you create tax-free retirement income without the risk of the stock market.

Based on the information I have, I've prepared a personalized analysis that shows how you could potentially:

1. Generate approximately $5,000 per month in tax-free retirement income
2. Build cash value that grows without the risk of market losses
3. Provide a death benefit of around $720,000 to protect your family
4. Create a financial strategy that offers both protection and growth potential

Many of my clients are concerned about:
- Market volatility affecting their retirement savings
- Taxes reducing their retirement income
- Having enough money to maintain their lifestyle in retirement
- Protecting their family financially

The strategy I'd like to share with you addresses all these concerns through an Indexed Universal Life policy specifically designed for your situation.

Would you be interested in a brief 30-minute conversation to review this analysis? If so, please call me or reply to this email with a good time to connect.

You can also learn more about our services by visiting our website at {AGENT_INFO['website']}.

Thank you for your time, and I look forward to speaking with you soon.

Best regards,
{AGENT_INFO['name']}
{AGENT_INFO['agency']}
{AGENT_INFO['website']}
    """
    return subject, body

def send_email_with_smtp():
    """Send email using SMTP with environment variables"""
    print("=" * 80)
    print("ATTEMPTING TO SEND EMAIL WITH SMTP")
    print("=" * 80)
    
    # Get email credentials from environment variables
    sender_email = os.getenv("EMAIL_SENDER")
    password = os.getenv("EMAIL_PASSWORD")
    
    if not sender_email or not password:
        print("Email credentials not found in environment variables.")
        return False
    
    subject, body = create_email_content()
    
    # Create message
    message = MIMEMultipart()
    message["From"] = sender_email
    message["To"] = PAUL_EDWARDS["email"]
    message["Subject"] = subject
    
    # Attach body
    message.attach(MIMEText(body, "plain"))
    
    try:
        # Create secure connection and send email
        context = ssl.create_default_context()
        
        # Determine SMTP server based on email domain
        if "@gmail.com" in sender_email.lower():
            smtp_server = "smtp.gmail.com"
            smtp_port = 587
        elif "@outlook.com" in sender_email.lower() or "@hotmail.com" in sender_email.lower():
            smtp_server = "smtp.office365.com"
            smtp_port = 587
        elif "@yahoo.com" in sender_email.lower():
            smtp_server = "smtp.mail.yahoo.com"
            smtp_port = 587
        else:
            smtp_server = os.getenv("SMTP_SERVER", "smtp.gmail.com")
            smtp_port = int(os.getenv("SMTP_PORT", "587"))
        
        with smtplib.SMTP(smtp_server, smtp_port) as server:
            server.starttls(context=context)
            server.login(sender_email, password)
            server.sendmail(sender_email, PAUL_EDWARDS["email"], message.as_string())
        
        print(f"Email sent successfully to {PAUL_EDWARDS['email']}")
        return True
    except Exception as e:
        print(f"Error sending email with SMTP: {str(e)}")
        return False

def send_email_with_default_client():
    """Send email using the default email client"""
    print("=" * 80)
    print("ATTEMPTING TO SEND EMAIL WITH DEFAULT CLIENT")
    print("=" * 80)
    
    subject, body = create_email_content()
    
    # URL encode the subject and body
    subject_encoded = urllib.parse.quote(subject)
    body_encoded = urllib.parse.quote(body)
    
    # Create mailto URL
    mailto_url = f"mailto:{PAUL_EDWARDS['email']}?subject={subject_encoded}&body={body_encoded}"
    
    try:
        # Open the default email client
        webbrowser.open(mailto_url)
        print(f"Default email client opened with message to {PAUL_EDWARDS['email']}")
        print("Please complete the email sending process in your email client.")
        return True
    except Exception as e:
        print(f"Error opening default email client: {str(e)}")
        return False

def send_email_with_command_line():
    """Send email using command line tools"""
    print("=" * 80)
    print("ATTEMPTING TO SEND EMAIL WITH COMMAND LINE")
    print("=" * 80)
    
    subject, body = create_email_content()
    
    try:
        if platform.system() == "Darwin":  # macOS
            # Create a temporary file with the email body
            with open("temp_email.txt", "w") as f:
                f.write(body)
            
            # Use the mail command
            cmd = f"cat temp_email.txt | mail -s '{subject}' {PAUL_EDWARDS['email']}"
            result = subprocess.run(cmd, shell=True, capture_output=True, text=True)
            
            # Remove the temporary file
            os.remove("temp_email.txt")
            
            if result.returncode == 0:
                print(f"Email sent to {PAUL_EDWARDS['email']} using mail command")
                return True
            else:
                print(f"Error sending email with mail command: {result.stderr}")
                return False
        else:
            print(f"Command line email sending not implemented for {platform.system()}")
            return False
    except Exception as e:
        print(f"Error sending email with command line: {str(e)}")
        return False

def main():
    """Try multiple methods to send an email"""
    print("=" * 80)
    print("SENDING EMAIL TO PAUL EDWARDS")
    print("=" * 80)
    
    # Try SMTP first
    if send_email_with_smtp():
        return True
    
    print("\nSMTP method failed. Trying default email client...")
    
    # Try default email client
    if send_email_with_default_client():
        return True
    
    print("\nDefault email client method failed. Trying command line...")
    
    # Try command line
    if send_email_with_command_line():
        return True
    
    print("\nAll email sending methods failed.")
    return False

if __name__ == "__main__":
    main()
