"""
Visualization tools for trading performance analysis
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime
from typing import Dict, List
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class TradingVisualizer:
    """Generate visualizations for trading performance analysis"""
    
    def __init__(self, save_path="trading_analysis"):
        self.save_path = save_path
        self._setup_style()
        
    def _setup_style(self):
        """Set up plotting style"""
        plt.style.use('seaborn')
        sns.set_palette("husl")
        plt.rcParams['figure.figsize'] = (12, 6)
        plt.rcParams['font.size'] = 10
        
    def plot_performance_comparison(self, results: Dict[str, Dict]):
        """Plot performance comparison across different scenarios"""
        try:
            # Create DataFrame from results
            df = pd.DataFrame(results).T
            
            # Plot total PnL
            plt.figure(figsize=(12, 6))
            df['total_pnl'].plot(kind='bar')
            plt.title('Total PnL by Scenario')
            plt.xlabel('Scenario')
            plt.ylabel('PnL ($)')
            plt.xticks(rotation=45)
            plt.tight_layout()
            plt.savefig(f"{self.save_path}/pnl_comparison.png")
            plt.close()
            
            # Plot win rates
            plt.figure(figsize=(12, 6))
            df['win_rate'].plot(kind='bar', color='green')
            plt.title('Win Rate by Scenario')
            plt.xlabel('Scenario')
            plt.ylabel('Win Rate (%)')
            plt.xticks(rotation=45)
            plt.tight_layout()
            plt.savefig(f"{self.save_path}/win_rates.png")
            plt.close()
            
            # Create risk metrics plot
            plt.figure(figsize=(12, 6))
            risk_metrics = pd.DataFrame({
                'Max Loss': df['largest_loss'],
                'Avg Loss': df['avg_loss'],
                'Avg Win': df['avg_win'],
                'Max Win': df['largest_win']
            })
            risk_metrics.plot(kind='bar', width=0.8)
            plt.title('Risk Metrics by Scenario')
            plt.xlabel('Scenario')
            plt.ylabel('Amount ($)')
            plt.xticks(rotation=45)
            plt.legend(bbox_to_anchor=(1.05, 1), loc='upper left')
            plt.tight_layout()
            plt.savefig(f"{self.save_path}/risk_metrics.png")
            plt.close()
            
            logger.info("Performance comparison plots saved")
            
        except Exception as e:
            logger.error(f"Error creating performance comparison plots: {str(e)}")
            
    def plot_equity_curve(self, trade_history: List[Dict]):
        """Plot equity curve from trade history"""
        try:
            # Convert trade history to DataFrame
            df = pd.DataFrame(trade_history)
            df['cumulative_pnl'] = df['pnl'].cumsum()
            
            plt.figure(figsize=(12, 6))
            plt.plot(df['timestamp'], df['cumulative_pnl'])
            plt.title('Equity Curve')
            plt.xlabel('Date')
            plt.ylabel('Cumulative PnL ($)')
            plt.grid(True)
            plt.xticks(rotation=45)
            plt.tight_layout()
            plt.savefig(f"{self.save_path}/equity_curve.png")
            plt.close()
            
            logger.info("Equity curve plot saved")
            
        except Exception as e:
            logger.error(f"Error creating equity curve plot: {str(e)}")
            
    def plot_strategy_performance(self, strategy_results: Dict[str, Dict]):
        """Plot performance comparison across different strategies"""
        try:
            strategies = list(strategy_results.keys())
            metrics = ['total_pnl', 'win_rate', 'total_trades']
            
            fig, axes = plt.subplots(len(metrics), 1, figsize=(12, 4*len(metrics)))
            fig.suptitle('Strategy Performance Comparison')
            
            for i, metric in enumerate(metrics):
                values = [strategy_results[s][metric] for s in strategies]
                axes[i].bar(strategies, values)
                axes[i].set_title(f'{metric.replace("_", " ").title()}')
                axes[i].set_xticklabels(strategies, rotation=45)
                
            plt.tight_layout()
            plt.savefig(f"{self.save_path}/strategy_comparison.png")
            plt.close()
            
            logger.info("Strategy comparison plot saved")
            
        except Exception as e:
            logger.error(f"Error creating strategy comparison plot: {str(e)}")
            
    def plot_risk_analysis(self, risk_results: Dict[str, Dict]):
        """Plot risk management analysis"""
        try:
            risk_levels = list(risk_results.keys())
            
            # Create risk vs reward plot
            plt.figure(figsize=(12, 6))
            plt.scatter(
                [risk_results[r]['largest_loss'] for r in risk_levels],
                [risk_results[r]['total_pnl'] for r in risk_levels]
            )
            
            for i, risk in enumerate(risk_levels):
                plt.annotate(
                    risk,
                    (risk_results[risk]['largest_loss'], 
                     risk_results[risk]['total_pnl'])
                )
                
            plt.title('Risk vs Reward Analysis')
            plt.xlabel('Maximum Drawdown ($)')
            plt.ylabel('Total PnL ($)')
            plt.grid(True)
            plt.tight_layout()
            plt.savefig(f"{self.save_path}/risk_reward.png")
            plt.close()
            
            # Create risk metrics comparison
            fig, axes = plt.subplots(2, 1, figsize=(12, 8))
            
            # Win rate vs Risk Level
            win_rates = [risk_results[r]['win_rate'] for r in risk_levels]
            axes[0].bar(risk_levels, win_rates)
            axes[0].set_title('Win Rate by Risk Level')
            axes[0].set_xticklabels(risk_levels, rotation=45)
            
            # Profit Factor
            profit_factors = [
                abs(risk_results[r]['avg_win'] / risk_results[r]['avg_loss'])
                for r in risk_levels
            ]
            axes[1].bar(risk_levels, profit_factors)
            axes[1].set_title('Profit Factor by Risk Level')
            axes[1].set_xticklabels(risk_levels, rotation=45)
            
            plt.tight_layout()
            plt.savefig(f"{self.save_path}/risk_metrics_comparison.png")
            plt.close()
            
            logger.info("Risk analysis plots saved")
            
        except Exception as e:
            logger.error(f"Error creating risk analysis plots: {str(e)}")
            
    def create_performance_report(self, all_results: Dict):
        """Create comprehensive performance report"""
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            
            # Plot all visualizations
            self.plot_performance_comparison(all_results.get('market_results', {}))
            self.plot_strategy_performance(all_results.get('strategy_results', {}))
            self.plot_risk_analysis(all_results.get('risk_results', {}))
            
            # Create summary report
            with open(f"{self.save_path}/performance_report_{timestamp}.txt", 'w') as f:
                f.write("Trading Performance Analysis Report\n")
                f.write("=================================\n\n")
                
                # Market Conditions Analysis
                f.write("Market Conditions Performance:\n")
                f.write("--------------------------\n")
                for scenario, metrics in all_results.get('market_results', {}).items():
                    f.write(f"\n{scenario}:\n")
                    for metric, value in metrics.items():
                        f.write(f"  {metric}: {value}\n")
                
                # Strategy Analysis
                f.write("\nStrategy Performance:\n")
                f.write("-------------------\n")
                for strategy, metrics in all_results.get('strategy_results', {}).items():
                    f.write(f"\n{strategy}:\n")
                    for metric, value in metrics.items():
                        f.write(f"  {metric}: {value}\n")
                
                # Risk Analysis
                f.write("\nRisk Management Analysis:\n")
                f.write("----------------------\n")
                for risk_level, metrics in all_results.get('risk_results', {}).items():
                    f.write(f"\n{risk_level}:\n")
                    for metric, value in metrics.items():
                        f.write(f"  {metric}: {value}\n")
                        
            logger.info(f"Performance report saved: performance_report_{timestamp}.txt")
            
        except Exception as e:
            logger.error(f"Error creating performance report: {str(e)}")

if __name__ == "__main__":
    # Example usage
    visualizer = TradingVisualizer()
    
    # Example data
    example_results = {
        'market_results': {
            'Bull Market': {'total_pnl': 5000, 'win_rate': 0.65},
            'Bear Market': {'total_pnl': -1000, 'win_rate': 0.45}
        },
        'strategy_results': {
            'ML': {'total_pnl': 3000, 'win_rate': 0.60},
            'Technical': {'total_pnl': 2000, 'win_rate': 0.55}
        }
    }
    
    visualizer.create_performance_report(example_results)