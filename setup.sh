#!/bin/bash

echo "Setting up AI Agent System..."

# Create and activate virtual environment
echo "Creating virtual environment..."
python3 -m venv venv
source venv/bin/activate

# Install dependencies
echo "Installing dependencies..."
pip install --upgrade pip
pip install -r requirements.txt

# Create necessary directories
echo "Creating system directories..."
mkdir -p data/knowledge_base
mkdir -p data/client_records
mkdir -p data/credentials
mkdir -p logs

# Initialize environment file if it doesn't exist
if [ ! -f .env ]; then
    echo "Creating .env file..."
    cat > .env << EOL
# Agent System Configuration
ENVIRONMENT=development
LOG_LEVEL=INFO

# API Keys
ELEVENLABS_API_KEY=***************************************************

# Twilio Configuration
TWILIO_ACCOUNT_SID=**********************************
TWILIO_AUTH_TOKEN=0458bc22d41d6756fc8e62d3e2938382

# Website
WEBSITE_URL=https://www.flofaction.com/insurance

# Carrier API Endpoints
UHC_API_URL=https://jarvys.com
MOO_API_URL=https://www.mutualofomaha.com
AMERICO_API_URL=https://www.americo.com
AETNA_API_URL=https://producerworld.com
CIGNA_API_URL=https://cignaforbrokers.com
EOL
fi

# Download required NLTK data
echo "Downloading NLTK data..."
python3 -c "import nltk; nltk.download('punkt'); nltk.download('averaged_perceptron_tagger'); nltk.download('wordnet')"

# Download spaCy model
echo "Downloading spaCy model..."
python3 -m spacy download en_core_web_sm

echo "Setup complete! You can now run the system using: ./run.sh"