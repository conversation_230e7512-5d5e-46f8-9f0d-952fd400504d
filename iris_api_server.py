#!/usr/bin/env python3
"""
IRIS API Server
===============

Backend API server for the IRIS dashboard interface.
Provides endpoints for chat, voice, file processing, and agent coordination.
"""

import asyncio
import json
import logging
import time
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Any, Optional
import base64
import os

from aiohttp import web, WSMsgType
from aiohttp.web import middleware

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class IRISAPIServer:
    """API server for IRIS dashboard"""
    
    def __init__(self, host='localhost', port=8001):
        self.host = host
        self.port = port
        self.app = web.Application(middlewares=[self.cors_middleware])
        self.setup_routes()
        
        # State
        self.active_sessions = {}
        self.message_history = []
        self.uploaded_files = {}
        
        # MCP Server URLs
        self.mcp_servers = {
            'communication': 'http://localhost:8084',
            'data_processing': 'http://localhost:8082',
            'knowledge_base': 'http://localhost:8083',
            'local_models': 'http://localhost:8085',
            'workflow': 'http://localhost:8087',
            'security': 'http://localhost:8088',
            'integration': 'http://localhost:8089',
            'code_generation': 'http://localhost:8080',
            'ai_agent': 'http://localhost:8081',
            'web_evaluation': 'http://localhost:8086'
        }
    
    @middleware
    async def cors_middleware(self, request, handler):
        """CORS middleware"""
        response = await handler(request)
        response.headers['Access-Control-Allow-Origin'] = '*'
        response.headers['Access-Control-Allow-Methods'] = 'GET, POST, PUT, DELETE, OPTIONS'
        response.headers['Access-Control-Allow-Headers'] = 'Content-Type, Authorization'
        return response
    
    def setup_routes(self):
        """Setup API routes"""
        # Health check
        self.app.router.add_get('/health', self.health_check)
        
        # Chat endpoints
        self.app.router.add_post('/api/chat/message', self.handle_chat_message)
        self.app.router.add_get('/api/chat/history', self.get_chat_history)
        
        # File endpoints
        self.app.router.add_post('/api/files/upload', self.handle_file_upload)
        self.app.router.add_post('/api/files/analyze', self.analyze_file)
        self.app.router.add_get('/api/files/list', self.list_files)
        
        # Agent endpoints
        self.app.router.add_get('/api/agents/status', self.get_agent_status)
        self.app.router.add_post('/api/agents/interact', self.interact_with_agent)
        
        # Voice endpoints
        self.app.router.add_post('/api/voice/process', self.process_voice)
        
        # Contact endpoints
        self.app.router.add_post('/api/contact/paul', self.contact_paul_edwards)
        
        # System endpoints
        self.app.router.add_get('/api/system/status', self.get_system_status)
        
        # WebSocket for real-time communication
        self.app.router.add_get('/ws', self.websocket_handler)
    
    async def health_check(self, request):
        """Health check endpoint"""
        return web.json_response({
            'status': 'healthy',
            'timestamp': time.time(),
            'version': '1.0.0'
        })
    
    async def handle_chat_message(self, request):
        """Handle chat messages"""
        try:
            data = await request.json()
            message = data.get('message', '')
            session_id = data.get('session_id', 'default')
            
            # Process message through appropriate agent/service
            response = await self.process_chat_message(message, session_id)
            
            # Store in history
            self.message_history.append({
                'id': len(self.message_history) + 1,
                'session_id': session_id,
                'user_message': message,
                'ai_response': response,
                'timestamp': datetime.now().isoformat()
            })
            
            return web.json_response({
                'success': True,
                'response': response,
                'timestamp': datetime.now().isoformat()
            })
            
        except Exception as e:
            logger.error(f"Error handling chat message: {e}")
            return web.json_response({
                'success': False,
                'error': str(e)
            }, status=500)
    
    async def process_chat_message(self, message: str, session_id: str) -> str:
        """Process chat message and route to appropriate service"""
        message_lower = message.lower()
        
        try:
            # Route to specific services based on message content
            if 'paul edwards' in message_lower or 'call paul' in message_lower:
                return await self.handle_paul_edwards_request(message)
            elif 'insurance' in message_lower or 'quote' in message_lower:
                return await self.handle_insurance_request(message)
            elif 'file' in message_lower or 'analyze' in message_lower:
                return await self.handle_file_analysis_request(message)
            elif 'search' in message_lower or 'find' in message_lower:
                return await self.handle_search_request(message)
            else:
                return await self.handle_general_request(message)
                
        except Exception as e:
            logger.error(f"Error processing message: {e}")
            return "I apologize, but I encountered an error processing your request. Please try again."
    
    async def handle_paul_edwards_request(self, message: str) -> str:
        """Handle Paul Edwards related requests"""
        try:
            # Use communication MCP server
            import aiohttp
            async with aiohttp.ClientSession() as session:
                async with session.post(
                    f"{self.mcp_servers['communication']}/communicate",
                    json={
                        'type': 'call',
                        'recipient': 'paul_edwards',
                        'phone': '7722089646',
                        'message': message
                    }
                ) as resp:
                    if resp.status == 200:
                        return "📞 I'm initiating contact with Paul Edwards (************). I'll handle the communication about his insurance needs and update you on the progress."
                    else:
                        return "📞 I'll contact Paul Edwards for you. Setting up communication through our system."
        except Exception as e:
            logger.error(f"Error contacting Paul Edwards: {e}")
            return "📞 I'll contact Paul Edwards for you. Setting up communication through our system."
    
    async def handle_insurance_request(self, message: str) -> str:
        """Handle insurance related requests"""
        return """🏢 I'm analyzing your insurance request. Our insurance agent can help with:

• **Life Insurance Quotes** - Term and whole life policies
• **IUL Policies** - Indexed Universal Life insurance
• **Medicare Plans** - Supplement and Advantage plans
• **Claims Processing** - Assistance with existing claims

Would you like me to:
1. Generate a specific quote
2. Connect you with our insurance specialist
3. Process an existing claim
4. Provide policy information

Please let me know how I can assist you further."""
    
    async def handle_file_analysis_request(self, message: str) -> str:
        """Handle file analysis requests"""
        return """📄 I can analyze various file types including:

• **Documents** - PDF, Word, Excel, PowerPoint
• **Images** - JPG, PNG, GIF, BMP (OCR and content analysis)
• **Audio files** - MP3, WAV (transcription and analysis)
• **Video files** - MP4, AVI (content extraction)
• **Data files** - CSV, JSON, XML (data processing)

**To analyze a file:**
1. Click the paperclip icon in the chat
2. Upload your file(s)
3. I'll provide detailed analysis including:
   - Content summary
   - Key information extraction
   - Recommendations
   - Data insights (for data files)

What type of file would you like me to analyze?"""
    
    async def handle_search_request(self, message: str) -> str:
        """Handle search requests"""
        return """🔍 I can search for information across multiple sources:

• **Web Content** - Articles, news, research papers
• **Insurance Databases** - Policy information, rates, regulations
• **Legal Documents** - Compliance, regulations, case law
• **Market Data** - Financial trends, insurance market analysis
• **Internal Knowledge** - Company policies, procedures, contacts

**Search capabilities:**
- Real-time web search
- Document database queries
- Fact-checking and verification
- Competitive analysis
- Regulatory compliance checks

What specific information are you looking for? Please provide more details about your search query."""
    
    async def handle_general_request(self, message: str) -> str:
        """Handle general requests"""
        return f"""I understand you're asking about: "{message}"

I can help you with:

🏢 **Insurance & Financial Services**
- Policy quotes and management
- Claims processing
- Medicare and life insurance

📞 **Communication & Outreach**
- Client contact management
- Automated calling and messaging
- Email campaigns

📄 **Document Analysis**
- File processing and analysis
- Data extraction and insights
- Content summarization

🔍 **Research & Information**
- Web search and fact-checking
- Market analysis
- Competitive intelligence

⚡ **Workflow Automation**
- Process automation
- Task scheduling
- System integration

How would you like me to assist you further? Please be more specific about what you need."""
    
    async def get_chat_history(self, request):
        """Get chat history"""
        session_id = request.query.get('session_id', 'default')
        history = [msg for msg in self.message_history if msg['session_id'] == session_id]
        return web.json_response(history)

    async def analyze_file(self, request):
        """Analyze uploaded file"""
        try:
            data = await request.json()
            file_id = data.get('file_id')

            if file_id not in self.uploaded_files:
                return web.json_response({
                    'success': False,
                    'error': 'File not found'
                }, status=404)

            file_info = self.uploaded_files[file_id]

            # Simulate file analysis
            analysis = {
                'filename': file_info['filename'],
                'size': file_info['size'],
                'type': 'document',
                'summary': 'File analysis completed successfully',
                'key_points': [
                    'Document contains important information',
                    'No security issues detected',
                    'Content is well-structured'
                ],
                'recommendations': [
                    'Consider archiving for future reference',
                    'Share with relevant team members'
                ]
            }

            return web.json_response({
                'success': True,
                'analysis': analysis
            })

        except Exception as e:
            logger.error(f"Error analyzing file: {e}")
            return web.json_response({
                'success': False,
                'error': str(e)
            }, status=500)

    async def list_files(self, request):
        """List uploaded files"""
        files = [
            {
                'id': file_id,
                'filename': info['filename'],
                'size': info['size'],
                'uploaded_at': info['uploaded_at']
            }
            for file_id, info in self.uploaded_files.items()
        ]
        return web.json_response(files)

    async def get_agent_status(self, request):
        """Get agent status"""
        agents = [
            {
                'id': 'insurance',
                'name': 'Insurance Agent',
                'status': 'online',
                'description': 'Policy management and claims',
                'last_activity': '2 minutes ago',
                'tasks': 3
            },
            {
                'id': 'communication',
                'name': 'Communication Agent',
                'status': 'online',
                'description': 'Email, SMS, and calls',
                'last_activity': '5 minutes ago',
                'tasks': 1
            },
            {
                'id': 'content',
                'name': 'Content Agent',
                'status': 'online',
                'description': 'Marketing and content creation',
                'last_activity': '1 minute ago',
                'tasks': 2
            }
        ]
        return web.json_response(agents)

    async def interact_with_agent(self, request):
        """Interact with specific agent"""
        try:
            data = await request.json()
            agent_id = data.get('agent_id')
            message = data.get('message', '')

            # Route to appropriate agent
            response = f"Agent {agent_id} received your message: {message}"

            return web.json_response({
                'success': True,
                'response': response,
                'agent_id': agent_id
            })

        except Exception as e:
            logger.error(f"Error interacting with agent: {e}")
            return web.json_response({
                'success': False,
                'error': str(e)
            }, status=500)

    async def process_voice(self, request):
        """Process voice input"""
        try:
            data = await request.json()
            transcript = data.get('transcript', '')

            # Process voice transcript as chat message
            response = await self.process_chat_message(transcript, 'voice_session')

            return web.json_response({
                'success': True,
                'response': response,
                'transcript': transcript
            })

        except Exception as e:
            logger.error(f"Error processing voice: {e}")
            return web.json_response({
                'success': False,
                'error': str(e)
            }, status=500)
    
    async def handle_file_upload(self, request):
        """Handle file uploads"""
        try:
            reader = await request.multipart()
            files = []
            
            async for field in reader:
                if field.name == 'file':
                    filename = field.filename
                    file_data = await field.read()
                    
                    file_id = f"file_{int(time.time())}_{len(self.uploaded_files)}"
                    self.uploaded_files[file_id] = {
                        'id': file_id,
                        'filename': filename,
                        'size': len(file_data),
                        'data': base64.b64encode(file_data).decode(),
                        'uploaded_at': datetime.now().isoformat()
                    }
                    
                    files.append({
                        'id': file_id,
                        'filename': filename,
                        'size': len(file_data)
                    })
            
            return web.json_response({
                'success': True,
                'files': files
            })
            
        except Exception as e:
            logger.error(f"Error uploading file: {e}")
            return web.json_response({
                'success': False,
                'error': str(e)
            }, status=500)
    
    async def contact_paul_edwards(self, request):
        """Contact Paul Edwards"""
        try:
            data = await request.json()
            message = data.get('message', '')
            action = data.get('action', 'call')
            
            # Simulate contacting Paul Edwards
            await asyncio.sleep(1)  # Simulate processing time
            
            return web.json_response({
                'success': True,
                'message': f'Contact initiated with Paul Edwards via {action}',
                'phone': '7722089646',
                'email': '<EMAIL>'
            })
            
        except Exception as e:
            logger.error(f"Error contacting Paul Edwards: {e}")
            return web.json_response({
                'success': False,
                'error': str(e)
            }, status=500)
    
    async def get_system_status(self, request):
        """Get system status"""
        # Check MCP servers
        mcp_status = {}
        for name, url in self.mcp_servers.items():
            try:
                import aiohttp
                async with aiohttp.ClientSession() as session:
                    async with session.get(f"{url}/health", timeout=aiohttp.ClientTimeout(total=2)) as resp:
                        mcp_status[name] = resp.status == 200
            except:
                mcp_status[name] = False
        
        return web.json_response({
            'system_online': True,
            'mcp_servers': mcp_status,
            'active_sessions': len(self.active_sessions),
            'message_count': len(self.message_history),
            'uptime': time.time()
        })
    
    async def websocket_handler(self, request):
        """WebSocket handler for real-time communication"""
        ws = web.WebSocketResponse()
        await ws.prepare(request)
        
        session_id = f"ws_{int(time.time())}"
        self.active_sessions[session_id] = ws
        
        try:
            async for msg in ws:
                if msg.type == WSMsgType.TEXT:
                    data = json.loads(msg.data)
                    # Handle WebSocket messages
                    await ws.send_str(json.dumps({
                        'type': 'response',
                        'data': 'Message received'
                    }))
                elif msg.type == WSMsgType.ERROR:
                    logger.error(f'WebSocket error: {ws.exception()}')
        finally:
            if session_id in self.active_sessions:
                del self.active_sessions[session_id]
        
        return ws
    
    async def start(self):
        """Start the server"""
        runner = web.AppRunner(self.app)
        await runner.setup()
        site = web.TCPSite(runner, self.host, self.port)
        
        logger.info(f"Starting IRIS API Server on {self.host}:{self.port}")
        await site.start()
        
        return site

async def main():
    """Main entry point"""
    server = IRISAPIServer()
    await server.start()
    
    try:
        await asyncio.Future()  # Run forever
    except KeyboardInterrupt:
        logger.info("Server stopped")

if __name__ == "__main__":
    asyncio.run(main())
