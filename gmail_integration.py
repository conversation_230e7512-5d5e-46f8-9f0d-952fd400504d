"""
Gmail Integration for Flo Faction

This script demonstrates how to integrate Gmail with the Flo Faction website
for sending automated emails to clients like <PERSON>.
"""

import os
import smtplib
import ssl
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
from email.mime.image import MIMEImage
import base64
import json
import time
from datetime import datetime

# Gmail credentials
GMAIL_EMAIL = "<EMAIL>"
GMAIL_APP_PASSWORD = os.environ.get("GMAIL_APP_PASSWORD", "your_app_password_here")

# Flo Faction branding
FLO_FACTION = {
    "name": "Flo Faction Insurance",
    "website": "https://www.flofaction.com",
    "facebook": "https://www.facebook.com/flofaction",
    "instagram": "https://www.instagram.com/flofaction",
    "phone": "************",
    "address": "123 Insurance Way, Insurance City, FL 12345",
    "logo_path": "assets/flo_faction_logo.png"  # Path to logo image file
}

# Email templates
EMAIL_TEMPLATES = {
    "welcome": {
        "subject": "Welcome to Flo Faction Insurance",
        "body_html": """
        <html>
        <head>
            <style>
                body {{
                    font-family: Arial, sans-serif;
                    line-height: 1.6;
                    color: #333333;
                }}
                .container {{
                    max-width: 600px;
                    margin: 0 auto;
                    padding: 20px;
                }}
                .header {{
                    text-align: center;
                    margin-bottom: 20px;
                }}
                .logo {{
                    max-width: 200px;
                }}
                .footer {{
                    margin-top: 30px;
                    font-size: 12px;
                    color: #777777;
                    text-align: center;
                }}
                .social-links {{
                    margin-top: 20px;
                }}
                .social-links a {{
                    margin: 0 10px;
                    text-decoration: none;
                }}
                .button {{
                    display: inline-block;
                    background-color: #4CAF50;
                    color: white;
                    padding: 10px 20px;
                    text-decoration: none;
                    border-radius: 5px;
                    margin-top: 20px;
                }}
            </style>
        </head>
        <body>
            <div class="container">
                <div class="header">
                    <img src="cid:logo" alt="Flo Faction Insurance" class="logo">
                    <h1>Welcome to Flo Faction Insurance!</h1>
                </div>
                
                <p>Dear {client_name},</p>
                
                <p>Thank you for choosing Flo Faction Insurance for your insurance needs. We're excited to have you as a client and look forward to providing you with exceptional service.</p>
                
                <p>Here's what you can expect from us:</p>
                <ul>
                    <li>Personalized insurance solutions tailored to your needs</li>
                    <li>Prompt and professional customer service</li>
                    <li>Regular policy reviews to ensure you have the right coverage</li>
                    <li>Assistance with claims and policy questions</li>
                </ul>
                
                <p>Your dedicated agent, {agent_name}, will be in touch shortly to discuss your specific insurance needs. In the meantime, feel free to explore our website or contact us with any questions.</p>
                
                <center><a href="{website}" class="button">Visit Our Website</a></center>
                
                <div class="social-links">
                    <p>Connect with us on social media:</p>
                    <a href="{facebook}">Facebook</a> | 
                    <a href="{instagram}">Instagram</a>
                </div>
                
                <div class="footer">
                    <p>{company_name}<br>
                    {address}<br>
                    Phone: {phone}<br>
                    Email: {email}</p>
                    
                    <p>© {current_year} {company_name}. All rights reserved.</p>
                </div>
            </div>
        </body>
        </html>
        """
    },
    "iul_introduction": {
        "subject": "Creating Tax-Free Retirement Income Without Market Risk",
        "body_html": """
        <html>
        <head>
            <style>
                body {{
                    font-family: Arial, sans-serif;
                    line-height: 1.6;
                    color: #333333;
                }}
                .container {{
                    max-width: 600px;
                    margin: 0 auto;
                    padding: 20px;
                }}
                .header {{
                    text-align: center;
                    margin-bottom: 20px;
                }}
                .logo {{
                    max-width: 200px;
                }}
                .footer {{
                    margin-top: 30px;
                    font-size: 12px;
                    color: #777777;
                    text-align: center;
                }}
                .social-links {{
                    margin-top: 20px;
                }}
                .social-links a {{
                    margin: 0 10px;
                    text-decoration: none;
                }}
                .button {{
                    display: inline-block;
                    background-color: #4CAF50;
                    color: white;
                    padding: 10px 20px;
                    text-decoration: none;
                    border-radius: 5px;
                    margin-top: 20px;
                }}
                .highlight {{
                    background-color: #f9f9f9;
                    padding: 15px;
                    border-left: 4px solid #4CAF50;
                    margin: 20px 0;
                }}
            </style>
        </head>
        <body>
            <div class="container">
                <div class="header">
                    <img src="cid:logo" alt="Flo Faction Insurance" class="logo">
                    <h1>Tax-Free Retirement Income Strategy</h1>
                </div>
                
                <p>Dear {client_name},</p>
                
                <p>I hope this email finds you well. My name is {agent_name} with {company_name}, and I specialize in helping people like you create tax-free retirement income without the risk of the stock market.</p>
                
                <p>Based on the information I have, I've prepared a personalized analysis that shows how you could potentially:</p>
                
                <div class="highlight">
                    <ol>
                        <li>Generate approximately <strong>${retirement_income}/month</strong> in tax-free retirement income</li>
                        <li>Build cash value that grows without the risk of market losses</li>
                        <li>Provide a death benefit of around <strong>${death_benefit}</strong> to protect your family</li>
                        <li>Create a financial strategy that offers both protection and growth potential</li>
                    </ol>
                </div>
                
                <p>Many of my clients are concerned about:</p>
                <ul>
                    <li>Market volatility affecting their retirement savings</li>
                    <li>Taxes reducing their retirement income</li>
                    <li>Having enough money to maintain their lifestyle in retirement</li>
                    <li>Protecting their family financially</li>
                </ul>
                
                <p>The strategy I'd like to share with you addresses all these concerns through an Indexed Universal Life policy specifically designed for your situation.</p>
                
                <p>Would you be interested in a brief 30-minute conversation to review this analysis? If so, please call me at {phone} or reply to this email with a good time to connect.</p>
                
                <center><a href="{website}/schedule" class="button">Schedule a Consultation</a></center>
                
                <p>You can also learn more about our services by visiting our website or following us on social media:</p>
                
                <div class="social-links">
                    <a href="{facebook}">Facebook</a> | 
                    <a href="{instagram}">Instagram</a>
                </div>
                
                <p>Thank you for your time, and I look forward to speaking with you soon.</p>
                
                <p>Best regards,<br>
                {agent_name}<br>
                {company_name}<br>
                {phone}<br>
                {email}</p>
                
                <div class="footer">
                    <p>{company_name}<br>
                    {address}<br>
                    Phone: {phone}<br>
                    Email: {email}</p>
                    
                    <p>© {current_year} {company_name}. All rights reserved.</p>
                </div>
            </div>
        </body>
        </html>
        """
    },
    "follow_up": {
        "subject": "Following Up - Flo Faction Insurance",
        "body_html": """
        <html>
        <head>
            <style>
                body {{
                    font-family: Arial, sans-serif;
                    line-height: 1.6;
                    color: #333333;
                }}
                .container {{
                    max-width: 600px;
                    margin: 0 auto;
                    padding: 20px;
                }}
                .header {{
                    text-align: center;
                    margin-bottom: 20px;
                }}
                .logo {{
                    max-width: 200px;
                }}
                .footer {{
                    margin-top: 30px;
                    font-size: 12px;
                    color: #777777;
                    text-align: center;
                }}
                .social-links {{
                    margin-top: 20px;
                }}
                .social-links a {{
                    margin: 0 10px;
                    text-decoration: none;
                }}
                .button {{
                    display: inline-block;
                    background-color: #4CAF50;
                    color: white;
                    padding: 10px 20px;
                    text-decoration: none;
                    border-radius: 5px;
                    margin-top: 20px;
                }}
            </style>
        </head>
        <body>
            <div class="container">
                <div class="header">
                    <img src="cid:logo" alt="Flo Faction Insurance" class="logo">
                    <h1>Following Up</h1>
                </div>
                
                <p>Dear {client_name},</p>
                
                <p>I hope this email finds you well. I'm following up on my previous message about {topic}.</p>
                
                <p>I wanted to check if you've had a chance to review the information I shared and see if you have any questions I can answer.</p>
                
                <p>Additionally, I came across some information that I thought would be particularly relevant to your situation:</p>
                
                <ul>
                    <li>{additional_info_1}</li>
                    <li>{additional_info_2}</li>
                </ul>
                
                <p>I'm available to discuss this further at your convenience. Feel free to call me at {phone} or reply to this email with a good time to connect.</p>
                
                <center><a href="{website}/schedule" class="button">Schedule a Call</a></center>
                
                <p>Thank you for your time, and I look forward to speaking with you soon.</p>
                
                <p>Best regards,<br>
                {agent_name}<br>
                {company_name}<br>
                {phone}<br>
                {email}</p>
                
                <div class="social-links">
                    <p>Connect with us on social media:</p>
                    <a href="{facebook}">Facebook</a> | 
                    <a href="{instagram}">Instagram</a>
                </div>
                
                <div class="footer">
                    <p>{company_name}<br>
                    {address}<br>
                    Phone: {phone}<br>
                    Email: {email}</p>
                    
                    <p>© {current_year} {company_name}. All rights reserved.</p>
                </div>
            </div>
        </body>
        </html>
        """
    }
}

def send_html_email(to_email, template_name, template_data=None):
    """
    Send an HTML email using a template
    
    Args:
        to_email: Recipient email address
        template_name: Name of the template to use
        template_data: Dictionary with template variables
        
    Returns:
        Boolean indicating success or failure
    """
    if template_name not in EMAIL_TEMPLATES:
        print(f"Error: Template '{template_name}' not found")
        return False
    
    template = EMAIL_TEMPLATES[template_name]
    
    # Default template data
    default_data = {
        "client_name": "Valued Client",
        "agent_name": "Insurance Agent",
        "company_name": FLO_FACTION["name"],
        "website": FLO_FACTION["website"],
        "facebook": FLO_FACTION["facebook"],
        "instagram": FLO_FACTION["instagram"],
        "phone": FLO_FACTION["phone"],
        "email": GMAIL_EMAIL,
        "address": FLO_FACTION["address"],
        "current_year": datetime.now().year,
        "retirement_income": "5,000",
        "death_benefit": "720,000",
        "topic": "insurance options",
        "additional_info_1": "A recent study showed that people who incorporate tax-free strategies into their retirement planning often end up with 20-30% more spendable income.",
        "additional_info_2": "For someone in your age group, starting an IUL policy now could potentially result in over $1 million in tax-free retirement income over a 20-year retirement period."
    }
    
    # Update with provided template data
    if template_data:
        default_data.update(template_data)
    
    try:
        # Create message
        message = MIMEMultipart("related")
        message["Subject"] = template["subject"]
        message["From"] = f"{FLO_FACTION['name']} <{GMAIL_EMAIL}>"
        message["To"] = to_email
        
        # Create HTML content
        html_content = template["body_html"].format(**default_data)
        html_part = MIMEMultipart("alternative")
        html_part.attach(MIMEText(html_content, "html"))
        message.attach(html_part)
        
        # Attach logo
        if os.path.exists(FLO_FACTION["logo_path"]):
            with open(FLO_FACTION["logo_path"], "rb") as logo_file:
                logo_data = logo_file.read()
                logo_part = MIMEImage(logo_data)
                logo_part.add_header("Content-ID", "<logo>")
                message.attach(logo_part)
        
        # Send email
        context = ssl.create_default_context()
        with smtplib.SMTP(host="smtp.gmail.com", port=587) as server:
            server.starttls(context=context)
            server.login(GMAIL_EMAIL, GMAIL_APP_PASSWORD)
            server.send_message(message)
        
        print(f"Email sent to {to_email} using template '{template_name}'")
        return True
    except Exception as e:
        print(f"Error sending email: {str(e)}")
        return False

def send_paul_edwards_email():
    """Send a personalized email to Paul Edwards"""
    paul_data = {
        "client_name": "Paul Edwards",
        "agent_name": "Sandra Smith",
        "retirement_income": "5,000",
        "death_benefit": "720,000"
    }
    
    return send_html_email(
        "<EMAIL>",
        "iul_introduction",
        paul_data
    )

def setup_gmail_instructions():
    """Print instructions for setting up Gmail integration"""
    print("=" * 80)
    print("GMAIL INTEGRATION SETUP INSTRUCTIONS")
    print("=" * 80)
    
    print("""
To set up Gmail integration for Flo Faction Insurance:

1. Generate an App Password for Gmail:
   a. Go to your Google Account settings: https://myaccount.google.com/
   b. Click on "Security" in the left sidebar
   c. Under "Signing in to Google," click on "2-Step Verification" (you must have this enabled)
   d. Scroll to the bottom and click on "App passwords"
   e. Select "Mail" as the app and "Other" as the device
   f. Enter "Flo Faction Insurance Script" as the name
   g. Click "Generate"
   h. Google will display a 16-character password - copy this password
   i. Use this password in the script instead of your regular Gmail password

2. Update the Email Configuration:
   a. Open the script and update the GMAIL_APP_PASSWORD variable
   b. Or set it as an environment variable:
      export GMAIL_APP_PASSWORD="your_16_character_app_password"

3. Create a Logo for Flo Faction:
   a. Design a logo or use an existing one
   b. Save it as "flo_faction_logo.png" in the "assets" directory
   c. Make sure the path in the script matches the actual location

4. Test the Email Integration:
   a. Run this script to send a test email to Paul Edwards
   b. Check that the email is delivered and looks correct
   c. Verify that the logo and formatting appear as expected

5. Integrate with Wix Website:
   a. In your Wix dashboard, go to "Settings" > "Email Settings"
   b. Select "Connect a custom email provider"
   c. Choose "Other" as the provider
   d. Enter the following SMTP settings:
      - SMTP Server: smtp.gmail.com
      - SMTP Port: 587
      - Username: <EMAIL>
      - Password: [Your app password]
      - Encryption: TLS
   e. Click "Connect" to test the connection
   f. Set up email notifications for form submissions

6. Create Email Templates in Wix:
   a. Go to "Marketing & SEO" > "Email Marketing"
   b. Click "Create Email" to design templates
   c. Create templates that match the ones in this script
   d. Save them for future use

7. Set Up Automated Email Sequences:
   a. In Wix, go to "Marketing & SEO" > "Automations"
   b. Create a new automation for "New Contact Form Submission"
   c. Add an email action using your templates
   d. Set up follow-up emails at appropriate intervals
    """)
    
    print("=" * 80)

if __name__ == "__main__":
    print("This script demonstrates Gmail integration for Flo Faction Insurance.")
    print("It can send personalized HTML emails to clients like Paul Edwards.")
    
    # Check if logo file exists
    if not os.path.exists(FLO_FACTION["logo_path"]):
        print(f"Warning: Logo file not found at {FLO_FACTION['logo_path']}")
        print("Create an 'assets' directory and add a logo file named 'flo_faction_logo.png'")
    
    # Check if app password is set
    if GMAIL_APP_PASSWORD == "your_app_password_here":
        print("Warning: Gmail app password not set")
        print("Set the GMAIL_APP_PASSWORD environment variable or update the script")
    
    action = input("What would you like to do? (send_email/show_instructions): ")
    
    if action.lower() == "send_email":
        if GMAIL_APP_PASSWORD == "your_app_password_here":
            print("Cannot send email: App password not set")
        else:
            success = send_paul_edwards_email()
            if success:
                print("Email sent successfully to Paul Edwards!")
            else:
                print("Failed to send email to Paul Edwards")
    elif action.lower() == "show_instructions":
        setup_gmail_instructions()
    else:
        print("Invalid action. Please choose 'send_email' or 'show_instructions'.")
