import subprocess
import sys
import logging
import os
from pathlib import Path

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def install_requirements():
    """Install required packages for stock analysis"""
    
    requirements = [
        'pandas',
        'numpy',
        'yfinance',
        'scikit-learn',
        'matplotlib',
        'seaborn'
    ]
    
    logger.info("Installing analysis dependencies...")
    
    for package in requirements:
        try:
            subprocess.check_call([
                sys.executable, 
                "-m", 
                "pip", 
                "install", 
                "--upgrade",
                package
            ])
            logger.info(f"Successfully installed {package}")
        except Exception as e:
            logger.error(f"Error installing {package}: {str(e)}")
            return False
    
    return True

def create_directories():
    """Create necessary directories for analysis results"""
    directories = [
        'test_results',
        'test_results/charts',
        'test_results/data',
        'test_results/reports'
    ]
    
    logger.info("Creating project directories...")
    
    for directory in directories:
        try:
            Path(directory).mkdir(parents=True, exist_ok=True)
            logger.info(f"Created directory: {directory}")
        except Exception as e:
            logger.error(f"Error creating {directory}: {str(e)}")
            return False
    
    return True

def verify_installation():
    """Verify all components are properly installed"""
    try:
        # Test imports
        import pandas as pd
        import numpy as np
        import yfinance as yf
        from sklearn.ensemble import RandomForestRegressor
        import matplotlib.pyplot as plt
        import seaborn as sns
        
        # Test basic functionality
        logger.info("Testing yfinance...")
        data = yf.download("SPY", period="1d")
        if data is None or len(data) == 0:
            raise Exception("Failed to download test data")
            
        logger.info("Testing matplotlib...")
        plt.figure()
        plt.close()
        
        return True
        
    except Exception as e:
        logger.error(f"Verification failed: {str(e)}")
        return False

def main():
    """Run setup process"""
    logger.info("Setting up stock analysis environment...")
    
    # Install requirements
    if not install_requirements():
        logger.error("Failed to install requirements")
        return False
        
    # Create directories
    if not create_directories():
        logger.error("Failed to create directories")
        return False
        
    # Verify installation
    if not verify_installation():
        logger.error("Installation verification failed")
        return False
        
    logger.info("\nSetup completed successfully!")
    logger.info("You can now run the analysis with:")
    logger.info("python test_stock_analysis.py")
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)