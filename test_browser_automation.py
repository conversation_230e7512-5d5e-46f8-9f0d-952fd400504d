#!/usr/bin/env python3
"""
Test Browser Automation
=======================

Test script to demonstrate working browser automation capabilities.
"""

import time
import json
from working_browser_automation import WorkingBrowserAutomation

def test_basic_navigation():
    """Test basic browser navigation"""
    print("🧪 Testing Basic Navigation...")
    
    automation = WorkingBrowserAutomation(headless=True)
    
    # Test 1: Google Search
    task_id = automation.create_task(
        name="Google Search Test",
        url="https://www.google.com",
        actions=[
            {"type": "navigate", "target": "https://www.google.com"},
            {"type": "get_title"},
            {"type": "get_url"},
            {"type": "screenshot"}
        ]
    )
    
    task = automation.get_task(task_id)
    result = automation.execute_task(task)
    print(f"✅ Google Test Result: {result['success']}")
    
    # Test 2: Example.com
    task_id2 = automation.create_task(
        name="Example.com Test",
        url="https://example.com",
        actions=[
            {"type": "navigate", "target": "https://example.com"},
            {"type": "get_title"},
            {"type": "get_text", "target": "h1"},
            {"type": "screenshot"}
        ]
    )
    
    task2 = automation.get_task(task_id2)
    result2 = automation.execute_task(task2)
    print(f"✅ Example.com Test Result: {result2['success']}")
    
    automation.cleanup()
    return True

def test_form_interaction():
    """Test form interaction"""
    print("🧪 Testing Form Interaction...")
    
    automation = WorkingBrowserAutomation(headless=True)
    
    # Test form interaction on httpbin.org
    task_id = automation.create_task(
        name="Form Interaction Test",
        url="https://httpbin.org/forms/post",
        actions=[
            {"type": "navigate", "target": "https://httpbin.org/forms/post"},
            {"type": "get_title"},
            {"type": "type", "target": "input[name='custname']", "value": "Test User"},
            {"type": "type", "target": "input[name='custtel']", "value": "************"},
            {"type": "type", "target": "input[name='custemail']", "value": "<EMAIL>"},
            {"type": "screenshot"}
        ]
    )
    
    task = automation.get_task(task_id)
    result = automation.execute_task(task)
    print(f"✅ Form Test Result: {result['success']}")
    
    automation.cleanup()
    return True

def test_multiple_pages():
    """Test navigation across multiple pages"""
    print("🧪 Testing Multiple Page Navigation...")
    
    automation = WorkingBrowserAutomation(headless=True)
    
    pages_to_test = [
        "https://httpbin.org/",
        "https://jsonplaceholder.typicode.com/",
        "https://reqres.in/"
    ]
    
    actions = []
    for i, url in enumerate(pages_to_test):
        actions.extend([
            {"type": "navigate", "target": url},
            {"type": "get_title"},
            {"type": "get_url"},
            {"type": "wait", "value": "1"}
        ])
    
    task_id = automation.create_task(
        name="Multiple Pages Test",
        url="",
        actions=actions
    )
    
    task = automation.get_task(task_id)
    result = automation.execute_task(task)
    print(f"✅ Multiple Pages Test Result: {result['success']}")
    
    automation.cleanup()
    return True

def test_api_endpoints():
    """Test browser automation API endpoints"""
    print("🧪 Testing API Endpoints...")
    
    import requests
    import time
    
    # Wait for server to be ready
    time.sleep(2)
    
    base_url = "http://localhost:8080"
    
    try:
        # Test status endpoint
        response = requests.get(f"{base_url}/api/status")
        print(f"✅ Status API: {response.status_code} - {response.json()}")
        
        # Test create task endpoint
        task_data = {
            "name": "API Test Task",
            "url": "https://httpbin.org/get",
            "actions": [
                {"type": "navigate", "target": "https://httpbin.org/get"},
                {"type": "get_title"}
            ]
        }
        
        response = requests.post(f"{base_url}/api/tasks", json=task_data)
        task_result = response.json()
        print(f"✅ Create Task API: {response.status_code} - {task_result}")
        
        if task_result.get("success"):
            task_id = task_result["task_id"]
            
            # Test get task endpoint
            response = requests.get(f"{base_url}/api/tasks/{task_id}")
            print(f"✅ Get Task API: {response.status_code}")
            
            # Test execute task endpoint
            response = requests.post(f"{base_url}/api/tasks/{task_id}/execute")
            execute_result = response.json()
            print(f"✅ Execute Task API: {response.status_code} - Success: {execute_result.get('success')}")
        
        # Test list tasks endpoint
        response = requests.get(f"{base_url}/api/tasks")
        tasks = response.json()
        print(f"✅ List Tasks API: {response.status_code} - Found {len(tasks)} tasks")
        
        return True
        
    except requests.exceptions.ConnectionError:
        print("❌ Server not running. Please start the server first.")
        return False
    except Exception as e:
        print(f"❌ API Test Error: {e}")
        return False

def main():
    """Run all tests"""
    print("🚀 Starting Browser Automation Tests...")
    print("=" * 50)
    
    tests = [
        ("Basic Navigation", test_basic_navigation),
        ("Form Interaction", test_form_interaction),
        ("Multiple Pages", test_multiple_pages),
        ("API Endpoints", test_api_endpoints)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n📋 Running {test_name} Test...")
        try:
            result = test_func()
            results.append((test_name, result))
            if result:
                print(f"✅ {test_name} Test: PASSED")
            else:
                print(f"❌ {test_name} Test: FAILED")
        except Exception as e:
            print(f"❌ {test_name} Test: ERROR - {e}")
            results.append((test_name, False))
    
    print("\n" + "=" * 50)
    print("📊 Test Results Summary:")
    print("=" * 50)
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"{test_name:20} {status}")
    
    print(f"\nOverall: {passed}/{total} tests passed ({passed/total*100:.1f}%)")
    
    if passed == total:
        print("🎉 All tests passed! Browser automation is working perfectly!")
    else:
        print("⚠️  Some tests failed. Check the output above for details.")

if __name__ == "__main__":
    main()
