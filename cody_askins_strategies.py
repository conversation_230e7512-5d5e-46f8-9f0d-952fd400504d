"""
Cody Askins Insurance Agency Scaling Strategies

This module incorporates best practices from Cody Askins' 8% Nation and Kaboom Leads
to help insurance agencies scale their operations effectively.
"""

from typing import Dict, List, Optional
import logging

logger = logging.getLogger(__name__)

class CodyAskinsStrategies:
    """Implementation of Cody Askins' insurance agency scaling strategies"""
    
    def __init__(self):
        """Initialize Cody Askins strategies"""
        
        # Core business models from Cody Askins
        self.business_models = {
            "direct_mail": {
                "description": "Targeted direct mail campaigns to specific demographics",
                "cost_per_lead": 5.00,
                "conversion_rate": 0.03,
                "best_for": ["Final Expense", "Medicare Supplement", "IUL for seniors"],
                "implementation_difficulty": "Medium",
                "time_to_results": "2-4 weeks"
            },
            "facebook_ads": {
                "description": "Targeted Facebook ad campaigns with specific insurance offers",
                "cost_per_lead": 3.50,
                "conversion_rate": 0.04,
                "best_for": ["IUL", "Mortgage Protection", "Term Life"],
                "implementation_difficulty": "Medium",
                "time_to_results": "1-2 weeks"
            },
            "referral_systems": {
                "description": "Systematic approach to generating referrals from clients",
                "cost_per_lead": 0.00,
                "conversion_rate": 0.15,
                "best_for": ["All products"],
                "implementation_difficulty": "Low",
                "time_to_results": "Immediate"
            },
            "agent_recruitment": {
                "description": "Building a team of agents to multiply production",
                "cost_per_agent": 500.00,
                "roi_multiplier": 5.0,
                "best_for": ["Agency owners", "IMOs"],
                "implementation_difficulty": "High",
                "time_to_results": "3-6 months"
            },
            "kaboom_leads": {
                "description": "Premium lead generation service for insurance agents",
                "cost_per_lead": 20.00,
                "conversion_rate": 0.12,
                "best_for": ["IUL", "Final Expense", "Medicare"],
                "implementation_difficulty": "Low",
                "time_to_results": "Immediate"
            }
        }
        
        # Sales scripts and frameworks
        self.sales_frameworks = {
            "iul_script": {
                "opening": "I help people like you create tax-free retirement income without the risk of the stock market. Would an extra {retirement_amount} per month in tax-free retirement income be helpful?",
                "discovery_questions": [
                    "What are your current retirement plans?",
                    "How much are you currently saving for retirement?",
                    "Are you concerned about market volatility?",
                    "How would you feel about a strategy that protects your principal while still giving you growth potential?"
                ],
                "value_proposition": "This IUL strategy gives you three key benefits: tax-free growth, tax-free access to your money, and a tax-free death benefit for your family.",
                "close": "Based on what you've shared, I recommend we start with a {monthly_premium} monthly premium which can provide approximately {retirement_income} in annual tax-free retirement income. Should we move forward with the application?"
            },
            "8_percent_framework": {
                "step1": "Build rapport (2-3 minutes)",
                "step2": "Identify pain points (5-7 minutes)",
                "step3": "Present solution (10 minutes)",
                "step4": "Handle objections (5 minutes)",
                "step5": "Close (3-5 minutes)",
                "success_rate": "30-40% close rate when followed correctly"
            }
        }
        
        # Training resources
        self.training_resources = {
            "8_percent_nation": "https://8percentnation.com",
            "cody_askins_youtube": "https://www.youtube.com/c/CodyAskins",
            "secure_agent_mentor": "https://secureagentmentor.com",
            "kaboom_leads": "https://kaboomleads.com"
        }
    
    def get_recommended_strategy(self, product_type: str, budget: float) -> Dict:
        """
        Get recommended lead generation strategy based on product and budget
        
        Args:
            product_type: Type of insurance product
            budget: Monthly marketing budget
            
        Returns:
            Dictionary with recommended strategy
        """
        if budget < 500:
            return {
                "primary_strategy": "referral_systems",
                "secondary_strategy": "facebook_ads" if product_type.lower() in ["iul", "term_life"] else "direct_mail",
                "budget_allocation": {
                    "referral_systems": 0.00,
                    "facebook_ads" if product_type.lower() in ["iul", "term_life"] else "direct_mail": budget
                }
            }
        elif budget < 2000:
            return {
                "primary_strategy": "facebook_ads" if product_type.lower() in ["iul", "term_life"] else "direct_mail",
                "secondary_strategy": "referral_systems",
                "budget_allocation": {
                    "primary": budget * 0.7,
                    "secondary": budget * 0.3
                }
            }
        else:
            return {
                "primary_strategy": "kaboom_leads",
                "secondary_strategy": "facebook_ads" if product_type.lower() in ["iul", "term_life"] else "direct_mail",
                "tertiary_strategy": "agent_recruitment" if budget > 5000 else "referral_systems",
                "budget_allocation": {
                    "kaboom_leads": budget * 0.5,
                    "facebook_ads" if product_type.lower() in ["iul", "term_life"] else "direct_mail": budget * 0.3,
                    "agent_recruitment" if budget > 5000 else "referral_systems": budget * 0.2
                }
            }
    
    def get_sales_script(self, product_type: str, client_data: Dict) -> str:
        """
        Get customized sales script based on product type and client data
        
        Args:
            product_type: Type of insurance product
            client_data: Dictionary with client information
            
        Returns:
            Customized sales script
        """
        if product_type.lower() == "iul":
            # Calculate retirement amount based on client data
            age = client_data.get("age", 40)
            income = client_data.get("income", 75000)
            years_to_retirement = max(65 - age, 20)
            monthly_retirement = round((income * 0.7) / 12, -2)  # 70% income replacement rounded to nearest 100
            
            script = self.sales_frameworks["iul_script"]["opening"].format(
                retirement_amount=f"${monthly_retirement:,}"
            )
            
            script += "\n\nDiscovery Questions:\n"
            for question in self.sales_frameworks["iul_script"]["discovery_questions"]:
                script += f"- {question}\n"
                
            script += f"\n{self.sales_frameworks['iul_script']['value_proposition']}\n\n"
            
            # Calculate recommended premium and income
            recommended_premium = round(client_data.get("budget", income * 0.05 / 12), -1)  # Round to nearest 10
            annual_retirement_income = round(recommended_premium * 12 * 15 * 0.05)  # Simplified calculation
            
            script += self.sales_frameworks["iul_script"]["close"].format(
                monthly_premium=f"${recommended_premium}",
                retirement_income=f"${annual_retirement_income:,}"
            )
            
            return script
        else:
            # Default to 8% framework outline
            script = "# 8% Nation Sales Framework\n\n"
            for step, description in self.sales_frameworks["8_percent_framework"].items():
                if step != "success_rate":
                    script += f"## {step.replace('step', 'Step ')}: {description}\n"
            
            script += f"\nExpected success rate: {self.sales_frameworks['8_percent_framework']['success_rate']}"
            return script
            
    def get_training_resources(self) -> Dict:
        """Get training resources for insurance agents"""
        return self.training_resources
