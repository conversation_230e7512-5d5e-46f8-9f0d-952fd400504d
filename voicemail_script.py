"""
Voicemail Script for <PERSON>

This script provides a voicemail script to read when leaving a message for <PERSON>.
"""

import platform
import os
import webbrowser

# <PERSON> contact information
PAUL_PHONE = "+17722089646"
PAUL_NAME = "<PERSON>"

def generate_voicemail_script():
    """Generate a voicemail script for <PERSON>"""
    script = f"""
VOICEMAIL SCRIPT FOR PAUL EDWARDS

"Hi {PAUL_NAME.split()[0]}, this is <PERSON> with Flo Faction Insurance. I'm calling because I help people like you create tax-free retirement income without the risk of the stock market.

Based on your profile, I've found that you might qualify for a strategy that could provide significant tax-free retirement income while also protecting your family with a death benefit.

I'd love to share a personalized analysis I've prepared for you. Please give me a call back when you have a moment at [YOUR PHONE NUMBER].

Again, this is <PERSON> with Flo Faction Insurance. I look forward to speaking with you soon. Thank you!"
    """
    return script

def open_phone_app():
    """Open the default phone app to call <PERSON>"""
    print("=" * 80)
    print("OPENING PHONE APP TO CALL PAUL EDWARDS")
    print("=" * 80)
    
    # Create phone URL based on platform
    if platform.system() == "Darwin":  # macOS
        # For macOS, use the tel: protocol
        phone_url = f"tel:{PAUL_PHONE}"
    elif platform.system() == "Windows":
        # For Windows, use the tel: protocol
        phone_url = f"tel:{PAUL_PHONE}"
    elif platform.system() == "Linux":
        # For Linux, try the tel: protocol
        phone_url = f"tel:{PAUL_PHONE}"
    else:
        print(f"Unsupported platform: {platform.system()}")
        return False
    
    try:
        # Open the default phone app
        webbrowser.open(phone_url)
        print(f"Default phone app opened to call {PAUL_PHONE}")
        print("Please complete the call in your phone app.")
        print("If Paul doesn't answer, leave a voicemail using the script below.")
        return True
    except Exception as e:
        print(f"Error opening default phone app: {str(e)}")
        return False

if __name__ == "__main__":
    print("This script provides a voicemail script to read when leaving a message for Paul Edwards.")
    print(f"Recipient: {PAUL_PHONE}")
    
    # Print the voicemail script
    print("\n" + "=" * 80)
    print("VOICEMAIL SCRIPT")
    print("=" * 80)
    print(generate_voicemail_script())
    
    proceed = input("\nDo you want to call Paul Edwards now? (yes/no): ")
    
    if proceed.lower() == "yes":
        open_phone_app()
    else:
        print("Operation cancelled.")
