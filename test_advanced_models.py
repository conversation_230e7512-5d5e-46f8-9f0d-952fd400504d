#!/usr/bin/env python3
"""
Advanced Models Test Suite
==========================

Tests all advanced AI models and agents to verify proper installation and functionality.
"""

import asyncio
import logging
import time
import json
from typing import Dict, Any
from pathlib import Path

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class AdvancedModelsTestSuite:
    """Test suite for all advanced AI models"""
    
    def __init__(self):
        self.test_results = {}
        self.test_queries = [
            "What is artificial intelligence?",
            "Explain the benefits of renewable energy",
            "How does machine learning work?",
            "Analyze the impact of climate change",
            "What are the latest developments in quantum computing?"
        ]
        
    async def run_all_tests(self):
        """Run all tests"""
        logger.info("Starting Advanced Models Test Suite...")
        
        try:
            # Test imports
            await self._test_imports()
            
            # Test individual models
            await self._test_manus_agent()
            await self._test_mimo_vl_agent()
            await self._test_detail_flow_agent()
            await self._test_giga_agent()
            await self._test_honest_ai_agent()
            
            # Test unified interface
            await self._test_unified_interface()
            
            # Test performance
            await self._test_performance()
            
            # Generate report
            self._generate_test_report()
            
            logger.info("Test suite completed successfully!")
            
        except Exception as e:
            logger.error(f"Test suite failed: {e}")
            raise
    
    async def _test_imports(self):
        """Test that all modules can be imported"""
        logger.info("Testing imports...")
        
        try:
            from advanced_models import AdvancedModelManager
            from advanced_models.manus_agent import ManusAgent
            from advanced_models.mimo_vl_agent import MimoVLAgent
            from advanced_models.detail_flow_agent import DetailFlowAgent
            from advanced_models.giga_agent import GigaAgent
            from advanced_models.honest_ai_agent import HonestAIAgent
            from advanced_models.unified_interface import UnifiedModelInterface
            
            self.test_results['imports'] = {
                'status': 'PASSED',
                'message': 'All modules imported successfully'
            }
            logger.info("✓ Import tests passed")
            
        except Exception as e:
            self.test_results['imports'] = {
                'status': 'FAILED',
                'message': f'Import failed: {str(e)}'
            }
            logger.error(f"✗ Import tests failed: {e}")
    
    async def _test_manus_agent(self):
        """Test MANUS agent"""
        logger.info("Testing MANUS agent...")
        
        try:
            from advanced_models.manus_agent import ManusAgent
            
            agent = ManusAgent()
            await agent.initialize()
            
            # Test query processing
            result = await agent.process_query(
                "Test MANUS reasoning capabilities",
                {"test_context": "This is a test"}
            )
            
            if result and 'response' in result:
                self.test_results['manus'] = {
                    'status': 'PASSED',
                    'response_length': len(result['response']),
                    'confidence': result.get('confidence', 0),
                    'capabilities': agent.get_capabilities()
                }
                logger.info("✓ MANUS agent test passed")
            else:
                raise Exception("Invalid response format")
                
        except Exception as e:
            self.test_results['manus'] = {
                'status': 'FAILED',
                'message': f'MANUS test failed: {str(e)}'
            }
            logger.error(f"✗ MANUS agent test failed: {e}")
    
    async def _test_mimo_vl_agent(self):
        """Test MiMo-VL-7B agent"""
        logger.info("Testing MiMo-VL-7B agent...")
        
        try:
            from advanced_models.mimo_vl_agent import MimoVLAgent
            
            agent = MimoVLAgent()
            await agent.initialize()
            
            # Test text query
            result = await agent.process_query(
                "Test MiMo-VL vision capabilities",
                {"test_context": "Vision-language model test"}
            )
            
            if result and 'response' in result:
                self.test_results['mimo_vl'] = {
                    'status': 'PASSED',
                    'response_length': len(result['response']),
                    'confidence': result.get('confidence', 0),
                    'capabilities': agent.get_capabilities()
                }
                logger.info("✓ MiMo-VL-7B agent test passed")
            else:
                raise Exception("Invalid response format")
                
        except Exception as e:
            self.test_results['mimo_vl'] = {
                'status': 'FAILED',
                'message': f'MiMo-VL test failed: {str(e)}'
            }
            logger.error(f"✗ MiMo-VL-7B agent test failed: {e}")
    
    async def _test_detail_flow_agent(self):
        """Test Detail Flow agent"""
        logger.info("Testing Detail Flow agent...")
        
        try:
            from advanced_models.detail_flow_agent import DetailFlowAgent
            
            agent = DetailFlowAgent()
            await agent.initialize()
            
            # Test query processing
            result = await agent.process_query(
                "Test Detail Flow step-by-step processing",
                {"test_context": "Flow-based processing test"}
            )
            
            if result and 'response' in result:
                self.test_results['detail_flow'] = {
                    'status': 'PASSED',
                    'response_length': len(result['response']),
                    'confidence': result.get('confidence', 0),
                    'capabilities': agent.get_capabilities()
                }
                logger.info("✓ Detail Flow agent test passed")
            else:
                raise Exception("Invalid response format")
                
        except Exception as e:
            self.test_results['detail_flow'] = {
                'status': 'FAILED',
                'message': f'Detail Flow test failed: {str(e)}'
            }
            logger.error(f"✗ Detail Flow agent test failed: {e}")
    
    async def _test_giga_agent(self):
        """Test Giga Agent"""
        logger.info("Testing Giga Agent...")
        
        try:
            from advanced_models.giga_agent import GigaAgent
            
            agent = GigaAgent()
            await agent.initialize()
            
            # Test autonomous processing
            result = await agent.process_query(
                "Test Giga Agent autonomous capabilities",
                {"test_context": "Autonomous agent test"}
            )
            
            if result and 'response' in result:
                self.test_results['giga_agent'] = {
                    'status': 'PASSED',
                    'response_length': len(result['response']),
                    'confidence': result.get('confidence', 0),
                    'capabilities': agent.get_capabilities()
                }
                logger.info("✓ Giga Agent test passed")
            else:
                raise Exception("Invalid response format")
                
        except Exception as e:
            self.test_results['giga_agent'] = {
                'status': 'FAILED',
                'message': f'Giga Agent test failed: {str(e)}'
            }
            logger.error(f"✗ Giga Agent test failed: {e}")
    
    async def _test_honest_ai_agent(self):
        """Test Honest AI Agent"""
        logger.info("Testing Honest AI Agent...")
        
        try:
            from advanced_models.honest_ai_agent import HonestAIAgent
            
            agent = HonestAIAgent()
            await agent.initialize()
            
            # Test honest processing
            result = await agent.process_query(
                "Test Honest AI research capabilities",
                {"test_context": "Honest AI research test"}
            )
            
            if result and 'response' in result:
                self.test_results['honest_ai'] = {
                    'status': 'PASSED',
                    'response_length': len(result['response']),
                    'confidence': result.get('confidence', 0),
                    'capabilities': agent.get_capabilities()
                }
                logger.info("✓ Honest AI Agent test passed")
            else:
                raise Exception("Invalid response format")
                
        except Exception as e:
            self.test_results['honest_ai'] = {
                'status': 'FAILED',
                'message': f'Honest AI test failed: {str(e)}'
            }
            logger.error(f"✗ Honest AI Agent test failed: {e}")
    
    async def _test_unified_interface(self):
        """Test unified interface"""
        logger.info("Testing Unified Interface...")
        
        try:
            from advanced_models.unified_interface import UnifiedModelInterface, ResponseStrategy
            
            interface = UnifiedModelInterface()
            await interface.initialize()
            
            # Test different strategies
            strategies = [
                ResponseStrategy.BEST_SINGLE,
                ResponseStrategy.PARALLEL_ALL,
                ResponseStrategy.SPECIALIZED
            ]
            
            strategy_results = {}
            
            for strategy in strategies:
                start_time = time.time()
                
                result = await interface.query(
                    "Test unified interface with multiple strategies",
                    context={"test": "unified_interface"},
                    strategy=strategy
                )
                
                processing_time = time.time() - start_time
                
                strategy_results[strategy.value] = {
                    'response_length': len(result.primary_response),
                    'confidence': result.confidence,
                    'processing_time': processing_time,
                    'models_used': len(result.model_responses)
                }
            
            self.test_results['unified_interface'] = {
                'status': 'PASSED',
                'strategies_tested': len(strategies),
                'strategy_results': strategy_results,
                'model_status': interface.get_model_status()
            }
            logger.info("✓ Unified Interface test passed")
            
        except Exception as e:
            self.test_results['unified_interface'] = {
                'status': 'FAILED',
                'message': f'Unified Interface test failed: {str(e)}'
            }
            logger.error(f"✗ Unified Interface test failed: {e}")
    
    async def _test_performance(self):
        """Test performance with multiple queries"""
        logger.info("Testing performance...")
        
        try:
            from advanced_models.unified_interface import UnifiedModelInterface
            
            interface = UnifiedModelInterface()
            await interface.initialize()
            
            performance_results = []
            
            for query in self.test_queries:
                start_time = time.time()
                
                result = await interface.query(query)
                
                processing_time = time.time() - start_time
                
                performance_results.append({
                    'query': query[:50] + "..." if len(query) > 50 else query,
                    'processing_time': processing_time,
                    'confidence': result.confidence,
                    'response_length': len(result.primary_response)
                })
            
            avg_time = sum(r['processing_time'] for r in performance_results) / len(performance_results)
            avg_confidence = sum(r['confidence'] for r in performance_results) / len(performance_results)
            
            self.test_results['performance'] = {
                'status': 'PASSED',
                'queries_tested': len(self.test_queries),
                'average_processing_time': avg_time,
                'average_confidence': avg_confidence,
                'individual_results': performance_results
            }
            logger.info("✓ Performance test passed")
            
        except Exception as e:
            self.test_results['performance'] = {
                'status': 'FAILED',
                'message': f'Performance test failed: {str(e)}'
            }
            logger.error(f"✗ Performance test failed: {e}")
    
    def _generate_test_report(self):
        """Generate comprehensive test report"""
        print("\\n" + "="*80)
        print("ADVANCED MODELS TEST REPORT")
        print("="*80)
        
        total_tests = len(self.test_results)
        passed_tests = sum(1 for result in self.test_results.values() if result['status'] == 'PASSED')
        
        print(f"Total Tests: {total_tests}")
        print(f"Passed: {passed_tests}")
        print(f"Failed: {total_tests - passed_tests}")
        print(f"Success Rate: {(passed_tests/total_tests)*100:.1f}%")
        
        print("\\n" + "-"*80)
        print("DETAILED RESULTS")
        print("-"*80)
        
        for test_name, result in self.test_results.items():
            status_symbol = "✓" if result['status'] == 'PASSED' else "✗"
            print(f"{status_symbol} {test_name.upper()}: {result['status']}")
            
            if result['status'] == 'PASSED':
                if 'capabilities' in result:
                    print(f"   Capabilities: {len(result['capabilities'])} features")
                if 'confidence' in result:
                    print(f"   Confidence: {result['confidence']:.2f}")
                if 'response_length' in result:
                    print(f"   Response Length: {result['response_length']} chars")
            else:
                if 'message' in result:
                    print(f"   Error: {result['message']}")
            print()
        
        # Performance summary
        if 'performance' in self.test_results and self.test_results['performance']['status'] == 'PASSED':
            perf = self.test_results['performance']
            print("-"*80)
            print("PERFORMANCE SUMMARY")
            print("-"*80)
            print(f"Average Processing Time: {perf['average_processing_time']:.2f}s")
            print(f"Average Confidence: {perf['average_confidence']:.2f}")
            print(f"Queries Tested: {perf['queries_tested']}")
        
        # Model status
        if 'unified_interface' in self.test_results and self.test_results['unified_interface']['status'] == 'PASSED':
            model_status = self.test_results['unified_interface']['model_status']
            print("\\n" + "-"*80)
            print("MODEL STATUS")
            print("-"*80)
            for model_name, status in model_status.items():
                enabled_symbol = "✓" if status['enabled'] else "✗"
                available_symbol = "✓" if status['available'] else "✗"
                print(f"{model_name}: Enabled {enabled_symbol} | Available {available_symbol} | Priority {status['priority']}")
        
        print("\\n" + "="*80)
        
        # Save detailed report to file
        report_file = Path("advanced_models_test_report.json")
        with open(report_file, 'w') as f:
            json.dump(self.test_results, f, indent=2, default=str)
        
        print(f"Detailed report saved to: {report_file}")
        print("="*80)

async def main():
    """Main test function"""
    test_suite = AdvancedModelsTestSuite()
    await test_suite.run_all_tests()

if __name__ == "__main__":
    asyncio.run(main())
