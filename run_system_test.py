#!/usr/bin/env python3
"""
Comprehensive System Test for Web Automation Dashboard
=====================================================

This script tests the complete web automation system including:
- All component imports and initialization
- Dashboard functionality
- Task creation and management
- API endpoints
- Deployment readiness
"""

import asyncio
import json
import sys
import time
from pathlib import Path

# Add current directory to path
sys.path.append('.')

async def test_component_imports():
    """Test all component imports"""
    print("🔍 Testing Component Imports...")
    
    components = {
        'Web-UI Component': ('web_ui_component', ['WebUIComponent', 'AutomationTask']),
        'WebRover Component': ('webrover_component', ['WebRoverComponent', 'WebPage', 'NavigationAction']),
        'MidScene Integration': ('midscene_integration', ['MidSceneIntegration', 'MidSceneAction']),
        'Unified Dashboard': ('unified_web_automation_dashboard', ['UnifiedWebAutomationDashboard', 'UnifiedTask']),
        'Google Cloud Deployer': ('deploy_to_gcloud', ['GoogleCloudDeployer'])
    }
    
    results = {}
    
    for name, (module_name, classes) in components.items():
        try:
            module = __import__(module_name)
            for class_name in classes:
                getattr(module, class_name)
            results[name] = True
            print(f"  ✅ {name}")
        except Exception as e:
            results[name] = False
            print(f"  ❌ {name}: {e}")
    
    return results

async def test_dashboard_initialization():
    """Test dashboard initialization and basic functionality"""
    print("\n🚀 Testing Dashboard Initialization...")
    
    try:
        from unified_web_automation_dashboard import UnifiedWebAutomationDashboard, UnifiedTask
        
        # Create dashboard
        dashboard = UnifiedWebAutomationDashboard()
        print("  ✅ Dashboard created")
        
        # Initialize components
        await dashboard.initialize()
        print("  ✅ Components initialized")
        
        # Check system status
        status = dashboard.system_status
        print(f"  📊 System Status: {status}")
        
        # Test task creation
        test_task = UnifiedTask(
            id="system_test_001",
            name="System Test Task",
            type="webrover"
        )
        
        dashboard.tasks[test_task.id] = test_task
        print(f"  ✅ Task created: {test_task.name}")
        
        # Test task retrieval
        retrieved_task = dashboard.tasks.get(test_task.id)
        assert retrieved_task is not None
        assert retrieved_task.name == "System Test Task"
        print("  ✅ Task retrieval working")
        
        return True, dashboard
        
    except Exception as e:
        print(f"  ❌ Dashboard initialization failed: {e}")
        import traceback
        traceback.print_exc()
        return False, None

async def test_component_functionality():
    """Test individual component functionality"""
    print("\n🔧 Testing Component Functionality...")
    
    results = {}
    
    # Test WebRover Component
    try:
        from webrover_component import WebRoverComponent
        webrover = WebRoverComponent()
        await webrover.initialize()
        
        # Test URL validation
        assert webrover._is_valid_url("https://example.com") == True
        assert webrover._is_valid_url("invalid-url") == False
        
        results['WebRover'] = True
        print("  ✅ WebRover Component")
        
    except Exception as e:
        results['WebRover'] = False
        print(f"  ❌ WebRover Component: {e}")
    
    # Test MidScene Integration
    try:
        from midscene_integration import MidSceneIntegration, MidSceneAction
        midscene = MidSceneIntegration(headless=True)
        
        # Test action creation
        action = MidSceneAction("click", "#button")
        assert action.action_type == "click"
        assert action.target == "#button"
        
        # Check config file exists
        assert midscene.config_file.exists()
        
        results['MidScene'] = True
        print("  ✅ MidScene Integration")
        
    except Exception as e:
        results['MidScene'] = False
        print(f"  ❌ MidScene Integration: {e}")
    
    # Test Google Cloud Deployer
    try:
        from deploy_to_gcloud import GoogleCloudDeployer
        deployer = GoogleCloudDeployer("test-project", "us-central1")
        
        assert deployer.project_id == "test-project"
        assert deployer.region == "us-central1"
        
        results['Deployer'] = True
        print("  ✅ Google Cloud Deployer")
        
    except Exception as e:
        results['Deployer'] = False
        print(f"  ❌ Google Cloud Deployer: {e}")
    
    return results

async def test_api_endpoints(dashboard):
    """Test API endpoints (mock test)"""
    print("\n🌐 Testing API Endpoints...")
    
    try:
        # Test status endpoint logic
        status_data = {
            "status": "operational",
            "components": dashboard.system_status,
            "tasks": {
                "total": len(dashboard.tasks),
                "active": len([t for t in dashboard.tasks.values() if t.status == "running"]),
                "completed": len([t for t in dashboard.tasks.values() if t.status == "completed"])
            },
            "timestamp": time.time()
        }
        
        assert "status" in status_data
        assert "components" in status_data
        assert "tasks" in status_data
        print("  ✅ Status endpoint logic")
        
        # Test task creation logic
        task_data = {
            "name": "API Test Task",
            "type": "webrover",
            "component_data": {
                "url": "https://example.com",
                "action": "crawl"
            }
        }
        
        from unified_web_automation_dashboard import UnifiedTask
        new_task = UnifiedTask(
            id=f"api_test_{int(time.time())}",
            name=task_data["name"],
            type=task_data["type"]
        )
        
        dashboard.tasks[new_task.id] = new_task
        print("  ✅ Task creation endpoint logic")
        
        # Test task listing logic
        tasks_list = [
            {
                "id": task.id,
                "name": task.name,
                "type": task.type,
                "status": task.status,
                "created_at": task.created_at.isoformat(),
                "updated_at": task.updated_at.isoformat()
            }
            for task in dashboard.tasks.values()
        ]
        
        assert len(tasks_list) > 0
        print("  ✅ Task listing endpoint logic")
        
        return True
        
    except Exception as e:
        print(f"  ❌ API endpoint testing failed: {e}")
        return False

def test_deployment_readiness():
    """Test deployment readiness"""
    print("\n🚀 Testing Deployment Readiness...")
    
    required_files = [
        'unified_web_automation_dashboard.py',
        'web_ui_component.py',
        'webrover_component.py',
        'midscene_integration.py',
        'deploy_to_gcloud.py',
        '.github/workflows/deploy-web-automation.yml',
        'requirements.txt'
    ]
    
    missing_files = []
    for file_path in required_files:
        if not Path(file_path).exists():
            missing_files.append(file_path)
        else:
            print(f"  ✅ {file_path}")
    
    if missing_files:
        print(f"  ❌ Missing files: {missing_files}")
        return False
    
    # Check directory structure
    required_dirs = [
        'dashboard/static',
        'web_ui/static',
        'midscene_temp',
        '.github/workflows'
    ]
    
    missing_dirs = []
    for dir_path in required_dirs:
        if not Path(dir_path).exists():
            missing_dirs.append(dir_path)
        else:
            print(f"  ✅ {dir_path}/")
    
    if missing_dirs:
        print(f"  ❌ Missing directories: {missing_dirs}")
        return False
    
    print("  ✅ All deployment files and directories present")
    return True

async def main():
    """Run comprehensive system test"""
    print("🎯 Web Automation System - Comprehensive Test Suite")
    print("=" * 60)
    
    # Test component imports
    import_results = await test_component_imports()
    
    # Test dashboard initialization
    dashboard_success, dashboard = await test_dashboard_initialization()
    
    # Test component functionality
    component_results = await test_component_functionality()
    
    # Test API endpoints (if dashboard initialized)
    api_success = False
    if dashboard_success and dashboard:
        api_success = await test_api_endpoints(dashboard)
    
    # Test deployment readiness
    deployment_ready = test_deployment_readiness()
    
    # Generate final report
    print("\n" + "=" * 60)
    print("📊 FINAL TEST REPORT")
    print("=" * 60)
    
    print("\n🔍 Component Imports:")
    for name, success in import_results.items():
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"  {name}: {status}")
    
    print(f"\n🚀 Dashboard Initialization: {'✅ PASS' if dashboard_success else '❌ FAIL'}")
    
    print("\n🔧 Component Functionality:")
    for name, success in component_results.items():
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"  {name}: {status}")
    
    print(f"\n🌐 API Endpoints: {'✅ PASS' if api_success else '❌ FAIL'}")
    print(f"\n🚀 Deployment Ready: {'✅ PASS' if deployment_ready else '❌ FAIL'}")
    
    # Calculate overall success
    total_tests = len(import_results) + len(component_results) + 3  # +3 for dashboard, api, deployment
    passed_tests = sum(import_results.values()) + sum(component_results.values()) + sum([dashboard_success, api_success, deployment_ready])
    
    success_rate = (passed_tests / total_tests) * 100
    
    print(f"\n🎯 OVERALL SUCCESS RATE: {success_rate:.1f}% ({passed_tests}/{total_tests} tests passed)")
    
    if success_rate >= 80:
        print("\n🎉 SYSTEM READY FOR DEPLOYMENT!")
        print("✅ Web Automation Dashboard is fully functional")
        print("✅ All core components are working")
        print("✅ Deployment pipeline is configured")
        print("\n🚀 Next Steps:")
        print("  1. Run: python unified_web_automation_dashboard.py")
        print("  2. Open: http://localhost:8000")
        print("  3. Deploy: python deploy_to_gcloud.py --project-id YOUR_PROJECT")
    else:
        print("\n⚠️  SYSTEM NEEDS ATTENTION")
        print("Some components require fixes before deployment")
    
    return success_rate >= 80

if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
