#!/bin/bash

# MCP Server Startup Script
# This script ensures all 10 MCP servers start properly and stay running

# Set up logging
LOG_FILE="mcp_servers_startup.log"
echo "$(date) - Starting MCP servers startup script" >> "$LOG_FILE"

# Function to check if a port is in use
check_port() {
    nc -z localhost "$1" > /dev/null 2>&1
    return $?
}

# Function to start a server
start_server() {
    local server_id="$1"
    local port="$2"
    local start_cmd="$3"

    echo "$(date) - Starting server: $server_id on port $port" >> "$LOG_FILE"

    # Check if server is already running
    if check_port "$port"; then
        echo "$(date) - Server $server_id already running on port $port" >> "$LOG_FILE"
        return 0
    fi

    # Start the server
    eval "$start_cmd" >> "$LOG_FILE" 2>&1 &

    # Wait for server to start
    for i in {1..30}; do
        if check_port "$port"; then
            echo "$(date) - Server $server_id started successfully on port $port" >> "$LOG_FILE"
            return 0
        fi
        sleep 1
    done

    echo "$(date) - Failed to start server $server_id on port $port" >> "$LOG_FILE"
    return 1
}

# Create necessary directories
mkdir -p $HOME/.config/llm_models 2>/dev/null || echo "Warning: Could not create ~/.config/llm_models directory"
mkdir -p $HOME/.local/models 2>/dev/null || echo "Warning: Could not create ~/.local/models directory"

# If we couldn't create the standard directories, create them in the current directory
if [ ! -d "$HOME/.config/llm_models" ]; then
    mkdir -p ./config/llm_models
    echo "Created local directory ./config/llm_models instead"
fi

if [ ! -d "$HOME/.local/models" ]; then
    mkdir -p ./local/models
    echo "Created local directory ./local/models instead"
fi

# Ensure mcp_config.json exists and is properly configured
if [ ! -f "mcp_config.json" ]; then
    echo "$(date) - Creating mcp_config.json" >> "$LOG_FILE"
    cat > mcp_config.json << EOL
{
    "servers": [
        {
            "server_id": "local-models-mcp",
            "name": "Local Models MCP Server",
            "url": "http://localhost:8085",
            "port": 8085,
            "start_command": "python3 local_models_mcp_server.py --port 8085",
            "capabilities": [
                "text-generation",
                "code-completion",
                "reasoning",
                "tool-use"
            ],
            "type": "process"
        },
        {
            "server_id": "web-eval-agent",
            "name": "UI T.A.R.S. Web Evaluation Agent",
            "url": "http://localhost:8086",
            "port": 8086,
            "start_command": "uvx --refresh-package webEvalAgent --from git+https://github.com/Operative-Sh/web-eval-agent.git webEvalAgent",
            "capabilities": [
                "web-evaluation",
                "browser-automation"
            ],
            "type": "process"
        },
        {
            "server_id": "local-codegen",
            "name": "Local Code Generation",
            "url": "http://localhost:8080",
            "port": 8080,
            "start_command": "docker run -d -p 8080:8080 --name mcp-codegen mcp-codegen:latest",
            "capabilities": [
                "code-completion",
                "text-embedding"
            ],
            "type": "docker"
        },
        {
            "server_id": "ai-agent-mcp",
            "name": "AI Agent MCP",
            "url": "http://localhost:8081",
            "port": 8081,
            "start_command": "docker run -d -p 8081:8081 --name mcp-ai-agent mcp-ai-agent:latest",
            "capabilities": [
                "agent-tools",
                "code-execution"
            ],
            "type": "docker"
        },
        {
            "server_id": "data-processing-mcp",
            "name": "Data Processing MCP",
            "url": "http://localhost:8082",
            "port": 8082,
            "start_command": "python3 data_processing_server.py --port 8082",
            "capabilities": [
                "data-processing",
                "data-analysis"
            ],
            "type": "process"
        },
        {
            "server_id": "knowledge-base-mcp",
            "name": "Knowledge Base MCP",
            "url": "http://localhost:8083",
            "port": 8083,
            "start_command": "python3 knowledge_base_server.py --port 8083",
            "capabilities": [
                "knowledge-retrieval",
                "document-search"
            ],
            "type": "process"
        },
        {
            "server_id": "communication-mcp",
            "name": "Communication MCP",
            "url": "http://localhost:8084",
            "port": 8084,
            "start_command": "python3 communication_server.py --port 8084",
            "capabilities": [
                "email-sending",
                "sms-sending",
                "voice-calling"
            ],
            "type": "process"
        },
        {
            "server_id": "workflow-mcp",
            "name": "Workflow MCP",
            "url": "http://localhost:8087",
            "port": 8087,
            "start_command": "python3 workflow_server.py --port 8087",
            "capabilities": [
                "workflow-automation",
                "task-scheduling"
            ],
            "type": "process"
        },
        {
            "server_id": "security-mcp",
            "name": "Security MCP",
            "url": "http://localhost:8088",
            "port": 8088,
            "start_command": "python3 security_server.py --port 8088",
            "capabilities": [
                "credential-management",
                "access-control"
            ],
            "type": "process"
        },
        {
            "server_id": "integration-mcp",
            "name": "Integration MCP",
            "url": "http://localhost:8089",
            "port": 8089,
            "start_command": "python3 integration_server.py --port 8089",
            "capabilities": [
                "api-integration",
                "service-connection"
            ],
            "type": "process"
        }
    ],
    "registered_servers": [
        "local-models-mcp",
        "web-eval-agent",
        "local-codegen",
        "ai-agent-mcp",
        "data-processing-mcp",
        "knowledge-base-mcp",
        "communication-mcp",
        "workflow-mcp",
        "security-mcp",
        "integration-mcp"
    ]
}
EOL
fi

# Start the main MCP server activator
echo "$(date) - Starting main MCP server activator" >> "$LOG_FILE"
python3 start_mcp_servers.py >> "$LOG_FILE" 2>&1 &

# Give it a moment to initialize
sleep 5

# Check if servers are running, start any that aren't
echo "$(date) - Checking and starting individual servers" >> "$LOG_FILE"

# Parse mcp_config.json and start each server
python3 -c '
import json
import os
import subprocess
import time

with open("mcp_config.json", "r") as f:
    config = json.load(f)

for server in config.get("servers", []):
    server_id = server.get("server_id")
    port = server.get("port")
    start_command = server.get("start_command")

    if not server_id or not port or not start_command:
        continue

    # Check if server is already running
    try:
        import socket
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        result = sock.connect_ex(("localhost", port))
        sock.close()

        if result == 0:
            print(f"Server {server_id} already running on port {port}")
            continue
    except:
        pass

    # Start the server
    print(f"Starting server: {server_id} on port {port}")

    try:
        if server.get("type") == "docker":
            # For docker servers, use docker command
            subprocess.Popen(start_command.split(), stdout=subprocess.PIPE, stderr=subprocess.PIPE)
        else:
            # For process servers, use python or other command
            subprocess.Popen(start_command.split(), stdout=subprocess.PIPE, stderr=subprocess.PIPE)

        # Give it a moment to start
        time.sleep(2)

        # Check if it started
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        result = sock.connect_ex(("localhost", port))
        sock.close()

        if result == 0:
            print(f"Server {server_id} started successfully on port {port}")
        else:
            print(f"Failed to start server {server_id} on port {port}")
    except Exception as e:
        print(f"Error starting server {server_id}: {str(e)}")
' >> "$LOG_FILE" 2>&1

echo "$(date) - MCP servers startup complete" >> "$LOG_FILE"

# Keep the script running to ensure servers stay alive
while true; do
    sleep 60

    # Check if main MCP server activator is running, restart if needed
    if ! pgrep -f "python3 start_mcp_servers.py" > /dev/null; then
        echo "$(date) - Main MCP server activator not running, restarting" >> "$LOG_FILE"
        python3 start_mcp_servers.py >> "$LOG_FILE" 2>&1 &
    fi
done
