# Python logging configuration for Agent System

version: 1
disable_existing_loggers: false

formatters:
  json:
    class: pythonjsonlogger.jsonlogger.JsonFormatter
    format: '%(timestamp)s %(level)s %(name)s %(message)s %(pathname)s %(lineno)d %(funcName)s %(thread)d %(threadName)s %(process)d %(processName)s'
    rename_fields:
      levelname: level
      asctime: timestamp
    json_ensure_ascii: false
    json_indent: null
    timestamp: true

  console:
    format: '%(asctime)s [%(levelname)s] %(name)s: %(message)s'
    datefmt: '%Y-%m-%d %H:%M:%S'

filters:
  correlation_id:
    (): utils.logging_setup.CorrelationIdFilter

  trace_context:
    (): utils.logging_setup.TraceContextFilter

handlers:
  # JSON file handler for main system logs
  system_json:
    class: logging.handlers.RotatingFileHandler
    level: INFO
    formatter: json
    filename: /var/log/agent_system/system.log
    maxBytes: 104857600  # 100MB
    backupCount: 20
    encoding: utf8
    filters: [correlation_id, trace_context]

  # JSON file handler for insurance agent logs
  insurance_json:
    class: logging.handlers.RotatingFileHandler
    level: INFO
    formatter: json
    filename: /var/log/insurance/agents.log
    maxBytes: 104857600  # 100MB
    backupCount: 20
    encoding: utf8
    filters: [correlation_id, trace_context]

  # JSON file handler for trading agent logs
  trading_json:
    class: logging.handlers.RotatingFileHandler
    level: INFO
    formatter: json
    filename: /var/log/trading/agents.log
    maxBytes: 104857600  # 100MB
    backupCount: 20
    encoding: utf8
    filters: [correlation_id, trace_context]

  # Console handler for development
  console:
    class: logging.StreamHandler
    level: DEBUG
    formatter: console
    stream: ext://sys.stdout
    filters: [correlation_id, trace_context]

  # Error logs handler
  error_file:
    class: logging.handlers.RotatingFileHandler
    level: ERROR
    formatter: json
    filename: /var/log/agent_system/error.log
    maxBytes: 104857600  # 100MB
    backupCount: 20
    encoding: utf8
    filters: [correlation_id, trace_context]

loggers:
  # Main system logger
  agent_system:
    level: INFO
    handlers: [system_json, error_file]
    propagate: false

  # Insurance agents logger
  insurance_agents:
    level: INFO
    handlers: [insurance_json, error_file]
    propagate: false

  # Trading agents logger
  trading_agents:
    level: INFO
    handlers: [trading_json, error_file]
    propagate: false

  # Database operations logger
  database:
    level: INFO
    handlers: [system_json, error_file]
    propagate: false

  # Cache operations logger
  cache:
    level: INFO
    handlers: [system_json, error_file]
    propagate: false

  # Security logger
  security:
    level: INFO
    handlers: [system_json, error_file]
    propagate: false

  # Plugin system logger
  plugins:
    level: INFO
    handlers: [system_json, error_file]
    propagate: false

  # Workflow engine logger
  workflows:
    level: INFO
    handlers: [system_json, error_file]
    propagate: false

  # Health check logger
  health_check:
    level: INFO
    handlers: [system_json, error_file]
    propagate: false

  # Test framework logger
  test_framework:
    level: DEBUG
    handlers: [console, system_json]
    propagate: false

root:
  level: WARNING
  handlers: [console, error_file]

# Advanced settings
incremental: false
root_level_name: NOTSET

# Custom attributes to be added to all log records
extra_attributes:
  environment: ${ENVIRONMENT:-development}
  service: agent-system
  version: ${VERSION:-unknown}

# Log record enrichment
callbacks:
  enrich_record:
    (): utils.logging_setup.enrich_log_record