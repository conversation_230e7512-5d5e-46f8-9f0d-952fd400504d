auth_enabled: false

server:
  http_listen_port: 3100
  grpc_listen_port: 9096

common:
  path_prefix: /loki
  storage:
    filesystem:
      chunks_directory: /loki/chunks
      rules_directory: /loki/rules
  replication_factor: 1
  ring:
    kvstore:
      store: inmemory

schema_config:
  configs:
    - from: 2023-01-01
      store: boltdb-shipper
      object_store: filesystem
      schema: v11
      index:
        prefix: index_
        period: 24h

compactor:
  working_directory: /loki/compactor
  shared_store: filesystem
  compaction_interval: 10m
  retention_enabled: true
  retention_delete_delay: 2h
  retention_delete_worker_count: 150

limits_config:
  retention_period: 744h  # 31 days
  enforce_metric_name: false
  reject_old_samples: true
  reject_old_samples_max_age: 168h  # 7 days
  ingestion_rate_mb: 4
  ingestion_burst_size_mb: 6
  max_global_streams_per_user: 5000
  max_query_length: 721h  # 30 days + 1 hour for queries across month boundaries
  max_query_parallelism: 32
  max_streams_per_user: 0
  max_line_size: 256000
  max_entries_limit_per_query: 5000
  max_cache_freshness_per_query: 1m

chunk_store_config:
  max_look_back_period: 744h  # 31 days
  chunk_cache_config:
    enable_fifocache: true
    fifocache:
      max_size_bytes: 512MB
      validity: 24h

table_manager:
  retention_deletes_enabled: true
  retention_period: 744h  # 31 days

ruler:
  enable_api: true
  enable_alertmanager_v2: true
  alertmanager_url: http://alertmanager:9093
  storage:
    type: local
    local:
      directory: /loki/rules
  rule_path: /loki/rules-temp
  evaluation_interval: 1m
  poll_interval: 1m
  ring:
    kvstore:
      store: inmemory

analytics:
  reporting_enabled: false

ingester:
  chunk_idle_period: 1h
  chunk_block_size: 262144
  chunk_retain_period: 1m
  max_transfer_retries: 0
  wal:
    enabled: true
    dir: /loki/wal
  lifecycler:
    ring:
      kvstore:
        store: inmemory
      replication_factor: 1

querier:
  max_concurrent: 20
  engine:
    timeout: 3m
    max_look_back_period: 744h  # 31 days

query_range:
  align_queries_with_step: true
  max_retries: 5
  cache_results: true
  results_cache:
    cache:
      enable_fifocache: true
      fifocache:
        max_size_bytes: 512MB
        validity: 24h

frontend:
  compress_responses: true
  log_queries_longer_than: 10s
  downstream_url: http://querier:9095

frontend_worker:
  frontend_address: frontend:9095
  grpc_client_config:
    max_send_msg_size: 100MB
    max_recv_msg_size: 100MB

distributor:
  ring:
    kvstore:
      store: inmemory

memberlist:
  join_members:
    - loki:7946
  abort_if_cluster_join_fails: false

storage_config:
  boltdb_shipper:
    active_index_directory: /loki/index
    cache_location: /loki/cache
    cache_ttl: 24h
    shared_store: filesystem