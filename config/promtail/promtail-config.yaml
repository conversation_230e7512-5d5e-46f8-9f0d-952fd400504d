server:
  http_listen_port: 9080
  grpc_listen_port: 0

positions:
  filename: /tmp/positions.yaml

clients:
  - url: http://loki:3100/loki/api/v1/push

scrape_configs:
  # Agent System Logs
  - job_name: agent_system
    static_configs:
      - targets:
          - localhost
        labels:
          job: agent_system
          __path__: /var/log/agent_system/*.log
    pipeline_stages:
      - json:
          expressions:
            timestamp: timestamp
            level: level
            message: message
            logger: logger
            trace_id: trace_id
            span_id: span_id
      - timestamp:
          source: timestamp
          format: RFC3339Nano
      - labels:
          level:
          logger:
          trace_id:
          span_id:

  # Insurance Agent Logs
  - job_name: insurance_agents
    static_configs:
      - targets:
          - localhost
        labels:
          job: insurance
          __path__: /var/log/insurance/*.log
    pipeline_stages:
      - json:
          expressions:
            timestamp: timestamp
            level: level
            message: message
            agent: agent
            product: product
      - timestamp:
          source: timestamp
          format: RFC3339Nano
      - labels:
          level:
          agent:
          product:

  # Trading Agent Logs
  - job_name: trading_agents
    static_configs:
      - targets:
          - localhost
        labels:
          job: trading
          __path__: /var/log/trading/*.log
    pipeline_stages:
      - json:
          expressions:
            timestamp: timestamp
            level: level
            message: message
            agent: agent
            symbol: symbol
            strategy: strategy
      - timestamp:
          source: timestamp
          format: RFC3339Nano
      - labels:
          level:
          agent:
          symbol:
          strategy:

  # System Components
  - job_name: system_components
    static_configs:
      - targets:
          - localhost
        labels:
          job: system
          __path__: /var/log/system/*.log
    pipeline_stages:
      - json:
          expressions:
            timestamp: timestamp
            level: level
            message: message
            component: component
      - timestamp:
          source: timestamp
          format: RFC3339Nano
      - labels:
          level:
          component:

  # PostgreSQL Logs
  - job_name: postgresql
    static_configs:
      - targets:
          - localhost
        labels:
          job: postgresql
          __path__: /var/log/postgresql/*.log
    pipeline_stages:
      - regex:
          expression: '(?P<timestamp>\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}.\d+) \[(?P<pid>\d+)\] (?P<level>\w+): (?P<message>.*)'
      - timestamp:
          source: timestamp
          format: '2006-01-02 15:04:05.000'
      - labels:
          level:
          pid:

  # Redis Logs
  - job_name: redis
    static_configs:
      - targets:
          - localhost
        labels:
          job: redis
          __path__: /var/log/redis/*.log
    pipeline_stages:
      - regex:
          expression: '(?P<pid>\d+):(?P<role>\w+) (?P<timestamp>\d{2} \w{3} \d{2}:\d{2}:\d{2}) (?P<message>.*)'
      - timestamp:
          source: timestamp
          format: '02 Jan 15:04:05'
      - labels:
          role:
          pid:

  # RabbitMQ Logs
  - job_name: rabbitmq
    static_configs:
      - targets:
          - localhost
        labels:
          job: rabbitmq
          __path__: /var/log/rabbitmq/*.log
    pipeline_stages:
      - regex:
          expression: '=(?P<level>\w+) REPORT==== (?P<timestamp>\d{2}-\w{3}-\d{4}::\d{2}:\d{2}:\d{2}) === (?P<message>.*)'
      - timestamp:
          source: timestamp
          format: '02-Jan-2006::15:04:05'
      - labels:
          level:

limit_config:
  readline_rate_limit: 10M
  readline_rate_limit_burst: 100M

tracing:
  enabled: true
  endpoint: tempo:4317