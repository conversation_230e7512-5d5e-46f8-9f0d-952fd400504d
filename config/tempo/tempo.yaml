# Tempo configuration for distributed tracing

server:
  http_listen_port: 3200
  grpc_listen_port: 9095

distributor:
  receivers:
    jaeger:
      protocols:
        thrift_http:
          endpoint: "0.0.0.0:14268"
        grpc:
          endpoint: "0.0.0.0:14250"
    otlp:
      protocols:
        http:
          endpoint: "0.0.0.0:4318"
        grpc:
          endpoint: "0.0.0.0:4317"
    zipkin:
      endpoint: "0.0.0.0:9411"

ingester:
  max_block_duration: 5m
  trace_idle_period: 10s
  flush_check_period: 1s
  complete_block_timeout: 30s

compactor:
  compaction:
    block_retention: 168h  # 7 days
    compacted_block_retention: 336h  # 14 days
    max_compaction_objects: 1000000
    max_block_bytes: 100_000_000  # 100MB
    block_interval: 2h
    compaction_window: 1h
    max_time_per_tenant: 15m
    compaction_cycle: 30s

storage:
  trace:
    backend: local  # Can be: local, gcs, s3
    wal:
      path: /tmp/tempo/wal
    local:
      path: /tmp/tempo/blocks
    pool:
      max_workers: 100
      queue_depth: 10000

memberlist:
  abort_if_cluster_join_fails: false
  bind_port: 7946
  join_members:
    - tempo:7946

querier:
  frontend_worker:
    frontend_address: "tempo:9095"
    grpc_client_config:
      max_send_msg_size: 41943040  # 40MB
      max_recv_msg_size: 41943040  # 40MB
  max_concurrent_queries: 20

metrics_generator:
  registry:
    external_labels:
      source: tempo
      cluster: agent-system
  storage:
    path: /tmp/tempo/generator/wal
  processors:
    - service-graphs
    - span-metrics

search_enabled: true

usage_report:
  reporting_enabled: false

overrides:
  defaults:
    ingestion_rate_limit: 100_000
    ingestion_burst_size: 150_000
    max_traces_per_user: 1_000_000
    max_global_traces_per_user: 1_000_000
    max_bytes_per_trace: 50_000  # 50KB
    max_search_bytes_per_trace: 10_000  # 10KB
    metrics_generator:
      processors:
        - service-graphs
        - span-metrics

multitenancy_enabled: false

query_frontend:
  search:
    max_duration: 168h  # 7 days
  trace_by_id:
    query_timeout: 30s

traceql:
  enabled: true
  backend: local
  cache:
    enabled: true
    max_mib: 512

blocklist:
  block_rate: 10_000
  max_block_bytes: 100_000_000  # 100MB
  target_block_duration: 2h
  retention_period: 336h  # 14 days
  worker_count: 10

lifecycle:
  ring:
    kvstore:
      store: memberlist
      prefix: collectors/
    heartbeat_period: 5s
    heartbeat_timeout: 15s

http_api_prefix: ""

log_level: info
log_format: logfmt