global:
  scrape_interval: 15s
  evaluation_interval: 15s
  scrape_timeout: 10s

  external_labels:
    monitor: 'agent-system'

# Alerting configuration
alerting:
  alertmanagers:
    - static_configs:
        - targets:
          # - alertmanager:9093

# Rule files to load
rule_files:
  - "rules/*.yml"

scrape_configs:
  # Agent System metrics
  - job_name: 'agent-system'
    static_configs:
      - targets: ['localhost:9090']
    metrics_path: '/metrics'
    scheme: 'http'
    relabel_configs:
      - source_labels: [__address__]
        target_label: instance
        regex: '([^:]+)(?::\\d+)?'
        replacement: '${1}'

  # Individual Agent metrics
  - job_name: 'agents'
    file_sd_configs:
      - files:
        - 'targets/agents/*.yml'
        refresh_interval: 5m
    metrics_path: '/metrics'
    scheme: 'http'
    relabel_configs:
      - source_labels: [__meta_agent_type]
        target_label: agent_type
      - source_labels: [__meta_agent_name]
        target_label: agent_name

  # Database metrics
  - job_name: 'postgresql'
    static_configs:
      - targets: ['localhost:9187']
    metrics_path: '/metrics'
    scheme: 'http'

  # Redis metrics
  - job_name: 'redis'
    static_configs:
      - targets: ['localhost:9121']
    metrics_path: '/metrics'
    scheme: 'http'

  # Message broker metrics
  - job_name: 'rabbitmq'
    static_configs:
      - targets: ['localhost:15692']
    metrics_path: '/metrics'
    scheme: 'http'

  # Node/system metrics
  - job_name: 'node'
    static_configs:
      - targets: ['localhost:9100']
    metrics_path: '/metrics'
    scheme: 'http'

# Remote write configuration (optional)
# remote_write:
#   - url: "http://remote-storage:9201/write"
#     remote_timeout: 30s
#     queue_config:
#       capacity: 10000
#       max_shards: 200
#       max_samples_per_send: 500
#       batch_send_deadline: 5s
#       min_backoff: 30ms
#       max_backoff: 100ms

# Remote read configuration (optional)
# remote_read:
#   - url: "http://remote-storage:9201/read"
#     read_recent: true
#     remote_timeout: 30s