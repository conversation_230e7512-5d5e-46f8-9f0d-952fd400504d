{"annotations": {"list": [{"builtIn": 1, "datasource": "-- <PERSON><PERSON> --", "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "type": "dashboard"}]}, "editable": true, "gnetId": null, "graphTooltip": 0, "id": 1, "links": [], "panels": [{"title": "System Overview", "type": "row", "collapsed": false, "panels": [{"title": "Active Agents", "type": "stat", "datasource": "Prometheus", "targets": [{"expr": "count(agent_status{status='running'})", "instant": true, "refId": "A"}], "gridPos": {"h": 4, "w": 6, "x": 0, "y": 0}}, {"title": "System Health", "type": "gauge", "datasource": "Prometheus", "targets": [{"expr": "system_health_score", "instant": true, "refId": "A"}], "gridPos": {"h": 4, "w": 6, "x": 6, "y": 0}, "options": {"min": 0, "max": 100, "thresholds": [{"color": "red", "value": 0}, {"color": "yellow", "value": 60}, {"color": "green", "value": 80}]}}]}, {"title": "Agent Metrics", "type": "row", "collapsed": false, "panels": [{"title": "Agent Operations Rate", "type": "graph", "datasource": "Prometheus", "targets": [{"expr": "rate(agent_operations_total[5m])", "legendFormat": "{{agent_name}}", "refId": "A"}], "gridPos": {"h": 8, "w": 12, "x": 0, "y": 4}}, {"title": "Agent <PERSON><PERSON><PERSON> Rate", "type": "graph", "datasource": "Prometheus", "targets": [{"expr": "rate(agent_errors_total[5m])", "legendFormat": "{{agent_name}}", "refId": "A"}], "gridPos": {"h": 8, "w": 12, "x": 12, "y": 4}}]}, {"title": "Resource Usage", "type": "row", "collapsed": false, "panels": [{"title": "CPU Usage", "type": "graph", "datasource": "Prometheus", "targets": [{"expr": "rate(process_cpu_seconds_total[1m])", "legendFormat": "{{instance}}", "refId": "A"}], "gridPos": {"h": 8, "w": 8, "x": 0, "y": 12}}, {"title": "Memory Usage", "type": "graph", "datasource": "Prometheus", "targets": [{"expr": "process_resident_memory_bytes", "legendFormat": "{{instance}}", "refId": "A"}], "gridPos": {"h": 8, "w": 8, "x": 8, "y": 12}}, {"title": "Goroutines", "type": "graph", "datasource": "Prometheus", "targets": [{"expr": "go_goroutines", "legendFormat": "{{instance}}", "refId": "A"}], "gridPos": {"h": 8, "w": 8, "x": 16, "y": 12}}]}, {"title": "Database & Cache", "type": "row", "collapsed": false, "panels": [{"title": "Database Connections", "type": "graph", "datasource": "Prometheus", "targets": [{"expr": "postgresql_stat_activity_count", "legendFormat": "active connections", "refId": "A"}], "gridPos": {"h": 8, "w": 12, "x": 0, "y": 20}}, {"title": "<PERSON><PERSON> Hit Rate", "type": "graph", "datasource": "Prometheus", "targets": [{"expr": "rate(cache_hits_total[5m]) / (rate(cache_hits_total[5m]) + rate(cache_misses_total[5m]))", "legendFormat": "hit rate", "refId": "A"}], "gridPos": {"h": 8, "w": 12, "x": 12, "y": 20}}]}], "refresh": "10s", "schemaVersion": 27, "style": "dark", "tags": ["agent-system"], "templating": {"list": [{"name": "agent", "type": "query", "datasource": "Prometheus", "query": "label_values(agent_name)"}]}, "time": {"from": "now-6h", "to": "now"}, "timepicker": {"refresh_intervals": ["5s", "10s", "30s", "1m", "5m", "15m", "30m", "1h", "2h", "1d"]}, "timezone": "browser", "title": "Agent System Dashboard", "uid": "agent_system", "version": 1}