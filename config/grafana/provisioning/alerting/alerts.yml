# Alert rules for Agent System monitoring

groups:
  - name: agent_system_alerts
    rules:
      # System Health Alerts
      - alert: SystemHealthCritical
        expr: system_health_score < 60
        for: 5m
        labels:
          severity: critical
        annotations:
          summary: System health is critical
          description: System health score has been below 60 for 5 minutes

      - alert: HighSystemLoad
        expr: rate(process_cpu_seconds_total[5m]) > 0.8
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: High system load detected
          description: System CPU usage has been above 80% for 5 minutes

      # Agent Alerts
      - alert: AgentError
        expr: rate(agent_errors_total[5m]) > 0.1
        for: 2m
        labels:
          severity: warning
        annotations:
          summary: High agent error rate
          description: "{{ $labels.agent_name }} is experiencing high error rates"

      - alert: AgentDown
        expr: up{job="agents"} == 0
        for: 1m
        labels:
          severity: critical
        annotations:
          summary: Agent is down
          description: "{{ $labels.agent_name }} is not reporting metrics"

      # Resource Alerts
      - alert: HighMemoryUsage
        expr: process_resident_memory_bytes > 1e9
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: High memory usage
          description: Process is using more than 1GB of memory

      - alert: DiskSpaceLow
        expr: node_filesystem_avail_bytes{mountpoint="/"} / node_filesystem_size_bytes{mountpoint="/"} * 100 < 10
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: Low disk space
          description: Less than 10% disk space remaining

      # Database Alerts
      - alert: HighDatabaseConnections
        expr: postgresql_stat_activity_count > 100
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: High database connections
          description: Database has over 100 active connections

      - alert: DatabaseErrors
        expr: rate(postgresql_errors_total[5m]) > 0
        for: 2m
        labels:
          severity: critical
        annotations:
          summary: Database errors detected
          description: Database is reporting errors

      # Cache Alerts
      - alert: LowCacheHitRate
        expr: rate(cache_hits_total[5m]) / (rate(cache_hits_total[5m]) + rate(cache_misses_total[5m])) < 0.5
        for: 10m
        labels:
          severity: warning
        annotations:
          summary: Low cache hit rate
          description: Cache hit rate is below 50%

      - alert: CacheServiceDown
        expr: up{job="redis"} == 0
        for: 1m
        labels:
          severity: critical
        annotations:
          summary: Cache service is down
          description: Redis cache service is not responding

      # Message Queue Alerts
      - alert: MessageQueueLag
        expr: rabbitmq_queue_messages_ready > 1000
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: High message queue lag
          description: Message queue has over 1000 pending messages

      - alert: MessageQueueDown
        expr: up{job="rabbitmq"} == 0
        for: 1m
        labels:
          severity: critical
        annotations:
          summary: Message queue is down
          description: RabbitMQ service is not responding

      # API Alerts
      - alert: HighAPILatency
        expr: rate(http_request_duration_seconds_sum[5m]) / rate(http_request_duration_seconds_count[5m]) > 1
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: High API latency
          description: API requests are taking more than 1 second on average

      - alert: HighErrorRate
        expr: rate(http_requests_total{status=~"5.."}[5m]) / rate(http_requests_total[5m]) > 0.05
        for: 2m
        labels:
          severity: warning
        annotations:
          summary: High HTTP error rate
          description: More than 5% of requests are resulting in errors

# Alert notification channels are configured in Grafana UI
# or through provisioning/notifiers/notifiers.yml