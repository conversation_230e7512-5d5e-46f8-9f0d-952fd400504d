# Grafana datasource configuration

apiVersion: 1

deleteDatasources:
  - name: Prometheus
    orgId: 1
  - name: Tempo
    orgId: 1
  - name: Loki
    orgId: 1

datasources:
  # Prometheus datasource for metrics
  - name: Prometheus
    type: prometheus
    access: proxy
    orgId: 1
    uid: prometheus
    url: http://localhost:9090
    jsonData:
      timeInterval: "15s"
      queryTimeout: "30s"
      httpMethod: POST
      manageAlerts: true
      alertmanagerUid: alertmanager
      exemplarTraceIdDestinations:
        - name: TraceID
          datasourceUid: tempo
    editable: true
    isDefault: true

  # Tempo datasource for distributed tracing
  - name: Tempo
    type: tempo
    access: proxy
    orgId: 1
    uid: tempo
    url: http://tempo:3200
    jsonData:
      httpMethod: GET
      serviceMap:
        datasourceUid: prometheus
      search:
        hide: false
      nodeGraph:
        enabled: true
      lokiSearch:
        datasourceUid: loki
    editable: true

  # Loki datasource for logs
  - name: Loki
    type: loki
    access: proxy
    orgId: 1
    uid: loki
    url: http://loki:3100
    jsonData:
      maxLines: 1000
      derivedFields:
        - name: TraceID
          matcherRegex: "trace_id=(\\w+)"
          url: "${__value.raw}"
          datasourceUid: tempo
    editable: true

  # PostgreSQL datasource for direct database queries
  - name: PostgreSQL
    type: postgres
    access: proxy
    orgId: 1
    uid: postgres
    url: localhost:5432
    user: ${POSTGRES_USER}
    secureJsonData:
      password: ${POSTGRES_PASSWORD}
    jsonData:
      database: agent_system
      sslmode: disable
      maxOpenConns: 10
      maxIdleConns: 5
      connMaxLifetime: 14400
      postgresVersion: 1200
      timescaledb: false
    editable: false

  # Redis datasource for cache monitoring
  - name: Redis
    type: redis-datasource
    access: proxy
    orgId: 1
    uid: redis
    url: redis://localhost:6379
    jsonData:
      client: standalone
      poolSize: 5
      timeout: 10
      pingInterval: 0
      pipelineWindow: 0
    editable: false

  # Elasticsearch datasource for full-text search (optional)
  # - name: Elasticsearch
  #   type: elasticsearch
  #   access: proxy
  #   orgId: 1
  #   uid: elasticsearch
  #   url: http://elasticsearch:9200
  #   jsonData:
  #     index: "agent-system-*"
  #     timeField: "@timestamp"
  #     esVersion: "7.10.0"
  #     maxConcurrentShardRequests: 5
  #     logMessageField: message
  #     logLevelField: level
  #   editable: false