# Grafana configuration for Agent System

[paths]
data = /var/lib/grafana/data
logs = /var/lib/grafana/logs
plugins = /var/lib/grafana/plugins
provisioning = /etc/grafana/provisioning

[server]
protocol = http
http_addr = 0.0.0.0
http_port = 3000
domain = localhost
root_url = %(protocol)s://%(domain)s:%(http_port)s/
serve_from_sub_path = false

[security]
# Change these in production
admin_user = admin
admin_password = admin
secret_key = SW2YcwTIb9zpOOhoPsMm

# Allow embedding in iframes for agent dashboard
allow_embedding = true

[auth]
disable_login_form = false
oauth_auto_login = false

[auth.anonymous]
enabled = true
org_name = Main Org.
org_role = Viewer

[analytics]
reporting_enabled = false
check_for_updates = true

[dashboards]
versions_to_keep = 20
min_refresh_interval = 5s

[database]
type = sqlite3
path = grafana.db

[session]
provider = file
provider_config = sessions

[log]
mode = console file
level = info
filters = 

[metrics]
enabled = true
interval_seconds = 10

[snapshots]
external_enabled = false

[alerting]
enabled = true
execute_alerts = true
evaluation_timeout_seconds = 30
notification_timeout_seconds = 30
max_attempts = 3

[unified_alerting]
enabled = true

[panels]
disable_sanitize_html = false

[smtp]
enabled = false
# Configure for email alerts
# host = smtp.gmail.com:587
# user = <EMAIL>
# password = your-app-specific-password
# from_address = <EMAIL>
# from_name = Grafana Alert

[prometheus]
# Server configuration for Prometheus datasource
enabled = true
api_url = http://localhost:9090

[date_formats]
default_timezone = UTC

[users]
allow_sign_up = false
allow_org_create = false
auto_assign_org = true
auto_assign_org_role = Viewer

[auth.basic]
enabled = true

[auth.proxy]
enabled = false

[auth.jwt]
enabled = false

[feature_toggles]
enable = tempoSearch tempoBackendSearch tempoServiceGraph

[tracing]
# Tempo configuration for distributed tracing
enabled = true
address = tempo:4317
provider = tempo

[plugin.grafana-piechart-panel]
path = /var/lib/grafana/plugins/grafana-piechart-panel

[plugin.prometheus]
path = /var/lib/grafana/plugins/prometheus

[plugin.tempo]
path = /var/lib/grafana/plugins/tempo

[plugin.loki]
path = /var/lib/grafana/plugins/loki

[unified_alerting.screenshots]
capture = true