"""
Send <PERSON> Text Message - Simplified Version

This script sends a text message to <PERSON> using Twilio.
It's a simplified version to verify basic functionality.
"""

import os
from twilio.rest import Client

# <PERSON> contact information
PAUL_EDWARDS = {
    "first_name": "<PERSON>",
    "last_name": "<PERSON>",
    "email": "<EMAIL>",
    "phone": "+***********",  # Primary contact number
    "alt_phone": "+***********"  # Alternative contact number
}

# ======================== CONFIGURATION ========================
# Twilio credentials
TWILIO_ACCOUNT_SID = "AC187c871afa232bbbc978caf33f3e25d9"
TWILIO_AUTH_TOKEN = "CqpVewwter1BEMdFIFHrN2XmUyt22wBP"

# Agent information
AGENT_INFO = {
    "name": "<PERSON>",
    "agency": "Flo Faction Insurance",
    "phone": "Flo Faction AI Assistant",
    "email": "<EMAIL>",
    "website": "https://www.flofaction.com",
}

# ======================== TWILIO FUNCTIONS ========================
def initialize_twilio_client():
    """Initialize Twilio client"""
    return Client(TWILIO_ACCOUNT_SID, TWILIO_AUTH_TOKEN)

def send_text_message(to_number, message):
    """Send a text message using Twilio"""
    client = initialize_twilio_client()
    
    # Format phone number for Twilio
    if not to_number.startswith("+"):
        to_number = "+1" + to_number
    
    try:
        # For testing, we'll use Twilio's default messaging service
        # This will select an appropriate sender number automatically
        message = client.messages.create(
            body=message,
            messaging_service_sid=None,  # Let Twilio choose the best sender
            to=to_number
        )
        
        print(f"Text message sent with SID: {message.sid}")
        return message.sid
    except Exception as e:
        print(f"Error sending text message: {str(e)}")
        return None

# ======================== MAIN FUNCTION ========================
def send_text_to_paul():
    """Send a text message to Paul Edwards"""
    print("=" * 80)
    print("SENDING TEXT MESSAGE TO PAUL EDWARDS")
    print("=" * 80)
    
    # Create custom text message with Flo Faction branding
    text_message = f"""
Hi {PAUL_EDWARDS['first_name']}, this is {AGENT_INFO['name']} with {AGENT_INFO['agency']}. I help people create tax-free retirement income without stock market risk. I've prepared a personalized analysis for you. Would you be interested in seeing this analysis? Visit {AGENT_INFO['website']} for more information. Thanks!
    """
    
    # Send to primary number
    print("\nSENDING TEXT MESSAGE TO PRIMARY NUMBER")
    print("-" * 80)
    primary_sid = send_text_message(PAUL_EDWARDS["phone"], text_message)
    
    # Send to alternative number
    print("\nSENDING TEXT MESSAGE TO ALTERNATIVE NUMBER")
    print("-" * 80)
    alt_sid = send_text_message(PAUL_EDWARDS["alt_phone"], text_message)
    
    # Summary
    print("\n" + "=" * 80)
    print("TEXT MESSAGE SUMMARY")
    print("=" * 80)
    
    print(f"Primary Number Text SID: {primary_sid}")
    print(f"Alternative Number Text SID: {alt_sid}")
    
    print("\nText messages have been sent to Paul Edwards.")
    print("=" * 80)

if __name__ == "__main__":
    print("This script will send a text message to Paul Edwards.")
    print("Make sure you have proper authorization before proceeding.")
    
    proceed = input("Do you want to proceed? (yes/no): ")
    
    if proceed.lower() == "yes":
        send_text_to_paul()
    else:
        print("Operation cancelled.")
