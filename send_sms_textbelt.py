"""
Send SMS to <PERSON> using TextBelt

This script sends an SMS to <PERSON> using the TextBelt API.
TextBelt offers a free tier that allows sending one text message per day.
"""

import requests

# <PERSON> contact information
PAUL_PHONE = "7722089646"  # TextBelt requires phone number without the + sign
PAUL_NAME = "<PERSON>"

def send_sms():
    """Send an SMS to <PERSON> using TextBelt"""
    print("=" * 80)
    print("SENDING SMS TO PAUL EDWARDS USING TEXTBELT")
    print("=" * 80)
    
    # Create message content
    message = f"""
Hi {PAUL_NAME.split()[0]}, this is <PERSON> with Flo Faction Insurance. I help people create tax-free retirement income without stock market risk. I've prepared a personalized analysis for you. Would you be interested in seeing this analysis? Visit https://www.flofaction.com/insurance for more information. Thanks!
    """.strip()
    
    # TextBelt API endpoint
    url = "https://textbelt.com/text"
    
    # Prepare the request data
    data = {
        "phone": PAUL_PHONE,
        "message": message,
        "key": "textbelt"  # Use the free tier key (1 text per day)
    }
    
    try:
        # Make the request
        response = requests.post(url, data=data)
        response_data = response.json()
        
        # Check if the request was successful
        if response_data.get("success"):
            print(f"SMS sent successfully!")
            print(f"Quota remaining: {response_data.get('quotaRemaining')}")
            print(f"Text ID: {response_data.get('textId')}")
            return True
        else:
            print(f"Error sending SMS: {response_data.get('error')}")
            return False
    except Exception as e:
        print(f"Exception sending SMS: {str(e)}")
        return False

if __name__ == "__main__":
    print("This script will send an SMS to Paul Edwards using TextBelt.")
    print(f"Recipient: {PAUL_PHONE}")
    print("Note: TextBelt's free tier allows sending one text message per day.")
    
    proceed = input("Do you want to proceed? (yes/no): ")
    
    if proceed.lower() == "yes":
        send_sms()
    else:
        print("Operation cancelled.")
