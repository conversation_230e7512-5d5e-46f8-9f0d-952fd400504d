#!/usr/bin/env python3
"""
Insurance Quote Form Handler

This module provides a comprehensive form collection system for insurance quotes.
It integrates with the agent system to collect client information required for
submitting insurance applications to carriers.

The form includes all required fields including personal information, health information,
financial details, and plan preferences.
"""

import os
import sys
import json
import random
import logging
import datetime
from typing import Dict, List, Any, Optional

# Configure logging
logging.basicConfig(level=logging.INFO,
                    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class InsuranceQuoteForm:
    """
    Insurance quote form handler for collecting client information
    required for insurance applications and quotes
    """
    
    def __init__(self):
        """Initialize the insurance quote form handler"""
        self.form_data = {}
        self.required_fields = [
            "first_name",
            "last_name",
            "date_of_birth",
            "gender",
            "address_line1",
            "city",
            "state",
            "zip_code",
            "phone_number",
            "email",
            "citizenship_status"
        ]
        
        self.sensitive_fields = [
            "social_security_number",
            "bank_account_number",
            "bank_routing_number"
        ]
        
        self.health_fields = [
            "current_health_conditions",
            "medications",
            "height",
            "weight",
            "tobacco_use",
            "alcohol_use"
        ]
        
        self.insurance_fields = [
            "insurance_type",
            "current_coverage",
            "desired_coverage",
            "budget_monthly",
            "preferred_carriers"
        ]
        
    def collect_basic_information(self, client_data: Dict[str, Any]) -> bool:
        """
        Collect basic client information
        
        Args:
            client_data: Dictionary containing client basic information
            
        Returns:
            True if successful, False otherwise
        """
        # Validate required fields
        missing_fields = []
        for field in self.required_fields:
            if field not in client_data or not client_data[field]:
                missing_fields.append(field)
                
        if missing_fields:
            logger.warning(f"Missing required fields: {', '.join(missing_fields)}")
            return False
            
        # Add data to form
        for field, value in client_data.items():
            self.form_data[field] = value
            
        logger.info("Basic client information collected successfully")
        return True
        
    def collect_sensitive_information(self, sensitive_data: Dict[str, Any], partial: bool = True) -> bool:
        """
        Collect sensitive client information
        
        Args:
            sensitive_data: Dictionary containing sensitive information
            partial: Whether partial information is acceptable
            
        Returns:
            True if successful, False otherwise
        """
        if not partial:
            # Check if all sensitive fields are provided
            missing_fields = []
            for field in self.sensitive_fields:
                if field not in sensitive_data or not sensitive_data[field]:
                    missing_fields.append(field)
                    
            if missing_fields:
                logger.warning(f"Missing sensitive fields: {', '.join(missing_fields)}")
                return False
                
        # Add available data to form
        for field, value in sensitive_data.items():
            if field in self.sensitive_fields:
                self.form_data[field] = value
                
        logger.info("Sensitive client information collected")
        return True
        
    def collect_health_information(self, health_data: Dict[str, Any]) -> bool:
        """
        Collect client health information
        
        Args:
            health_data: Dictionary containing health information
            
        Returns:
            True if successful, False otherwise
        """
        # Add health data to form
        for field, value in health_data.items():
            if field in self.health_fields:
                self.form_data[field] = value
                
        logger.info("Health information collected")
        return True
        
    def collect_insurance_preferences(self, insurance_data: Dict[str, Any]) -> bool:
        """
        Collect client insurance preferences
        
        Args:
            insurance_data: Dictionary containing insurance preferences
            
        Returns:
            True if successful, False otherwise
        """
        # Ensure insurance type is specified
        if "insurance_type" not in insurance_data or not insurance_data["insurance_type"]:
            logger.warning("Insurance type not specified")
            return False
            
        # Add insurance preferences to form
        for field, value in insurance_data.items():
            if field in self.insurance_fields:
                self.form_data[field] = value
                
        logger.info("Insurance preferences collected")
        return True
        
    def validate_form(self, require_sensitive: bool = False) -> Dict[str, Any]:
        """
        Validate the form data and return validation results
        
        Args:
            require_sensitive: Whether sensitive information is required
            
        Returns:
            Dictionary with validation results
        """
        missing_required = []
        missing_sensitive = []
        missing_health = []
        missing_insurance = []
        
        # Check required fields
        for field in self.required_fields:
            if field not in self.form_data or not self.form_data[field]:
                missing_required.append(field)
                
        # Check sensitive fields if required
        if require_sensitive:
            for field in self.sensitive_fields:
                if field not in self.form_data or not self.form_data[field]:
                    missing_sensitive.append(field)
                    
        # Check if insurance type is specified
        if "insurance_type" not in self.form_data or not self.form_data["insurance_type"]:
            missing_insurance.append("insurance_type")
            
        # Prepare validation results
        validation = {
            "is_valid": len(missing_required) == 0 and (not require_sensitive or len(missing_sensitive) == 0),
            "is_complete": (len(missing_required) + len(missing_sensitive) + len(missing_insurance)) == 0,
            "missing_required": missing_required,
            "missing_sensitive": missing_sensitive,
            "missing_insurance": missing_insurance,
            "can_quote": len(missing_required) == 0 and "insurance_type" in self.form_data
        }
        
        if validation["is_valid"]:
            logger.info("Form validation passed")
        else:
            logger.warning("Form validation failed")
            
        return validation
        
    def get_form_data(self) -> Dict[str, Any]:
        """Get the collected form data"""
        return self.form_data
        
    def generate_quote_request(self) -> Dict[str, Any]:
        """
        Generate a quote request for insurance carriers
        
        Returns:
            Dictionary with quote request data
        """
        # Validate form first
        validation = self.validate_form(require_sensitive=False)
        if not validation["can_quote"]:
            logger.error("Cannot generate quote: Form validation failed")
            return {"error": "Form validation failed", "validation": validation}
            
        # Prepare quote request
        insurance_type = self.form_data.get("insurance_type", "").lower()
        
        if "medicare" in insurance_type:
            return self._generate_medicare_quote()
        elif "health" in insurance_type:
            return self._generate_health_quote()
        elif "life" in insurance_type:
            return self._generate_life_quote()
        else:
            return self._generate_general_quote()
            
    def _generate_medicare_quote(self) -> Dict[str, Any]:
        """Generate Medicare quote request"""
        quote_data = {
            "quote_type": "medicare",
            "client_info": {
                "first_name": self.form_data.get("first_name", ""),
                "last_name": self.form_data.get("last_name", ""),
                "date_of_birth": self.form_data.get("date_of_birth", ""),
                "gender": self.form_data.get("gender", ""),
                "zip_code": self.form_data.get("zip_code", ""),
                "phone_number": self.form_data.get("phone_number", ""),
                "email": self.form_data.get("email", "")
            },
            "eligibility": {
                "is_over_65": self._is_over_65(self.form_data.get("date_of_birth", "")),
                "has_medicare_part_a": self.form_data.get("has_medicare_part_a", False),
                "has_medicare_part_b": self.form_data.get("has_medicare_part_b", False),
                "medicare_id": self.form_data.get("medicare_id", "")
            },
            "preferences": {
                "plan_type": self.form_data.get("medicare_plan_type", "Advantage"),
                "prescription_coverage": self.form_data.get("prescription_coverage", True),
                "dental_coverage": self.form_data.get("dental_coverage", False),
                "vision_coverage": self.form_data.get("vision_coverage", False),
                "current_doctors": self.form_data.get("current_doctors", []),
                "current_medications": self.form_data.get("medications", [])
            }
        }
        
        # Add sensitive information if available
        if "social_security_number" in self.form_data:
            quote_data["client_info"]["ssn"] = self.form_data["social_security_number"]
            
        logger.info("Medicare quote request generated")
        return quote_data
        
    def _generate_health_quote(self) -> Dict[str, Any]:
        """Generate health insurance quote request"""
        quote_data = {
            "quote_type": "health",
            "client_info": {
                "first_name": self.form_data.get("first_name", ""),
                "last_name": self.form_data.get("last_name", ""),
                "date_of_birth": self.form_data.get("date_of_birth", ""),
                "gender": self.form_data.get("gender", ""),
                "address": {
                    "street": self.form_data.get("address_line1", ""),
                    "city": self.form_data.get("city", ""),
                    "state": self.form_data.get("state", ""),
                    "zip_code": self.form_data.get("zip_code", "")
                },
                "contact": {
                    "phone": self.form_data.get("phone_number", ""),
                    "email": self.form_data.get("email", "")
                }
            },
            "health_info": {
                "height": self.form_data.get("height", ""),
                "weight": self.form_data.get("weight", ""),
                "tobacco_use": self.form_data.get("tobacco_use", False),
                "conditions": self.form_data.get("current_health_conditions", []),
                "medications": self.form_data.get("medications", [])
            },
            "coverage_needs": {
                "deductible_preference": self.form_data.get("deductible_preference", "medium"),
                "copay_preference": self.form_data.get("copay_preference", "medium"),
                "include_dental": self.form_data.get("dental_coverage", False),
                "include_vision": self.form_data.get("vision_coverage", False),
                "max_monthly_premium": self.form_data.get("budget_monthly", 0)
            }
        }
        
        # Add sensitive information if available
        if "social_security_number" in self.form_data:
            quote_data["client_info"]["ssn"] = self.form_data["social_security_number"]
            
        logger.info("Health insurance quote request generated")
        return quote_data
        
    def _generate_life_quote(self) -> Dict[str, Any]:
        """Generate life insurance quote request"""
        quote_data = {
            "quote_type": "life",
            "client_info": {
                "first_name": self.form_data.get("first_name", ""),
                "last_name": self.form_data.get("last_name", ""),
                "date_of_birth": self.form_data.get("date_of_birth", ""),
                "gender": self.form_data.get("gender", ""),
                "address": {
                    "street": self.form_data.get("address_line1", ""),
                    "city": self.form_data.get("city", ""),
                    "state": self.form_data.get("state", ""),
                    "zip_code": self.form_data.get("zip_code", "")
                },
                "contact": {
                    "phone": self.form_data.get("phone_number", ""),
                    "email": self.form_data.get("email", "")
                }
            },
            "health_info": {
                "height": self.form_data.get("height", ""),
                "weight": self.form_data.get("weight", ""),
                "tobacco_use": self.form_data.get("tobacco_use", False),
                "alcohol_use": self.form_data.get("alcohol_use", ""),
                "conditions": self.form_data.get("current_health_conditions", []),
                "medications": self.form_data.get("medications", []),
                "family_history": self.form_data.get("family_history", {})
            },
            "policy_info": {
                "coverage_amount": self.form_data.get("coverage_amount", 0),
                "term_length": self.form_data.get("term_length", ""),
                "policy_type": self.form_data.get("policy_type", "term"),
                "beneficiaries": self.form_data.get("beneficiaries", []),
                "max_monthly_premium": self.form_data.get("budget_monthly", 0)
            }
        }
        
        # Add sensitive information if available
        if "social_security_number" in self.form_data:
            quote_data["client_info"]["ssn"] = self.form_data["social_security_number"]
            
        logger.info("Life insurance quote request generated")
        return quote_data
        
    def _generate_general_quote(self) -> Dict[str, Any]:
        """Generate general insurance quote request"""
        quote_data = {
            "quote_type": self.form_data.get("insurance_type", "general"),
            "client_info": {
                "first_name": self.form_data.get("first_name", ""),
                "last_name": self.form_data.get("last_name", ""),
                "date_of_birth": self.form_data.get("date_of_birth", ""),
                "gender": self.form_data.get("gender", ""),
                "address": {
                    "street": self.form_data.get("address_line1", ""),
                    "city": self.form_data.get("city", ""),
                    "state": self.form_data.get("state", ""),
                    "zip_code": self.form_data.get("zip_code", "")
                },
                "contact": {
                    "phone": self.form_data.get("phone_number", ""),
                    "email": self.form_data.get("email", "")
                }
            },
            "coverage_needs": {
                "desired_coverage": self.form_data.get("desired_coverage", ""),
                "current_coverage": self.form_data.get("current_coverage", ""),
                "preferred_carriers": self.form_data.get("preferred_carriers", []),
                "max_monthly_premium": self.form_data.get("budget_monthly", 0)
            }
        }
        
        # Add sensitive information if available
        if "social_security_number" in self.form_data:
            quote_data["client_info"]["ssn"] = self.form_data["social_security_number"]
            
        logger.info("General insurance quote request generated")
        return quote_data
        
    def _is_over_65(self, date_of_birth: str) -> bool:
        """Check if client is over 65 based on date of birth"""
        if not date_of_birth:
            return False
            
        try:
            # Parse date of birth (format: YYYY-MM-DD)
            dob = datetime.datetime.strptime(date_of_birth, "%Y-%m-%d").date()
            today = datetime.date.today()
            
            # Calculate age
            age = today.year - dob.year - ((today.month, today.day) < (dob.month, dob.day))
            
            return age >= 65
        except Exception as e:
            logger.error(f"Error calculating age: {e}")
            return False
            
    def save_to_file(self, file_path: str) -> bool:
        """
        Save form data to file
        
        Args:
            file_path: Path to save the form data
            
        Returns:
            True if successful, False otherwise
        """
        try:
            # Create directory if it doesn't exist
            os.makedirs(os.path.dirname(os.path.abspath(file_path)), exist_ok=True)
            
            # Save form data as JSON
            with open(file_path, 'w') as f:
                json.dump(self.form_data, f, indent=2)
                
            logger.info(f"Form data saved to {file_path}")
            return True
        except Exception as e:
            logger.error(f"Error saving form data: {e}")
            return False
            
    def load_from_file(self, file_path: str) -> bool:
        """
        Load form data from file
        
        Args:
            file_path: Path to load the form data from
            
        Returns:
            True if successful, False otherwise
        """
        try:
            # Check if file exists
            if not os.path.exists(file_path):
                logger.error(f"File not found: {file_path}")
                return False
                
            # Load form data from JSON
            with open(file_path, 'r') as f:
                self.form_data = json.load(f)
                
            logger.info(f"Form data loaded from {file_path}")
            return True
        except Exception as e:
            logger.error(f"Error loading form data: {e}")
            return False


class InsuranceQuoteSystem:
    """Insurance quote system for generating and submitting quotes"""
    
    def __init__(self):
        """Initialize the insurance quote system"""
        self.form = InsuranceQuoteForm()
        self.carriers = {
            "medicare": [
                "Aetna",
                "Humana",
                "United Healthcare",
                "Cigna",
                "Blue Cross Blue Shield",
                "Mutual of Omaha"
            ],
            "health": [
                "Aetna",
                "Cigna",
                "Blue Cross Blue Shield",
                "United Healthcare",
                "Kaiser Permanente"
            ],
            "life": [
                "Prudential",
                "Northwestern Mutual",
                "New York Life",
                "MetLife",
                "Mutual of Omaha",
                "USAA"
            ]
        }
        
    def collect_client_information(self, interactive: bool = True) -> Dict[str, Any]:
        """
        Collect client information through interactive prompts or API
        
        Args:
            interactive: Whether to use interactive prompts
            
        Returns:
            Dictionary with collected information
        """
        if interactive:
            return self._collect_interactively()
        else:
            # This would be an API endpoint in a real application
            # For now, return an example client
            return self._get_example_client()
            
    def _collect_interactively(self) -> Dict[str, Any]:
        """Collect client information interactively"""
        print("\n============ INSURANCE QUOTE FORM ============")
        print("Please enter the following information to get a quote.")
        print("Fields marked with * are required.")
        print("Note: Social Security Number and bank information can be provided later,")
        print("but will be required for the final application.\n")
        
        # Basic information
        basic_info = {}
        basic_info["first_name"] = input("* First Name: ")
        basic_info["last_name"] = input("* Last Name: ")
        basic_info["date_of_birth"] = input("* Date of Birth (YYYY-MM-DD): ")
        basic_info["gender"] = input("* Gender (M/F): ")
        basic_info["address_line1"] = input("* Address Line 1: ")
        basic_info["city"] = input("* City: ")
        basic_info["state"] = input("* State (2-letter code): ")
        basic_info["zip_code"] = input("* ZIP Code: ")
        basic_info["phone_number"] = input("* Phone Number: ")
        basic_info["email"] = input("* Email: ")
        basic_info["citizenship_status"] = input("* Citizenship Status (U.S. Citizen, Legal Resident, etc.): ")
        
        self.form.collect_basic_information(basic_info)
        
        # Sensitive information
        print("\nSensitive Information (can be provided later if needed)")
        sensitive_info = {}
        ssn = input("Social Security Number (XXX-XX-XXXX): ")
        if ssn:
            sensitive_info["social_security_number"] = ssn
            
        bank_account = input("Bank Account Number (optional): ")
        if bank_account:
            sensitive_info["bank_account_number"] = bank_account
            
        bank_routing = input("Bank Routing Number (optional): ")
        if bank_routing:
            sensitive_info["bank_routing_number"] = bank_routing
            
        self.form.collect_sensitive_information(sensitive_info, partial=True)
        
        # Health information
        print("\nHealth Information")
        health_info = {}
        health_info["height"] = input("Height (in inches): ")
        health_info["weight"] = input("Weight (in pounds): ")
        tobacco = input("Tobacco Use (Yes/No): ").lower()
        health_info["tobacco_use"] = tobacco.startswith("y")
        
        health_conditions = input("Current Health Conditions (comma separated): ")
        if health_conditions:
            health_info["current_health_conditions"] = [c.strip() for c in health_conditions.split(",")]
            
        medications = input("Current Medications (comma separated): ")
        if medications:
            health_info["medications"] = [m.strip() for m in medications.split(",")]
            
        self.form.collect_health_information(health_info)
        
        # Insurance preferences
        print("\nInsurance Preferences")
        insurance_info = {}
        insurance_info["insurance_type"] = input("* Insurance Type (Medicare, Health, Life, etc.): ")
        insurance_info["current_coverage"] = input("Current Coverage (if any): ")
        insurance_info["desired_coverage"] = input("Desired Coverage: ")
        insurance_info["budget_monthly"] = float(input("Monthly Budget ($): ") or "0")
        
        carriers = input("Preferred Carriers (comma separated): ")
        if carriers:
            insurance_info["preferred_carriers"] = [c.strip() for c in carriers.split(",")]
            
        self.form.collect_insurance_preferences(insurance_info)
        
        # Return collected data
        return self.form.get_form_data()
        
    def _get_example_client(self) -> Dict[str, Any]:
        """Get example client data"""
        # Basic information
        basic_info = {
            "first_name": "John",
            "last_name": "Doe",
            "date_of_birth": "1956-05-15",
            "gender": "M",
            "address_line1": "123 Main St",
            "city": "Springfield",
            "state": "IL",
            "zip_code": "62701",
            "phone_number": "************",
            "email": "<EMAIL>",
            "citizenship_status": "U.S. Citizen"
        }
        
        self.form.collect_basic_information(basic_info)
        
        # Sensitive information
        sensitive_info = {
            "social_security_number": "XXX-XX-1234"
        }
        
        self.form.collect_sensitive_information(sensitive_info, partial=True)
        
        # Health information
        health_info = {
            "height": "70",
            "weight": "180",
            "tobacco_use": False,
            "current_health_conditions": ["Hypertension", "High Cholesterol"],
            "medications": ["Lisinopril", "Lipitor"]
        }
        
        self.form.collect_health_information(health_info)
        
        # Insurance preferences
        insurance_info = {
            "insurance_type": "Medicare",
            "current_coverage": "Employer Insurance",
            "desired_coverage": "Medicare Advantage",
            "budget_monthly": 150.0,
            "preferred_carriers": ["Aetna", "Humana"]
        }
        
        self.form.collect_insurance_preferences(insurance_info)
        
        # Additional Medicare-specific information
        medicare_info = {
            "has_medicare_part_a": True,
            "has_medicare_part_b": True,
            "medicare_id": "1234-567-A",
            "medicare_plan_type": "Advantage",
            "prescription_coverage": True,
            "dental_coverage": True,
            "vision_coverage": True
        }
        
        for key, value in medicare_info.items():
            self.form.form_data[key] = value
            
        return self.form.get_form_data()
        
    def generate_quote(self) -> Dict[str, Any]:
        """
        Generate insurance quote
        
        Returns:
            Dictionary with quote data
        """
        # Validate form first
        validation = self.form.validate_form()
        if not validation["can_quote"]:
            print("Cannot generate quote: Missing required information")
            for field in validation["missing_required"]:
                print(f"- Missing required field: {field}")
            return {"error": "Missing required information"}
            
        # Generate quote request
        quote_request = self.form.generate_quote_request()
        
        # Get quote from carriers
        quotes = self._get_carrier_quotes(quote_request)
        
        return {
            "client": self.form.get_form_data(),
            "quotes": quotes
        }
        
    def _get_carrier_quotes(self, quote_request: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        Get quotes from carriers
        
        Args:
            quote_request: Quote request data
            
        Returns:
            List of carrier quotes
        """
        quote_type = quote_request.get("quote_type", "").lower()
        carriers = self.carriers.get(quote_type, self.carriers.get("health", []))
        
        quotes = []
        
        # In a real application, this would call carrier APIs
        # For now, generate mock quotes
        for carrier in carriers:
            quotes.append(self._generate_mock_quote(carrier, quote_request))
            
        return quotes
        
    def _generate_mock_quote(self, carrier: str, quote_request: Dict[str, Any]) -> Dict[str, Any]:
        """
        Generate mock quote from carrier
        
        Args:
            carrier: Carrier name
            quote_request: Quote request data
            
        Returns:
            Dictionary with mock quote data
        """
        quote_type = quote_request.get("quote_type", "").lower()
        
        # Base premium calculation (would be much more complex in reality)
        base_premium = 0
        
        # Age factor
        client_info = quote_request.get("client_info", {})
        date_of_birth = client_info.get("date_of_birth", "")
        if date_of_birth:
            try:
                dob = datetime.datetime.strptime(date_of_birth, "%Y-%m-%d").date()
                today = datetime.date.today()
                age = today.year - dob.year - ((today.month, today.day) < (dob.month, dob.day))
                
                # Age factor
                if age < 30:
                    base_premium = 150
                elif age < 40:
                    base_premium = 200
                elif age < 50:
                    base_premium = 275
                elif age < 60:
                    base_premium = 350
                elif age < 70:
                    base_premium = 450
                else:
                    base_premium = 550
            except Exception:
                base_premium = 300  # Default if age calculation fails
        else:
            base_premium = 300  # Default
            
        # Tobacco use factor
        health_info = quote_request.get("health_info", {})
        tobacco_use = health_info.get("tobacco_use", False)
        if tobacco_use:
            base_premium *= 1.5
            
        # Carrier factor (random variation between carriers)
        import random
        carrier_factor = random.uniform(0.85, 1.15)
        premium = base_premium * carrier_factor
        
        # Discount for preferred carriers
        preferred_carriers = quote_request.get("coverage_needs", {}).get("preferred_carriers", [])
        if carrier in preferred_carriers:
            premium *= 0.95
            
        # Create mock quote
        quote = {
            "carrier": carrier,
            "plan_name": f"{carrier} {quote_type.capitalize()} Plan",
            "premium": round(premium, 2),
            "benefits": self._generate_mock_benefits(quote_type),
            "coverage_amount": self._generate_mock_coverage(quote_type),
            "effective_date": (datetime.date.today() + datetime.timedelta(days=30)).strftime("%Y-%m-%d"),
            "quote_id": f"{carrier.lower().replace(' ', '')}-{random.randint(10000, 99999)}"
        }
        
        return quote
        
    def _generate_mock_benefits(self, quote_type: str) -> Dict[str, Any]:
        """Generate mock benefits based on quote type"""
        if quote_type == "medicare":
            return {
                "part_a": True,
                "part_b": True,
                "part_d": True,
                "dental": random.choice([True, False]),
                "vision": random.choice([True, False]),
                "hearing": random.choice([True, False]),
                "gym_membership": random.choice([True, False]),
                "transportation": random.choice([True, False])
            }
        elif quote_type == "health":
            return {
                "deductible": random.choice([500, 1000, 1500, 2000, 2500]),
                "copay": random.choice([20, 25, 30, 40, 50]),
                "coinsurance": random.choice([0.1, 0.15, 0.2, 0.25, 0.3]),
                "out_of_pocket_max": random.choice([5000, 6000, 7000, 8000, 9000]),
                "prescription": random.choice([True, False]),
                "dental": random.choice([True, False]),
                "vision": random.choice([True, False])
            }
        elif quote_type == "life":
            return {
                "death_benefit": True,
                "cash_value": random.choice([True, False]),
                "accelerated_death_benefit": random.choice([True, False]),
                "waiver_of_premium": random.choice([True, False]),
                "term_conversion": random.choice([True, False])
            }
        else:
            return {
                "basic_coverage": True,
                "additional_benefits": random.choice([True, False])
            }
            
    def _generate_mock_coverage(self, quote_type: str) -> Any:
        """Generate mock coverage amount based on quote type"""
        if quote_type == "medicare":
            return "Standard Medicare Coverage"
        elif quote_type == "health":
            return random.choice(["Bronze", "Silver", "Gold", "Platinum"])
        elif quote_type == "life":
            return random.choice([100000, 250000, 500000, 750000, 1000000])
        else:
            return "Standard Coverage"
            
    def print_quote_results(self, quote_results: Dict[str, Any]):
        """
        Print quote results
        
        Args:
            quote_results: Quote results to print
        """
        if "error" in quote_results:
            print(f"\nError: {quote_results['error']}")
            return
            
        client = quote_results.get("client", {})
        quotes = quote_results.get("quotes", [])
        
        print("\n============ INSURANCE QUOTE RESULTS ============")
        print(f"Client: {client.get('first_name', '')} {client.get('last_name', '')}")
        print(f"Insurance Type: {client.get('insurance_type', 'Unknown')}")
        print(f"Date of Birth: {client.get('date_of_birth', 'Unknown')}")
        print(f"Contact: {client.get('email', '')}, {client.get('phone_number', '')}")
        
        if not quotes:
            print("\nNo quotes available.")
            return
            
        print(f"\nFound {len(quotes)} quotes from carriers:")
        
        for i, quote in enumerate(quotes, 1):
            print(f"\nQuote {i}: {quote.get('carrier', 'Unknown')}")
            print(f"Plan: {quote.get('plan_name', 'Unknown')}")
            print(f"Monthly Premium: ${quote.get('premium', 0):.2f}")
            print(f"Effective Date: {quote.get('effective_date', 'Unknown')}")
            print(f"Quote ID: {quote.get('quote_id', 'Unknown')}")
            
            print("Benefits:")
            benefits = quote.get("benefits", {})
            for benefit, value in benefits.items():
                print(f"  - {benefit.replace('_', ' ').title()}: {'Yes' if value is True else 'No' if value is False else value}")
                
            coverage = quote.get("coverage_amount", "")
            if coverage:
                print(f"Coverage: {coverage}")
                
        print("\nNote: The above quotes are estimates only. Actual premiums may vary.")
        print("To proceed with an application, please contact our office or")
        print("provide the required sensitive information for online submission.")
        print("All information provided is secure and encrypted.")
        
    def save_quote_results(self, quote_results: Dict[str, Any], file_path: str) -> bool:
        """
        Save quote results to file
        
        Args:
            quote_results: Quote results to save
            file_path: Path to save the results
            
        Returns:
            True if successful, False otherwise
        """
        try:
            # Create directory if it doesn't exist
            os.makedirs(os.path.dirname(os.path.abspath(file_path)), exist_ok=True)
            
            # Save results as JSON
            with open(file_path, 'w') as f:
                json.dump(quote_results, f, indent=2)
                
            logger.info(f"Quote results saved to {file_path}")
            return True
        except Exception as e:
            logger.error(f"Error saving quote results: {e}")
            return False


def main():
    """Main function"""
    # Initialize the quote system
    system = InsuranceQuoteSystem()
    
    # Process command line arguments
    import argparse
    parser = argparse.ArgumentParser(description="Insurance Quote System")
    parser.add_argument("--interactive", action="store_true", help="Use interactive mode")
    parser.add_argument("--example", action="store_true", help="Use example client data")
    parser.add_argument("--output", help="Output file path for saving results")
    args = parser.parse_args()
    
    # Collect client information
    if args.example:
        client_data = system._get_example_client()
        print("Using example client data")
    else:
        client_data = system.collect_client_information(interactive=args.interactive)
        
    # Generate quote
    quote_results = system.generate_quote()
    
    # Print results
    system.print_quote_results(quote_results)
    
    # Save results if output file specified
    if args.output:
        if system.save_quote_results(quote_results, args.output):
            print(f"\nQuote results saved to {args.output}")
        else:
            print(f"\nError saving quote results to {args.output}")
    

if __name__ == "__main__":
    main()