import os
import logging
from dotenv import load_dotenv
from agent_system import AgentSystem, GmailManagerAgent, OutlookManagerAgent, PhoneSystemAgent

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_email_systems():
    """Test Gmail and Outlook integration"""
    try:
        # Initialize agent system
        system = AgentSystem()
        
        # Test Gmail Manager
        gmail_agent = system.agents['gmail_manager']
        logger.info("Testing Gmail accounts...")
        
        # Add your Gmail accounts here
        gmail_accounts = [
            {"email": "<EMAIL>", "password": "prompt_for_password"},
            {"email": "<EMAIL>", "password": "prompt_for_password"},
            # Add more Gmail accounts as needed
        ]
        
        for account in gmail_accounts:
            # In production, use secure password input
            print(f"\nEnter password for {account['email']}")
            password = input("Password: ")  # In production, use getpass for secure input
            
            if gmail_agent.add_account(account['email'], password):
                # Test reading emails
                messages = gmail_agent.read_emails(account['email'], limit=5)
                logger.info(f"Retrieved {len(messages)} messages from {account['email']}")
                
                # Display latest email subjects
                for msg in messages:
                    logger.info(f"Subject: {msg['subject']}")
        
        # Test Outlook Manager
        logger.info("\nTesting Outlook integration...")
        outlook_agent = system.agents['outlook_manager']
        
        if outlook_agent.outlook and outlook_agent.namespace:
            messages = outlook_agent.read_emails(limit=5)
            logger.info(f"Retrieved {len(messages)} messages from Outlook")
            
            # Display latest email subjects
            for msg in messages:
                logger.info(f"Subject: {msg['subject']}")
        else:
            logger.warning("Outlook connection not available")
        
        return True
        
    except Exception as e:
        logger.error(f"Error testing email systems: {e}")
        return False

def test_phone_system():
    """Test Twilio integration"""
    try:
        # Initialize phone system with Twilio credentials
        phone_agent = PhoneSystemAgent(
            account_sid=os.getenv("TWILIO_ACCOUNT_SID"),
            auth_token=os.getenv("TWILIO_AUTH_TOKEN"),
            phone_number=os.getenv("TWILIO_PHONE_NUMBER")
        )
        
        # Test SMS
        test_message = "Test message from FloFaction Agent System"
        test_number = input("\nEnter phone number to test SMS (including country code): ")
        
        if phone_agent.send_sms(test_number, test_message):
            logger.info("SMS test successful")
            return True
        else:
            logger.error("SMS test failed")
            return False
            
    except Exception as e:
        logger.error(f"Error testing phone system: {e}")
        return False

def test_business_agents():
    """Test other business-related agents"""
    try:
        system = AgentSystem()
        
        # Test each agent's initialization
        for agent_name, agent in system.agents.items():
            logger.info(f"Initialized {agent_name} agent: {type(agent).__name__}")
        
        # Here you can add specific tests for each business agent
        # For example:
        # - Test insurance quote generation
        # - Test content creation
        # - Test social media post scheduling
        # - Test customer support ticket handling
        # - Test financial reporting
        # - etc.
        
        return True
        
    except Exception as e:
        logger.error(f"Error testing business agents: {e}")
        return False

def main():
    """Run all agent system tests"""
    logger.info("Starting Agent System Tests\n")
    
    # Test email systems
    logger.info("=== Testing Email Systems ===")
    if test_email_systems():
        logger.info("Email systems test completed successfully")
    else:
        logger.error("Email systems test failed")
    
    # Test phone system
    logger.info("\n=== Testing Phone System ===")
    if test_phone_system():
        logger.info("Phone system test completed successfully")
    else:
        logger.error("Phone system test failed")
    
    # Test business agents
    logger.info("\n=== Testing Business Agents ===")
    if test_business_agents():
        logger.info("Business agents test completed successfully")
    else:
        logger.error("Business agents test failed")
    
    logger.info("\nAgent System Testing Completed")

if __name__ == "__main__":
    main()