#!/usr/bin/env python3
"""
Unified Web Automation Dashboard
================================

Comprehensive dashboard integrating all web automation tools:
- Web-UI Component (React-based interface)
- WebRover (Autonomous web navigation)
- MidScene.js (AI-powered browser automation)
- UI TARS 1.5 (Web evaluation agent)

Provides real-time monitoring, control, and orchestration of web automation tasks.
"""

import asyncio
import json
import logging
import os
import time
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Any, Optional
from dataclasses import dataclass, asdict
import aiohttp
from aiohttp import web, WSMsgType

# Import our components
try:
    from web_ui_component import WebUIComponent, AutomationTask
    from webrover_component import WebRoverComponent, CrawlSession, NavigationAction
    from midscene_integration import MidSceneIntegration, MidSceneAction
    from advanced_models.web_vision_agent import WebVisionAgent
except ImportError as e:
    logging.warning(f"Some components not available: {e}")

logger = logging.getLogger(__name__)

@dataclass
class UnifiedTask:
    id: str
    name: str
    type: str  # web_ui, webrover, midscene, ui_tars
    status: str = "pending"
    created_at: str = ""
    started_at: Optional[str] = None
    completed_at: Optional[str] = None
    progress: float = 0.0
    result: Optional[Dict[str, Any]] = None
    error: Optional[str] = None
    component_data: Optional[Dict[str, Any]] = None

class UnifiedWebAutomationDashboard:
    """Unified dashboard for all web automation components"""

    def __init__(self, host='localhost', port=8000):
        self.host = host
        self.port = port
        self.app = web.Application()
        self.setup_routes()

        # Component instances
        self.web_ui = None
        self.webrover = None
        self.midscene = None
        self.web_vision = None

        # State management
        self.tasks: Dict[str, UnifiedTask] = {}
        self.websockets: List[web.WebSocketResponse] = []
        self.system_status = {
            'web_ui': False,
            'webrover': False,
            'midscene': False,
            'ui_tars': False,
            'web_vision': False
        }

        # Configuration
        self.config = {
            'max_concurrent_tasks': 10,
            'task_timeout': 600,
            'enable_screenshots': True,
            'enable_logging': True,
            'auto_cleanup': True
        }

    async def initialize(self):
        """Initialize all web automation components"""
        logger.info("Initializing Unified Web Automation Dashboard...")

        # Initialize Web-UI Component
        try:
            self.web_ui = WebUIComponent(port=3001)  # Different port to avoid conflicts
            await self.web_ui.start_server()
            self.system_status['web_ui'] = True
            logger.info("✅ Web-UI Component initialized")
        except Exception as e:
            logger.error(f"❌ Web-UI Component failed: {e}")

        # Initialize WebRover
        try:
            self.webrover = WebRoverComponent()
            await self.webrover.initialize()
            self.system_status['webrover'] = True
            logger.info("✅ WebRover Component initialized")
        except Exception as e:
            logger.error(f"❌ WebRover Component failed: {e}")

        # Initialize MidScene
        try:
            self.midscene = MidSceneIntegration(headless=True)
            self.system_status['midscene'] = True
            logger.info("✅ MidScene Integration initialized")
        except Exception as e:
            logger.error(f"❌ MidScene Integration failed: {e}")

        # Initialize Web Vision Agent
        try:
            self.web_vision = WebVisionAgent()
            await self.web_vision.initialize()
            self.system_status['web_vision'] = True
            logger.info("✅ Web Vision Agent initialized")
        except Exception as e:
            logger.error(f"❌ Web Vision Agent failed: {e}")

        logger.info("Dashboard initialization completed")

    def setup_routes(self):
        """Setup HTTP routes for the unified dashboard"""
        # Main dashboard
        self.app.router.add_get('/', self.serve_dashboard)
        self.app.router.add_static('/static/', path='dashboard/static', name='static')

        # Unified API
        self.app.router.add_get('/api/status', self.get_system_status)
        self.app.router.add_get('/api/tasks', self.get_all_tasks)
        self.app.router.add_post('/api/tasks', self.create_unified_task)
        self.app.router.add_get('/api/tasks/{task_id}', self.get_task)
        self.app.router.add_delete('/api/tasks/{task_id}', self.delete_task)
        self.app.router.add_post('/api/tasks/{task_id}/start', self.start_task)
        self.app.router.add_post('/api/tasks/{task_id}/stop', self.stop_task)

        # Component-specific endpoints
        self.app.router.add_post('/api/webrover/crawl', self.start_crawl_session)
        self.app.router.add_get('/api/webrover/sessions', self.get_crawl_sessions)
        self.app.router.add_post('/api/midscene/execute', self.execute_midscene_actions)
        self.app.router.add_post('/api/web-vision/navigate', self.web_vision_navigate)

        # WebSocket for real-time updates
        self.app.router.add_get('/ws', self.websocket_handler)

        # Configuration
        self.app.router.add_get('/api/config', self.get_config)
        self.app.router.add_post('/api/config', self.update_config)

    async def serve_dashboard(self, request):
        """Serve the main dashboard HTML"""
        return web.FileResponse('dashboard/unified_dashboard.html')

    async def get_system_status(self, request):
        """Get overall system status"""
        return web.json_response({
            'status': 'running',
            'components': self.system_status,
            'tasks': {
                'total': len(self.tasks),
                'running': len([t for t in self.tasks.values() if t.status == 'running']),
                'completed': len([t for t in self.tasks.values() if t.status == 'completed']),
                'failed': len([t for t in self.tasks.values() if t.status == 'failed'])
            },
            'websockets': len(self.websockets),
            'uptime': time.time() - getattr(self, 'start_time', time.time())
        })

    async def get_all_tasks(self, request):
        """Get all unified tasks"""
        tasks_data = [asdict(task) for task in self.tasks.values()]
        return web.json_response({'tasks': tasks_data})

    async def create_unified_task(self, request):
        """Create a new unified automation task"""
        try:
            data = await request.json()

            task = UnifiedTask(
                id=f"unified_{int(time.time())}_{len(self.tasks)}",
                name=data.get('name', 'Unnamed Task'),
                type=data.get('type', 'web_ui'),
                created_at=datetime.now().isoformat(),
                component_data=data.get('component_data', {})
            )

            self.tasks[task.id] = task

            # Notify connected clients
            await self.broadcast_update('task_created', asdict(task))

            return web.json_response({
                'success': True,
                'task': asdict(task)
            })

        except Exception as e:
            logger.error(f"Error creating unified task: {e}")
            return web.json_response({
                'success': False,
                'error': str(e)
            }, status=400)

    async def get_task(self, request):
        """Get a specific task"""
        task_id = request.match_info['task_id']

        if task_id not in self.tasks:
            return web.json_response({
                'success': False,
                'error': 'Task not found'
            }, status=404)

        return web.json_response({
            'success': True,
            'task': asdict(self.tasks[task_id])
        })

    async def delete_task(self, request):
        """Delete a task"""
        task_id = request.match_info['task_id']

        if task_id not in self.tasks:
            return web.json_response({
                'success': False,
                'error': 'Task not found'
            }, status=404)

        del self.tasks[task_id]

        await self.broadcast_update('task_deleted', {'task_id': task_id})

        return web.json_response({'success': True})

    async def start_task(self, request):
        """Start executing a unified task"""
        task_id = request.match_info['task_id']

        if task_id not in self.tasks:
            return web.json_response({
                'success': False,
                'error': 'Task not found'
            }, status=404)

        task = self.tasks[task_id]
        task.status = 'running'
        task.started_at = datetime.now().isoformat()

        # Start task execution based on type
        asyncio.create_task(self.execute_unified_task(task))

        await self.broadcast_update('task_started', asdict(task))

        return web.json_response({
            'success': True,
            'task': asdict(task)
        })

    async def stop_task(self, request):
        """Stop a running task"""
        task_id = request.match_info['task_id']

        if task_id not in self.tasks:
            return web.json_response({
                'success': False,
                'error': 'Task not found'
            }, status=404)

        task = self.tasks[task_id]
        task.status = 'stopped'
        task.completed_at = datetime.now().isoformat()

        await self.broadcast_update('task_stopped', asdict(task))

        return web.json_response({
            'success': True,
            'task': asdict(task)
        })

    async def execute_unified_task(self, task: UnifiedTask):
        """Execute a unified task based on its type"""
        try:
            logger.info(f"Executing {task.type} task: {task.name}")

            if task.type == 'webrover' and self.webrover:
                await self.execute_webrover_task(task)
            elif task.type == 'midscene' and self.midscene:
                await self.execute_midscene_task(task)
            elif task.type == 'web_vision' and self.web_vision:
                await self.execute_web_vision_task(task)
            elif task.type == 'web_ui' and self.web_ui:
                await self.execute_web_ui_task(task)
            else:
                raise Exception(f"Component {task.type} not available or not supported")

            task.status = 'completed'
            task.completed_at = datetime.now().isoformat()
            task.progress = 100.0

            await self.broadcast_update('task_completed', asdict(task))

        except Exception as e:
            logger.error(f"Task execution failed: {e}")
            task.status = 'failed'
            task.error = str(e)
            task.completed_at = datetime.now().isoformat()

            await self.broadcast_update('task_failed', asdict(task))

    async def execute_webrover_task(self, task: UnifiedTask):
        """Execute a WebRover task"""
        data = task.component_data or {}

        if data.get('action') == 'crawl':
            session = await self.webrover.intelligent_crawl(
                start_url=data.get('url', ''),
                max_pages=data.get('max_pages', 10),
                max_depth=data.get('max_depth', 3)
            )
            task.result = {
                'session_id': session.session_id,
                'pages_crawled': len(session.pages_crawled),
                'urls_visited': len(session.visited_urls)
            }
        elif data.get('action') == 'extract':
            result = await self.webrover.extract_data(
                url=data.get('url', ''),
                extraction_rules=data.get('extraction_rules', {})
            )
            task.result = result
        else:
            # Simple navigation
            page = await self.webrover.navigate_to(data.get('url', ''))
            task.result = {
                'title': page.title,
                'links_found': len(page.links),
                'forms_found': len(page.forms)
            }

    async def execute_midscene_task(self, task: UnifiedTask):
        """Execute a MidScene task"""
        data = task.component_data or {}
        actions_data = data.get('actions', [])

        actions = [
            MidSceneAction(
                action_type=action.get('action_type', ''),
                target=action.get('target', ''),
                value=action.get('value'),
                options=action.get('options'),
                wait_for=action.get('wait_for')
            )
            for action in actions_data
        ]

        results = await self.midscene.execute_actions(actions)
        task.result = {
            'actions_executed': len(results),
            'successful_actions': len([r for r in results if r.success]),
            'results': [asdict(r) for r in results]
        }

    async def execute_web_vision_task(self, task: UnifiedTask):
        """Execute a Web Vision task"""
        data = task.component_data or {}

        if data.get('action') == 'navigate':
            result = await self.web_vision.navigate_to_url(data.get('url', ''))
            task.result = result
        elif data.get('action') == 'interact':
            result = await self.web_vision.interact_with_element(
                element_description=data.get('element_description', ''),
                action_type=data.get('interaction_type', 'click')
            )
            task.result = result
        else:
            # Default screenshot
            result = await self.web_vision.take_screenshot()
            task.result = {'screenshot_taken': True}

    async def execute_web_ui_task(self, task: UnifiedTask):
        """Execute a Web-UI task (delegate to Web-UI component)"""
        # This would typically delegate to the Web-UI component
        # For now, simulate execution
        await asyncio.sleep(2)
        task.result = {'message': 'Web-UI task completed', 'component': 'web_ui'}

    # Component-specific endpoints
    async def start_crawl_session(self, request):
        """Start a WebRover crawl session"""
        if not self.webrover:
            return web.json_response({
                'success': False,
                'error': 'WebRover not available'
            }, status=503)

        try:
            data = await request.json()
            session = await self.webrover.intelligent_crawl(
                start_url=data.get('url', ''),
                max_pages=data.get('max_pages', 10),
                max_depth=data.get('max_depth', 3)
            )

            return web.json_response({
                'success': True,
                'session': asdict(session)
            })

        except Exception as e:
            return web.json_response({
                'success': False,
                'error': str(e)
            }, status=400)

    async def get_crawl_sessions(self, request):
        """Get all WebRover crawl sessions"""
        if not self.webrover:
            return web.json_response({
                'success': False,
                'error': 'WebRover not available'
            }, status=503)

        sessions = self.webrover.get_all_sessions()
        return web.json_response({
            'success': True,
            'sessions': [asdict(session) for session in sessions]
        })

    async def execute_midscene_actions(self, request):
        """Execute MidScene actions directly"""
        if not self.midscene:
            return web.json_response({
                'success': False,
                'error': 'MidScene not available'
            }, status=503)

        try:
            data = await request.json()
            actions_data = data.get('actions', [])

            actions = [
                MidSceneAction(
                    action_type=action.get('action_type', ''),
                    target=action.get('target', ''),
                    value=action.get('value'),
                    options=action.get('options'),
                    wait_for=action.get('wait_for')
                )
                for action in actions_data
            ]

            results = await self.midscene.execute_actions(actions)

            return web.json_response({
                'success': True,
                'results': [asdict(r) for r in results]
            })

        except Exception as e:
            return web.json_response({
                'success': False,
                'error': str(e)
            }, status=400)

    async def web_vision_navigate(self, request):
        """Navigate using Web Vision Agent"""
        if not self.web_vision:
            return web.json_response({
                'success': False,
                'error': 'Web Vision Agent not available'
            }, status=503)

        try:
            data = await request.json()
            result = await self.web_vision.navigate_to_url(data.get('url', ''))

            return web.json_response({
                'success': True,
                'result': result
            })

        except Exception as e:
            return web.json_response({
                'success': False,
                'error': str(e)
            }, status=400)

    async def websocket_handler(self, request):
        """Handle WebSocket connections for real-time updates"""
        ws = web.WebSocketResponse()
        await ws.prepare(request)

        self.websockets.append(ws)
        logger.info("WebSocket client connected to unified dashboard")

        try:
            async for msg in ws:
                if msg.type == WSMsgType.TEXT:
                    try:
                        data = json.loads(msg.data)
                        await self.handle_websocket_message(ws, data)
                    except json.JSONDecodeError:
                        await ws.send_str(json.dumps({
                            'type': 'error',
                            'message': 'Invalid JSON'
                        }))
                elif msg.type == WSMsgType.ERROR:
                    logger.error(f'WebSocket error: {ws.exception()}')

        except Exception as e:
            logger.error(f"WebSocket error: {e}")
        finally:
            if ws in self.websockets:
                self.websockets.remove(ws)
            logger.info("WebSocket client disconnected from unified dashboard")

        return ws

    async def handle_websocket_message(self, ws, data):
        """Handle incoming WebSocket messages"""
        msg_type = data.get('type')

        if msg_type == 'ping':
            await ws.send_str(json.dumps({'type': 'pong'}))
        elif msg_type == 'subscribe':
            await ws.send_str(json.dumps({
                'type': 'subscribed',
                'message': 'Successfully subscribed to unified dashboard updates'
            }))
        elif msg_type == 'get_status':
            status = await self.get_system_status(None)
            await ws.send_str(json.dumps({
                'type': 'status_update',
                'data': json.loads(status.text)
            }))

    async def broadcast_update(self, event_type: str, data: Any):
        """Broadcast update to all connected WebSocket clients"""
        if not self.websockets:
            return

        message = json.dumps({
            'type': event_type,
            'data': data,
            'timestamp': datetime.now().isoformat()
        })

        # Remove closed connections
        active_websockets = []
        for ws in self.websockets:
            if not ws.closed:
                try:
                    await ws.send_str(message)
                    active_websockets.append(ws)
                except Exception as e:
                    logger.error(f"Error sending WebSocket message: {e}")

        self.websockets = active_websockets

    async def get_config(self, request):
        """Get current configuration"""
        return web.json_response(self.config)

    async def update_config(self, request):
        """Update configuration"""
        try:
            data = await request.json()
            self.config.update(data)

            await self.broadcast_update('config_updated', self.config)

            return web.json_response({
                'success': True,
                'config': self.config
            })

        except Exception as e:
            return web.json_response({
                'success': False,
                'error': str(e)
            }, status=400)

    async def start_server(self):
        """Start the unified dashboard server"""
        self.start_time = time.time()

        # Create dashboard directory if it doesn't exist
        os.makedirs('dashboard/static', exist_ok=True)

        # Create dashboard HTML if it doesn't exist
        if not os.path.exists('dashboard/unified_dashboard.html'):
            await self.create_dashboard_html()

        runner = web.AppRunner(self.app)
        await runner.setup()

        site = web.TCPSite(runner, self.host, self.port)
        await site.start()

        logger.info(f"🚀 Unified Web Automation Dashboard started at http://{self.host}:{self.port}")

    async def create_dashboard_html(self):
        """Create the unified dashboard HTML interface"""
        html_content = '''<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Unified Web Automation Dashboard</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; background: #0f172a; color: #e2e8f0; }
        .container { max-width: 1400px; margin: 0 auto; padding: 20px; }
        .header { background: linear-gradient(135deg, #1e293b, #334155); padding: 30px; border-radius: 12px; margin-bottom: 30px; }
        .header h1 { font-size: 2.5rem; margin-bottom: 10px; background: linear-gradient(45deg, #3b82f6, #8b5cf6); -webkit-background-clip: text; -webkit-text-fill-color: transparent; }
        .header p { color: #94a3b8; font-size: 1.1rem; }
        .grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; margin-bottom: 30px; }
        .card { background: #1e293b; border: 1px solid #334155; border-radius: 12px; padding: 25px; transition: all 0.3s ease; }
        .card:hover { border-color: #3b82f6; transform: translateY(-2px); }
        .card h3 { color: #3b82f6; margin-bottom: 15px; font-size: 1.3rem; }
        .status-indicator { display: inline-block; width: 12px; height: 12px; border-radius: 50%; margin-right: 8px; }
        .status-online { background: #10b981; }
        .status-offline { background: #ef4444; }
        .btn { background: #3b82f6; color: white; padding: 12px 24px; border: none; border-radius: 8px; cursor: pointer; font-size: 14px; transition: all 0.3s ease; }
        .btn:hover { background: #2563eb; transform: translateY(-1px); }
        .btn-secondary { background: #6b7280; }
        .btn-secondary:hover { background: #4b5563; }
        .btn-danger { background: #ef4444; }
        .btn-danger:hover { background: #dc2626; }
        .task-list { max-height: 400px; overflow-y: auto; }
        .task-item { background: #0f172a; border: 1px solid #334155; border-radius: 8px; padding: 15px; margin-bottom: 10px; }
        .task-status { padding: 4px 12px; border-radius: 20px; font-size: 12px; font-weight: bold; }
        .status-pending { background: #374151; color: #9ca3af; }
        .status-running { background: #f59e0b; color: #fbbf24; }
        .status-completed { background: #10b981; color: #34d399; }
        .status-failed { background: #ef4444; color: #f87171; }
        .form-group { margin-bottom: 15px; }
        .form-group label { display: block; margin-bottom: 5px; color: #94a3b8; }
        .form-group input, .form-group select, .form-group textarea { width: 100%; padding: 10px; background: #0f172a; border: 1px solid #334155; border-radius: 6px; color: #e2e8f0; }
        .modal { display: none; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.8); z-index: 1000; }
        .modal-content { background: #1e293b; margin: 5% auto; padding: 30px; border-radius: 12px; width: 90%; max-width: 600px; }
        .close { float: right; font-size: 28px; cursor: pointer; color: #94a3b8; }
        .close:hover { color: #e2e8f0; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 Unified Web Automation Dashboard</h1>
            <p>Control and monitor all your web automation components from one place</p>
        </div>

        <div class="grid">
            <div class="card">
                <h3>System Status</h3>
                <div id="system-status">Loading...</div>
            </div>

            <div class="card">
                <h3>Component Health</h3>
                <div id="component-status">Loading...</div>
            </div>

            <div class="card">
                <h3>Quick Actions</h3>
                <button class="btn" onclick="openTaskModal()">Create New Task</button>
                <button class="btn btn-secondary" onclick="refreshDashboard()">Refresh</button>
                <button class="btn btn-secondary" onclick="openConfigModal()">Settings</button>
            </div>
        </div>

        <div class="card">
            <h3>Active Tasks</h3>
            <div id="tasks-container">Loading...</div>
        </div>
    </div>

    <!-- Task Creation Modal -->
    <div id="taskModal" class="modal">
        <div class="modal-content">
            <span class="close" onclick="closeTaskModal()">&times;</span>
            <h2>Create New Automation Task</h2>
            <form id="taskForm">
                <div class="form-group">
                    <label>Task Name:</label>
                    <input type="text" id="taskName" required>
                </div>
                <div class="form-group">
                    <label>Component:</label>
                    <select id="taskType" required>
                        <option value="webrover">WebRover (Crawling)</option>
                        <option value="midscene">MidScene (AI Automation)</option>
                        <option value="web_vision">Web Vision (Visual AI)</option>
                        <option value="web_ui">Web UI (Interface)</option>
                    </select>
                </div>
                <div class="form-group">
                    <label>Target URL:</label>
                    <input type="url" id="taskUrl" required>
                </div>
                <div class="form-group">
                    <label>Configuration (JSON):</label>
                    <textarea id="taskConfig" rows="5" placeholder='{"action": "crawl", "max_pages": 10}'></textarea>
                </div>
                <button type="submit" class="btn">Create Task</button>
                <button type="button" class="btn btn-secondary" onclick="closeTaskModal()">Cancel</button>
            </form>
        </div>
    </div>

    <script>
        // WebSocket connection
        const ws = new WebSocket(`ws://${window.location.host}/ws`);

        ws.onopen = function() {
            console.log('Connected to unified dashboard');
            ws.send(JSON.stringify({type: 'subscribe'}));
        };

        ws.onmessage = function(event) {
            const data = JSON.parse(event.data);
            console.log('Dashboard update:', data);

            if (data.type === 'task_created' || data.type === 'task_updated' ||
                data.type === 'task_completed' || data.type === 'task_failed') {
                loadTasks();
            }
        };

        // Load system status
        async function loadSystemStatus() {
            try {
                const response = await fetch('/api/status');
                const data = await response.json();

                document.getElementById('system-status').innerHTML = `
                    <p><strong>Status:</strong> ${data.status}</p>
                    <p><strong>Total Tasks:</strong> ${data.tasks.total}</p>
                    <p><strong>Running:</strong> ${data.tasks.running}</p>
                    <p><strong>Completed:</strong> ${data.tasks.completed}</p>
                    <p><strong>WebSockets:</strong> ${data.websockets}</p>
                `;

                const componentHtml = Object.entries(data.components).map(([name, status]) => `
                    <div style="margin: 5px 0;">
                        <span class="status-indicator ${status ? 'status-online' : 'status-offline'}"></span>
                        ${name}: ${status ? 'Online' : 'Offline'}
                    </div>
                `).join('');

                document.getElementById('component-status').innerHTML = componentHtml;

            } catch (error) {
                console.error('Error loading status:', error);
            }
        }

        // Load tasks
        async function loadTasks() {
            try {
                const response = await fetch('/api/tasks');
                const data = await response.json();

                const tasksHtml = data.tasks.map(task => `
                    <div class="task-item">
                        <div style="display: flex; justify-content: space-between; align-items: center;">
                            <div>
                                <h4>${task.name} <span class="task-status status-${task.status}">${task.status}</span></h4>
                                <p><strong>Type:</strong> ${task.type}</p>
                                <p><strong>Created:</strong> ${new Date(task.created_at).toLocaleString()}</p>
                                ${task.progress > 0 ? `<p><strong>Progress:</strong> ${task.progress}%</p>` : ''}
                            </div>
                            <div>
                                ${task.status === 'pending' ? `<button class="btn" onclick="startTask('${task.id}')">Start</button>` : ''}
                                ${task.status === 'running' ? `<button class="btn btn-secondary" onclick="stopTask('${task.id}')">Stop</button>` : ''}
                                <button class="btn btn-danger" onclick="deleteTask('${task.id}')">Delete</button>
                            </div>
                        </div>
                        ${task.error ? `<p style="color: #ef4444; margin-top: 10px;">Error: ${task.error}</p>` : ''}
                    </div>
                `).join('');

                document.getElementById('tasks-container').innerHTML = tasksHtml || '<p>No tasks found</p>';

            } catch (error) {
                console.error('Error loading tasks:', error);
            }
        }

        // Task management functions
        function openTaskModal() {
            document.getElementById('taskModal').style.display = 'block';
        }

        function closeTaskModal() {
            document.getElementById('taskModal').style.display = 'none';
        }

        document.getElementById('taskForm').onsubmit = async function(e) {
            e.preventDefault();

            const taskData = {
                name: document.getElementById('taskName').value,
                type: document.getElementById('taskType').value,
                component_data: {
                    url: document.getElementById('taskUrl').value,
                    ...JSON.parse(document.getElementById('taskConfig').value || '{}')
                }
            };

            try {
                const response = await fetch('/api/tasks', {
                    method: 'POST',
                    headers: {'Content-Type': 'application/json'},
                    body: JSON.stringify(taskData)
                });

                if (response.ok) {
                    closeTaskModal();
                    loadTasks();
                    document.getElementById('taskForm').reset();
                }
            } catch (error) {
                console.error('Error creating task:', error);
            }
        };

        async function startTask(taskId) {
            try {
                const response = await fetch(`/api/tasks/${taskId}/start`, {method: 'POST'});
                if (response.ok) loadTasks();
            } catch (error) {
                console.error('Error starting task:', error);
            }
        }

        async function stopTask(taskId) {
            try {
                const response = await fetch(`/api/tasks/${taskId}/stop`, {method: 'POST'});
                if (response.ok) loadTasks();
            } catch (error) {
                console.error('Error stopping task:', error);
            }
        }

        async function deleteTask(taskId) {
            if (confirm('Are you sure you want to delete this task?')) {
                try {
                    const response = await fetch(`/api/tasks/${taskId}`, {method: 'DELETE'});
                    if (response.ok) loadTasks();
                } catch (error) {
                    console.error('Error deleting task:', error);
                }
            }
        }

        function refreshDashboard() {
            loadSystemStatus();
            loadTasks();
        }

        // Initial load
        loadSystemStatus();
        loadTasks();

        // Auto-refresh every 10 seconds
        setInterval(refreshDashboard, 10000);
    </script>
</body>
</html>'''

        os.makedirs('dashboard', exist_ok=True)
        with open('dashboard/unified_dashboard.html', 'w') as f:
            f.write(html_content)

    async def cleanup(self):
        """Cleanup all components"""
        if self.webrover:
            await self.webrover.cleanup()
        if self.midscene:
            self.midscene.cleanup()
        if self.web_vision:
            await self.web_vision.cleanup()

# Main execution
if __name__ == "__main__":
    async def main():
        dashboard = UnifiedWebAutomationDashboard()

        try:
            await dashboard.initialize()
            await dashboard.start_server()

            logger.info("🎯 Unified Web Automation Dashboard is running!")
            logger.info("📊 Dashboard: http://localhost:8000")
            logger.info("🔌 WebSocket: ws://localhost:8000/ws")

            # Keep the server running
            while True:
                await asyncio.sleep(1)

        except KeyboardInterrupt:
            logger.info("Shutting down Unified Web Automation Dashboard...")
            await dashboard.cleanup()

    # Configure logging
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )

    asyncio.run(main())