#!/usr/bin/env python3
"""
Google Cloud Deployment Script
==============================

Automated deployment of the Unified Web Automation Dashboard to Google Cloud Platform.
Supports Cloud Run, App Engine, and Compute Engine deployments.
"""

import os
import json
import subprocess
import logging
from pathlib import Path
from typing import Dict, List, Any, Optional

logger = logging.getLogger(__name__)

class GoogleCloudDeployer:
    """Google Cloud deployment manager for web automation tools"""
    
    def __init__(self, project_id: str, region: str = "us-central1"):
        self.project_id = project_id
        self.region = region
        self.service_name = "web-automation-dashboard"
        self.image_name = f"gcr.io/{project_id}/{self.service_name}"
        
        # Deployment configuration
        self.config = {
            'cloud_run': {
                'memory': '2Gi',
                'cpu': '2',
                'max_instances': 10,
                'min_instances': 1,
                'port': 8000,
                'timeout': 300,
                'concurrency': 80
            },
            'app_engine': {
                'runtime': 'python39',
                'instance_class': 'F2',
                'automatic_scaling': {
                    'min_instances': 1,
                    'max_instances': 10,
                    'target_cpu_utilization': 0.6
                }
            },
            'compute_engine': {
                'machine_type': 'e2-standard-2',
                'disk_size': '20GB',
                'zone': f"{region}-a"
            }
        }
        
    def check_prerequisites(self) -> bool:
        """Check if all prerequisites are met"""
        logger.info("Checking deployment prerequisites...")
        
        # Check if gcloud CLI is installed
        try:
            result = subprocess.run(['gcloud', 'version'], 
                                  capture_output=True, text=True, check=True)
            logger.info("✅ Google Cloud CLI is installed")
        except (subprocess.CalledProcessError, FileNotFoundError):
            logger.error("❌ Google Cloud CLI not found. Please install it first.")
            return False
            
        # Check if Docker is installed
        try:
            result = subprocess.run(['docker', '--version'], 
                                  capture_output=True, text=True, check=True)
            logger.info("✅ Docker is installed")
        except (subprocess.CalledProcessError, FileNotFoundError):
            logger.error("❌ Docker not found. Please install it first.")
            return False
            
        # Check authentication
        try:
            result = subprocess.run(['gcloud', 'auth', 'list', '--filter=status:ACTIVE'], 
                                  capture_output=True, text=True, check=True)
            if 'ACTIVE' in result.stdout:
                logger.info("✅ Google Cloud authentication is active")
            else:
                logger.error("❌ Please authenticate with Google Cloud: gcloud auth login")
                return False
        except subprocess.CalledProcessError:
            logger.error("❌ Error checking Google Cloud authentication")
            return False
            
        # Set project
        try:
            subprocess.run(['gcloud', 'config', 'set', 'project', self.project_id], 
                          check=True)
            logger.info(f"✅ Project set to {self.project_id}")
        except subprocess.CalledProcessError:
            logger.error(f"❌ Error setting project to {self.project_id}")
            return False
            
        return True
        
    def create_dockerfile(self):
        """Create optimized Dockerfile for web automation"""
        dockerfile_content = f'''# Multi-stage build for web automation dashboard
FROM node:18-slim as node-builder

# Install Node.js dependencies for MidScene.js
WORKDIR /app/node
COPY package*.json ./
RUN npm install --production

FROM python:3.9-slim

# Install system dependencies
RUN apt-get update && apt-get install -y \\
    wget \\
    gnupg \\
    unzip \\
    curl \\
    xvfb \\
    && rm -rf /var/lib/apt/lists/*

# Install Chrome for browser automation
RUN wget -q -O - https://dl-ssl.google.com/linux/linux_signing_key.pub | apt-key add - \\
    && echo "deb [arch=amd64] http://dl.google.com/linux/chrome/deb/ stable main" >> /etc/apt/sources.list.d/google.list \\
    && apt-get update \\
    && apt-get install -y google-chrome-stable \\
    && rm -rf /var/lib/apt/lists/*

# Set up working directory
WORKDIR /app

# Copy Node.js dependencies
COPY --from=node-builder /app/node/node_modules ./node_modules

# Copy Python requirements and install
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# Install additional dependencies for web automation
RUN pip install --no-cache-dir \\
    playwright==1.40.0 \\
    selenium==4.15.0 \\
    aiohttp==3.9.0 \\
    beautifulsoup4==4.12.0 \\
    opencv-python-headless==********

# Install Playwright browsers
RUN playwright install chromium

# Copy application code
COPY . .

# Create necessary directories
RUN mkdir -p dashboard/static logs

# Set environment variables
ENV PYTHONPATH=/app
ENV DISPLAY=:99
ENV CHROME_BIN=/usr/bin/google-chrome
ENV CHROME_PATH=/usr/bin/google-chrome

# Expose port
EXPOSE {self.config['cloud_run']['port']}

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \\
    CMD curl -f http://localhost:{self.config['cloud_run']['port']}/api/status || exit 1

# Start command
CMD ["python", "unified_web_automation_dashboard.py"]
'''
        
        with open('Dockerfile', 'w') as f:
            f.write(dockerfile_content)
            
        logger.info("✅ Dockerfile created")
        
    def create_package_json(self):
        """Create package.json for Node.js dependencies"""
        package_json = {
            "name": "web-automation-dashboard",
            "version": "1.0.0",
            "description": "Unified Web Automation Dashboard",
            "dependencies": {
                "playwright": "^1.40.0",
                "@playwright/test": "^1.40.0"
            },
            "engines": {
                "node": ">=18.0.0"
            }
        }
        
        with open('package.json', 'w') as f:
            json.dump(package_json, f, indent=2)
            
        logger.info("✅ package.json created")
        
    def create_dockerignore(self):
        """Create .dockerignore file"""
        dockerignore_content = '''# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
env/
venv/
.venv/
pip-log.txt
pip-delete-this-directory.txt
.tox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
*.log
.git
.mypy_cache
.pytest_cache
.hypothesis

# Node
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Temporary files
*.tmp
*.temp
temp/
tmp/

# Logs
logs/
*.log

# Screenshots and test artifacts
screenshots/
test-results/
playwright-report/
'''
        
        with open('.dockerignore', 'w') as f:
            f.write(dockerignore_content)
            
        logger.info("✅ .dockerignore created")
        
    def build_and_push_image(self) -> bool:
        """Build and push Docker image to Google Container Registry"""
        logger.info("Building and pushing Docker image...")
        
        try:
            # Configure Docker for GCR
            subprocess.run(['gcloud', 'auth', 'configure-docker'], check=True)
            
            # Build image
            logger.info("Building Docker image...")
            subprocess.run(['docker', 'build', '-t', self.image_name, '.'], check=True)
            
            # Push image
            logger.info("Pushing Docker image to GCR...")
            subprocess.run(['docker', 'push', self.image_name], check=True)
            
            logger.info("✅ Docker image built and pushed successfully")
            return True
            
        except subprocess.CalledProcessError as e:
            logger.error(f"❌ Error building/pushing Docker image: {e}")
            return False
            
    def deploy_to_cloud_run(self) -> bool:
        """Deploy to Google Cloud Run"""
        logger.info("Deploying to Google Cloud Run...")
        
        try:
            cmd = [
                'gcloud', 'run', 'deploy', self.service_name,
                '--image', self.image_name,
                '--platform', 'managed',
                '--region', self.region,
                '--allow-unauthenticated',
                '--memory', self.config['cloud_run']['memory'],
                '--cpu', self.config['cloud_run']['cpu'],
                '--max-instances', str(self.config['cloud_run']['max_instances']),
                '--min-instances', str(self.config['cloud_run']['min_instances']),
                '--port', str(self.config['cloud_run']['port']),
                '--timeout', str(self.config['cloud_run']['timeout']),
                '--concurrency', str(self.config['cloud_run']['concurrency']),
                '--set-env-vars', 'ENVIRONMENT=production,PORT=8000'
            ]
            
            subprocess.run(cmd, check=True)
            
            # Get service URL
            result = subprocess.run([
                'gcloud', 'run', 'services', 'describe', self.service_name,
                '--platform', 'managed',
                '--region', self.region,
                '--format', 'value(status.url)'
            ], capture_output=True, text=True, check=True)
            
            service_url = result.stdout.strip()
            logger.info(f"✅ Cloud Run deployment successful!")
            logger.info(f"🌐 Service URL: {service_url}")
            
            return True
            
        except subprocess.CalledProcessError as e:
            logger.error(f"❌ Cloud Run deployment failed: {e}")
            return False
            
    def create_app_yaml(self):
        """Create app.yaml for App Engine deployment"""
        app_yaml_content = f'''runtime: python39
instance_class: {self.config['app_engine']['instance_class']}

automatic_scaling:
  min_instances: {self.config['app_engine']['automatic_scaling']['min_instances']}
  max_instances: {self.config['app_engine']['automatic_scaling']['max_instances']}
  target_cpu_utilization: {self.config['app_engine']['automatic_scaling']['target_cpu_utilization']}

env_variables:
  ENVIRONMENT: production
  PORT: 8080

handlers:
- url: /static
  static_dir: dashboard/static
  
- url: /.*
  script: auto
  
entrypoint: python unified_web_automation_dashboard.py
'''
        
        with open('app.yaml', 'w') as f:
            f.write(app_yaml_content)
            
        logger.info("✅ app.yaml created")
        
    def deploy_to_app_engine(self) -> bool:
        """Deploy to Google App Engine"""
        logger.info("Deploying to Google App Engine...")
        
        try:
            self.create_app_yaml()
            
            subprocess.run(['gcloud', 'app', 'deploy', '--quiet'], check=True)
            
            # Get service URL
            result = subprocess.run([
                'gcloud', 'app', 'browse', '--no-launch-browser'
            ], capture_output=True, text=True, check=True)
            
            logger.info("✅ App Engine deployment successful!")
            logger.info(f"🌐 Service URL: https://{self.project_id}.appspot.com")
            
            return True
            
        except subprocess.CalledProcessError as e:
            logger.error(f"❌ App Engine deployment failed: {e}")
            return False
            
    def setup_monitoring(self):
        """Setup monitoring and logging"""
        logger.info("Setting up monitoring and logging...")
        
        try:
            # Enable required APIs
            apis = [
                'run.googleapis.com',
                'cloudbuild.googleapis.com',
                'containerregistry.googleapis.com',
                'monitoring.googleapis.com',
                'logging.googleapis.com'
            ]
            
            for api in apis:
                subprocess.run(['gcloud', 'services', 'enable', api], check=True)
                
            logger.info("✅ Monitoring and logging configured")
            
        except subprocess.CalledProcessError as e:
            logger.error(f"❌ Error setting up monitoring: {e}")
            
    def deploy(self, deployment_type: str = 'cloud_run') -> bool:
        """Main deployment function"""
        logger.info(f"🚀 Starting {deployment_type} deployment...")
        
        if not self.check_prerequisites():
            return False
            
        # Create deployment files
        self.create_dockerfile()
        self.create_package_json()
        self.create_dockerignore()
        
        # Setup monitoring
        self.setup_monitoring()
        
        if deployment_type == 'cloud_run':
            if not self.build_and_push_image():
                return False
            return self.deploy_to_cloud_run()
            
        elif deployment_type == 'app_engine':
            return self.deploy_to_app_engine()
            
        else:
            logger.error(f"❌ Unsupported deployment type: {deployment_type}")
            return False

# Example usage
if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description='Deploy Web Automation Dashboard to Google Cloud')
    parser.add_argument('--project-id', required=True, help='Google Cloud Project ID')
    parser.add_argument('--region', default='us-central1', help='Deployment region')
    parser.add_argument('--type', choices=['cloud_run', 'app_engine'], 
                       default='cloud_run', help='Deployment type')
    
    args = parser.parse_args()
    
    # Configure logging
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s'
    )
    
    deployer = GoogleCloudDeployer(args.project_id, args.region)
    
    success = deployer.deploy(args.type)
    
    if success:
        logger.info("🎉 Deployment completed successfully!")
    else:
        logger.error("💥 Deployment failed!")
        exit(1)
