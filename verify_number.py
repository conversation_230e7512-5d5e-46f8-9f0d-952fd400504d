"""
Verify a phone number with <PERSON><PERSON><PERSON> for trial account usage
"""

from twilio.rest import Client

# Twilio credentials
TWILIO_ACCOUNT_SID = "**********************************"
TWILIO_AUTH_TOKEN = "0458bc22d41d6756fc8e62d3e2938382"

# Phone number to verify
PHONE_NUMBER = "+***********"  # <PERSON>'s alternative number

def verify_phone_number():
    """Verify a phone number with <PERSON><PERSON><PERSON>"""
    client = Client(TWILIO_ACCOUNT_SID, TWILIO_AUTH_TOKEN)
    
    try:
        validation_request = client.validation_requests.create(
            friendly_name="<PERSON> Alternative",
            phone_number=PHONE_NUMBER
        )
        
        print(f"Validation request created with SID: {validation_request.sid}")
        print("A call will be made to the phone number with a verification code.")
        print("Please enter the code when prompted on the phone.")
        
        return validation_request.sid
    except Exception as e:
        print(f"Error creating validation request: {str(e)}")
        return None

if __name__ == "__main__":
    print("This script will attempt to verify a phone number with <PERSON><PERSON><PERSON>.")
    print(f"Phone number to verify: {PHONE_NUMBER}")
    
    proceed = input("Do you want to proceed? (yes/no): ")
    
    if proceed.lower() == "yes":
        verify_phone_number()
    else:
        print("Verification cancelled.")
