# IRIS Unified AI System - User Guide

## 🚀 Welcome to IRIS

IRIS (Integrated RAG Intelligence System) is your comprehensive AI assistant that combines:
- **5 Advanced AI Models**: MANUS, MiMo-VL-7B, Detail Flow, Giga Agent, Honest AI
- **Traditional Specialized Agents**: Insurance, Content, Email, Social Media
- **Vision Capabilities**: Image analysis, OCR, document processing
- **Web Automation**: Browser control, form filling, navigation
- **Knowledge Management**: RAG system, domain expertise, MCP servers

## 📋 Quick Start Guide

### 1. System Requirements
```bash
# Ensure you have Python 3.8+ installed
python --version

# Navigate to the IRIS directory
cd /Users/<USER>/AIAgentProjects/PaulEdwardsAI
```

### 2. Start IRIS Dashboard (Recommended)
```bash
# Start the unified dashboard
python iris_dashboard.py

# Or use the interactive CLI
python iris_cli.py
```

### 3. Interactive CLI Usage
```bash
# Start interactive mode
python iris_cli.py

# You'll see:
🚀 IRIS INTERACTIVE CLI
============================================================
Initializing IRIS system...
✅ IRIS is ready!

🤖 IRIS> 
```

## 💬 How to Use IRIS

### Basic Text Queries
Simply type your question or request:

```
🤖 IRIS> What is term life insurance?
🤖 IRIS> Explain the difference between whole life and term life insurance
🤖 IRIS> Research the latest insurance market trends
🤖 IRIS> Write a marketing email for our new policy
```

### Image Analysis
Analyze images, documents, or photos:

```
🤖 IRIS> /image policy_document.pdf Extract the policy number and coverage details
🤖 IRIS> /image insurance_form.png Fill out this form with sample data
🤖 IRIS> /image chart.jpg Analyze this insurance market chart
```

### Web Automation
Automate web tasks and navigation:

```
🤖 IRIS> /web https://example.com Navigate to the contact page
🤖 IRIS> /web https://insurance-site.com Fill out the quote request form
🤖 IRIS> Navigate to Progressive's website and get an auto insurance quote
```

### Complex Multi-Step Tasks
IRIS can handle complex workflows:

```
🤖 IRIS> Research life insurance options, then create a comparison chart, and draft an email to send to clients
🤖 IRIS> Analyze this policy document, extract key terms, and create a summary for the client
🤖 IRIS> Find information about flood insurance, then navigate to FEMA's website and download the application form
```

## 🔧 CLI Commands Reference

| Command | Description | Example |
|---------|-------------|---------|
| `<text>` | Ask any question | `What is liability insurance?` |
| `/image <path> <query>` | Analyze an image | `/image doc.pdf Extract policy details` |
| `/web <url> <task>` | Web automation | `/web https://site.com Fill contact form` |
| `/status` | Show system status | `/status` |
| `/history` | Show query history | `/history` |
| `/help` | Show help information | `/help` |
| `/quit` or `/exit` | Exit IRIS | `/quit` |

## 🎯 Use Case Examples

### Insurance Agent Scenarios

#### 1. Policy Research and Comparison
```bash
🤖 IRIS> Compare term life insurance policies from top 5 carriers for a 35-year-old non-smoker
```

#### 2. Document Analysis
```bash
🤖 IRIS> /image client_application.pdf Review this application for completeness and flag any missing information
```

#### 3. Client Communication
```bash
🤖 IRIS> Draft a professional email explaining why the client's claim was denied, including next steps and appeal process
```

#### 4. Market Research
```bash
🤖 IRIS> Research current trends in disability insurance and create a summary for our sales team
```

### Content Creation Scenarios

#### 1. Marketing Materials
```bash
🤖 IRIS> Create a social media post about the importance of life insurance for young families
```

#### 2. Educational Content
```bash
🤖 IRIS> Write a blog post explaining the difference between HMO and PPO health insurance plans
```

#### 3. Client Presentations
```bash
🤖 IRIS> Create an outline for a presentation about retirement planning and annuities
```

### Web Automation Scenarios

#### 1. Carrier Website Navigation
```bash
🤖 IRIS> /web https://www.statefarm.com Navigate to auto insurance quotes and analyze the form fields
```

#### 2. Form Completion
```bash
🤖 IRIS> /web https://progressive.com Fill out an auto insurance quote for a 2020 Honda Civic
```

#### 3. Information Gathering
```bash
🤖 IRIS> Navigate to the Department of Insurance website and find the latest regulatory updates
```

## 📊 System Status and Monitoring

### Check System Status
```bash
🤖 IRIS> /status
```

This shows:
- ✅/❌ Advanced AI Models status
- ✅/❌ Traditional Agents status  
- ✅/❌ Vision Capabilities status
- ✅/❌ Knowledge Base status
- ✅/❌ MCP Servers status
- ✅/❌ Web Automation status

### View Query History
```bash
🤖 IRIS> /history
```

Shows your recent queries and their success status.

## 🧠 How IRIS Works

### Intelligent Query Routing
IRIS automatically analyzes your query and routes it to the best combination of:

1. **Vision Queries** → MiMo-VL-7B + OCR engines
2. **Insurance Queries** → Insurance Agent + Advanced Models
3. **Research Queries** → Honest AI + Giga Agent
4. **Web Tasks** → Web Vision Agent + Browser automation
5. **Complex Tasks** → Multiple agents working in parallel

### Response Aggregation
IRIS combines responses from multiple sources:
- **Best Single**: Uses the highest confidence response
- **Consensus**: Aggregates multiple model responses
- **Parallel**: Runs multiple agents simultaneously
- **Specialized**: Routes to domain-specific agents

### Multi-Modal Processing
IRIS can handle:
- **Text**: Natural language queries and responses
- **Images**: Document analysis, OCR, visual understanding
- **Web**: Browser automation and visual navigation
- **Files**: PDF, images, documents processing

## 🔧 Advanced Configuration

### Environment Variables
Set these for enhanced functionality:
```bash
export OPENAI_API_KEY="your_openai_key"
export ANTHROPIC_API_KEY="your_anthropic_key"
export HUGGINGFACE_TOKEN="your_hf_token"
export ELEVENLABS_API_KEY="your_elevenlabs_key"
```

### Custom Configuration
Edit `iris_dashboard.py` to modify:
- Response strategies
- Model preferences
- Timeout settings
- Cache configuration

## 🚨 Troubleshooting

### Common Issues

#### 1. "IRIS system not fully available"
```bash
# Install missing dependencies
pip install -r requirements.txt

# Install vision dependencies
pip install opencv-python easyocr pytesseract

# Install browser automation
playwright install
```

#### 2. Vision capabilities not working
```bash
# Install OpenCV
pip install opencv-python

# Install OCR engines
pip install easyocr pytesseract

# For macOS, install Tesseract
brew install tesseract
```

#### 3. Web automation failing
```bash
# Install browser drivers
playwright install

# Or use Selenium
pip install selenium webdriver-manager
```

#### 4. Advanced models not responding
- Check API keys in environment variables
- Verify internet connection
- Check model availability status

### Getting Help

1. **In CLI**: Type `/help` for commands
2. **System Status**: Type `/status` to check components
3. **Logs**: Check `iris_dashboard.log` for detailed errors
4. **History**: Type `/history` to see previous queries

## 🎉 Success Examples

### Example Session
```bash
🤖 IRIS> What is the difference between term and whole life insurance?

🔍 Processing: What is the difference between term and whole life insurance?
🏢 Processing with insurance agents...

============================================================
📋 IRIS RESPONSE
============================================================
✅ Success: Term life insurance provides coverage for a specific period (term) at lower premiums, while whole life insurance provides permanent coverage with a cash value component that builds over time. Term is ideal for temporary needs like mortgage protection, while whole life serves as both insurance and investment for lifelong coverage.

🎯 Confidence: 0.92
⚡ Processing Time: 2.34s
🔧 Method: insurance_specialized
🤖 Models Used: honest_ai, detail_flow
📚 Sources: traditional_insurance_agent, advanced_models
============================================================

🤖 IRIS> /image policy.pdf Extract the policy number

🖼️ Analyzing image: policy.pdf
Query: Extract the policy number

🔍 Processing with vision capabilities...

============================================================
📋 IRIS RESPONSE
============================================================
✅ Success: Policy Number: POL-2024-789456. The document also contains policyholder information for Sarah Johnson, with coverage effective from January 1, 2024, for $500,000 term life insurance.

🎯 Confidence: 0.89
⚡ Processing Time: 3.12s
🔧 Method: vision_analysis
🤖 Models Used: mimo_vl_7b
============================================================
```

## 🏆 IRIS Capabilities Summary

✅ **Advanced AI Models**: 5 state-of-the-art models working together
✅ **Vision Processing**: Real image analysis, OCR, document understanding
✅ **Web Automation**: Browser control with visual recognition
✅ **Insurance Expertise**: Specialized knowledge and processing
✅ **Multi-Modal**: Text, image, and web interaction
✅ **Intelligent Routing**: Automatic selection of best processing method
✅ **Response Aggregation**: Combines multiple sources for accuracy
✅ **Real-Time Processing**: Fast, efficient query handling
✅ **Comprehensive Logging**: Full audit trail and history
✅ **Easy Interface**: Simple CLI and dashboard access

---

**🤖 IRIS is ready to assist you with any insurance, research, analysis, or automation task!**

For more advanced usage, see the technical documentation in the codebase.
