import os
import json
import logging
from cryptography.fernet import <PERSON><PERSON><PERSON>
from typing import Dict, Optional

logger = logging.getLogger(__name__)

class SecureCredentialsUpdate:
    """Utility to update secure credentials"""
    
    def __init__(self, credentials_file="data/credentials/secure_creds.enc"):
        self.credentials_file = credentials_file
        self.key = self._load_or_generate_key()
        self.fernet = Fernet(self.key)
        self._ensure_directory()

    def _ensure_directory(self):
        """Ensure credentials directory exists"""
        os.makedirs(os.path.dirname(self.credentials_file), exist_ok=True)

    def _load_or_generate_key(self):
        """Load existing key or generate new one"""
        key_file = "data/credentials/key.key"
        os.makedirs(os.path.dirname(key_file), exist_ok=True)
        
        if os.path.exists(key_file):
            with open(key_file, 'rb') as f:
                return f.read()
        else:
            key = Fernet.generate_key()
            with open(key_file, 'wb') as f:
                f.write(key)
            return key

    def update_credentials(self):
        """Update credentials with latest carrier information"""
        credentials = {
            "sandra": {
                "name": "<PERSON>",
                "fl_license": "WID6649923",
                "carriers": {
                    "uhc": {
                        "username": "sGarcia62858",
                        "password": "Jar65*-HyaR#WA9",
                        "website": "jarvys.com"
                    },
                    "mutual_of_omaha": {
                        "username": "SG1959PE",
                        "password": "GodisSoGood!777",
                        "phone": "(*************"
                    },
                    "americo": {
                        "username": "SG1959PE",
                        "password": "GodisSoGood",
                        "phone": "(*************"
                    },
                    "aetna": {
                        "username": "Medicare@me",
                        "password": "Medicare1128@65",
                        "website": "Producerworld.com",
                        "phone": "(*************"
                    },
                    "cigna": {
                        "username": "sandybeach23",
                        "password": "NewPWforCigna",
                        "writing_id": "664357"
                    },
                    "healthsherpa": {
                        "username": "<EMAIL>",
                        "password": ""
                    },
                    "thinkagent": {
                        "username": "SencionGarciaS4923",
                        "password": "Think@65$$"
                    },
                    "crump": {
                        "id": "30037542",
                        "password": "CrumpedUp$66",
                        "website": "www.crump.com"
                    },
                    "ffl": {
                        "username": "Sandybeach23",
                        "password": "Bibleversr@66",
                        "website": "https://ww3.familyfirstlife.com/"
                    }
                }
            },
            "justine": {
                "name": "Justine",
                "fl_license": "G199267",
                "npn": "21424617",
                "dice_login": {
                    "username": "Jaylove90",
                    "password": "Make$2025$"
                },
                "email": "<EMAIL>",
                "phone": "************",
                "carriers": {
                    "ffl": {
                        "email": "<EMAIL>",
                        "password": "Make$2025$"
                    },
                    "americo": {
                        "username": "<EMAIL>",
                        "password": "Make$2025$"
                    },
                    "mutual_of_omaha": {
                        "username": "<EMAIL>",
                        "password": "Make$2025$"
                    },
                    "crump": {
                        "email": "<EMAIL>",
                        "password": "Make$2025$"
                    }
                }
            }
        }
        
        # Encrypt and save credentials
        encrypted_data = self.fernet.encrypt(json.dumps(credentials).encode())
        with open(self.credentials_file, 'wb') as f:
            f.write(encrypted_data)
            
        logger.info("Credentials updated successfully")
        return True

def main():
    """Update carrier credentials"""
    logging.basicConfig(level=logging.INFO)
    
    try:
        updater = SecureCredentialsUpdate()
        success = updater.update_credentials()
        
        if success:
            logger.info("Carrier credentials have been updated successfully")
            logger.info("You can now run the carrier integration tests")
        else:
            logger.error("Failed to update credentials")
            
    except Exception as e:
        logger.error(f"Error updating credentials: {str(e)}")

if __name__ == "__main__":
    main()