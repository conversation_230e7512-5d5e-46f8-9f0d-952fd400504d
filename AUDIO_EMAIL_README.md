# Audio Email System for <PERSON>

This system allows you to generate audio using Eleven Labs and send it via email to <PERSON> without requiring <PERSON><PERSON><PERSON>.

## Files

- `automated_audio_email.py`: Main Python script that generates audio and sends it via email
- `send_audio_email_to_paul.sh`: Shell script to run the automated process
- `generate_elevenlabs_audio.py`: Original script to generate audio (for reference)
- `send_audio_email.py`: Original script to send audio email (for reference)

## How to Use

### Option 1: Run the Shell Script (Recommended)

1. Make sure the shell script is executable:
   ```
   chmod +x send_audio_email_to_paul.sh
   ```

2. Run the shell script:
   ```
   ./send_audio_email_to_paul.sh
   ```

### Option 2: Run the Python Script Directly

```
python3 automated_audio_email.py
```

## Email Sending Methods

The system tries multiple methods to send the email, in this order:

1. **SMTP**: If you have set the `EMAIL_PASSWORD` environment variable, it will try to send the email using SMTP.
2. **Command Line**: On macOS, it will try to use the `mail` command to send the email.
3. **Default Email Client**: As a last resort, it will open your default email client with a pre-populated email.

## Environment Variables

You can customize the behavior by setting these environment variables:

```
# Email credentials
EMAIL_SENDER=<EMAIL>
EMAIL_PASSWORD=your_app_password_here

# Eleven Labs credentials
ELEVENLABS_API_KEY=***************************************************
ELEVEN_LABS_VOICE_ID=21m00Tcm4TlvDq8ikWAM

# Paul Edwards contact information
PAUL_FIRST_NAME=Paul
PAUL_LAST_NAME=Edwards
PAUL_EMAIL=<EMAIL>
PAUL_PRIMARY_PHONE=+***********
PAUL_SECONDARY_PHONE=+***********

# Agent information
AGENT_NAME=Sandra Smith
AGENT_AGENCY=Flo Faction Insurance
AGENT_EMAIL=<EMAIL>
AGENT_WEBSITE=https://www.flofaction.com/insurance
```

## Gmail App Password Setup

If you're using Gmail, you'll need to create an App Password:

1. Go to your Google Account settings
2. Navigate to Security > 2-Step Verification
3. At the bottom, click on "App passwords"
4. Select "Mail" and "Other (Custom name)"
5. Enter a name like "Flo Faction Email System"
6. Click "Generate"
7. Use the generated 16-character password as your `EMAIL_PASSWORD`

## Troubleshooting

### Audio Generation Issues

- Check that your Eleven Labs API key is valid
- Ensure you have internet connectivity
- Check the console output for specific error messages

### Email Sending Issues

- If SMTP fails, check your email credentials
- For Gmail, make sure you're using an App Password, not your regular password
- If all methods fail, the script will guide you through opening your default email client

## Notes

- The audio file is saved in the `audio/` directory
- The script automatically creates this directory if it doesn't exist
- The default voice is "Rachel" (female voice)
