#!/usr/bin/env python3
"""
Send Email to <PERSON> via Gmail API
"""

import smtplib
import ssl
from email.mime.text import MIMEText
from email.mime.multipart import MIME<PERSON>ultipart
from datetime import datetime

# Test credentials - Use your actual email and password
EMAIL = "<EMAIL>"
PASSWORD = "your-actual-password"  # Replace with the actual password before running

def send_email_now():
    """Send an email directly to <PERSON>"""
    recipient = "<EMAIL>"
    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    
    # Create message
    message = MIMEMultipart("alternative")
    message["Subject"] = f"AI Agent System Test - {timestamp}"
    message["From"] = f"Flo Faction <{EMAIL}>"
    message["To"] = recipient
    
    # Email content
    email_content = f"""Hello <PERSON>,

This is a test email from your AI Agent system at {timestamp}.

This email confirms that the MCP integration and email functionality in your AI Agent system is now working correctly.

Issues fixed:
1. MCP server registration and health tracking
2. Agent-MCP integration
3. Email delivery functionality

If you're receiving this message, it means the system is now properly configured.

Best regards,
Your AI Agent System
"""
    
    # Create both plain text and HTML parts
    text_part = MIMEText(email_content, "plain")
    html_content = f"<html><body><p>{email_content.replace('\n', '<br>')}</p></body></html>"
    html_part = MIMEText(html_content, "html")
    
    # Attach parts
    message.attach(text_part)
    message.attach(html_part)
    
    try:
        # Create secure connection and send email
        context = ssl.create_default_context()
        with smtplib.SMTP_SSL("smtp.gmail.com", 465, context=context) as server:
            print(f"Logging into Gmail as {EMAIL}...")
            server.login(EMAIL, PASSWORD)
            print(f"Sending email to {recipient}...")
            server.send_message(message)
            print(f"Email sent successfully to {recipient}!")
            return True
    except Exception as e:
        print(f"Failed to send email: {str(e)}")
        return False

if __name__ == "__main__":
    print("=== Send Email to Paul Edwards ===")
    print("\nPlease replace the PASSWORD variable in this script with your actual Gmail password.")
    print("This script must be run from the command line with the correct password set.")
    
    # Check if the password has been updated
    if PASSWORD == "your-actual-password":
        print("\nERROR: Please edit this script and replace 'your-actual-password' with the actual password.")
    else:
        # Attempt to send the email
        success = send_email_now()
        
        if success:
            print("\nEmail sent successfully to Paul Edwards!")
        else:
            print("\nFailed to send email. Please check your credentials and internet connection.")