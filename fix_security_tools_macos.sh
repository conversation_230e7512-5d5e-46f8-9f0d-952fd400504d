#!/bin/bash

# <PERSON><PERSON>t to fix security tools that had installation issues
# Focus on <PERSON> the <PERSON>per, <PERSON><PERSON><PERSON>, OWASP ZAP, sqlmap, and Social Engineer <PERSON><PERSON>it

echo "════════════════════════════════════════════════════════════════════"
echo "  FIXING SECURITY TOOLS INSTALLATION"
echo "════════════════════════════════════════════════════════════════════"

# Function to check if a command exists
command_exists() {
  command -v "$1" >/dev/null 2>&1
}

# Function to check if a directory exists
directory_exists() {
  [ -d "$1" ]
}

# Install John the Ripper
fix_john_the_ripper() {
  echo "→ Fixing John the Ripper installation..."
  if ! command_exists john; then
    brew install john || {
      echo "Trying alternative installation method for John the Ripper..."
      mkdir -p ~/tools
      cd ~/tools
      if [ ! -d "john" ]; then
        git clone https://github.com/openwall/john -b bleeding-jumbo john
        cd john/src
        ./configure && make -s clean && make -sj4
        echo 'export PATH=$PATH:~/tools/john/run' >> ~/.zshrc
        echo 'export PATH=$PATH:~/tools/john/run' >> ~/.bash_profile
        source ~/.zshrc 2>/dev/null || source ~/.bash_profile 2>/dev/null
      fi
    }
  fi
  if command_exists john; then
    echo "✓ Success: John the Ripper is now installed"
  else
    echo "✗ Error: Failed to install <PERSON> the Ripper"
  fi
}

# Install Kismet
fix_kismet() {
  echo "→ Fixing Kismet installation..."
  if ! command_exists kismet; then
    brew install kismet || {
      echo "Trying alternative installation method for Kismet..."
      mkdir -p ~/tools
      cd ~/tools
      if [ ! -d "kismet" ]; then
        git clone https://github.com/kismetwireless/kismet.git
        cd kismet
        ./configure && make -j4 && sudo make install
      fi
    }
  fi
  if command_exists kismet; then
    echo "✓ Success: Kismet is now installed"
  else
    echo "✗ Error: Failed to install Kismet"
  fi
}

# Install OWASP ZAP
fix_owasp_zap() {
  echo "→ Fixing OWASP ZAP installation..."
  if ! command_exists zap.sh; then
    brew install --cask owasp-zap || {
      echo "Trying alternative installation method for OWASP ZAP..."
      mkdir -p ~/tools
      cd ~/tools
      if [ ! -d "ZAP" ]; then
        curl -L "https://github.com/zaproxy/zaproxy/releases/download/v2.14.0/ZAP_2.14.0_macOS.dmg" -o zaproxy.dmg
        hdiutil attach zaproxy.dmg
        cp -R "/Volumes/OWASP ZAP 2.14.0/OWASP ZAP.app" /Applications/
        hdiutil detach "/Volumes/OWASP ZAP 2.14.0"
        rm zaproxy.dmg
      fi
    }
  fi
  if [ -d "/Applications/OWASP ZAP.app" ]; then
    echo "✓ Success: OWASP ZAP is now installed"
  else
    echo "✗ Error: Failed to install OWASP ZAP"
  fi
}

# Install sqlmap
fix_sqlmap() {
  echo "→ Fixing sqlmap installation..."
  if ! command_exists sqlmap; then
    brew install sqlmap || {
      echo "Trying alternative installation method for sqlmap..."
      mkdir -p ~/tools
      cd ~/tools
      if [ ! -d "sqlmap" ]; then
        git clone --depth 1 https://github.com/sqlmapproject/sqlmap.git
        echo 'export PATH=$PATH:~/tools/sqlmap' >> ~/.zshrc
        echo 'export PATH=$PATH:~/tools/sqlmap' >> ~/.bash_profile
        source ~/.zshrc 2>/dev/null || source ~/.bash_profile 2>/dev/null
      fi
    }
  fi
  if command_exists sqlmap || [ -d ~/tools/sqlmap ]; then
    echo "✓ Success: sqlmap is now installed"
  else
    echo "✗ Error: Failed to install sqlmap"
  fi
}

# Fix Social Engineer Toolkit
fix_set() {
  echo "→ Fixing Social Engineer Toolkit installation..."
  if ! directory_exists ~/tools/set; then
    mkdir -p ~/tools
    cd ~/tools
    git clone https://github.com/trustedsec/social-engineer-toolkit set
    cd set
    pip3 install -r requirements.txt
    echo "alias setoolkit='cd ~/tools/set && python3 setoolkit'" >> ~/.zshrc
    echo "alias setoolkit='cd ~/tools/set && python3 setoolkit'" >> ~/.bash_profile
    source ~/.zshrc 2>/dev/null || source ~/.bash_profile 2>/dev/null
  fi
  if directory_exists ~/tools/set; then
    echo "✓ Success: Social Engineer Toolkit is now installed"
  else
    echo "✗ Error: Failed to install Social Engineer Toolkit"
  fi
}

# Run the fix functions
fix_john_the_ripper
fix_kismet
fix_owasp_zap
fix_sqlmap
fix_set

echo "════════════════════════════════════════════════════════════════════"
echo "  INSTALLATION FIX COMPLETED!"
echo "════════════════════════════════════════════════════════════════════"
echo ""
echo "Attempted to fix the following tools:"
echo "  • John the Ripper"
echo "  • Kismet"
echo "  • OWASP ZAP"
echo "  • sqlmap"
echo "  • Social Engineer Toolkit (SET)"
echo ""
echo "Please check the output above to verify which tools were successfully fixed."
echo "For any tools that still have issues, you may need to install them manually."