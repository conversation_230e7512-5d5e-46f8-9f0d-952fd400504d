#!/usr/bin/env python3
"""
Workflow MCP Server

This module provides MCP server functionality for workflow automation and task scheduling.
"""

import os
import sys
import json
import logging
import asyncio
import argparse
import time
import requests
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List, Optional, Union, Any
from aiohttp import web

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s [%(levelname)s] %(name)s: %(message)s",
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler("workflow_server.log")
    ]
)
logger = logging.getLogger("workflow-server")

class WorkflowServer:
    """MCP Server implementation for workflow automation"""

    def __init__(self, host: str = "localhost", port: int = 8087):
        self.host = host
        self.port = port
        self.app = web.Application()
        self.register_routes()
        self.workflows = {}
        self.tasks = {}
        self.scheduled_tasks = {}

    def register_routes(self):
        """Register API routes"""
        self.app.router.add_get("/", self.handle_root)
        self.app.router.add_get("/health", self.handle_health)
        self.app.router.add_get("/capabilities", self.handle_capabilities)
        self.app.router.add_post("/workflow/create", self.handle_create_workflow)
        self.app.router.add_post("/workflow/execute", self.handle_execute_workflow)
        self.app.router.add_post("/task/schedule", self.handle_schedule_task)
        self.app.router.add_get("/task/list", self.handle_list_tasks)

    async def handle_root(self, request):
        """Handle root endpoint"""
        return web.json_response({
            "name": "Workflow MCP Server",
            "version": "1.0.0",
            "status": "running"
        })

    async def handle_health(self, request):
        """Handle health check endpoint"""
        return web.json_response({
            "status": "healthy",
            "timestamp": asyncio.get_event_loop().time()
        })

    async def handle_capabilities(self, request):
        """Handle capabilities endpoint"""
        return web.json_response({
            "capabilities": [
                "workflow-automation",
                "task-scheduling",
                "process-orchestration",
                "event-handling"
            ]
        })

    async def handle_create_workflow(self, request):
        """Handle workflow creation endpoint"""
        try:
            data = await request.json()
            workflow_id = data.get("id", f"workflow_{len(self.workflows) + 1}")
            name = data.get("name", "Unnamed Workflow")
            steps = data.get("steps", [])

            # Create the workflow
            self.workflows[workflow_id] = {
                "id": workflow_id,
                "name": name,
                "steps": steps,
                "created_at": datetime.now().isoformat(),
                "status": "created"
            }

            logger.info(f"Created workflow: {name} ({workflow_id}) with {len(steps)} steps")

            return web.json_response({
                "status": "success",
                "workflow_id": workflow_id,
                "message": f"Workflow '{name}' created successfully"
            })

        except Exception as e:
            logger.error(f"Error creating workflow: {e}")
            return web.json_response({
                "status": "error",
                "message": str(e)
            }, status=500)

    async def handle_execute_workflow(self, request):
        """Handle workflow execution endpoint"""
        try:
            data = await request.json()
            workflow_id = data.get("workflow_id", "")

            if workflow_id not in self.workflows:
                return web.json_response({
                    "status": "error",
                    "message": f"Workflow with ID '{workflow_id}' not found"
                }, status=404)

            workflow = self.workflows[workflow_id]

            # Update workflow status
            workflow["status"] = "running"
            workflow["started_at"] = datetime.now().isoformat()

            # Execute workflow steps
            for i, step in enumerate(workflow["steps"]):
                try:
                    step_type = step.get("type", "unknown")
                    action = step.get("action", "")
                    params = step.get("params", {})
                    schedule = step.get("schedule", "now")

                    logger.info(f"Processing step {i+1}/{len(workflow['steps'])}: {step_type} - {action}")

                    # Handle scheduling
                    if schedule == "now":
                        # Execute immediately
                        await self._execute_step(step_type, action, params)
                    else:
                        # Schedule for later
                        task_id = f"{workflow_id}_step_{i+1}"
                        schedule_time = self._parse_schedule(schedule)

                        # Create the task
                        self.tasks[task_id] = {
                            "id": task_id,
                            "name": f"{workflow['name']} - Step {i+1}",
                            "schedule_time": schedule_time.isoformat(),
                            "action": {
                                "type": step_type,
                                "action": action,
                                "params": params
                            },
                            "created_at": datetime.now().isoformat(),
                            "status": "scheduled"
                        }

                        logger.info(f"Scheduled step {i+1} for {schedule_time.isoformat()}")
                except Exception as step_error:
                    logger.error(f"Error executing step {i+1}: {step_error}")

            # Update workflow status
            workflow["status"] = "completed"
            workflow["completed_at"] = datetime.now().isoformat()

            logger.info(f"Executed workflow: {workflow['name']} ({workflow_id})")

            return web.json_response({
                "status": "success",
                "workflow_id": workflow_id,
                "message": f"Workflow '{workflow['name']}' executed successfully"
            })

        except Exception as e:
            logger.error(f"Error executing workflow: {e}")
            return web.json_response({
                "status": "error",
                "message": str(e)
            }, status=500)

    async def handle_schedule_task(self, request):
        """Handle task scheduling endpoint"""
        try:
            data = await request.json()
            task_id = data.get("id", f"task_{len(self.tasks) + 1}")
            name = data.get("name", "Unnamed Task")
            schedule_time = data.get("schedule_time", (datetime.now() + timedelta(hours=1)).isoformat())
            action = data.get("action", {})

            # Create the task
            self.tasks[task_id] = {
                "id": task_id,
                "name": name,
                "schedule_time": schedule_time,
                "action": action,
                "created_at": datetime.now().isoformat(),
                "status": "scheduled"
            }

            logger.info(f"Scheduled task: {name} ({task_id}) for {schedule_time}")

            return web.json_response({
                "status": "success",
                "task_id": task_id,
                "message": f"Task '{name}' scheduled successfully"
            })

        except Exception as e:
            logger.error(f"Error scheduling task: {e}")
            return web.json_response({
                "status": "error",
                "message": str(e)
            }, status=500)

    async def handle_list_tasks(self, request):
        """Handle task listing endpoint"""
        try:
            status_filter = request.query.get("status", None)

            # Filter tasks by status if specified
            if status_filter:
                filtered_tasks = {k: v for k, v in self.tasks.items() if v["status"] == status_filter}
            else:
                filtered_tasks = self.tasks

            return web.json_response({
                "status": "success",
                "tasks": list(filtered_tasks.values())
            })

        except Exception as e:
            logger.error(f"Error listing tasks: {e}")
            return web.json_response({
                "status": "error",
                "message": str(e)
            }, status=500)

    async def start(self):
        """Start the server"""
        runner = web.AppRunner(self.app)
        await runner.setup()
        site = web.TCPSite(runner, self.host, self.port)

        logger.info(f"Starting Workflow MCP Server on {self.host}:{self.port}")
        await site.start()

        # Start task scheduler
        asyncio.create_task(self._task_scheduler())

        return site

    async def _parse_schedule(self, schedule):
        """Parse schedule string into datetime"""
        now = datetime.now()

        if schedule == "now":
            return now

        if schedule.startswith("+"):
            # Relative time (e.g., "+1 hour", "+2 days")
            parts = schedule[1:].strip().split()
            if len(parts) != 2:
                raise ValueError(f"Invalid schedule format: {schedule}")

            amount = int(parts[0])
            unit = parts[1].lower()

            if unit in ["second", "seconds"]:
                return now + timedelta(seconds=amount)
            elif unit in ["minute", "minutes"]:
                return now + timedelta(minutes=amount)
            elif unit in ["hour", "hours"]:
                return now + timedelta(hours=amount)
            elif unit in ["day", "days"]:
                return now + timedelta(days=amount)
            elif unit in ["week", "weeks"]:
                return now + timedelta(weeks=amount)
            else:
                raise ValueError(f"Unknown time unit: {unit}")
        else:
            # Absolute time
            return datetime.fromisoformat(schedule)

    async def _execute_step(self, step_type, action, params):
        """Execute a workflow step"""
        logger.info(f"Executing step: {step_type} - {action}")

        try:
            if step_type == "email" and action == "send_email":
                await self._send_email(params)
            elif step_type == "sms" and action == "send_sms":
                await self._send_sms(params)
            elif step_type == "call" and action == "make_call":
                await self._make_call(params)
            else:
                logger.warning(f"Unknown step type or action: {step_type} - {action}")

            logger.info(f"Step executed successfully: {step_type} - {action}")
            return True
        except Exception as e:
            logger.error(f"Error executing step: {e}")
            return False

    async def _send_email(self, params):
        """Send an email"""
        to = params.get("to", "")
        subject = params.get("subject", "")
        body = params.get("body", "")

        if not to or not subject or not body:
            raise ValueError("Missing required parameters for email")

        logger.info(f"Sending email to {to}: {subject}")

        # Make API call to communication server
        response = requests.post(
            "http://localhost:8084/send_email",
            json={
                "to": to,
                "subject": subject,
                "body": body
            }
        )

        if response.status_code == 200:
            logger.info(f"Email sent successfully to {to}")
            return True
        else:
            logger.error(f"Error sending email: {response.text}")
            return False

    async def _send_sms(self, params):
        """Send an SMS"""
        to = params.get("to", "")
        message = params.get("message", "")

        if not to or not message:
            raise ValueError("Missing required parameters for SMS")

        logger.info(f"Sending SMS to {to}")

        # Make API call to communication server
        response = requests.post(
            "http://localhost:8084/send_sms",
            json={
                "to": to,
                "message": message
            }
        )

        if response.status_code == 200:
            logger.info(f"SMS sent successfully to {to}")
            return True
        else:
            logger.error(f"Error sending SMS: {response.text}")
            return False

    async def _make_call(self, params):
        """Make a call"""
        to = params.get("to", "")
        message = params.get("message", "")

        if not to or not message:
            raise ValueError("Missing required parameters for call")

        logger.info(f"Making call to {to}")

        # Make API call to communication server
        response = requests.post(
            "http://localhost:8084/make_call",
            json={
                "to": to,
                "message": message
            }
        )

        if response.status_code == 200:
            logger.info(f"Call initiated successfully to {to}")
            return True
        else:
            logger.error(f"Error making call: {response.text}")
            return False

    async def _task_scheduler(self):
        """Background task scheduler"""
        while True:
            try:
                now = datetime.now()

                # Check for tasks that need to be executed
                for task_id, task in list(self.tasks.items()):
                    if task["status"] == "scheduled":
                        schedule_time = datetime.fromisoformat(task["schedule_time"])
                        if now >= schedule_time:
                            # Execute the task
                            logger.info(f"Executing scheduled task: {task['name']} ({task_id})")

                            # Update task status
                            task["status"] = "running"
                            task["started_at"] = now.isoformat()

                            # Execute task action
                            action = task["action"]
                            step_type = action.get("type", "unknown")
                            action_name = action.get("action", "")
                            params = action.get("params", {})

                            await self._execute_step(step_type, action_name, params)

                            # Update task status
                            task["status"] = "completed"
                            task["completed_at"] = datetime.now().isoformat()

                            logger.info(f"Completed task: {task['name']} ({task_id})")
            except Exception as e:
                logger.error(f"Error in task scheduler: {e}")

            # Sleep for a short time before checking again
            await asyncio.sleep(1)

def main():
    """Main entry point"""
    parser = argparse.ArgumentParser(description="Workflow MCP Server")
    parser.add_argument("--host", default="localhost", help="Host to bind to")
    parser.add_argument("--port", type=int, default=8087, help="Port to bind to")
    args = parser.parse_args()

    server = WorkflowServer(host=args.host, port=args.port)

    loop = asyncio.get_event_loop()
    try:
        loop.run_until_complete(server.start())
        loop.run_forever()
    except KeyboardInterrupt:
        logger.info("Server stopped by keyboard interrupt")
    finally:
        loop.close()

if __name__ == "__main__":
    main()
