#!/usr/bin/env python3
"""OpenCV Fallback - Simple computer vision operations"""

import numpy as np
from PIL import Image
import io

# Mock cv2 module for basic functionality
class CV2Fallback:
    IMREAD_COLOR = 1
    IMREAD_GRAYSCALE = 0
    
    @staticmethod
    def imread(path, flags=1):
        """Read image file"""
        try:
            img = Image.open(path)
            if flags == 0:  # Grayscale
                img = img.convert('L')
                return np.array(img)
            else:  # Color
                img = img.convert('RGB')
                return np.array(img)[:, :, ::-1]  # RGB to BGR
        except:
            return None
    
    @staticmethod
    def imwrite(path, img):
        """Write image file"""
        try:
            if len(img.shape) == 3:
                img_pil = Image.fromarray(img[:, :, ::-1])  # BGR to RGB
            else:
                img_pil = Image.fromarray(img)
            img_pil.save(path)
            return True
        except:
            return False
    
    @staticmethod
    def resize(img, size):
        """Resize image"""
        img_pil = Image.fromarray(img)
        img_pil = img_pil.resize(size)
        return np.array(img_pil)

# Create cv2 module
import sys
sys.modules['cv2'] = CV2Fallback()
