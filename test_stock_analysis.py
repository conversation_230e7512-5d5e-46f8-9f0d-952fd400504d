"""
Test stock analysis and trading strategies
"""

import logging
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import yfinance as yf
from simple_trading_agent import SimpleTradingAgent, TradingParameters
from trading_visualizer import TradingVisualizer

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def analyze_stock(symbol: str, period: str = "1y") -> dict:
    """Analyze stock performance and patterns"""
    try:
        # Get historical data
        data = yf.download(symbol, period=period)
        
        # Calculate basic metrics
        returns = data['Close'].pct_change()
        
        analysis = {
            "symbol": symbol,
            "period": period,
            "current_price": data['Close'].iloc[-1],
            "avg_daily_volume": data['Volume'].mean(),
            "volatility": returns.std() * np.sqrt(252),  # Annualized
            "avg_daily_return": returns.mean(),
            "sharpe_ratio": (returns.mean() / returns.std()) * np.sqrt(252),
            "max_drawdown": calculate_max_drawdown(data['Close'])
        }
        
        # Add trend analysis
        analysis.update(analyze_trend(data))
        
        return analysis
        
    except Exception as e:
        logger.error(f"Error analyzing {symbol}: {str(e)}")
        return None

def calculate_max_drawdown(prices: pd.Series) -> float:
    """Calculate maximum drawdown percentage"""
    peak = prices.expanding(min_periods=1).max()
    drawdown = (prices - peak) / peak
    return drawdown.min()

def analyze_trend(data: pd.DataFrame) -> dict:
    """Analyze price trends and patterns"""
    
    # Calculate moving averages
    data['MA20'] = data['Close'].rolling(window=20).mean()
    data['MA50'] = data['Close'].rolling(window=50).mean()
    data['MA200'] = data['Close'].rolling(window=200).mean()
    
    current_price = data['Close'].iloc[-1]
    
    trend_analysis = {
        "trend_20d": "upward" if current_price > data['MA20'].iloc[-1] else "downward",
        "trend_50d": "upward" if current_price > data['MA50'].iloc[-1] else "downward",
        "trend_200d": "upward" if current_price > data['MA200'].iloc[-1] else "downward",
        "golden_cross": data['MA50'].iloc[-1] > data['MA200'].iloc[-1],
        "death_cross": data['MA50'].iloc[-1] < data['MA200'].iloc[-1]
    }
    
    return trend_analysis

def test_trading_strategy(symbol: str, strategy: str = "hybrid"):
    """Test trading strategy on a stock"""
    try:
        logger.info(f"\nTesting {strategy} strategy on {symbol}")
        
        # Initialize parameters
        params = TradingParameters(
            symbol=symbol,
            timeframe="1d",
            initial_capital=100000,
            risk_per_trade=0.02,
            stop_loss=0.02,
            take_profit=0.04,
            strategy=strategy
        )
        
        # Create trading agent
        agent = SimpleTradingAgent(params)
        
        # Get training data (1 year)
        end_date = datetime.now()
        start_date = end_date - timedelta(days=365)
        data = yf.download(symbol, start=start_date, end=end_date)
        
        # Train model
        agent.train_model(data)
        
        # Simulate trading
        for i in range(len(data)-1):
            current_data = data.iloc[:i+1]
            analysis = agent.analyze_market(current_data)
            
            # Execute trades
            agent.execute_trade(analysis['signal'], current_data)
            
            # Update positions
            current_price = current_data['Close'].iloc[-1]
            agent.update_positions(current_price)
            
        # Get performance metrics
        metrics = agent.get_performance_metrics()
        
        return {
            "symbol": symbol,
            "strategy": strategy,
            "metrics": metrics,
            "trade_history": agent.trade_history
        }
        
    except Exception as e:
        logger.error(f"Error testing strategy on {symbol}: {str(e)}")
        return None

def main():
    """Run stock analysis and strategy tests"""
    
    # Test stocks
    stocks = [
        "AAPL",  # Large cap tech
        "JPM",   # Financial sector
        "XOM",   # Energy sector
        "PG",    # Consumer staples
        "SPY"    # S&P 500 ETF
    ]
    
    # Trading strategies to test
    strategies = ["ml", "technical", "hybrid"]
    
    results = {
        "analysis": {},
        "trading": {}
    }
    
    # Create visualizer
    visualizer = TradingVisualizer()
    
    # Analyze stocks
    logger.info("Analyzing stocks...")
    for symbol in stocks:
        analysis = analyze_stock(symbol)
        if analysis:
            results["analysis"][symbol] = analysis
            logger.info(f"\n{symbol} Analysis:")
            for metric, value in analysis.items():
                logger.info(f"{metric}: {value}")
                
    # Test trading strategies
    logger.info("\nTesting trading strategies...")
    for symbol in stocks:
        results["trading"][symbol] = {}
        for strategy in strategies:
            strategy_result = test_trading_strategy(symbol, strategy)
            if strategy_result:
                results["trading"][symbol][strategy] = strategy_result
                
                logger.info(f"\n{symbol} - {strategy} Strategy Results:")
                for metric, value in strategy_result["metrics"].items():
                    logger.info(f"{metric}: {value}")
                    
    # Generate visualizations
    logger.info("\nGenerating performance visualizations...")
    
    # Prepare data for visualization
    strategy_results = {}
    for symbol in results["trading"]:
        for strategy in strategies:
            key = f"{symbol}_{strategy}"
            metrics = results["trading"][symbol][strategy]["metrics"]
            strategy_results[key] = metrics
            
    visualizer.plot_strategy_performance(strategy_results)
    
    # Save results
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    results_df = pd.DataFrame(strategy_results).T
    results_df.to_csv(f"test_results/strategy_results_{timestamp}.csv")
    
    logger.info("\nAnalysis complete! Results saved to test_results directory.")

if __name__ == "__main__":
    main()