"""
FastAPI Server for Agentic Workflow Orchestration
Exposes HTTP endpoints for workflow submission, monitoring, and status.
Integrates with agent_integration.py and enhanced_agent_interface.py
"""

import asyncio
from fastapi import FastAPI, Request, BackgroundTasks, UploadFile, File, Form
from fastapi.responses import <PERSON><PERSON><PERSON><PERSON>po<PERSON>, HTMLResponse
from fastapi.templating import Jin<PERSON>2Templates
from pydantic import BaseModel
import uvicorn
import logging
from agent_integration import AgentIntegration, Agent<PERSON>ole
from agent_access_system import AgentAccessSystem

app = FastAPI(title="Agentic Workflow Orchestration API")
templates = Jinja2Templates(directory="templates")

# Global agent integration instance
integration = AgentIntegration()
agent_access = AgentAccessSystem()

# Pydantic models for request/response
class WorkflowRequest(BaseModel):
    workflow_type: str
    data: dict

@app.on_event("startup")
async def startup_event():
    await integration.initialize_agents()

@app.get("/")
def root(request: Request):
    return templates.TemplateResponse("dashboard.html", {"request": request})

@app.post("/submit_workflow")
async def submit_workflow(request: WorkflowRequest, background_tasks: BackgroundTasks):
    async def run_workflow():
        await integration.process_workflow(request.workflow_type, request.data)
    background_tasks.add_task(run_workflow)
    return {"status": "submitted", "workflow_type": request.workflow_type}

@app.get("/workflow_status/{workflow_id}")
def workflow_status(workflow_id: str):
    status = integration.get_workflow_status(workflow_id)
    if status:
        return status
    return JSONResponse(status_code=404, content={"error": "Workflow not found"})

@app.get("/agent_status/{agent}")
def agent_status(agent: str):
    try:
        role = AgentRole(agent)
    except Exception:
        return JSONResponse(status_code=400, content={"error": "Invalid agent role"})
    return integration.get_agent_status(role)

@app.get("/active_workflows")
def active_workflows():
    return integration.active_workflows

@app.post("/browser_takeover")
async def browser_takeover(browser_type: str = Form("opera-neon"), url: str = Form(None)):
    """
    Trigger browser takeover/control using Playwright (Operator-style).
    """
    result = agent_access.browser_takeover(browser_type, url)
    return {"success": result}

@app.post("/visual_gui_action")
async def visual_gui_action(prompt: str = Form(...), screenshot: UploadFile = File(...)):
    """
    Use UI-TARS 1.5 to predict and execute a GUI action based on a screenshot and prompt.
    """
    import tempfile
    with tempfile.NamedTemporaryFile(delete=False, suffix=".png") as tmp:
        tmp.write(await screenshot.read())
        tmp_path = tmp.name
    result = agent_access.visual_gui_action(tmp_path, prompt)
    return {"result": result}

if __name__ == "__main__":
    uvicorn.run("fastapi_server:app", host="0.0.0.0", port=8000, reload=True)
