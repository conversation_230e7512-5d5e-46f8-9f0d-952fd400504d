<!DOCTYPE html><style nonce="_mFUK2WgdRR57G5BxTpn1A">body{height:100%;margin:0;width:100%}@media (max-height:350px){.button{font-size:10px}.button-container{margin-top:16px}.button.primary-button,.button.primary-button:active,.button.primary-button:focus,.button.primary-button:hover{padding:4px 12px}.title-text{font-size:22px;line-height:24px}.subtitle-text{font-size:12px;line-height:18px}}@media (min-height:350px){.button{font-size:14px}.button-container{margin-top:16px}.button.primary-button,.button.primary-button:active,.button.primary-button:focus,.button.primary-button:hover{padding:12px 24px}.title-text{font-size:28px;line-height:36px}.subtitle-text{font-size:16px;line-height:24px}}.document-root{display:-webkit-box;display:-webkit-flex;display:-moz-box;display:-ms-flexbox;display:flex;inset:0;position:absolute}.error,.login,.request-storage-access{display:none}.error,.login,.request-storage-access,.too-many-login-redirects{margin:auto;padding:36px}.document-root.show-error .error,.document-root.show-login-page .login,.document-root.show-storage-access .request-storage-access,.too-many-login-redirects{-webkit-box-align:center;-webkit-align-items:center;-moz-box-align:center;-ms-flex-align:center;align-items:center;display:-webkit-box;display:-webkit-flex;display:-moz-box;display:-ms-flexbox;display:flex;-webkit-box-orient:vertical;-webkit-box-direction:normal;-webkit-flex-direction:column;-moz-box-orient:vertical;-moz-box-direction:normal;-ms-flex-direction:column;flex-direction:column}.button-container{display:-webkit-box;display:-webkit-flex;display:-moz-box;display:-ms-flexbox;display:flex;-webkit-flex-wrap:wrap;-ms-flex-wrap:wrap;flex-wrap:wrap;-webkit-box-pack:center;-webkit-justify-content:center;-moz-box-pack:center;-ms-flex-pack:center;justify-content:center}.button{border:none;cursor:pointer;color:#0b57d0;-webkit-flex-shrink:0;-ms-flex-negative:0;flex-shrink:0;font-family:Google Sans Text,Roboto,sans-serif;border-radius:100px;padding:12px;margin:0 8px;text-decoration:none}.button:hover{background-color:rgba(11,87,208,.078)}.button:active,.button:focus{background-color:rgba(11,87,208,.122)}.button.primary-button,.button.primary-button:active,.button.primary-button:focus,.button.primary-button:hover{background-color:#0b57d0;color:#fff}.button.primary-button:hover{box-shadow:0 1px 3px 1px rgba(0,0,0,.149),0 1px 2px 0 rgba(0,0,0,.302)}.icon{height:48px;margin-bottom:16px}.title-text{font-family:Google Sans,Roboto,sans-serif;text-align:center}.subtitle-text{font-family:Google Sans Text,Roboto,sans-serif;margin-top:16px;text-align:center}
/*# sourceMappingURL=style.css.map */</style><script nonce="rakKqX4j3DQKWLXkuDwV7g">'use strict';function h(a){var b=0;return function(){return b<a.length?{done:!1,value:a[b++]}:{done:!0}}}function k(a){var b=typeof Symbol!="undefined"&&Symbol.iterator&&a[Symbol.iterator];if(b)return b.call(a);if(typeof a.length=="number")return{next:h(a)};throw Error(String(a)+" is not an iterable or ArrayLike");};var l=["storage_access_granted","not_in_iframe","login_counter"];function m(a,b,c){c=c===void 0?"true":c;a=new URL(a);for(var d=0;d<l.length;d++)a.searchParams.delete(l[d]);a.searchParams.set(b,c);return a.toString()};/*

 Copyright The Closure Library Authors.
 SPDX-License-Identifier: Apache-2.0
*/
/*

 Copyright Google LLC
 SPDX-License-Identifier: Apache-2.0
*/
function n(){var a=new p,b=new q,c=document.getElementsByClassName("document-root")[0],d=this;this.g=new r;this.h=a;this.l=b;this.i=c;c.getElementsByClassName("accept-button")[0].addEventListener("click",function(){return void t(d)});c.getElementsByClassName("sign-in-button")[0].addEventListener("click",function(e){return void u(d,e)})}
function v(){var a=new n;w()?x()||typeof document.hasStorageAccess!=="function"||typeof document.requestStorageAccess!=="function"?y(a,"show-login-page"):a.h.hasStorageAccess().then(function(b){b?y(a,"show-login-page"):z().then(function(c){c==="prompt"?y(a,"show-storage-access"):c==="granted"?t(a):y(a,"show-error")})},function(){y(a,"show-error")}):A(a,window.location.href,"not_in_iframe")}
function A(a,b,c){c=c?m(b,c):b;if(a.g.get()){if(b=a.g.get())c=B(c),c=C(c),c!==void 0&&(b.action=c);a.g.submit()}else window.location.href===c?window.location.reload():(a=window.location,b=B(c)||D,b=C(b),b!==void 0&&(a.href=b))}function y(a,b){a.i.className="document-root "+b}function t(a){a.h.requestStorageAccess().then(function(){A(a,window.location.href,"storage_access_granted")},function(){y(a,"show-error")})}
function u(a,b){var c;if(b=(c=b.currentTarget)==null?void 0:c.getAttribute("data-popup-url")){var d=E(window,B(b)||D);F(a.l,function(){d&&d.close();var e=window.location.href;var f=(new URL(e)).searchParams,g=1;f.has("login_counter")&&(f=Number(f.get("login_counter")),isFinite(f)&&(g=f+1));e=m(e,"login_counter",String(g));A(a,e)})}};function G(a){this.g=a}G.prototype.toString=function(){return this.g};var D=new G("about:invalid#zClosurez");function H(a){this.j=a}function I(a){return new H(function(b){return b.substr(0,a.length+1).toLowerCase()===a+":"})}var J=[I("data"),I("http"),I("https"),I("mailto"),I("ftp"),new H(function(a){return/^[^:]*([/?#]|$)/.test(a)})];function B(a){var b=b===void 0?J:b;if(a instanceof G)return a;for(var c=0;c<b.length;++c){var d=b[c];if(d instanceof H&&d.j(a))return new G(a)}}var K=/^\s*(?!javascript:)(?:[\w+.-]+:|[^:/?#]*(?:[/?#]|$))/i;
function C(a){if(a instanceof G)if(a instanceof G)a=a.g;else throw Error("");else a=K.test(a)?a:void 0;return a};function E(a,b){b=C(b);return b!==void 0?a.open(b,"popupWindow","popup=yes,height=500,width=690"):null};function r(){}r.prototype.get=function(){return document.querySelector("form")};r.prototype.submit=function(){var a;(a=this.get())==null||a.submit()};function L(a){for(var b=k(document.cookie.split(";")),c=b.next();!c.done;c=b.next())if(c=c.value.split("="),c[0].trim()===a)return c[1]};function q(){this.h=["SAPISID","__Secure-1PAPISID","__Secure-3PAPISID"];this.g=void 0}function F(a,b){a.g&&clearInterval(a.g);for(var c={},d=k(a.h),e=d.next();!e.done;e=d.next())e=e.value,c[e]=L(e);a.g=setInterval(function(){a:{var f=k(a.h);for(var g=f.next();!g.done;g=f.next())if(g=g.value,L(g)!==void 0&&c[g]!==L(g)){f=!0;break a}f=!1}f&&(clearInterval(a.g),a.g=void 0,b())},1E3)};function w(){var a=!0;try{a=window.self!==window.top}catch(b){}return a};function p(){}p.prototype.hasStorageAccess=function(){return document.hasStorageAccess()};function z(){return navigator.permissions.query({name:"storage-access"}).then(function(a){return a.state}).catch(function(){return"prompt"})}p.prototype.requestStorageAccess=function(){return document.requestStorageAccess()};
function x(){if(window.navigator.userAgentData&&window.navigator.userAgentData.brands)for(var a=window.navigator.userAgentData.brands,b=0;b<a.length;b++){var c=a[b];if(c.brand==="Google Chrome")return c.version==="115"||c.version==="116"}return!1};document.readyState==="complete"?v():document.addEventListener("DOMContentLoaded",M);function M(){v()};
</script><div class="document-root loading"><div class="request-storage-access"><div><img src=&#47;&#47;ssl.gstatic.com&#47;docs&#47;common&#47;product&#47;docs_app_icon1.png alt=Google&#32;Docs class="icon"></div><div class="title-text">Allow Google Docs access to your necessary cookies</div><div class="subtitle-text">You won&#39;t be able to access this content if necessary cookies are turned off</div><div class="button-container"><a target="cookieAccessHelp" href="https://support.google.com/drive?p=enable_storage_access" class=" button">Learn more</a><button type="button" class="accept-button button primary-button">Allow cookies</button></div></div><div class="login"><div><img src=https:&#47;&#47;www.gstatic.com&#47;images&#47;branding&#47;googleg&#47;1x&#47;googleg_standard_color_48dp.png alt=Google&#32;logo class="icon"></div><div class="title-text">Sign in to your Google Account</div><div class="subtitle-text">You must sign in to access this content</div><div class="button-container"><button type="button" class="sign-in-button button primary-button" data-popup-url=https:&#47;&#47;accounts.google.com&#47;ServiceLogin?continue&#61;https:&#47;&#47;docs.google.com&#47;document&#47;d&#47;1Qcf7YSXMY4Qc0YMgftGVUZ&#45;BdT68V9Yt1r15SZ2GCbI&#47;export?format%3Dpdf%26key%3DAIzaSyBHD_g4us1KC5UTkxhrvSL1HBS3FRnsgww&amp;btmpl&#61;popup&amp;hl&#61;en>Sign in</button></div></div><div class="error"><div><img src=https:&#47;&#47;www.gstatic.com&#47;images&#47;branding&#47;googleg&#47;1x&#47;googleg_standard_color_48dp.png alt=Google&#32;logo class="icon"></div><div class="title-text">Can&#39;t access your Google Account</div><div class="subtitle-text">We can&#39;t access this content right now. Try signing into your Google account or allowing cookie access to proceed.</div><div class="button-container"><a target="cookieAccessHelp" href="https://support.google.com/drive?p=enable_storage_access" class="primary-button button">Learn more</a></div></div></div>