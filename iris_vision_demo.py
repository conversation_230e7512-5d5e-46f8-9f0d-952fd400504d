#!/usr/bin/env python3
"""
IRIS Vision System Demonstration
===============================

Comprehensive demonstration of IRIS's full vision capabilities including:
- Real MiMo-VL-7B model integration
- Web browser vision and automation
- Visual task execution
- Document analysis and OCR
- Insurance form automation
"""

import asyncio
import logging
import time
import base64
from pathlib import Path

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Import IRIS vision system
try:
    from advanced_models.unified_interface import UnifiedModelInterface
    from advanced_models.mimo_vl_agent import MimoVLAgent
    from advanced_models.web_vision_agent import WebVisionAgent
    from advanced_models.visual_task_executor import VisualTaskExecutor, VisualTask, TaskType
    IRIS_VISION_AVAILABLE = True
    print("✅ IRIS Vision System loaded successfully")
except ImportError as e:
    IRIS_VISION_AVAILABLE = False
    print(f"⚠️ IRIS Vision System not available: {e}")

async def demo_mimo_vl_real_model():
    """Demonstrate the actual MiMo-VL-7B model capabilities"""
    print("\n🔍 MIMO-VL-7B REAL MODEL DEMONSTRATION")
    print("-" * 50)
    
    if not IRIS_VISION_AVAILABLE:
        print("❌ IRIS Vision System not available")
        return
    
    try:
        # Initialize MiMo-VL agent
        mimo_agent = MimoVLAgent()
        await mimo_agent.initialize()
        
        # Create a test image (in real usage, this would be actual image data)
        test_image_data = create_test_image()
        
        # Test different vision capabilities
        vision_tests = [
            {
                'name': 'Image Description',
                'query': 'Describe this image in detail, including all visible elements, colors, and composition'
            },
            {
                'name': 'Text Extraction (OCR)',
                'query': 'Extract all text visible in this image and transcribe it accurately'
            },
            {
                'name': 'Object Counting',
                'query': 'Count all distinct objects visible in this image and provide the total count'
            },
            {
                'name': 'Color Analysis',
                'query': 'Analyze the color palette and dominant colors in this image'
            },
            {
                'name': 'Insurance Document Analysis',
                'query': 'Analyze this as an insurance document and extract relevant policy information'
            }
        ]
        
        for test in vision_tests:
            print(f"\n📋 {test['name']}:")
            print(f"Query: {test['query']}")
            
            start_time = time.time()
            result = await mimo_agent.process_vision_query(
                query=test['query'],
                image_data=test_image_data
            )
            processing_time = time.time() - start_time
            
            print(f"Response: {result['response'][:200]}...")
            print(f"Confidence: {result['confidence']:.2f}")
            print(f"Processing Time: {processing_time:.2f}s")
            print(f"Model: {result['metadata'].get('model', 'unknown')}")
        
        await mimo_agent.cleanup()
        print("\n✅ MiMo-VL-7B demonstration completed successfully")
        
    except Exception as e:
        logger.error(f"MiMo-VL-7B demo failed: {e}")
        print(f"❌ MiMo-VL-7B demo failed: {e}")

async def demo_web_vision_capabilities():
    """Demonstrate web browser vision capabilities"""
    print("\n🌐 WEB VISION CAPABILITIES DEMONSTRATION")
    print("-" * 50)
    
    if not IRIS_VISION_AVAILABLE:
        print("❌ IRIS Vision System not available")
        return
    
    try:
        # Initialize web vision agent
        web_agent = WebVisionAgent()
        await web_agent.initialize()
        
        print(f"Browser Type: {web_agent.browser_type}")
        
        # Test web navigation and analysis
        test_urls = [
            "https://example.com",
            "https://httpbin.org/forms/post",  # Form testing
            "https://www.google.com"
        ]
        
        for url in test_urls:
            print(f"\n🔗 Testing URL: {url}")
            
            try:
                # Navigate to URL
                state = await web_agent.navigate_to_url(url)
                print(f"✅ Navigation successful")
                print(f"Title: {state.title}")
                print(f"Elements found: {len(state.elements)}")
                print(f"Page text length: {len(state.page_text)} characters")
                
                # Analyze page visually
                analysis = await web_agent.analyze_page_visually(
                    "Analyze this webpage and identify all interactive elements, forms, and key content"
                )
                
                print(f"Visual Analysis: {analysis['analysis'][:150]}...")
                print(f"Analysis Confidence: {analysis['confidence']:.2f}")
                
                # Test element finding
                if state.elements:
                    element = await web_agent.find_element_by_description("button or link")
                    if element:
                        print(f"Found element: {element.tag} - '{element.text[:50]}'")
                    else:
                        print("No interactive elements found")
                
            except Exception as e:
                print(f"❌ URL test failed: {e}")
        
        await web_agent.cleanup()
        print("\n✅ Web vision demonstration completed successfully")
        
    except Exception as e:
        logger.error(f"Web vision demo failed: {e}")
        print(f"❌ Web vision demo failed: {e}")

async def demo_visual_task_execution():
    """Demonstrate visual task execution capabilities"""
    print("\n🎯 VISUAL TASK EXECUTION DEMONSTRATION")
    print("-" * 50)
    
    if not IRIS_VISION_AVAILABLE:
        print("❌ IRIS Vision System not available")
        return
    
    try:
        # Initialize visual task executor
        task_executor = VisualTaskExecutor()
        await task_executor.initialize()
        
        # Create test tasks
        test_tasks = [
            VisualTask(
                task_id="doc_analysis_001",
                task_type=TaskType.DOCUMENT_ANALYSIS,
                description="Analyze this insurance document and extract policy details",
                image_data=create_test_image()
            ),
            VisualTask(
                task_id="web_nav_001",
                task_type=TaskType.WEB_NAVIGATION,
                description="Navigate to example.com and analyze the page structure",
                target_url="https://example.com"
            ),
            VisualTask(
                task_id="workflow_001",
                task_type=TaskType.AUTOMATED_WORKFLOW,
                description="Navigate to a form page, analyze the form, and prepare for data entry"
            )
        ]
        
        for task in test_tasks:
            print(f"\n📋 Executing Task: {task.task_id}")
            print(f"Type: {task.task_type.value}")
            print(f"Description: {task.description}")
            
            start_time = time.time()
            result = await task_executor.execute_task(task)
            execution_time = time.time() - start_time
            
            print(f"✅ Success: {result.success}")
            print(f"Execution Time: {execution_time:.2f}s")
            print(f"Screenshots: {len(result.screenshots)}")
            
            if result.success:
                print(f"Result Keys: {list(result.result_data.keys())}")
            else:
                print(f"Error: {result.error_message}")
        
        # Show task capabilities
        print(f"\n📊 Task Executor Capabilities:")
        for capability in task_executor.get_capabilities():
            print(f"• {capability}")
        
        await task_executor.cleanup()
        print("\n✅ Visual task execution demonstration completed successfully")
        
    except Exception as e:
        logger.error(f"Visual task execution demo failed: {e}")
        print(f"❌ Visual task execution demo failed: {e}")

async def demo_unified_vision_interface():
    """Demonstrate the unified interface with all vision capabilities"""
    print("\n🚀 IRIS UNIFIED VISION INTERFACE DEMONSTRATION")
    print("-" * 50)
    
    if not IRIS_VISION_AVAILABLE:
        print("❌ IRIS Vision System not available")
        return
    
    try:
        # Initialize unified interface
        iris = UnifiedModelInterface()
        await iris.initialize()
        
        print("✅ IRIS Unified Interface initialized with vision capabilities")
        
        # Test vision capabilities
        vision_capabilities = iris.get_vision_capabilities()
        print(f"\n📊 Available Vision Capabilities:")
        for category, capabilities in vision_capabilities.items():
            print(f"\n{category.upper()}:")
            for capability in capabilities:
                print(f"  • {capability}")
        
        # Test unified vision queries
        test_image = create_test_image()
        
        vision_queries = [
            {
                'name': 'Standard Vision Query',
                'query': 'Analyze this image and provide detailed insights',
                'image_data': test_image
            },
            {
                'name': 'Insurance Document Analysis',
                'query': 'Extract insurance policy information from this document',
                'image_data': test_image
            },
            {
                'name': 'Web Analysis',
                'url': 'https://example.com',
                'query': 'Analyze this webpage for user interface elements'
            }
        ]
        
        for test in vision_queries:
            print(f"\n🔍 {test['name']}:")
            
            if 'image_data' in test:
                # Image-based query
                result = await iris.query(
                    query=test['query'],
                    image_data=test['image_data']
                )
                print(f"Response: {result.primary_response[:200]}...")
                print(f"Confidence: {result.confidence:.2f}")
                print(f"Models Used: {[r.model_type.value for r in result.model_responses]}")
            
            elif 'url' in test:
                # Web-based query
                result = await iris.navigate_and_analyze_web(test['url'], test['query'])
                print(f"Success: {result['success']}")
                if result['success']:
                    print(f"Title: {result['title']}")
                    print(f"Analysis: {result['analysis']['analysis'][:200]}...")
                    print(f"Elements Found: {result['elements_found']}")
        
        # Test visual task execution through unified interface
        print(f"\n🎯 Testing Visual Task Execution:")
        
        task_result = await iris.execute_visual_task(
            task_description="Analyze this document for insurance information",
            task_type="document_analysis",
            image_data=test_image
        )
        
        print(f"Task Success: {task_result['success']}")
        print(f"Execution Time: {task_result['execution_time']:.2f}s")
        print(f"Screenshots: {task_result['screenshots_count']}")
        
        # Test text extraction
        print(f"\n📝 Testing OCR Text Extraction:")
        
        ocr_result = await iris.extract_text_from_image(test_image)
        print(f"OCR Success: {ocr_result['success']}")
        if ocr_result['success']:
            print(f"Extracted Text: {ocr_result['extracted_text'][:100]}...")
            print(f"Confidence: {ocr_result['confidence']:.2f}")
        
        await iris.cleanup()
        print("\n✅ IRIS Unified Vision Interface demonstration completed successfully")
        
    except Exception as e:
        logger.error(f"Unified vision interface demo failed: {e}")
        print(f"❌ Unified vision interface demo failed: {e}")

def create_test_image() -> bytes:
    """Create a test image for demonstration purposes"""
    try:
        from PIL import Image, ImageDraw, ImageFont
        import io
        
        # Create a test image with text and shapes
        img = Image.new('RGB', (800, 600), color='white')
        draw = ImageDraw.Draw(img)
        
        # Add title
        draw.text((50, 50), "IRIS Vision Test Document", fill='black')
        draw.text((50, 100), "Insurance Policy Information", fill='blue')
        
        # Add some sample insurance data
        sample_text = [
            "Policy Number: INS-2024-001234",
            "Policyholder: John Doe",
            "Coverage Type: Life Insurance",
            "Premium: $150.00/month",
            "Effective Date: 01/01/2024",
            "Expiration Date: 01/01/2025"
        ]
        
        y_pos = 150
        for line in sample_text:
            draw.text((50, y_pos), line, fill='black')
            y_pos += 30
        
        # Add some shapes
        draw.rectangle([50, 350, 200, 400], outline='red', width=2)
        draw.text((60, 365), "Important", fill='red')
        
        draw.ellipse([250, 350, 350, 450], outline='green', width=2)
        draw.text((270, 390), "Verified", fill='green')
        
        # Convert to bytes
        img_bytes = io.BytesIO()
        img.save(img_bytes, format='PNG')
        return img_bytes.getvalue()
        
    except ImportError:
        # Fallback: return a simple byte pattern
        return b"PNG_TEST_IMAGE_DATA_FOR_IRIS_VISION_DEMO"

async def main():
    """Main demonstration function"""
    print("🚀 IRIS VISION SYSTEM COMPREHENSIVE DEMONSTRATION")
    print("=" * 60)
    print("Testing IRIS's complete vision capabilities:")
    print("• Real MiMo-VL-7B model integration")
    print("• Web browser vision and automation")
    print("• Visual task execution")
    print("• Document analysis and OCR")
    print("• Insurance form automation")
    print("• Unified vision interface")
    print()
    
    if not IRIS_VISION_AVAILABLE:
        print("❌ IRIS Vision System not available.")
        print("Please ensure all vision dependencies are installed:")
        print("pip install -r requirements.txt")
        return
    
    try:
        # Run all demonstrations
        await demo_mimo_vl_real_model()
        await demo_web_vision_capabilities()
        await demo_visual_task_execution()
        await demo_unified_vision_interface()
        
        print("\n" + "=" * 60)
        print("🎉 IRIS VISION SYSTEM DEMONSTRATION COMPLETED")
        print("=" * 60)
        print("✅ All vision capabilities tested successfully!")
        print("\n🚀 IRIS now has FULL VISION CAPABILITIES:")
        print("• Real MiMo-VL-7B model with native resolution processing")
        print("• Autonomous web browser control with visual understanding")
        print("• Advanced visual task execution and automation")
        print("• OCR and document analysis capabilities")
        print("• Insurance form automation and processing")
        print("• Unified interface for all vision operations")
        print("\n🎯 IRIS can now:")
        print("• See and understand images like a human")
        print("• Navigate websites using visual recognition")
        print("• Fill forms by visually identifying fields")
        print("• Extract text from any image or document")
        print("• Automate insurance workflows visually")
        print("• Execute complex visual tasks autonomously")
        
    except Exception as e:
        logger.error(f"Demonstration failed: {e}")
        print(f"\n❌ Demonstration encountered an error: {e}")

if __name__ == "__main__":
    asyncio.run(main())
