#!/usr/bin/env python3
"""
IRIS Complete System Test
=========================

Test all IRIS capabilities to demonstrate the fully operational system.
"""

import asyncio
import logging
from iris_dashboard import IRISDashboard

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

async def test_iris_complete_system():
    """Test the complete IRIS system with all capabilities"""
    
    print("🚀 TESTING COMPLETE IRIS SYSTEM")
    print("=" * 60)
    
    # Initialize dashboard
    dashboard = IRISDashboard()
    success = await dashboard.initialize()
    
    if not success:
        print("❌ Failed to initialize IRIS dashboard")
        return
    
    print("\n🧪 RUNNING COMPREHENSIVE TESTS")
    print("-" * 40)
    
    # Test 1: Insurance Query
    print("\n1️⃣ Testing Insurance Agent...")
    result = await dashboard.process_query(
        "What are the key benefits of life insurance?",
        query_type="insurance"
    )
    print(f"✅ Insurance Response: {result['response'][:100]}...")
    
    # Test 2: Research Query
    print("\n2️⃣ Testing Research Capabilities...")
    result = await dashboard.process_query(
        "Research the latest trends in AI and machine learning",
        query_type="research"
    )
    print(f"✅ Research Response: {result['response'][:100]}...")
    
    # Test 3: Complex Multi-Agent Query
    print("\n3️⃣ Testing Complex Multi-Agent Processing...")
    result = await dashboard.process_query(
        "Analyze the insurance market and create a content strategy for social media marketing",
        query_type="complex"
    )
    print(f"✅ Complex Response: {result['response'][:100]}...")
    
    # Test 4: General Query with Advanced Models
    print("\n4️⃣ Testing Advanced AI Models...")
    result = await dashboard.process_query(
        "Explain quantum computing in simple terms",
        query_type="general"
    )
    print(f"✅ Advanced Models Response: {result['response'][:100]}...")
    
    # Test 5: Web Automation Query
    print("\n5️⃣ Testing Web Automation...")
    result = await dashboard.process_query(
        "Navigate to a website and extract information",
        query_type="web_automation"
    )
    print(f"✅ Web Automation Response: {result['response'][:100]}...")
    
    # Display system information
    print("\n📊 SYSTEM INFORMATION")
    print("-" * 40)
    system_info = dashboard.get_system_info()
    
    print(f"Session ID: {system_info['session_id']}")
    print(f"Initialized: {system_info['initialized']}")
    print(f"Query History: {system_info['query_history_count']} queries")
    
    print("\n🌟 AVAILABLE CAPABILITIES:")
    for capability in system_info['capabilities']:
        print(f"  ✅ {capability}")
    
    print("\n🎯 SYSTEM STATUS:")
    for system, status in system_info['system_status'].items():
        icon = "✅" if status else "❌"
        system_name = system.replace('_', ' ').title()
        print(f"  {icon} {system_name}")
    
    # Cleanup
    await dashboard.cleanup()
    
    print("\n🎉 IRIS COMPLETE SYSTEM TEST FINISHED!")
    print("=" * 60)
    print("🚀 ALL SYSTEMS OPERATIONAL AND READY FOR USE!")
    print("   IRIS can now handle any task with maximum efficiency!")

async def main():
    """Main test function"""
    await test_iris_complete_system()

if __name__ == "__main__":
    asyncio.run(main())
