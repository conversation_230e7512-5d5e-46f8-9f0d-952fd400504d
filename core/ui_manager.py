import os
import sys
import asyncio
import logging
from typing import Any, Dict, Optional, List, Union
from abc import ABC, abstractmethod
from datetime import datetime
import json
import cv2
import numpy as np
from PIL import Image
import pyautogui
from pynput import mouse, keyboard

class UIEvent:
    def __init__(self, event_type: str, data: Dict[str, Any]):
        self.event_type = event_type
        self.data = data
        self.timestamp = datetime.now()

class UIActionResult:
    def __init__(self, success: bool, data: Any = None, error: Optional[str] = None):
        self.success = success
        self.data = data
        self.error = error
        self.timestamp = datetime.now()

class UIControllerBase(ABC):
    def __init__(self):
        self.event_listeners = []
        self.action_history = []
        self.screen_state = None
        self.mouse_position = (0, 0)
        
    @abstractmethod
    def initialize(self) -> bool:
        """Initialize the UI controller"""
        pass

    @abstractmethod
    def perform_action(self, action: str, params: Dict[str, Any]) -> UIActionResult:
        """Perform a UI action"""
        pass

    def add_event_listener(self, callback):
        self.event_listeners.append(callback)

    def notify_listeners(self, event: UIEvent):
        for listener in self.event_listeners:
            listener(event)

    def update_screen_state(self):
        """Capture and update current screen state"""
        try:
            screenshot = pyautogui.screenshot()
            self.screen_state = cv2.cvtColor(np.array(screenshot), cv2.COLOR_RGB2BGR)
            return True
        except Exception as e:
            logging.error(f"Failed to update screen state: {e}")
            return False

    def find_element_by_image(self, template_path: str, threshold: float = 0.8) -> Optional[Dict[str, int]]:
        """Find UI element using template matching"""
        try:
            if self.screen_state is None:
                self.update_screen_state()
            
            template = cv2.imread(template_path)
            result = cv2.matchTemplate(self.screen_state, template, cv2.TM_CCOEFF_NORMED)
            min_val, max_val, min_loc, max_loc = cv2.minMaxLoc(result)
            
            if max_val >= threshold:
                return {
                    "x": max_loc[0] + template.shape[1] // 2,
                    "y": max_loc[1] + template.shape[0] // 2,
                    "confidence": float(max_val)
                }
            return None
        except Exception as e:
            logging.error(f"Template matching failed: {e}")
            return None

class TARSDesktopController(UIControllerBase):
    def __init__(self):
        super().__init__()
        self.desktop_path = os.path.join(
            os.path.dirname(os.path.dirname(__file__)), 
            'external_agents', 
            'ui-tars-desktop'
        )
        sys.path.append(self.desktop_path)
        self._mouse_listener = None
        self._keyboard_listener = None
        
    def initialize(self) -> bool:
        try:
            # Start input listeners
            self._mouse_listener = mouse.Listener(
                on_move=self._on_mouse_move,
                on_click=self._on_mouse_click
            )
            self._keyboard_listener = keyboard.Listener(
                on_press=self._on_key_press,
                on_release=self._on_key_release
            )
            self._mouse_listener.start()
            self._keyboard_listener.start()
            return True
        except Exception as e:
            logging.error(f"Error initializing TARS Desktop: {e}")
            return False

    def perform_action(self, action: str, params: Dict[str, Any]) -> UIActionResult:
        try:
            if action == "click":
                pyautogui.click(params.get("x"), params.get("y"))
                self.action_history.append(UIEvent("click", params))
                return UIActionResult(True, {"x": params.get("x"), "y": params.get("y")})
            elif action == "type":
                pyautogui.write(params.get("text", ""))
                self.action_history.append(UIEvent("type", params))
                return UIActionResult(True, {"text": params.get("text")})
            elif action == "find_element":
                element = self.find_element_by_image(params.get("template"))
                return UIActionResult(True, element) if element else UIActionResult(False, error="Element not found")
            else:
                return UIActionResult(False, error=f"Unknown action: {action}")
        except Exception as e:
            return UIActionResult(False, error=str(e))

    def _on_mouse_move(self, x, y):
        self.mouse_position = (x, y)
        self.notify_listeners(UIEvent("mouse_move", {"x": x, "y": y}))

    def _on_mouse_click(self, x, y, button, pressed):
        self.notify_listeners(UIEvent("mouse_click", {
            "x": x, 
            "y": y,
            "button": str(button),
            "pressed": pressed
        }))

    def _on_key_press(self, key):
        self.notify_listeners(UIEvent("key_press", {"key": str(key)}))

    def _on_key_release(self, key):
        self.notify_listeners(UIEvent("key_release", {"key": str(key)}))

class TARS7BController(UIControllerBase):
    def __init__(self):
        super().__init__()
        self.model = None
        self.model_config = {
            "max_retries": 3,
            "timeout": 10.0,
            "confidence_threshold": 0.8
        }
        
    def initialize(self) -> bool:
        try:
            # Will be implemented when UI-TARS-1.5-7B is available
            return True
        except Exception as e:
            logging.error(f"Error initializing TARS 7B: {e}")
            return False

    def perform_action(self, action: str, params: Dict[str, Any]) -> UIActionResult:
        try:
            # Will be implemented when UI-TARS-1.5-7B is available
            return UIActionResult(True)
        except Exception as e:
            return UIActionResult(False, error=str(e))

class UIManager:
    def __init__(self):
        self.desktop_controller = TARSDesktopController()
        self.model_controller = TARS7BController()
        self._active_controller: Optional[UIControllerBase] = None
        self._event_queue = asyncio.Queue()
        self._action_queue = asyncio.Queue()
        
    def initialize(self, prefer_desktop: bool = True) -> bool:
        """Initialize the UI system with preferred controller"""
        if prefer_desktop:
            if self.desktop_controller.initialize():
                self._active_controller = self.desktop_controller
                self._start_event_processing()
                return True
            elif self.model_controller.initialize():
                self._active_controller = self.model_controller
                self._start_event_processing()
                return True
        else:
            if self.model_controller.initialize():
                self._active_controller = self.model_controller
                self._start_event_processing()
                return True
            elif self.desktop_controller.initialize():
                self._active_controller = self.desktop_controller
                self._start_event_processing()
                return True
        return False

    def _start_event_processing(self):
        """Start asynchronous event processing"""
        if self._active_controller:
            self._active_controller.add_event_listener(
                lambda event: self._event_queue.put_nowait(event)
            )

    async def process_events(self):
        """Process UI events asynchronously"""
        while True:
            event = await self._event_queue.get()
            # Process event based on type
            if event.event_type == "mouse_click":
                await self._handle_mouse_click(event)
            elif event.event_type == "key_press":
                await self._handle_key_press(event)
            self._event_queue.task_done()

    async def _handle_mouse_click(self, event: UIEvent):
        """Handle mouse click events"""
        # Add custom click handling logic
        pass

    async def _handle_key_press(self, event: UIEvent):
        """Handle keyboard press events"""
        # Add custom key press handling logic
        pass

    def perform_action(self, action: str, params: Dict[str, Any]) -> bool:
        """Perform a UI action using the active controller"""
        if self._active_controller is None:
            logging.error("No UI controller initialized")
            return False
            
        result = self._active_controller.perform_action(action, params)
        if not result.success:
            logging.error(f"UI action failed: {result.error}")
        return result.success

    def get_controller_status(self) -> Dict[str, Any]:
        """Get current controller status"""
        return {
            "active_controller": self._active_controller.__class__.__name__ if self._active_controller else None,
            "desktop_available": self.desktop_controller is not None,
            "model_available": self.model_controller is not None,
            "event_queue_size": self._event_queue.qsize(),
            "action_queue_size": self._action_queue.qsize()
        }