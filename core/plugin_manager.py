"""
Plugin Manager for handling system plugins and extensibility
"""

import asyncio
from typing import Dict, Any, Optional, List, Type, Callable
from dataclasses import dataclass
from enum import Enum
import importlib.util
import inspect
import os
import yaml
from pathlib import Path
import uuid
from utils.logging_setup import get_logger
from utils.metrics import get_metrics_collector
from utils.tracing import get_tracer
from core.event_system import get_event_system

class PluginState(Enum):
    """Plugin lifecycle states"""
    UNLOADED = "unloaded"
    LOADED = "loaded"
    INITIALIZED = "initialized"
    ACTIVE = "active"
    DISABLED = "disabled"
    ERROR = "error"

@dataclass
class PluginMetadata:
    """Plugin metadata"""
    name: str
    version: str
    description: str
    author: str
    dependencies: List[str]
    capabilities: List[str]
    entry_point: str
    config: Dict[str, Any] = None

@dataclass
class Plugin:
    """Plugin instance"""
    id: str
    metadata: PluginMetadata
    module: Any
    instance: Any
    state: PluginState
    error: Optional[str] = None

class PluginManager:
    """Manages system plugins"""
    
    def __init__(self):
        self.logger = get_logger("plugin_manager")
        self.metrics = get_metrics_collector("plugin_manager")
        self.tracer = get_tracer("plugin_manager")
        self.event_system = get_event_system()
        
        # Plugin registry
        self._plugins: Dict[str, Plugin] = {}
        
        # Plugin hooks
        self._hooks: Dict[str, List[Callable]] = {}
        
        # Plugin capabilities
        self._capabilities: Dict[str, List[str]] = {}
        
        # Dependency graph
        self._dependencies: Dict[str, List[str]] = {}
        
    async def load_plugins(self, plugin_dir: str):
        """Load plugins from directory"""
        try:
            with self.tracer.traced_operation("load_plugins"):
                plugin_path = Path(plugin_dir)
                if not plugin_path.exists():
                    raise ValueError(f"Plugin directory not found: {plugin_dir}")
                    
                # Find plugin descriptors
                for path in plugin_path.glob("*/plugin.yaml"):
                    try:
                        await self._load_plugin(path.parent)
                    except Exception as e:
                        self.logger.error(
                            f"Error loading plugin from {path}: {str(e)}"
                        )
                        
                # Initialize plugins in dependency order
                await self._initialize_plugins()
                
                self.logger.info(f"Loaded {len(self._plugins)} plugins")
                
        except Exception as e:
            self.logger.error(f"Error loading plugins: {str(e)}")
            self.metrics.record_error("plugin_load_error")
            self.tracer.record_exception(e)
            raise
            
    async def enable_plugin(self, plugin_id: str):
        """Enable plugin"""
        if plugin_id not in self._plugins:
            raise ValueError(f"Plugin not found: {plugin_id}")
            
        plugin = self._plugins[plugin_id]
        if plugin.state != PluginState.INITIALIZED:
            raise ValueError(
                f"Plugin {plugin_id} is not initialized"
            )
            
        try:
            # Call plugin enable method if exists
            if hasattr(plugin.instance, "enable"):
                await plugin.instance.enable()
                
            plugin.state = PluginState.ACTIVE
            
            await self.event_system.publish_event(
                "plugin.enabled",
                "plugin_manager",
                {"plugin_id": plugin_id}
            )
            
        except Exception as e:
            plugin.state = PluginState.ERROR
            plugin.error = str(e)
            raise
            
    async def disable_plugin(self, plugin_id: str):
        """Disable plugin"""
        if plugin_id not in self._plugins:
            raise ValueError(f"Plugin not found: {plugin_id}")
            
        plugin = self._plugins[plugin_id]
        if plugin.state != PluginState.ACTIVE:
            return
            
        try:
            # Call plugin disable method if exists
            if hasattr(plugin.instance, "disable"):
                await plugin.instance.disable()
                
            plugin.state = PluginState.DISABLED
            
            await self.event_system.publish_event(
                "plugin.disabled",
                "plugin_manager",
                {"plugin_id": plugin_id}
            )
            
        except Exception as e:
            plugin.state = PluginState.ERROR
            plugin.error = str(e)
            raise
            
    def register_hook(
        self,
        hook_name: str,
        handler: Callable
    ):
        """Register plugin hook"""
        if hook_name not in self._hooks:
            self._hooks[hook_name] = []
        self._hooks[hook_name].append(handler)
        
    async def execute_hook(
        self,
        hook_name: str,
        *args,
        **kwargs
    ) -> List[Any]:
        """Execute plugin hook"""
        results = []
        
        if hook_name not in self._hooks:
            return results
            
        for handler in self._hooks[hook_name]:
            try:
                result = handler(*args, **kwargs)
                if asyncio.iscoroutine(result):
                    result = await result
                results.append(result)
            except Exception as e:
                self.logger.error(
                    f"Error executing hook {hook_name}: {str(e)}"
                )
                
        return results
        
    def get_plugin(self, plugin_id: str) -> Optional[Plugin]:
        """Get plugin by ID"""
        return self._plugins.get(plugin_id)
        
    def get_plugins_by_capability(
        self,
        capability: str
    ) -> List[Plugin]:
        """Get plugins with specific capability"""
        return [
            plugin for plugin in self._plugins.values()
            if capability in plugin.metadata.capabilities
        ]
        
    def is_capability_available(self, capability: str) -> bool:
        """Check if capability is available"""
        return any(
            capability in plugin.metadata.capabilities
            for plugin in self._plugins.values()
            if plugin.state == PluginState.ACTIVE
        )
        
    async def _load_plugin(self, plugin_path: Path):
        """Load single plugin"""
        # Load metadata
        with open(plugin_path / "plugin.yaml") as f:
            metadata = PluginMetadata(**yaml.safe_load(f))
            
        # Generate plugin ID
        plugin_id = str(uuid.uuid4())
        
        # Load module
        spec = importlib.util.spec_from_file_location(
            f"plugin_{plugin_id}",
            plugin_path / metadata.entry_point
        )
        module = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(module)
        
        # Create plugin instance
        plugin_class = self._find_plugin_class(module)
        instance = plugin_class()
        
        # Register plugin
        plugin = Plugin(
            id=plugin_id,
            metadata=metadata,
            module=module,
            instance=instance,
            state=PluginState.LOADED
        )
        self._plugins[plugin_id] = plugin
        
        # Update capability registry
        for capability in metadata.capabilities:
            if capability not in self._capabilities:
                self._capabilities[capability] = []
            self._capabilities[capability].append(plugin_id)
            
        # Update dependency graph
        self._dependencies[plugin_id] = metadata.dependencies
        
        self.logger.info(f"Loaded plugin: {metadata.name}")
        return plugin
        
    def _find_plugin_class(self, module: Any) -> Type:
        """Find plugin class in module"""
        plugin_classes = []
        
        for item_name, item in inspect.getmembers(module):
            if inspect.isclass(item) and hasattr(item, "__plugin__"):
                plugin_classes.append(item)
                
        if not plugin_classes:
            raise ValueError("No plugin class found in module")
        if len(plugin_classes) > 1:
            raise ValueError("Multiple plugin classes found in module")
            
        return plugin_classes[0]
        
    async def _initialize_plugins(self):
        """Initialize plugins in dependency order"""
        # Build initialization order
        init_order = self._resolve_dependencies()
        
        # Initialize plugins
        for plugin_id in init_order:
            plugin = self._plugins[plugin_id]
            try:
                # Initialize plugin
                if hasattr(plugin.instance, "initialize"):
                    await plugin.instance.initialize()
                    
                plugin.state = PluginState.INITIALIZED
                
                # Register hooks
                self._register_plugin_hooks(plugin)
                
            except Exception as e:
                self.logger.error(
                    f"Error initializing plugin {plugin_id}: {str(e)}"
                )
                plugin.state = PluginState.ERROR
                plugin.error = str(e)
                
    def _resolve_dependencies(self) -> List[str]:
        """Resolve plugin dependencies"""
        visited = set()
        init_order = []
        
        def visit(plugin_id: str):
            if plugin_id in visited:
                return
            visited.add(plugin_id)
            
            # Visit dependencies first
            for dep in self._dependencies[plugin_id]:
                # Find plugin providing dependency
                dep_plugin = None
                for pid, plugin in self._plugins.items():
                    if dep in plugin.metadata.capabilities:
                        dep_plugin = pid
                        break
                        
                if not dep_plugin:
                    raise ValueError(
                        f"Dependency not found: {dep}"
                    )
                    
                visit(dep_plugin)
                
            init_order.append(plugin_id)
            
        # Visit all plugins
        for plugin_id in self._plugins:
            visit(plugin_id)
            
        return init_order
        
    def _register_plugin_hooks(self, plugin: Plugin):
        """Register plugin hooks"""
        for name, member in inspect.getmembers(plugin.instance):
            if hasattr(member, "__hook__"):
                hook_name = getattr(member, "__hook__")
                self.register_hook(hook_name, member)

def hook(name: str):
    """Decorator to mark plugin hooks"""
    def decorator(func):
        setattr(func, "__hook__", name)
        return func
    return decorator

def plugin:
    """Decorator to mark plugin classes"""
    def decorator(cls):
        setattr(cls, "__plugin__", True)
        return cls
    return decorator

# Global plugin manager instance
_plugin_manager: Optional[PluginManager] = None

def get_plugin_manager() -> PluginManager:
    """Get global plugin manager instance"""
    global _plugin_manager
    if _plugin_manager is None:
        _plugin_manager = PluginManager()
    return _plugin_manager

# Example usage
if __name__ == "__main__":
    @plugin
    class ExamplePlugin:
        def __init__(self):
            self.logger = get_logger("example_plugin")
            
        async def initialize(self):
            self.logger.info("Initializing example plugin")
            
        @hook("system.startup")
        async def on_startup(self):
            self.logger.info("System starting up")
            
    async def example():
        manager = get_plugin_manager()
        
        # Load plugins
        await manager.load_plugins("plugins")
        
        # Execute hook
        await manager.execute_hook("system.startup")
        
    asyncio.run(example())