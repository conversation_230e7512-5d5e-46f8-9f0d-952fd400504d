"""
Validator for handling data validation and schema verification
"""

from typing import Dict, Any, Optional, List, Union, Type, Callable
from datetime import datetime
import json
import re
from enum import Enum
from dataclasses import dataclass
import cerberus
from utils.logging_setup import get_logger
from utils.metrics import get_metrics_collector
from utils.tracing import get_tracer

@dataclass
class ValidationError:
    """Represents a validation error"""
    field: str
    error: str
    value: Any
    rule: str
    validator: str
    context: Dict[str, Any] = None

class ValidationType(Enum):
    """Types of validation"""
    SCHEMA = "schema"
    CUSTOM = "custom"
    REGEX = "regex"
    TYPE = "type"
    FORMAT = "format"

class Validator:
    """Manages data validation"""
    
    def __init__(self):
        self.logger = get_logger("validator")
        self.metrics = get_metrics_collector("validator")
        self.tracer = get_tracer("validator")
        
        # Schema registry
        self._schemas: Dict[str, Dict] = {}
        
        # Custom validators
        self._custom_validators: Dict[str, Callable] = {}
        
        # Regex patterns
        self._patterns: Dict[str, str] = {
            "email": r"^[a-zA-Z0-9_.+-]+@[a-zA-Z0-9-]+\.[a-zA-Z0-9-.]+$",
            "phone": r"^\+?1?\d{9,15}$",
            "uuid": r"^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$",
            "url": r"^https?:\/\/(?:www\.)?[-a-zA-Z0-9@:%._\+~#=]{1,256}\.[a-zA-Z0-9()]{1,6}\b(?:[-a-zA-Z0-9()@:%_\+.~#?&\/=]*)$"
        }
        
        # Format validators
        self._format_validators: Dict[str, Callable] = {
            "date": self._validate_date,
            "time": self._validate_time,
            "datetime": self._validate_datetime,
            "json": self._validate_json
        }
        
        # Initialize Cerberus
        self._cerberus = cerberus.Validator()
        
    def register_schema(self, name: str, schema: Dict):
        """Register validation schema"""
        try:
            # Validate schema
            self._cerberus.schema = schema
            
            # Store schema
            self._schemas[name] = schema
            
            self.logger.info(f"Registered schema: {name}")
            
        except Exception as e:
            self.logger.error(f"Error registering schema: {str(e)}")
            self.metrics.record_error("schema_registration_error")
            self.tracer.record_exception(e)
            raise
            
    def register_custom_validator(
        self,
        name: str,
        validator: Callable
    ):
        """Register custom validator"""
        self._custom_validators[name] = validator
        
    def register_pattern(self, name: str, pattern: str):
        """Register regex pattern"""
        try:
            # Test pattern
            re.compile(pattern)
            
            # Store pattern
            self._patterns[name] = pattern
            
        except Exception as e:
            self.logger.error(f"Invalid regex pattern: {str(e)}")
            raise
            
    def validate(
        self,
        data: Any,
        validation_type: ValidationType,
        validation_spec: Union[str, Dict],
        context: Optional[Dict] = None
    ) -> tuple[bool, List[ValidationError]]:
        """Validate data"""
        try:
            with self.tracer.traced_operation(
                "validate",
                {
                    "type": validation_type.value,
                    "spec": str(validation_spec)
                }
            ):
                errors = []
                
                if validation_type == ValidationType.SCHEMA:
                    # Get schema
                    if isinstance(validation_spec, str):
                        if validation_spec not in self._schemas:
                            raise ValueError(
                                f"Schema not found: {validation_spec}"
                            )
                        schema = self._schemas[validation_spec]
                    else:
                        schema = validation_spec
                        
                    # Validate against schema
                    self._cerberus.schema = schema
                    if not self._cerberus.validate(data):
                        errors.extend(
                            self._parse_cerberus_errors(
                                self._cerberus.errors
                            )
                        )
                        
                elif validation_type == ValidationType.CUSTOM:
                    # Get custom validator
                    if validation_spec not in self._custom_validators:
                        raise ValueError(
                            f"Custom validator not found: {validation_spec}"
                        )
                    validator = self._custom_validators[validation_spec]
                    
                    # Run custom validation
                    result = validator(data, context)
                    if not result:
                        errors.append(
                            ValidationError(
                                field="",
                                error="Custom validation failed",
                                value=data,
                                rule=validation_spec,
                                validator="custom"
                            )
                        )
                        
                elif validation_type == ValidationType.REGEX:
                    # Get pattern
                    if validation_spec in self._patterns:
                        pattern = self._patterns[validation_spec]
                    else:
                        pattern = validation_spec
                        
                    # Validate against pattern
                    if not re.match(pattern, str(data)):
                        errors.append(
                            ValidationError(
                                field="",
                                error="Regex validation failed",
                                value=data,
                                rule=pattern,
                                validator="regex"
                            )
                        )
                        
                elif validation_type == ValidationType.TYPE:
                    # Validate type
                    if not isinstance(data, validation_spec):
                        errors.append(
                            ValidationError(
                                field="",
                                error="Type validation failed",
                                value=data,
                                rule=str(validation_spec),
                                validator="type"
                            )
                        )
                        
                elif validation_type == ValidationType.FORMAT:
                    # Get format validator
                    if validation_spec not in self._format_validators:
                        raise ValueError(
                            f"Format validator not found: {validation_spec}"
                        )
                    validator = self._format_validators[validation_spec]
                    
                    # Validate format
                    if not validator(data):
                        errors.append(
                            ValidationError(
                                field="",
                                error="Format validation failed",
                                value=data,
                                rule=validation_spec,
                                validator="format"
                            )
                        )
                        
                # Record metrics
                if errors:
                    self.metrics.record_operation(
                        "validation_failed",
                        "error",
                        {
                            "type": validation_type.value,
                            "errors": len(errors)
                        }
                    )
                else:
                    self.metrics.record_operation(
                        "validation_passed",
                        "success",
                        {"type": validation_type.value}
                    )
                    
                return len(errors) == 0, errors
                
        except Exception as e:
            self.logger.error(f"Validation error: {str(e)}")
            self.metrics.record_error("validation_error")
            self.tracer.record_exception(e)
            raise
            
    def _parse_cerberus_errors(
        self,
        errors: Dict
    ) -> List[ValidationError]:
        """Parse Cerberus validation errors"""
        result = []
        
        def _parse_error(field: str, error: Any):
            if isinstance(error, dict):
                for sub_field, sub_error in error.items():
                    _parse_error(f"{field}.{sub_field}", sub_error)
            elif isinstance(error, list):
                for err in error:
                    if isinstance(err, str):
                        result.append(
                            ValidationError(
                                field=field,
                                error=err,
                                value=None,
                                rule="schema",
                                validator="cerberus"
                            )
                        )
                    elif isinstance(err, dict):
                        result.append(
                            ValidationError(
                                field=field,
                                error=str(err),
                                value=None,
                                rule="schema",
                                validator="cerberus"
                            )
                        )
                        
        for field, error in errors.items():
            _parse_error(field, error)
            
        return result
        
    def _validate_date(self, value: str) -> bool:
        """Validate date string"""
        try:
            datetime.strptime(value, "%Y-%m-%d")
            return True
        except:
            return False
            
    def _validate_time(self, value: str) -> bool:
        """Validate time string"""
        try:
            datetime.strptime(value, "%H:%M:%S")
            return True
        except:
            return False
            
    def _validate_datetime(self, value: str) -> bool:
        """Validate datetime string"""
        try:
            datetime.fromisoformat(value)
            return True
        except:
            return False
            
    def _validate_json(self, value: str) -> bool:
        """Validate JSON string"""
        try:
            json.loads(value)
            return True
        except:
            return False

# Global validator instance
_validator: Optional[Validator] = None

def get_validator() -> Validator:
    """Get global validator instance"""
    global _validator
    if _validator is None:
        _validator = Validator()
    return _validator

# Example usage
if __name__ == "__main__":
    # Create validator
    validator = get_validator()
    
    # Register schema
    user_schema = {
        'name': {'type': 'string', 'required': True},
        'email': {'type': 'string', 'regex': '^[^@]+@[^@]+\.[^@]+$'},
        'age': {'type': 'integer', 'min': 0}
    }
    validator.register_schema('user', user_schema)
    
    # Validate data
    data = {
        'name': 'John Doe',
        'email': 'invalid-email',
        'age': 30
    }
    
    is_valid, errors = validator.validate(
        data,
        ValidationType.SCHEMA,
        'user'
    )
    
    print(f"Validation {'passed' if is_valid else 'failed'}")
    for error in errors:
        print(f"Error in {error.field}: {error.error}")