from typing import Dict, Any, List, Optional, Callable
from datetime import datetime
import asyncio
from collections import defaultdict
import numpy as np
from core.enhanced_base_agent import EnhancedBaseAgent

class AdaptiveUISystem:
    """System for adapting UI behavior based on user interactions and success rates"""
    
    def __init__(self):
        self.interaction_patterns = defaultdict(list)
        self.success_rates = defaultdict(list)
        self.timing_data = defaultdict(list)
        self.behavioral_models = {}
        self.adaptation_callbacks = []

    async def record_interaction(self, context: Dict[str, Any], actions: List[Dict[str, Any]], success: bool):
        """Record an interaction for learning"""
        pattern_key = self._generate_pattern_key(context)
        self.interaction_patterns[pattern_key].append({
            "context": context,
            "actions": actions,
            "success": success,
            "timestamp": datetime.now()
        })
        
        self.success_rates[pattern_key].append(1.0 if success else 0.0)
        self._update_behavioral_model(pattern_key)

    def register_adaptation_callback(self, callback: Callable):
        """Register a callback for UI adaptations"""
        self.adaptation_callbacks.append(callback)

    async def get_adapted_actions(self, context: Dict[str, Any], 
                                base_actions: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Get context-adapted UI actions"""
        pattern_key = self._generate_pattern_key(context)
        
        if pattern_key in self.behavioral_models:
            model = self.behavioral_models[pattern_key]
            if self._should_adapt(model):
                return self._adapt_actions(base_actions, model)
        
        return base_actions

    def _generate_pattern_key(self, context: Dict[str, Any]) -> str:
        """Generate a unique key for an interaction pattern"""
        relevant_keys = ["platform", "interaction_type", "user_type", "time_of_day"]
        return "_".join(str(context.get(key, "unknown")) for key in relevant_keys)

    def _update_behavioral_model(self, pattern_key: str):
        """Update behavioral model based on interaction history"""
        recent_patterns = self.interaction_patterns[pattern_key][-50:]  # Last 50 interactions
        recent_success_rate = np.mean(self.success_rates[pattern_key][-50:])
        
        self.behavioral_models[pattern_key] = {
            "success_rate": recent_success_rate,
            "common_patterns": self._extract_common_patterns(recent_patterns),
            "timing_preferences": self._analyze_timing(recent_patterns)
        }
        
        # Notify callbacks of model update
        for callback in self.adaptation_callbacks:
            asyncio.create_task(callback(pattern_key, self.behavioral_models[pattern_key]))

    def _extract_common_patterns(self, patterns: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Extract common patterns from interaction history"""
        action_sequences = defaultdict(int)
        for pattern in patterns:
            sequence_key = self._action_sequence_to_key(pattern["actions"])
            action_sequences[sequence_key] += 1
            
        return {
            "most_common": max(action_sequences.items(), key=lambda x: x[1])[0],
            "frequencies": dict(action_sequences)
        }

    def _action_sequence_to_key(self, actions: List[Dict[str, Any]]) -> str:
        """Convert action sequence to a string key"""
        return "_".join(f"{action['action']}-{action['params'].get('description', '')}" 
                       for action in actions)

    def _analyze_timing(self, patterns: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Analyze timing patterns in interactions"""
        times = [p.get("timestamp").hour for p in patterns]
        return {
            "peak_hours": self._find_peak_hours(times),
            "avg_success_by_hour": self._calculate_hourly_success(patterns)
        }

    def _find_peak_hours(self, times: List[int]) -> List[int]:
        """Find peak hours of successful interaction"""
        hour_counts = defaultdict(int)
        for t in times:
            hour_counts[t] += 1
        return sorted(hour_counts.items(), key=lambda x: x[1], reverse=True)[:3]

    def _calculate_hourly_success(self, patterns: List[Dict[str, Any]]) -> Dict[int, float]:
        """Calculate success rates by hour"""
        hourly_success = defaultdict(list)
        for pattern in patterns:
            hour = pattern["timestamp"].hour
            hourly_success[hour].append(1.0 if pattern["success"] else 0.0)
            
        return {hour: np.mean(rates) for hour, rates in hourly_success.items()}

    def _should_adapt(self, model: Dict[str, Any]) -> bool:
        """Determine if adaptation is needed"""
        return (
            model["success_rate"] < 0.8 or  # Low success rate
            len(model["common_patterns"]["frequencies"]) > 3  # Multiple patterns emerging
        )

    def _adapt_actions(self, base_actions: List[Dict[str, Any]], 
                      model: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Adapt actions based on behavioral model"""
        if model["success_rate"] < 0.8:
            # Add verification steps
            adapted_actions = []
            for action in base_actions:
                adapted_actions.append(action)
                if action["action"] in ["click", "type"]:
                    adapted_actions.append({
                        "action": "find_element",
                        "params": {
                            "description": "Verification element",
                            "timeout": 2.0
                        }
                    })
            return adapted_actions
            
        return base_actions

class AdaptiveUIAgent(EnhancedBaseAgent):
    """Base agent class with adaptive UI capabilities"""
    
    def __init__(self, agent_name: str):
        super().__init__(agent_name, enable_ui=True)
        self.adaptive_ui = AdaptiveUISystem()
        self.adaptive_ui.register_adaptation_callback(self._handle_adaptation)

    async def perform_adaptive_actions(self, context: Dict[str, Any], 
                                    base_actions: List[Dict[str, Any]]) -> bool:
        """Perform UI actions with adaptive behavior"""
        try:
            adapted_actions = await self.adaptive_ui.get_adapted_actions(context, base_actions)
            results = await self.batch_ui_actions(adapted_actions)
            success = all(success for success, _ in results)
            
            # Record interaction for learning
            await self.adaptive_ui.record_interaction(context, adapted_actions, success)
            
            return success
        except Exception as e:
            self.logger.error(f"Error in adaptive UI actions: {str(e)}")
            return False

    async def _handle_adaptation(self, pattern_key: str, model: Dict[str, Any]):
        """Handle UI behavior adaptation"""
        self.logger.info(f"UI behavior adapted for pattern {pattern_key}: {model['success_rate']:.2f} success rate")
        # Update metrics
        self.metrics.set_gauge(f"ui_success_rate_{pattern_key}", model["success_rate"])