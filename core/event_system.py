"""
Event System for handling system-wide events
"""

import asyncio
from typing import Dict, List, Any, Callable, Optional
from datetime import datetime
import json
import uuid
from enum import Enum
from dataclasses import dataclass
from utils.logging_setup import get_logger
from utils.metrics import get_metrics_collector
from utils.tracing import get_tracer

class EventPriority(Enum):
    LOW = 0
    NORMAL = 1
    HIGH = 2
    CRITICAL = 3

@dataclass
class Event:
    """Represents a system event"""
    id: str
    type: str
    source: str
    timestamp: str
    data: Dict
    priority: EventPriority = EventPriority.NORMAL
    correlation_id: Optional[str] = None
    metadata: Optional[Dict] = None

class EventSystem:
    """Handles system-wide event processing"""
    
    def __init__(self):
        self.logger = get_logger("event_system")
        self.metrics = get_metrics_collector("event_system")
        self.tracer = get_tracer("event_system")
        
        # Event handlers by type
        self._handlers: Dict[str, List[Callable]] = {}
        
        # Event history
        self._history: List[Event] = []
        self._max_history = 1000
        
        # Priority queues
        self._queues: Dict[EventPriority, asyncio.PriorityQueue] = {
            priority: asyncio.PriorityQueue()
            for priority in EventPriority
        }
        
        # Processing tasks
        self._tasks: List[asyncio.Task] = []
        self._running = False
        
        # Event filters
        self._filters: List[Callable] = []
        
    async def start(self):
        """Start event processing"""
        if self._running:
            return
            
        self._running = True
        
        # Start queue processors
        for priority in EventPriority:
            task = asyncio.create_task(
                self._process_queue(priority)
            )
            self._tasks.append(task)
            
        self.logger.info("Event system started")
        
    async def stop(self):
        """Stop event processing"""
        if not self._running:
            return
            
        self._running = False
        
        # Cancel all tasks
        for task in self._tasks:
            task.cancel()
            try:
                await task
            except asyncio.CancelledError:
                pass
        self._tasks.clear()
        
        self.logger.info("Event system stopped")
        
    async def publish_event(
        self,
        event_type: str,
        source: str,
        data: Dict,
        priority: EventPriority = EventPriority.NORMAL,
        correlation_id: Optional[str] = None,
        metadata: Optional[Dict] = None
    ):
        """Publish a new event"""
        try:
            with self.tracer.traced_operation(
                "publish_event",
                {
                    "type": event_type,
                    "source": source,
                    "priority": priority.name
                }
            ):
                # Create event
                event = Event(
                    id=str(uuid.uuid4()),
                    type=event_type,
                    source=source,
                    timestamp=datetime.utcnow().isoformat(),
                    data=data,
                    priority=priority,
                    correlation_id=correlation_id,
                    metadata=metadata
                )
                
                # Apply filters
                if not self._apply_filters(event):
                    return
                    
                # Add to history
                self._add_to_history(event)
                
                # Add to priority queue
                await self._queues[priority].put(
                    (priority.value, event)
                )
                
                self.metrics.record_operation(
                    "event_published",
                    "success",
                    {"type": event_type}
                )
                
        except Exception as e:
            self.logger.error(f"Error publishing event: {str(e)}")
            self.metrics.record_error("publish_error")
            self.tracer.record_exception(e)
            raise
            
    def subscribe(
        self,
        event_type: str,
        handler: Callable
    ):
        """Subscribe to event type"""
        if event_type not in self._handlers:
            self._handlers[event_type] = []
        self._handlers[event_type].append(handler)
        
        self.logger.info(f"New handler for event type: {event_type}")
        
    def unsubscribe(
        self,
        event_type: str,
        handler: Callable
    ):
        """Unsubscribe from event type"""
        if event_type in self._handlers:
            self._handlers[event_type].remove(handler)
            if not self._handlers[event_type]:
                del self._handlers[event_type]
                
        self.logger.info(f"Removed handler for event type: {event_type}")
        
    def add_filter(self, filter_func: Callable):
        """Add event filter"""
        self._filters.append(filter_func)
        
    def remove_filter(self, filter_func: Callable):
        """Remove event filter"""
        self._filters.remove(filter_func)
        
    def get_event_history(
        self,
        event_type: Optional[str] = None,
        limit: int = 100
    ) -> List[Event]:
        """Get event history"""
        events = self._history
        
        if event_type:
            events = [e for e in events if e.type == event_type]
            
        return events[-limit:]
        
    async def _process_queue(self, priority: EventPriority):
        """Process events from priority queue"""
        queue = self._queues[priority]
        
        while self._running:
            try:
                # Get next event
                _, event = await queue.get()
                
                with self.tracer.traced_operation(
                    "process_event",
                    {
                        "type": event.type,
                        "priority": priority.name
                    }
                ):
                    # Get handlers
                    handlers = self._handlers.get(event.type, [])
                    
                    # Execute handlers
                    for handler in handlers:
                        try:
                            await handler(event)
                        except Exception as e:
                            self.logger.error(
                                f"Error in event handler: {str(e)}"
                            )
                            self.metrics.record_error("handler_error")
                            self.tracer.record_exception(e)
                            
                    self.metrics.record_operation(
                        "event_processed",
                        "success",
                        {"type": event.type}
                    )
                    
                queue.task_done()
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                self.logger.error(
                    f"Error processing event queue: {str(e)}"
                )
                self.metrics.record_error("queue_error")
                self.tracer.record_exception(e)
                await asyncio.sleep(1)
                
    def _apply_filters(self, event: Event) -> bool:
        """Apply event filters"""
        for filter_func in self._filters:
            try:
                if not filter_func(event):
                    return False
            except Exception as e:
                self.logger.error(f"Error in event filter: {str(e)}")
                self.metrics.record_error("filter_error")
                
        return True
        
    def _add_to_history(self, event: Event):
        """Add event to history"""
        self._history.append(event)
        
        # Trim history if needed
        while len(self._history) > self._max_history:
            self._history.pop(0)

# Global event system instance
_event_system: Optional[EventSystem] = None

def get_event_system() -> EventSystem:
    """Get global event system instance"""
    global _event_system
    if _event_system is None:
        _event_system = EventSystem()
    return _event_system

# Example usage
if __name__ == "__main__":
    async def example():
        event_system = get_event_system()
        await event_system.start()
        
        # Subscribe to events
        async def handle_event(event: Event):
            print(f"Received event: {event.type}")
            print(f"Data: {event.data}")
            
        event_system.subscribe("test_event", handle_event)
        
        # Publish event
        await event_system.publish_event(
            "test_event",
            "example",
            {"message": "Test event"},
            EventPriority.HIGH
        )
        
        # Wait for processing
        await asyncio.sleep(1)
        
        await event_system.stop()
        
    asyncio.run(example())