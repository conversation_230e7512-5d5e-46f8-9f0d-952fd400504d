"""
Advanced Reasoning Agent using Claude 3.7 Sonnet
"""

import asyncio
import json
from typing import Dict, List, Any, Optional, Union, Callable
import logging
from core.anthropic_client import AnthropicClient
from core.enhanced_base_agent import EnhancedBaseAgent

logger = logging.getLogger(__name__)

class AdvancedReasoningAgent(EnhancedBaseAgent):
    """Agent with advanced reasoning capabilities using Claude 3.7 Sonnet"""
    
    def __init__(self, agent_id: str, agent_name: str, agent_description: str):
        super().__init__(agent_id, agent_name, agent_description)
        self.anthropic_client = AnthropicClient()
        self.reasoning_system_prompt = """
        You are an advanced reasoning agent with exceptional problem-solving abilities.
        Your task is to analyze complex problems, break them down into manageable components,
        and provide detailed, step-by-step solutions.
        
        When reasoning through problems:
        1. Identify the core components and variables
        2. Consider multiple perspectives and approaches
        3. Evaluate trade-offs between different solutions
        4. Provide clear, logical explanations for your reasoning
        5. When appropriate, use mathematical notation, diagrams, or pseudocode
        
        Your responses should be thorough, precise, and demonstrate deep understanding
        of the subject matter. Avoid oversimplifications and acknowledge limitations
        or uncertainties in your analysis.
        """
        
    async def complex_reasoning(
        self,
        problem: str,
        context: Optional[Dict[str, Any]] = None,
        max_tokens: int = 4096,
        temperature: float = 0.2
    ) -> Dict[str, Any]:
        """Perform complex reasoning on a problem using Claude 3.7 Sonnet"""
        
        # Construct prompt with context if provided
        prompt = problem
        if context:
            context_str = json.dumps(context, indent=2)
            prompt = f"Problem: {problem}\n\nContext: {context_str}"
            
        try:
            response = await self.anthropic_client.generate_text(
                prompt=prompt,
                system_prompt=self.reasoning_system_prompt,
                max_tokens=max_tokens,
                temperature=temperature
            )
            
            # Log the reasoning process
            logger.info(f"Advanced reasoning completed for problem: {problem[:100]}...")
            
            return response
        except Exception as e:
            logger.error(f"Error in complex reasoning: {e}")
            return {"error": str(e)}
            
    async def multi_step_reasoning(
        self,
        problem: str,
        steps: List[str],
        context: Optional[Dict[str, Any]] = None,
        max_tokens_per_step: int = 2048
    ) -> List[Dict[str, Any]]:
        """Perform multi-step reasoning process"""
        results = []
        current_context = context or {}
        
        for i, step in enumerate(steps):
            step_prompt = f"Step {i+1}/{len(steps)}: {step}\n\nProblem: {problem}"
            
            # Add previous steps' results to context
            if results:
                previous_results = "\n\n".join([
                    f"Step {j+1} result: {r.get('content', '')}" 
                    for j, r in enumerate(results)
                ])
                step_prompt += f"\n\nPrevious steps:\n{previous_results}"
            
            # Add context if available
            if current_context:
                context_str = json.dumps(current_context, indent=2)
                step_prompt += f"\n\nContext: {context_str}"
                
            response = await self.anthropic_client.generate_text(
                prompt=step_prompt,
                system_prompt=self.reasoning_system_prompt,
                max_tokens=max_tokens_per_step,
                temperature=0.2
            )
            
            results.append(response)
            
            # Update context with this step's result
            if "content" in response:
                current_context[f"step_{i+1}_result"] = response["content"]
                
        return results
        
    async def agent_collaboration(
        self,
        problem: str,
        agent_roles: List[str],
        context: Optional[Dict[str, Any]] = None,
        max_iterations: int = 3
    ) -> Dict[str, Any]:
        """Simulate collaboration between multiple agent perspectives"""
        conversation = []
        current_context = context or {}
        
        # Initial problem statement
        conversation.append({
            "role": "system",
            "content": f"Problem to solve collaboratively: {problem}"
        })
        
        for iteration in range(max_iterations):
            for role in agent_roles:
                # Create role-specific system prompt
                role_prompt = f"""
                You are now taking on the role of {role}.
                Consider the problem from this perspective.
                Review the conversation so far and contribute your unique insights.
                Be specific about how your role's perspective helps solve the problem.
                """
                
                # Construct the conversation history
                conversation_history = "\n\n".join([
                    f"{msg['role']}: {msg['content']}" for msg in conversation
                ])
                
                prompt = f"Conversation so far:\n{conversation_history}\n\nAs {role}, what insights can you add?"
                
                response = await self.anthropic_client.generate_text(
                    prompt=prompt,
                    system_prompt=role_prompt,
                    max_tokens=2048,
                    temperature=0.4
                )
                
                # Add response to conversation
                conversation.append({
                    "role": role,
                    "content": response.get("content", "")
                })
                
        # Final synthesis
        synthesis_prompt = """
        Review the entire collaborative discussion and synthesize the key insights.
        Identify where different perspectives agreed and disagreed.
        Provide a comprehensive solution that integrates the most valuable contributions.
        """
        
        conversation_history = "\n\n".join([
            f"{msg['role']}: {msg['content']}" for msg in conversation
        ])
        
        synthesis = await self.anthropic_client.generate_text(
            prompt=f"Full conversation:\n{conversation_history}\n\nPlease synthesize the key insights and provide a final solution.",
            system_prompt=synthesis_prompt,
            max_tokens=3072,
            temperature=0.3
        )
        
        return {
            "conversation": conversation,
            "synthesis": synthesis
        }
