"""
Database Manager for handling database connections and operations
"""

import asyncio
from typing import Dict, Any, Optional, List, Union
from datetime import datetime
import json
import asyncpg
from asyncpg import Pool, Connection
from contextlib import asynccontextmanager
from utils.logging_setup import get_logger
from utils.metrics import get_metrics_collector
from utils.tracing import get_tracer
from core.config_manager import get_config_manager, DatabaseConfig

class DatabaseManager:
    """Manages database connections and operations"""
    
    def __init__(self):
        self.logger = get_logger("database_manager")
        self.metrics = get_metrics_collector("database_manager")
        self.tracer = get_tracer("database_manager")
        
        # Load config
        config = get_config_manager().get_config()
        self.config: DatabaseConfig = config.database
        
        # Connection pool
        self._pool: Optional[Pool] = None
        self._initialized = False
        
        # Connection management
        self._transaction_connections: Dict[str, Connection] = {}
        
        # Query tracking
        self._slow_query_threshold = 1.0  # seconds
        
    async def initialize(self):
        """Initialize database connection pool"""
        if self._initialized:
            return
            
        try:
            with self.tracer.traced_operation("init_database"):
                # Create connection pool
                self._pool = await asyncpg.create_pool(
                    host=self.config.host,
                    port=self.config.port,
                    database=self.config.database,
                    user=self.config.username,
                    password=self.config.password,
                    min_size=self.config.min_connections,
                    max_size=self.config.max_connections,
                    command_timeout=self.config.connection_timeout
                )
                
                # Test connection
                async with self._pool.acquire() as conn:
                    await conn.execute('SELECT 1')
                    
                self._initialized = True
                self.logger.info("Database initialized successfully")
                
        except Exception as e:
            self.logger.error(f"Database initialization error: {str(e)}")
            self.metrics.record_error("db_init_error")
            self.tracer.record_exception(e)
            raise
            
    async def cleanup(self):
        """Clean up database resources"""
        if self._pool:
            await self._pool.close()
            self._initialized = False
            
    @asynccontextmanager
    async def connection(self) -> Connection:
        """Get database connection from pool"""
        if not self._initialized:
            await self.initialize()
            
        async with self._pool.acquire() as conn:
            try:
                yield conn
            except Exception as e:
                self.logger.error(f"Database connection error: {str(e)}")
                self.metrics.record_error("db_connection_error")
                self.tracer.record_exception(e)
                raise
                
    async def execute(
        self,
        query: str,
        *args,
        timeout: Optional[float] = None
    ) -> str:
        """Execute database query"""
        start_time = datetime.now()
        
        try:
            async with self.connection() as conn:
                with self.tracer.traced_operation(
                    "db_execute",
                    {"query": query}
                ):
                    result = await conn.execute(
                        query,
                        *args,
                        timeout=timeout
                    )
                    
                    duration = (datetime.now() - start_time).total_seconds()
                    self._record_query_metrics(duration)
                    
                    return result
                    
        except Exception as e:
            self.logger.error(f"Query execution error: {str(e)}")
            self.metrics.record_error("db_query_error")
            self.tracer.record_exception(e)
            raise
            
    async def fetch(
        self,
        query: str,
        *args,
        timeout: Optional[float] = None
    ) -> List[Dict]:
        """Fetch rows from database"""
        start_time = datetime.now()
        
        try:
            async with self.connection() as conn:
                with self.tracer.traced_operation(
                    "db_fetch",
                    {"query": query}
                ):
                    rows = await conn.fetch(
                        query,
                        *args,
                        timeout=timeout
                    )
                    
                    duration = (datetime.now() - start_time).total_seconds()
                    self._record_query_metrics(duration)
                    
                    return [dict(row) for row in rows]
                    
        except Exception as e:
            self.logger.error(f"Query fetch error: {str(e)}")
            self.metrics.record_error("db_fetch_error")
            self.tracer.record_exception(e)
            raise
            
    async def fetch_one(
        self,
        query: str,
        *args,
        timeout: Optional[float] = None
    ) -> Optional[Dict]:
        """Fetch single row from database"""
        rows = await self.fetch(query, *args, timeout=timeout)
        return rows[0] if rows else None
        
    async def start_transaction(self) -> str:
        """Start new database transaction"""
        if not self._initialized:
            await self.initialize()
            
        # Get connection and start transaction
        conn = await self._pool.acquire()
        tr = conn.transaction()
        await tr.start()
        
        # Generate transaction ID
        tx_id = str(len(self._transaction_connections))
        self._transaction_connections[tx_id] = conn
        
        return tx_id
        
    async def commit_transaction(self, tx_id: str):
        """Commit database transaction"""
        if tx_id not in self._transaction_connections:
            raise ValueError(f"Invalid transaction ID: {tx_id}")
            
        conn = self._transaction_connections[tx_id]
        try:
            await conn.transaction().commit()
        finally:
            await self._pool.release(conn)
            del self._transaction_connections[tx_id]
            
    async def rollback_transaction(self, tx_id: str):
        """Rollback database transaction"""
        if tx_id not in self._transaction_connections:
            raise ValueError(f"Invalid transaction ID: {tx_id}")
            
        conn = self._transaction_connections[tx_id]
        try:
            await conn.transaction().rollback()
        finally:
            await self._pool.release(conn)
            del self._transaction_connections[tx_id]
            
    async def get_state(self, key: str) -> Any:
        """Get state from database"""
        row = await self.fetch_one(
            "SELECT value FROM system_state WHERE key = $1",
            key
        )
        if row:
            return json.loads(row["value"])
        return None
        
    async def set_state(
        self,
        key: str,
        value: Any,
        source: str
    ):
        """Set state in database"""
        await self.execute(
            """
            INSERT INTO system_state (key, value, source, updated_at)
            VALUES ($1, $2, $3, $4)
            ON CONFLICT (key) DO UPDATE
            SET value = $2, source = $3, updated_at = $4
            """,
            key,
            json.dumps(value),
            source,
            datetime.utcnow()
        )
        
    async def delete_state(self, key: str):
        """Delete state from database"""
        await self.execute(
            "DELETE FROM system_state WHERE key = $1",
            key
        )
        
    def _record_query_metrics(self, duration: float):
        """Record query metrics"""
        self.metrics.record_operation(
            "db_query",
            "success"
        )
        
        self.metrics.set_gauge(
            "db_query_duration",
            duration
        )
        
        if duration > self._slow_query_threshold:
            self.metrics.record_operation(
                "db_slow_query",
                "warning"
            )
            
    async def health_check(self) -> Dict[str, Any]:
        """Check database health"""
        try:
            start_time = datetime.now()
            async with self.connection() as conn:
                await conn.execute("SELECT 1")
                
            duration = (datetime.now() - start_time).total_seconds()
            
            pool_status = {
                "total_connections": len(self._pool._holders),
                "acquired_connections": len(
                    [h for h in self._pool._holders if h._con is not None]
                ),
                "free_connections": len(
                    [h for h in self._pool._holders if h._con is None]
                )
            }
            
            return {
                "status": "healthy",
                "response_time": duration,
                "pool": pool_status
            }
            
        except Exception as e:
            return {
                "status": "unhealthy",
                "error": str(e)
            }

# Global database manager instance
_database_manager: Optional[DatabaseManager] = None

def get_database_manager() -> DatabaseManager:
    """Get global database manager instance"""
    global _database_manager
    if _database_manager is None:
        _database_manager = DatabaseManager()
    return _database_manager

# Example usage
if __name__ == "__main__":
    async def example():
        db = get_database_manager()
        await db.initialize()
        
        # Execute query
        await db.execute(
            """
            CREATE TABLE IF NOT EXISTS test (
                id SERIAL PRIMARY KEY,
                name TEXT NOT NULL
            )
            """
        )
        
        # Insert data
        await db.execute(
            "INSERT INTO test (name) VALUES ($1)",
            "test_record"
        )
        
        # Query data
        rows = await db.fetch("SELECT * FROM test")
        print(f"Query results: {rows}")
        
        # Clean up
        await db.cleanup()
        
    asyncio.run(example())