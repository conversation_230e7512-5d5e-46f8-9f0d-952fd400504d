import os
import json
import asyncio
import httpx
from typing import Dict, List, Any, Optional
import torch
from transformers import AutoModelForCausalLM, AutoTokenizer, pipeline

class LocalLLMManager:
    """Manager for local language models and inference."""
    
    def __init__(self):
        self.models: Dict[str, Any] = {}
        self.tokenizers: Dict[str, Any] = {}
        self._cache: Dict[str, Any] = {}
        self.device = "cuda" if torch.cuda.is_available() else "cpu"
        
    async def load_model(self, model_name: str, model_path: Optional[str] = None) -> bool:
        """Load a local model either from Hugging Face or local path."""
        try:
            if model_name in self.models:
                return True
                
            if model_path and os.path.exists(model_path):
                path = model_path
            else:
                path = model_name
                
            self.tokenizers[model_name] = AutoTokenizer.from_pretrained(path)
            self.models[model_name] = AutoModelForCausalLM.from_pretrained(
                path,
                device_map="auto" if torch.cuda.is_available() else None,
                torch_dtype=torch.float16 if torch.cuda.is_available() else torch.float32
            )
            
            return True
        except Exception as e:
            print(f"Error loading model {model_name}: {str(e)}")
            return False
            
    async def generate_text(
        self,
        model_name: str,
        prompt: str,
        max_length: int = 100,
        temperature: float = 0.7,
        num_return_sequences: int = 1,
        cache_key: Optional[str] = None
    ) -> List[str]:
        """Generate text using a loaded model."""
        if model_name not in self.models:
            raise ValueError(f"Model {model_name} not loaded")
            
        # Check cache
        if cache_key and cache_key in self._cache:
            return self._cache[cache_key]
            
        inputs = self.tokenizers[model_name](
            prompt,
            return_tensors="pt",
            padding=True,
            truncation=True
        ).to(self.device)
        
        with torch.no_grad():
            outputs = self.models[model_name].generate(
                **inputs,
                max_length=max_length,
                num_return_sequences=num_return_sequences,
                temperature=temperature,
                pad_token_id=self.tokenizers[model_name].eos_token_id
            )
            
        generated_texts = [
            self.tokenizers[model_name].decode(output, skip_special_tokens=True)
            for output in outputs
        ]
        
        if cache_key:
            self._cache[cache_key] = generated_texts
            
        return generated_texts
        
    async def get_embeddings(
        self,
        model_name: str,
        texts: List[str],
        cache_key: Optional[str] = None
    ) -> List[List[float]]:
        """Get embeddings for input texts."""
        if model_name not in self.models:
            raise ValueError(f"Model {model_name} not loaded")
            
        if cache_key and cache_key in self._cache:
            return self._cache[cache_key]
            
        pipe = pipeline(
            "feature-extraction",
            model=self.models[model_name],
            tokenizer=self.tokenizers[model_name],
            device=self.device
        )
        
        embeddings = []
        for text in texts:
            features = pipe(text)
            # Take mean of token embeddings
            embedding = torch.mean(torch.tensor(features[0]), dim=0).tolist()
            embeddings.append(embedding)
            
        if cache_key:
            self._cache[cache_key] = embeddings
            
        return embeddings
        
    def clear_cache(self):
        """Clear the generation and embedding cache."""
        self._cache.clear()
        
    def unload_model(self, model_name: str):
        """Unload a model to free up memory."""
        if model_name in self.models:
            del self.models[model_name]
        if model_name in self.tokenizers:
            del self.tokenizers[model_name]