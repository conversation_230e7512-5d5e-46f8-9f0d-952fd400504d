from typing import Dict, Any, List, Optional, Tuple
import numpy as np
from datetime import datetime, timedelta
from collections import deque
import logging
from core.metrics import get_metrics_collector
from utils.logging_setup import get_logger
from sklearn.ensemble import IsolationForest
from sklearn.preprocessing import StandardScaler

class UIAnomalyDetector:
    """Detects anomalies in UI interactions using machine learning"""
    
    def __init__(self, window_size: int = 1000):
        self.logger = get_logger("ui_anomaly")
        self.metrics = get_metrics_collector("ui_anomaly")
        self.window_size = window_size
        self.interaction_history = deque(maxlen=window_size)
        self.scaler = StandardScaler()
        self.detector = IsolationForest(
            contamination=0.1,  # Expected proportion of anomalies
            random_state=42
        )
        self.baseline_established = False
        self.alert_thresholds = {
            "response_time": 2.0,  # seconds
            "error_rate": 0.2,     # 20%
            "deviation_threshold": 3.0  # standard deviations
        }

    async def process_interaction(self, interaction_data: Dict[str, Any]) -> Dict[str, Any]:
        """Process a new UI interaction and detect anomalies"""
        # Extract features
        features = self._extract_features(interaction_data)
        self.interaction_history.append(features)
        
        # Update metrics
        self.metrics.record_interaction(
            duration=features["duration"],
            success=features["success"],
            platform=interaction_data.get("platform", "unknown")
        )
        
        # Detect anomalies
        anomalies = await self._detect_anomalies(features)
        
        if anomalies["is_anomaly"]:
            self.logger.warning(f"UI Anomaly detected: {anomalies['details']}")
            self.metrics.record_error("anomaly_detected")
            
        return anomalies

    def _extract_features(self, interaction_data: Dict[str, Any]) -> Dict[str, float]:
        """Extract relevant features from interaction data"""
        return {
            "duration": interaction_data.get("duration", 0.0),
            "success": float(interaction_data.get("success", False)),
            "error_count": float(interaction_data.get("error_count", 0)),
            "action_count": float(interaction_data.get("action_count", 1)),
            "confidence": interaction_data.get("confidence", 1.0)
        }

    async def _detect_anomalies(self, features: Dict[str, float]) -> Dict[str, Any]:
        """Detect anomalies in interaction features"""
        if len(self.interaction_history) < 100:  # Need minimum data for baseline
            return {"is_anomaly": False, "reason": "Insufficient data"}
            
        # Prepare feature matrix
        feature_matrix = np.array([list(f.values()) for f in self.interaction_history])
        
        if not self.baseline_established:
            # Fit models on initial data
            self.scaler.fit(feature_matrix)
            self.detector.fit(self.scaler.transform(feature_matrix))
            self.baseline_established = True
        
        # Check for immediate red flags
        quick_checks = self._quick_anomaly_checks(features)
        if quick_checks["is_anomaly"]:
            return quick_checks
        
        # Perform full anomaly detection
        current_features = np.array(list(features.values())).reshape(1, -1)
        scaled_features = self.scaler.transform(current_features)
        
        # Get anomaly score (-1 for anomalies, 1 for normal)
        anomaly_score = self.detector.score_samples(scaled_features)[0]
        
        # Check feature-specific anomalies
        specific_anomalies = self._check_specific_anomalies(features, feature_matrix)
        
        is_anomaly = anomaly_score < -0.5 or specific_anomalies["is_anomaly"]
        
        return {
            "is_anomaly": is_anomaly,
            "score": float(anomaly_score),
            "details": {
                "global_score": float(anomaly_score),
                "specific_checks": specific_anomalies["details"],
                "features": features
            },
            "severity": "high" if anomaly_score < -0.7 else "medium" if is_anomaly else "low"
        }

    def _quick_anomaly_checks(self, features: Dict[str, float]) -> Dict[str, Any]:
        """Perform quick checks for obvious anomalies"""
        if features["duration"] > self.alert_thresholds["response_time"]:
            return {
                "is_anomaly": True,
                "reason": "Response time exceeded threshold",
                "details": {"duration": features["duration"]}
            }
            
        if features["error_count"] / max(1, features["action_count"]) > self.alert_thresholds["error_rate"]:
            return {
                "is_anomaly": True,
                "reason": "Error rate exceeded threshold",
                "details": {"error_rate": features["error_count"] / features["action_count"]}
            }
            
        return {"is_anomaly": False}

    def _check_specific_anomalies(self, features: Dict[str, float], 
                                history: np.ndarray) -> Dict[str, Any]:
        """Check for anomalies in specific features"""
        anomalies = []
        details = {}
        
        for i, feature_name in enumerate(features.keys()):
            feature_history = history[:, i]
            mean = np.mean(feature_history)
            std = np.std(feature_history)
            
            if std > 0:
                z_score = abs(features[feature_name] - mean) / std
                if z_score > self.alert_thresholds["deviation_threshold"]:
                    anomalies.append(feature_name)
                    details[feature_name] = {
                        "z_score": float(z_score),
                        "value": float(features[feature_name]),
                        "mean": float(mean),
                        "std": float(std)
                    }
        
        return {
            "is_anomaly": len(anomalies) > 0,
            "details": {
                "anomalous_features": anomalies,
                "feature_details": details
            }
        }

    def update_thresholds(self, new_thresholds: Dict[str, float]):
        """Update anomaly detection thresholds"""
        self.alert_thresholds.update(new_thresholds)
        self.logger.info(f"Updated anomaly thresholds: {self.alert_thresholds}")

    def get_statistics(self) -> Dict[str, Any]:
        """Get current anomaly detection statistics"""
        if len(self.interaction_history) == 0:
            return {"status": "No data collected"}
            
        feature_matrix = np.array([list(f.values()) for f in self.interaction_history])
        
        return {
            "total_interactions": len(self.interaction_history),
            "baseline_established": self.baseline_established,
            "feature_means": {
                name: float(mean) for name, mean in zip(
                    self.interaction_history[0].keys(),
                    np.mean(feature_matrix, axis=0)
                )
            },
            "feature_stds": {
                name: float(std) for name, std in zip(
                    self.interaction_history[0].keys(),
                    np.std(feature_matrix, axis=0)
                )
            }
        }