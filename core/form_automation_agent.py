from typing import Dict, Any, List, Optional
from datetime import datetime
from core.enhanced_base_agent import EnhancedBaseAgent

class FormAutomationAgent(EnhancedBaseAgent):
    def __init__(self, agent_name: str = "form_automation_agent"):
        super().__init__(agent_name, enable_ui=True)
        self.form_templates = {}
        self.field_cache = {}
        
    async def fill_form(self, form_data: Dict[str, Any], template_id: Optional[str] = None) -> bool:
        """Fill a form using cached templates and optimized UI interactions"""
        try:
            # Use template if available
            actions = []
            if template_id and template_id in self.form_templates:
                actions = self._generate_actions_from_template(template_id, form_data)
            else:
                # Generate actions by finding form elements
                actions = await self._discover_form_elements(form_data)
                
            # Execute all actions with retries and error handling
            results = await self.batch_ui_actions(actions)
            success = all(success for success, _ in results)
            
            # Update template if successful
            if success and template_id:
                self.form_templates[template_id] = {
                    "actions": actions,
                    "last_used": datetime.now(),
                    "success_count": self.form_templates.get(template_id, {}).get("success_count", 0) + 1
                }
                
            # Log performance metrics
            metrics = self.get_ui_performance_metrics()
            self.logger.info(f"Form Automation Performance: {metrics}")
            
            return success
            
        except Exception as e:
            self.logger.error(f"Error in form automation: {str(e)}")
            return False
            
    async def _discover_form_elements(self, form_data: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Dynamically discover form elements and generate actions"""
        actions = []
        
        # Find form fields using visual recognition
        for field_name, value in form_data.items():
            # Try to find field by label or placeholder
            field_action = {
                "action": "find_element",
                "params": {
                    "description": f"Input field for {field_name}",
                    "alternatives": [
                        field_name,
                        field_name.replace("_", " ").title(),
                        f"{field_name} field",
                        f"{field_name} input"
                    ]
                }
            }
            actions.append(field_action)
            
            # Add click and type actions
            actions.extend([
                {"action": "click", "params": {"x": "$ELEMENT_X", "y": "$ELEMENT_Y"}},
                {"action": "type", "params": {"text": str(value)}}
            ])
            
        # Find and add submit button action
        submit_action = {
            "action": "find_element",
            "params": {
                "description": "Submit button",
                "alternatives": ["Submit", "Save", "Continue", "Next"]
            }
        }
        actions.append(submit_action)
        actions.append({"action": "click", "params": {"x": "$ELEMENT_X", "y": "$ELEMENT_Y"}})
        
        return actions
        
    def _generate_actions_from_template(self, template_id: str, form_data: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Generate actions using a cached template"""
        template = self.form_templates[template_id]
        actions = []
        
        for action in template["actions"]:
            if action["action"] == "type":
                # Update text value from new form data
                field_name = action["params"].get("field_name")
                if field_name and field_name in form_data:
                    action = {
                        **action,
                        "params": {
                            **action["params"],
                            "text": str(form_data[field_name])
                        }
                    }
            actions.append(action)
            
        return actions
        
    async def validate_form_submission(self, expected_result: Dict[str, Any]) -> bool:
        """Validate form submission result"""
        # Look for success/error messages
        validation_actions = [
            {
                "action": "find_element",
                "params": {
                    "description": "Success message",
                    "alternatives": [
                        "Form submitted successfully",
                        "Thank you for your submission",
                        "Success!"
                    ]
                }
            }
        ]
        
        results = await self.batch_ui_actions(validation_actions)
        return any(success for success, _ in results)