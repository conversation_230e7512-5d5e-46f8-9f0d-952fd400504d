import asyncio
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional
import cv2
import numpy as np
from collections import deque
import logging
from core.metrics import get_metrics_collector
from utils.logging_setup import get_logger

class VisualInteractionMonitor:
    """Monitors and analyzes visual interactions for improved UI automation"""
    
    def __init__(self):
        self.logger = get_logger("visual_monitor")
        self.metrics = get_metrics_collector("visual_monitor")
        self.screen_history = deque(maxlen=100)  # Last 100 screen states
        self.interaction_map = {}  # Heatmap of interactions
        self.element_cache = {}  # Cache of found UI elements
        self.performance_data = {
            "recognition_times": [],
            "success_rates": [],
            "error_points": []
        }

    async def record_screen_state(self, screen_state: np.ndarray, 
                                interaction_data: Dict[str, Any]):
        """Record screen state and interaction data"""
        timestamp = datetime.now()
        
        # Store screen state with metadata
        self.screen_history.append({
            "timestamp": timestamp,
            "state": screen_state,
            "interaction": interaction_data
        })
        
        # Update interaction heatmap
        if "click_position" in interaction_data:
            x, y = interaction_data["click_position"]
            self.interaction_map[(x, y)] = self.interaction_map.get((x, y), 0) + 1
            
        # Update metrics
        self.metrics.increment_counter("screen_states_recorded")
        
    async def analyze_element_recognition(self, template: np.ndarray, 
                                       screen: np.ndarray) -> Dict[str, Any]:
        """Analyze UI element recognition performance"""
        try:
            start_time = datetime.now()
            
            # Perform template matching
            result = cv2.matchTemplate(screen, template, cv2.TM_CCOEFF_NORMED)
            min_val, max_val, min_loc, max_loc = cv2.minMaxLoc(result)
            
            recognition_time = (datetime.now() - start_time).total_seconds()
            self.performance_data["recognition_times"].append(recognition_time)
            
            success = max_val >= 0.8  # Confidence threshold
            self.performance_data["success_rates"].append(1.0 if success else 0.0)
            
            if not success:
                self.performance_data["error_points"].append({
                    "timestamp": datetime.now(),
                    "confidence": max_val,
                    "template_size": template.shape
                })
                
            return {
                "success": success,
                "confidence": max_val,
                "position": max_loc if success else None,
                "recognition_time": recognition_time
            }
            
        except Exception as e:
            self.logger.error(f"Error in element recognition: {str(e)}")
            return {"success": False, "error": str(e)}
            
    def get_performance_metrics(self) -> Dict[str, Any]:
        """Get visual interaction performance metrics"""
        if not self.performance_data["recognition_times"]:
            return {"status": "No data available"}
            
        avg_recognition_time = sum(self.performance_data["recognition_times"]) / len(self.performance_data["recognition_times"])
        success_rate = sum(self.performance_data["success_rates"]) / len(self.performance_data["success_rates"]) * 100
        
        recent_errors = [e for e in self.performance_data["error_points"] 
                        if e["timestamp"] > datetime.now() - timedelta(hours=1)]
        
        return {
            "average_recognition_time": avg_recognition_time,
            "success_rate_percent": success_rate,
            "recent_error_count": len(recent_errors),
            "total_interactions": len(self.performance_data["recognition_times"])
        }
        
    def get_interaction_hotspots(self) -> List[Dict[str, Any]]:
        """Get areas of frequent UI interaction"""
        if not self.interaction_map:
            return []
            
        hotspots = []
        for (x, y), count in self.interaction_map.items():
            if count > 5:  # Threshold for hotspot
                hotspots.append({
                    "position": (x, y),
                    "frequency": count,
                    "last_success": self._get_last_success_at_position(x, y)
                })
                
        return sorted(hotspots, key=lambda x: x["frequency"], reverse=True)
        
    def _get_last_success_at_position(self, x: int, y: int) -> Optional[datetime]:
        """Get timestamp of last successful interaction at position"""
        for state in reversed(self.screen_history):
            interaction = state["interaction"]
            if (interaction.get("click_position") == (x, y) and 
                interaction.get("success", False)):
                return state["timestamp"]
        return None
        
    def cache_element(self, element_id: str, template: np.ndarray, 
                     metadata: Dict[str, Any]):
        """Cache a UI element template for faster recognition"""
        self.element_cache[element_id] = {
            "template": template,
            "metadata": metadata,
            "last_updated": datetime.now(),
            "success_count": 0
        }
        
    def update_element_success(self, element_id: str, success: bool):
        """Update success statistics for cached element"""
        if element_id in self.element_cache:
            if success:
                self.element_cache[element_id]["success_count"] += 1
            self.element_cache[element_id]["last_updated"] = datetime.now()
            
    def cleanup_cache(self, max_age_hours: int = 24):
        """Clean up old cached elements"""
        current_time = datetime.now()
        expired_ids = [
            element_id for element_id, data in self.element_cache.items()
            if (current_time - data["last_updated"]).total_seconds() > max_age_hours * 3600
        ]
        
        for element_id in expired_ids:
            del self.element_cache[element_id]
            
        self.logger.info(f"Cleaned up {len(expired_ids)} expired cache entries")