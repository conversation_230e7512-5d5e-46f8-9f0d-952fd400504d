"""
Test Framework for agent system testing
"""

import asyncio
from typing import Dict, Any, Optional, List, Callable, Type, Union
from datetime import datetime
import inspect
import traceback
from enum import Enum
from dataclasses import dataclass
import uuid
from utils.logging_setup import get_logger
from utils.metrics import get_metrics_collector
from utils.tracing import get_tracer

class TestResult(Enum):
    """Test execution results"""
    PASSED = "passed"
    FAILED = "failed"
    SKIPPED = "skipped"
    ERROR = "error"

@dataclass
class TestCase:
    """Individual test case"""
    id: str
    name: str
    test_func: Callable
    setup: Optional[Callable] = None
    cleanup: Optional[Callable] = None
    timeout: int = 60
    retries: int = 0
    tags: List[str] = None
    dependencies: List[str] = None
    metadata: Dict[str, Any] = None

@dataclass
class TestSuite:
    """Collection of test cases"""
    id: str
    name: str
    cases: List[TestCase]
    setup: Optional[Callable] = None
    cleanup: Optional[Callable] = None
    parallel: bool = False
    timeout: int = 300
    metadata: Dict[str, Any] = None

@dataclass
class TestExecution:
    """Test execution details"""
    id: str
    case: TestCase
    result: TestResult
    start_time: datetime
    end_time: Optional[datetime] = None
    duration: Optional[float] = None
    error: Optional[str] = None
    traceback: Optional[str] = None
    output: Optional[str] = None
    context: Dict[str, Any] = None

class TestFramework:
    """Manages test execution and reporting"""
    
    def __init__(self):
        self.logger = get_logger("test_framework")
        self.metrics = get_metrics_collector("test_framework")
        self.tracer = get_tracer("test_framework")
        
        # Test registry
        self._suites: Dict[str, TestSuite] = {}
        self._cases: Dict[str, TestCase] = {}
        
        # Test results
        self._results: Dict[str, List[TestExecution]] = {}
        
        # Test contexts
        self._contexts: Dict[str, Dict[str, Any]] = {}
        
        # Result handlers
        self._result_handlers: List[Callable] = []
        
        # Test fixtures
        self._fixtures: Dict[str, Any] = {}
        
    def register_suite(
        self,
        name: str,
        cases: List[TestCase],
        **kwargs
    ) -> str:
        """Register test suite"""
        suite_id = str(uuid.uuid4())
        suite = TestSuite(
            id=suite_id,
            name=name,
            cases=cases,
            **kwargs
        )
        
        self._suites[suite_id] = suite
        for case in cases:
            self._cases[case.id] = case
            
        return suite_id
        
    def register_case(
        self,
        name: str,
        test_func: Callable,
        **kwargs
    ) -> str:
        """Register individual test case"""
        case_id = str(uuid.uuid4())
        case = TestCase(
            id=case_id,
            name=name,
            test_func=test_func,
            **kwargs
        )
        
        self._cases[case_id] = case
        return case_id
        
    def register_fixture(
        self,
        name: str,
        fixture: Any
    ):
        """Register test fixture"""
        self._fixtures[name] = fixture
        
    def register_result_handler(
        self,
        handler: Callable
    ):
        """Register test result handler"""
        self._result_handlers.append(handler)
        
    async def run_suite(
        self,
        suite_id: str,
        context: Optional[Dict] = None
    ) -> List[TestExecution]:
        """Run test suite"""
        if suite_id not in self._suites:
            raise ValueError(f"Suite not found: {suite_id}")
            
        suite = self._suites[suite_id]
        context = context or {}
        
        try:
            # Suite setup
            if suite.setup:
                await self._run_setup(suite.setup, context)
                
            # Run test cases
            results = []
            if suite.parallel:
                # Run cases in parallel
                tasks = [
                    asyncio.create_task(
                        self._run_case(case, context.copy())
                    )
                    for case in suite.cases
                ]
                results = await asyncio.gather(*tasks)
            else:
                # Run cases sequentially
                for case in suite.cases:
                    result = await self._run_case(case, context)
                    results.append(result)
                    
            # Store results
            self._results[suite_id] = results
            return results
            
        finally:
            # Suite cleanup
            if suite.cleanup:
                try:
                    await self._run_cleanup(suite.cleanup, context)
                except Exception as e:
                    self.logger.error(
                        f"Error in suite cleanup: {str(e)}"
                    )
                    
    async def run_case(
        self,
        case_id: str,
        context: Optional[Dict] = None
    ) -> TestExecution:
        """Run individual test case"""
        if case_id not in self._cases:
            raise ValueError(f"Case not found: {case_id}")
            
        result = await self._run_case(
            self._cases[case_id],
            context or {}
        )
        
        # Store result
        if case_id not in self._results:
            self._results[case_id] = []
        self._results[case_id].append(result)
        
        return result
        
    def get_results(
        self,
        test_id: Optional[str] = None
    ) -> Union[List[TestExecution], Dict[str, List[TestExecution]]]:
        """Get test results"""
        if test_id:
            return self._results.get(test_id, [])
        return self._results.copy()
        
    async def _run_case(
        self,
        case: TestCase,
        context: Dict
    ) -> TestExecution:
        """Run test case with retries"""
        attempts = 0
        last_error = None
        
        while attempts <= case.retries:
            try:
                return await self._execute_case(case, context)
            except Exception as e:
                attempts += 1
                last_error = e
                if attempts <= case.retries:
                    self.logger.warning(
                        f"Retrying test {case.name} "
                        f"(attempt {attempts}/{case.retries})"
                    )
                    await asyncio.sleep(1)
                    
        # All retries failed
        return TestExecution(
            id=str(uuid.uuid4()),
            case=case,
            result=TestResult.ERROR,
            start_time=datetime.utcnow(),
            end_time=datetime.utcnow(),
            duration=0,
            error=str(last_error),
            traceback=traceback.format_exc(),
            context=context
        )
        
    async def _execute_case(
        self,
        case: TestCase,
        context: Dict
    ) -> TestExecution:
        """Execute single test case"""
        execution = TestExecution(
            id=str(uuid.uuid4()),
            case=case,
            result=TestResult.SKIPPED,
            start_time=datetime.utcnow(),
            context=context
        )
        
        try:
            # Check dependencies
            if case.dependencies:
                for dep in case.dependencies:
                    if not self._check_dependency(dep, context):
                        execution.result = TestResult.SKIPPED
                        execution.error = f"Dependency not met: {dep}"
                        return execution
                        
            # Case setup
            if case.setup:
                await self._run_setup(case.setup, context)
                
            # Run test
            with self.tracer.traced_operation(
                "test_case",
                {"name": case.name}
            ):
                result = case.test_func(context, self._fixtures)
                if asyncio.iscoroutine(result):
                    result = await asyncio.wait_for(
                        result,
                        timeout=case.timeout
                    )
                    
            execution.result = TestResult.PASSED
            execution.output = result
            
        except Exception as e:
            execution.result = TestResult.FAILED
            execution.error = str(e)
            execution.traceback = traceback.format_exc()
            
            self.metrics.record_error(
                "test_failure",
                {"test": case.name}
            )
            
        finally:
            # Case cleanup
            if case.cleanup:
                try:
                    await self._run_cleanup(case.cleanup, context)
                except Exception as e:
                    self.logger.error(
                        f"Error in case cleanup: {str(e)}"
                    )
                    
            # Record completion
            execution.end_time = datetime.utcnow()
            execution.duration = (
                execution.end_time - execution.start_time
            ).total_seconds()
            
            # Notify handlers
            await self._notify_result(execution)
            
        return execution
        
    async def _run_setup(
        self,
        setup: Callable,
        context: Dict
    ):
        """Run setup function"""
        try:
            result = setup(context, self._fixtures)
            if asyncio.iscoroutine(result):
                await result
        except Exception as e:
            self.logger.error(f"Setup error: {str(e)}")
            raise
            
    async def _run_cleanup(
        self,
        cleanup: Callable,
        context: Dict
    ):
        """Run cleanup function"""
        try:
            result = cleanup(context, self._fixtures)
            if asyncio.iscoroutine(result):
                await result
        except Exception as e:
            self.logger.error(f"Cleanup error: {str(e)}")
            raise
            
    def _check_dependency(
        self,
        dependency: str,
        context: Dict
    ) -> bool:
        """Check if test dependency is satisfied"""
        if dependency not in self._results:
            return False
            
        results = self._results[dependency]
        return any(
            r.result == TestResult.PASSED
            for r in results
        )
        
    async def _notify_result(
        self,
        execution: TestExecution
    ):
        """Notify result handlers"""
        for handler in self._result_handlers:
            try:
                result = handler(execution)
                if asyncio.iscoroutine(result):
                    await result
            except Exception as e:
                self.logger.error(
                    f"Error in result handler: {str(e)}"
                )

def test_case(**kwargs):
    """Decorator to mark test case functions"""
    def decorator(func):
        setattr(func, "__test_case__", True)
        setattr(func, "__test_options__", kwargs)
        return func
    return decorator

def test_fixture(name: str):
    """Decorator to mark test fixtures"""
    def decorator(func):
        setattr(func, "__test_fixture__", name)
        return func
    return decorator

# Global test framework instance
_test_framework: Optional[TestFramework] = None

def get_test_framework() -> TestFramework:
    """Get global test framework instance"""
    global _test_framework
    if _test_framework is None:
        _test_framework = TestFramework()
    return _test_framework

# Example usage
if __name__ == "__main__":
    @test_fixture("db")
    async def setup_database():
        return {"connected": True}
        
    @test_case(timeout=30)
    async def test_example(context, fixtures):
        assert fixtures["db"]["connected"]
        return "Test passed"
        
    async def example():
        framework = get_test_framework()
        
        # Register fixture
        framework.register_fixture(
            "db",
            await setup_database()
        )
        
        # Register and run test
        case_id = framework.register_case(
            "Example Test",
            test_example
        )
        result = await framework.run_case(case_id)
        
        print(f"Test result: {result.result.value}")
        
    asyncio.run(example())