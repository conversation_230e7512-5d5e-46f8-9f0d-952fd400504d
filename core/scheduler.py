"""
Scheduler for handling scheduled tasks and periodic jobs
"""

import asyncio
from typing import Dict, Any, Optional, List, Union, Callable
from datetime import datetime, timedelta
import json
from dataclasses import dataclass
from enum import Enum
import uuid
from croniter import croniter
from utils.logging_setup import get_logger
from utils.metrics import get_metrics_collector
from utils.tracing import get_tracer

class ScheduleType(Enum):
    """Types of task schedules"""
    INTERVAL = "interval"  # Run at fixed intervals
    CRON = "cron"  # Run on cron schedule
    ONCE = "once"  # Run once at specific time
    ON_EVENT = "on_event"  # Run on event trigger

@dataclass
class ScheduledTask:
    """Represents a scheduled task"""
    id: str
    name: str
    handler: Callable
    schedule_type: ScheduleType
    schedule: Union[int, str, datetime]  # Interval in seconds, cron expr, or datetime
    metadata: Dict = None
    last_run: Optional[datetime] = None
    next_run: Optional[datetime] = None
    error_count: int = 0
    max_retries: int = 3
    retry_delay: int = 60  # seconds
    is_running: bool = False
    enabled: bool = True

class Scheduler:
    """Manages scheduled tasks and periodic jobs"""
    
    def __init__(self):
        self.logger = get_logger("scheduler")
        self.metrics = get_metrics_collector("scheduler")
        self.tracer = get_tracer("scheduler")
        
        # Task registry
        self._tasks: Dict[str, ScheduledTask] = {}
        
        # Task queues
        self._pending_tasks: asyncio.PriorityQueue = asyncio.PriorityQueue()
        self._retry_tasks: Dict[str, List[datetime]] = {}
        
        # Task results
        self._task_results: Dict[str, Any] = {}
        self._task_errors: Dict[str, List[str]] = {}
        
        # Running state
        self._running = False
        self._scheduler_task: Optional[asyncio.Task] = None
        
        # Event callbacks
        self._event_handlers: Dict[str, List[ScheduledTask]] = {}
        
    async def start(self):
        """Start the scheduler"""
        if self._running:
            return
            
        try:
            self._running = True
            self._scheduler_task = asyncio.create_task(
                self._scheduler_loop()
            )
            
            self.logger.info("Scheduler started")
            
        except Exception as e:
            self.logger.error(f"Error starting scheduler: {str(e)}")
            self.metrics.record_error("scheduler_start_error")
            self.tracer.record_exception(e)
            raise
            
    async def stop(self):
        """Stop the scheduler"""
        if not self._running:
            return
            
        try:
            self._running = False
            if self._scheduler_task:
                self._scheduler_task.cancel()
                try:
                    await self._scheduler_task
                except asyncio.CancelledError:
                    pass
                    
            self.logger.info("Scheduler stopped")
            
        except Exception as e:
            self.logger.error(f"Error stopping scheduler: {str(e)}")
            self.metrics.record_error("scheduler_stop_error")
            self.tracer.record_exception(e)
            raise
            
    def schedule_task(
        self,
        name: str,
        handler: Callable,
        schedule_type: ScheduleType,
        schedule: Union[int, str, datetime],
        metadata: Optional[Dict] = None
    ) -> str:
        """Schedule a new task"""
        try:
            # Generate task ID
            task_id = str(uuid.uuid4())
            
            # Create task
            task = ScheduledTask(
                id=task_id,
                name=name,
                handler=handler,
                schedule_type=schedule_type,
                schedule=schedule,
                metadata=metadata or {}
            )
            
            # Set next run time
            task.next_run = self._calculate_next_run(task)
            
            # Store task
            self._tasks[task_id] = task
            
            # Add to queue
            self._add_to_queue(task)
            
            self.logger.info(f"Scheduled task: {name} ({task_id})")
            self.metrics.record_operation(
                "task_scheduled",
                "success"
            )
            
            return task_id
            
        except Exception as e:
            self.logger.error(f"Error scheduling task: {str(e)}")
            self.metrics.record_error("schedule_error")
            self.tracer.record_exception(e)
            raise
            
    def schedule_on_event(
        self,
        name: str,
        handler: Callable,
        event_type: str,
        metadata: Optional[Dict] = None
    ) -> str:
        """Schedule task to run on event"""
        task_id = self.schedule_task(
            name,
            handler,
            ScheduleType.ON_EVENT,
            event_type,
            metadata
        )
        
        # Register event handler
        if event_type not in self._event_handlers:
            self._event_handlers[event_type] = []
        self._event_handlers[event_type].append(self._tasks[task_id])
        
        return task_id
        
    def cancel_task(self, task_id: str):
        """Cancel scheduled task"""
        if task_id not in self._tasks:
            raise ValueError(f"Invalid task ID: {task_id}")
            
        task = self._tasks[task_id]
        task.enabled = False
        
        self.logger.info(f"Cancelled task: {task.name} ({task_id})")
        
    async def trigger_event(self, event_type: str, event_data: Any = None):
        """Trigger event-based tasks"""
        if event_type not in self._event_handlers:
            return
            
        for task in self._event_handlers[event_type]:
            if not task.enabled:
                continue
                
            try:
                await self._run_task(task, event_data)
            except Exception as e:
                self.logger.error(
                    f"Error running event task {task.name}: {str(e)}"
                )
                
    def get_task_status(self, task_id: str) -> Dict[str, Any]:
        """Get task status"""
        if task_id not in self._tasks:
            raise ValueError(f"Invalid task ID: {task_id}")
            
        task = self._tasks[task_id]
        return {
            "id": task.id,
            "name": task.name,
            "schedule_type": task.schedule_type.value,
            "schedule": task.schedule,
            "last_run": task.last_run.isoformat() if task.last_run else None,
            "next_run": task.next_run.isoformat() if task.next_run else None,
            "error_count": task.error_count,
            "is_running": task.is_running,
            "enabled": task.enabled,
            "result": self._task_results.get(task_id),
            "errors": self._task_errors.get(task_id, [])
        }
        
    async def _scheduler_loop(self):
        """Main scheduler loop"""
        while self._running:
            try:
                now = datetime.now()
                
                # Get next task
                try:
                    _, task = await asyncio.wait_for(
                        self._pending_tasks.get(),
                        timeout=1
                    )
                except asyncio.TimeoutError:
                    continue
                    
                # Skip if task is disabled
                if not task.enabled:
                    continue
                    
                # Check if it's time to run
                if task.next_run and task.next_run > now:
                    # Re-queue task
                    self._add_to_queue(task)
                    continue
                    
                # Run task
                await self._run_task(task)
                
                # Schedule next run
                task.next_run = self._calculate_next_run(task)
                if task.next_run:
                    self._add_to_queue(task)
                    
            except asyncio.CancelledError:
                break
            except Exception as e:
                self.logger.error(f"Error in scheduler loop: {str(e)}")
                self.metrics.record_error("scheduler_loop_error")
                self.tracer.record_exception(e)
                await asyncio.sleep(1)
                
    async def _run_task(self, task: ScheduledTask, event_data: Any = None):
        """Run a scheduled task"""
        if task.is_running:
            return
            
        task.is_running = True
        
        try:
            with self.tracer.traced_operation(
                "run_task",
                {"task": task.name}
            ):
                # Run task handler
                start_time = datetime.now()
                result = await task.handler(event_data)
                duration = (datetime.now() - start_time).total_seconds()
                
                # Update task status
                task.last_run = start_time
                task.error_count = 0
                self._task_results[task.id] = result
                
                self.metrics.record_operation(
                    "task_completed",
                    "success",
                    {"task": task.name}
                )
                self.metrics.set_gauge(
                    "task_duration",
                    duration,
                    {"task": task.name}
                )
                
        except Exception as e:
            self.logger.error(f"Error running task {task.name}: {str(e)}")
            self.metrics.record_error("task_error")
            self.tracer.record_exception(e)
            
            # Handle error
            task.error_count += 1
            if task.id not in self._task_errors:
                self._task_errors[task.id] = []
            self._task_errors[task.id].append(str(e))
            
            # Schedule retry if needed
            if task.error_count <= task.max_retries:
                await self._schedule_retry(task)
                
        finally:
            task.is_running = False
            
    def _calculate_next_run(
        self,
        task: ScheduledTask
    ) -> Optional[datetime]:
        """Calculate next run time for task"""
        if not task.enabled:
            return None
            
        now = datetime.now()
        
        if task.schedule_type == ScheduleType.INTERVAL:
            return now + timedelta(seconds=task.schedule)
            
        elif task.schedule_type == ScheduleType.CRON:
            cron = croniter(task.schedule, now)
            return cron.get_next(datetime)
            
        elif task.schedule_type == ScheduleType.ONCE:
            return task.schedule if task.schedule > now else None
            
        return None
        
    def _add_to_queue(self, task: ScheduledTask):
        """Add task to priority queue"""
        if task.next_run:
            self._pending_tasks.put_nowait(
                (task.next_run.timestamp(), task)
            )
            
    async def _schedule_retry(self, task: ScheduledTask):
        """Schedule task retry"""
        retry_time = datetime.now() + timedelta(
            seconds=task.retry_delay
        )
        task.next_run = retry_time
        self._add_to_queue(task)
        
        self.logger.info(
            f"Scheduled retry for task {task.name} at {retry_time}"
        )

# Global scheduler instance
_scheduler: Optional[Scheduler] = None

def get_scheduler() -> Scheduler:
    """Get global scheduler instance"""
    global _scheduler
    if _scheduler is None:
        _scheduler = Scheduler()
    return _scheduler

# Example usage
if __name__ == "__main__":
    async def example():
        scheduler = get_scheduler()
        await scheduler.start()
        
        # Schedule interval task
        async def interval_task():
            print("Running interval task")
            
        scheduler.schedule_task(
            "test_interval",
            interval_task,
            ScheduleType.INTERVAL,
            60  # Run every 60 seconds
        )
        
        # Schedule cron task
        async def cron_task():
            print("Running cron task")
            
        scheduler.schedule_task(
            "test_cron",
            cron_task,
            ScheduleType.CRON,
            "*/5 * * * *"  # Run every 5 minutes
        )
        
        # Wait a bit
        await asyncio.sleep(120)
        
        # Stop scheduler
        await scheduler.stop()
        
    asyncio.run(example())