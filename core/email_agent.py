from typing import Dict, Any
from datetime import datetime
from core.enhanced_base_agent import EnhancedBaseAgent

class EmailAgent(EnhancedBaseAgent):
    def __init__(self, agent_name: str = "email_agent"):
        super().__init__(agent_name, enable_ui=True)
        self.email_client_template_cache = {}

    async def handle_email(self, email_data: Dict[str, Any]) -> bool:
        """Process and respond to an email using UI automation"""
        # Define UI interaction sequence
        compose_actions = [
            {"action": "find_element", "params": {"description": "Compose button"}},
            {"action": "click", "params": {"x": "$COMPOSE_X", "y": "$COMPOSE_Y"}},
            {"action": "type", "params": {"text": email_data.get("recipient", "")}},
            {"action": "find_element", "params": {"description": "Subject field"}},
            {"action": "click", "params": {"x": "$SUBJECT_X", "y": "$SUBJECT_Y"}},
            {"action": "type", "params": {"text": email_data.get("subject", "")}},
            {"action": "find_element", "params": {"description": "Email body"}},
            {"action": "click", "params": {"x": "$BODY_X", "y": "$BODY_Y"}},
            {"action": "type", "params": {"text": email_data.get("body", "")}},
            {"action": "find_element", "params": {"description": "Send button"}},
            {"action": "click", "params": {"x": "$SEND_X", "y": "$SEND_Y"}},
        ]

        try:
            # Execute email composition sequence
            results = await self.batch_ui_actions(compose_actions)
            success = all(success for success, _ in results)

            # Cache successful template if using one
            if success and email_data.get("template_id"):
                self.email_client_template_cache[email_data["template_id"]] = {
                    "last_used": datetime.now(),
                    "success_count": self.email_client_template_cache.get(
                        email_data["template_id"], {}).get("success_count", 0) + 1
                }

            # Log performance metrics
            metrics = self.get_ui_performance_metrics()
            self.logger.info(
                f"Email UI Performance: {metrics}"
                f"Template cache size: {len(self.email_client_template_cache)}"
            )

            return success
        except Exception as e:
            self.logger.error(f"Error in email UI automation: {str(e)}")
            return False

    async def find_email(self, search_criteria: Dict[str, Any]) -> Dict[str, Any]:
        """Search for an email using UI automation"""
        search_actions = [
            {"action": "find_element", "params": {"description": "Search field"}},
            {"action": "click", "params": {"x": "$SEARCH_X", "y": "$SEARCH_Y"}},
            {"action": "type", "params": {"text": search_criteria.get("query", "")}},
        ]

        results = await self.batch_ui_actions(search_actions)
        return {
            "success": all(success for success, _ in results),
            "found": any(result.get("found") for _, result in results if result)
        }