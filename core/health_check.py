"""
Health Check system for monitoring system health
"""

import asyncio
from typing import Dict, Any, Optional, List, Callable
from datetime import datetime
import json
from enum import Enum
from dataclasses import dataclass
from utils.logging_setup import get_logger
from utils.metrics import get_metrics_collector
from utils.tracing import get_tracer
from core.event_system import get_event_system

class HealthStatus(Enum):
    """Health check status levels"""
    HEALTHY = "healthy"
    DEGRADED = "degraded"
    UNHEALTHY = "unhealthy"
    UNKNOWN = "unknown"

@dataclass
class HealthCheck:
    """Individual health check"""
    name: str
    check: Callable
    interval: int  # seconds
    timeout: int = 30
    dependencies: List[str] = None
    critical: bool = False
    metadata: Dict[str, Any] = None

@dataclass
class HealthResult:
    """Health check result"""
    status: HealthStatus
    message: str
    details: Dict[str, Any]
    timestamp: datetime
    duration: float
    dependencies: Dict[str, HealthStatus] = None

class HealthChecker:
    """Manages system health checks"""
    
    def __init__(self):
        self.logger = get_logger("health_checker")
        self.metrics = get_metrics_collector("health_checker")
        self.tracer = get_tracer("health_checker")
        self.event_system = get_event_system()
        
        # Health checks registry
        self._checks: Dict[str, HealthCheck] = {}
        
        # Check results
        self._results: Dict[str, HealthResult] = {}
        
        # Check tasks
        self._tasks: Dict[str, asyncio.Task] = {}
        self._running = False
        
        # Status change callbacks
        self._status_callbacks: List[Callable] = []
        
    def register_check(
        self,
        name: str,
        check: Callable,
        interval: int,
        **kwargs
    ):
        """Register health check"""
        health_check = HealthCheck(
            name=name,
            check=check,
            interval=interval,
            **kwargs
        )
        self._checks[name] = health_check
        
    def register_status_callback(self, callback: Callable):
        """Register status change callback"""
        self._status_callbacks.append(callback)
        
    async def start(self):
        """Start health checking"""
        if self._running:
            return
            
        self._running = True
        
        # Start check tasks
        for name, check in self._checks.items():
            self._tasks[name] = asyncio.create_task(
                self._run_check_loop(name, check)
            )
            
        self.logger.info("Health checker started")
        
    async def stop(self):
        """Stop health checking"""
        if not self._running:
            return
            
        self._running = False
        
        # Cancel check tasks
        for task in self._tasks.values():
            task.cancel()
            try:
                await task
            except asyncio.CancelledError:
                pass
        self._tasks.clear()
        
        self.logger.info("Health checker stopped")
        
    def get_status(
        self,
        check_name: Optional[str] = None
    ) -> Union[HealthResult, Dict[str, HealthResult]]:
        """Get health check status"""
        if check_name:
            return self._results.get(check_name)
        return self._results.copy()
        
    def get_system_status(self) -> HealthStatus:
        """Get overall system status"""
        if not self._results:
            return HealthStatus.UNKNOWN
            
        # Check critical services
        for name, check in self._checks.items():
            if check.critical:
                result = self._results.get(name)
                if not result or result.status == HealthStatus.UNHEALTHY:
                    return HealthStatus.UNHEALTHY
                    
        # Count status types
        status_counts = {
            status: 0 for status in HealthStatus
        }
        for result in self._results.values():
            status_counts[result.status] += 1
            
        # Determine overall status
        if status_counts[HealthStatus.UNHEALTHY] > 0:
            return HealthStatus.DEGRADED
        if status_counts[HealthStatus.DEGRADED] > 0:
            return HealthStatus.DEGRADED
        if status_counts[HealthStatus.UNKNOWN] == len(self._results):
            return HealthStatus.UNKNOWN
        return HealthStatus.HEALTHY
        
    async def _run_check_loop(
        self,
        name: str,
        check: HealthCheck
    ):
        """Run periodic health check"""
        while self._running:
            try:
                # Check dependencies
                if check.dependencies:
                    dep_status = self._check_dependencies(check)
                    if dep_status != HealthStatus.HEALTHY:
                        result = HealthResult(
                            status=dep_status,
                            message="Dependencies not healthy",
                            details={},
                            timestamp=datetime.utcnow(),
                            duration=0,
                            dependencies=self._get_dependency_status(check)
                        )
                        await self._update_status(name, result)
                        await asyncio.sleep(check.interval)
                        continue
                        
                # Run check
                start_time = datetime.utcnow()
                try:
                    with self.tracer.traced_operation(
                        "health_check",
                        {"check": name}
                    ):
                        check_result = check.check()
                        if asyncio.iscoroutine(check_result):
                            check_result = await asyncio.wait_for(
                                check_result,
                                timeout=check.timeout
                            )
                            
                        status = HealthStatus.HEALTHY
                        message = "Check passed"
                        
                except asyncio.TimeoutError:
                    status = HealthStatus.UNHEALTHY
                    message = f"Check timed out after {check.timeout}s"
                    check_result = {}
                    
                except Exception as e:
                    status = HealthStatus.UNHEALTHY
                    message = str(e)
                    check_result = {
                        "error": str(e),
                        "traceback": self.tracer.get_traceback()
                    }
                    
                # Create result
                duration = (
                    datetime.utcnow() - start_time
                ).total_seconds()
                
                result = HealthResult(
                    status=status,
                    message=message,
                    details=check_result,
                    timestamp=start_time,
                    duration=duration,
                    dependencies=self._get_dependency_status(check)
                )
                
                # Update status
                await self._update_status(name, result)
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                self.logger.error(
                    f"Error running health check {name}: {str(e)}"
                )
                
            await asyncio.sleep(check.interval)
            
    def _check_dependencies(
        self,
        check: HealthCheck
    ) -> HealthStatus:
        """Check health check dependencies"""
        if not check.dependencies:
            return HealthStatus.HEALTHY
            
        for dep in check.dependencies:
            if dep not in self._results:
                return HealthStatus.UNKNOWN
                
            if self._results[dep].status == HealthStatus.UNHEALTHY:
                return HealthStatus.UNHEALTHY
                
        return HealthStatus.HEALTHY
        
    def _get_dependency_status(
        self,
        check: HealthCheck
    ) -> Optional[Dict[str, HealthStatus]]:
        """Get dependency status"""
        if not check.dependencies:
            return None
            
        return {
            dep: (
                self._results[dep].status
                if dep in self._results
                else HealthStatus.UNKNOWN
            )
            for dep in check.dependencies
        }
        
    async def _update_status(
        self,
        name: str,
        result: HealthResult
    ):
        """Update health check status"""
        old_result = self._results.get(name)
        self._results[name] = result
        
        # Record metrics
        self.metrics.record_operation(
            "health_check",
            result.status.value,
            {"check": name}
        )
        self.metrics.set_gauge(
            "health_check_duration",
            result.duration,
            {"check": name}
        )
        
        # Notify status change
        if (
            not old_result
            or old_result.status != result.status
        ):
            await self._notify_status_change(name, result)
            
    async def _notify_status_change(
        self,
        name: str,
        result: HealthResult
    ):
        """Notify status change"""
        # Publish event
        await self.event_system.publish_event(
            "health.status_change",
            "health_checker",
            {
                "check": name,
                "status": result.status.value,
                "message": result.message
            }
        )
        
        # Call callbacks
        for callback in self._status_callbacks:
            try:
                cb_result = callback(name, result)
                if asyncio.iscoroutine(cb_result):
                    await cb_result
            except Exception as e:
                self.logger.error(
                    f"Error in status callback: {str(e)}"
                )

# Global health checker instance
_health_checker: Optional[HealthChecker] = None

def get_health_checker() -> HealthChecker:
    """Get global health checker instance"""
    global _health_checker
    if _health_checker is None:
        _health_checker = HealthChecker()
    return _health_checker

# Example usage
if __name__ == "__main__":
    async def example():
        checker = get_health_checker()
        
        # Define check
        async def check_example():
            # Simulate work
            await asyncio.sleep(0.1)
            return {"value": 42}
            
        # Register check
        checker.register_check(
            "example",
            check_example,
            interval=60,
            critical=True
        )
        
        # Register callback
        async def on_status_change(name, result):
            print(
                f"Status changed: {name} -> {result.status.value}"
            )
            
        checker.register_status_callback(on_status_change)
        
        # Start checking
        await checker.start()
        
        # Wait a bit
        await asyncio.sleep(2)
        
        # Check status
        status = checker.get_system_status()
        print(f"System status: {status.value}")
        
        # Stop checking
        await checker.stop()
        
    asyncio.run(example())