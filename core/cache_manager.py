"""
Cache Manager for handling caching with Redis
"""

import asyncio
from typing import Dict, Any, Optional, Union, List
from datetime import datetime, timedelta
import json
import pickle
import aioredis
from aioredis import Redis
from enum import Enum
from utils.logging_setup import get_logger
from utils.metrics import get_metrics_collector
from utils.tracing import get_tracer
from core.config_manager import get_config_manager, RedisConfig

class CacheStrategy(Enum):
    """Cache storage strategies"""
    JSON = "json"
    PICKLE = "pickle"
    RAW = "raw"

class CacheManager:
    """Manages caching with Redis"""
    
    def __init__(self):
        self.logger = get_logger("cache_manager")
        self.metrics = get_metrics_collector("cache_manager")
        self.tracer = get_tracer("cache_manager")
        
        # Load config
        config = get_config_manager().get_config()
        self.config: RedisConfig = config.redis
        
        # Redis client
        self._redis: Optional[Redis] = None
        self._initialized = False
        
        # Default settings
        self._default_ttl = 3600  # 1 hour
        self._default_strategy = CacheStrategy.JSON
        
        # Local cache
        self._local_cache: Dict[str, Any] = {}
        self._local_cache_ttl: Dict[str, datetime] = {}
        self._local_cache_size = 1000
        
        # Cache patterns for invalidation
        self._invalidation_patterns: Dict[str, List[str]] = {}
        
    async def initialize(self):
        """Initialize Redis connection"""
        if self._initialized:
            return
            
        try:
            with self.tracer.traced_operation("init_cache"):
                # Create Redis connection
                self._redis = await aioredis.create_redis_pool(
                    f"redis://{self.config.host}:{self.config.port}",
                    db=self.config.db,
                    password=self.config.password,
                    ssl=self.config.ssl,
                    maxsize=self.config.pool_size,
                    timeout=self.config.timeout
                )
                
                # Test connection
                await self._redis.ping()
                
                self._initialized = True
                self.logger.info("Cache initialized successfully")
                
        except Exception as e:
            self.logger.error(f"Cache initialization error: {str(e)}")
            self.metrics.record_error("cache_init_error")
            self.tracer.record_exception(e)
            raise
            
    async def cleanup(self):
        """Clean up cache resources"""
        if self._redis:
            self._redis.close()
            await self._redis.wait_closed()
            self._initialized = False
            
    async def get(
        self,
        key: str,
        default: Any = None,
        strategy: Optional[CacheStrategy] = None
    ) -> Any:
        """Get value from cache"""
        try:
            # Check local cache first
            local_value = self._get_local(key)
            if local_value is not None:
                self.metrics.record_operation(
                    "cache_local_hit",
                    "success"
                )
                return local_value
                
            if not self._initialized:
                await self.initialize()
                
            with self.tracer.traced_operation(
                "cache_get",
                {"key": key}
            ):
                # Get from Redis
                value = await self._redis.get(key)
                
                if value is None:
                    self.metrics.record_operation(
                        "cache_miss",
                        "success"
                    )
                    return default
                    
                # Deserialize value
                strategy = strategy or self._default_strategy
                result = self._deserialize(value, strategy)
                
                # Update local cache
                self._set_local(key, result)
                
                self.metrics.record_operation(
                    "cache_hit",
                    "success"
                )
                return result
                
        except Exception as e:
            self.logger.error(f"Cache get error: {str(e)}")
            self.metrics.record_error("cache_get_error")
            self.tracer.record_exception(e)
            raise
            
    async def set(
        self,
        key: str,
        value: Any,
        ttl: Optional[int] = None,
        strategy: Optional[CacheStrategy] = None
    ):
        """Set value in cache"""
        try:
            if not self._initialized:
                await self.initialize()
                
            with self.tracer.traced_operation(
                "cache_set",
                {"key": key}
            ):
                # Serialize value
                strategy = strategy or self._default_strategy
                serialized = self._serialize(value, strategy)
                
                # Set in Redis
                ttl = ttl or self._default_ttl
                await self._redis.set(key, serialized, expire=ttl)
                
                # Update local cache
                self._set_local(key, value, ttl)
                
                # Update invalidation patterns
                self._update_patterns(key)
                
                self.metrics.record_operation(
                    "cache_set",
                    "success"
                )
                
        except Exception as e:
            self.logger.error(f"Cache set error: {str(e)}")
            self.metrics.record_error("cache_set_error")
            self.tracer.record_exception(e)
            raise
            
    async def delete(self, key: str):
        """Delete value from cache"""
        try:
            if not self._initialized:
                await self.initialize()
                
            # Remove from Redis
            await self._redis.delete(key)
            
            # Remove from local cache
            self._local_cache.pop(key, None)
            self._local_cache_ttl.pop(key, None)
            
            # Invalidate patterns
            await self._invalidate_patterns(key)
            
            self.metrics.record_operation(
                "cache_delete",
                "success"
            )
            
        except Exception as e:
            self.logger.error(f"Cache delete error: {str(e)}")
            self.metrics.record_error("cache_delete_error")
            self.tracer.record_exception(e)
            raise
            
    async def exists(self, key: str) -> bool:
        """Check if key exists in cache"""
        if not self._initialized:
            await self.initialize()
            
        return await self._redis.exists(key)
        
    async def clear(self):
        """Clear all cache entries"""
        if not self._initialized:
            await self.initialize()
            
        await self._redis.flushdb()
        self._local_cache.clear()
        self._local_cache_ttl.clear()
        
    def add_invalidation_pattern(self, pattern: str, keys: List[str]):
        """Add pattern for cache invalidation"""
        self._invalidation_patterns[pattern] = keys
        
    def remove_invalidation_pattern(self, pattern: str):
        """Remove invalidation pattern"""
        self._invalidation_patterns.pop(pattern, None)
        
    async def _invalidate_patterns(self, changed_key: str):
        """Invalidate cache based on patterns"""
        for pattern, keys in self._invalidation_patterns.items():
            if changed_key in keys:
                for key in keys:
                    await self.delete(key)
                    
    def _serialize(self, value: Any, strategy: CacheStrategy) -> bytes:
        """Serialize value for storage"""
        if strategy == CacheStrategy.JSON:
            return json.dumps(value).encode()
        elif strategy == CacheStrategy.PICKLE:
            return pickle.dumps(value)
        else:
            return str(value).encode()
            
    def _deserialize(self, value: bytes, strategy: CacheStrategy) -> Any:
        """Deserialize stored value"""
        if strategy == CacheStrategy.JSON:
            return json.loads(value.decode())
        elif strategy == CacheStrategy.PICKLE:
            return pickle.loads(value)
        else:
            return value.decode()
            
    def _get_local(self, key: str) -> Optional[Any]:
        """Get value from local cache"""
        if key not in self._local_cache:
            return None
            
        # Check TTL
        if key in self._local_cache_ttl:
            if datetime.now() > self._local_cache_ttl[key]:
                self._local_cache.pop(key)
                self._local_cache_ttl.pop(key)
                return None
                
        return self._local_cache[key]
        
    def _set_local(
        self,
        key: str,
        value: Any,
        ttl: Optional[int] = None
    ):
        """Set value in local cache"""
        # Enforce cache size limit
        if len(self._local_cache) >= self._local_cache_size:
            oldest_key = min(
                self._local_cache_ttl.items(),
                key=lambda x: x[1]
            )[0]
            self._local_cache.pop(oldest_key)
            self._local_cache_ttl.pop(oldest_key)
            
        self._local_cache[key] = value
        
        if ttl:
            self._local_cache_ttl[key] = datetime.now() + timedelta(
                seconds=ttl
            )
            
    def _update_patterns(self, key: str):
        """Update invalidation patterns"""
        for pattern, keys in self._invalidation_patterns.items():
            if key not in keys and any(k in key for k in keys):
                keys.append(key)
                
    async def health_check(self) -> Dict[str, Any]:
        """Check cache health"""
        try:
            start_time = datetime.now()
            await self._redis.ping()
            duration = (datetime.now() - start_time).total_seconds()
            
            info = await self._redis.info()
            
            return {
                "status": "healthy",
                "response_time": duration,
                "used_memory": info["used_memory"],
                "total_connections": info["total_connections_received"],
                "local_cache_size": len(self._local_cache)
            }
            
        except Exception as e:
            return {
                "status": "unhealthy",
                "error": str(e)
            }

# Global cache manager instance
_cache_manager: Optional[CacheManager] = None

def get_cache_manager() -> CacheManager:
    """Get global cache manager instance"""
    global _cache_manager
    if _cache_manager is None:
        _cache_manager = CacheManager()
    return _cache_manager

# Example usage
if __name__ == "__main__":
    async def example():
        cache = get_cache_manager()
        await cache.initialize()
        
        # Set value
        await cache.set("test_key", {"value": 42})
        
        # Get value
        value = await cache.get("test_key")
        print(f"Cached value: {value}")
        
        # Delete value
        await cache.delete("test_key")
        
        # Clean up
        await cache.cleanup()
        
    asyncio.run(example())