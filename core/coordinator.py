"""
System Coordinator for managing core component lifecycle
"""

import asyncio
from typing import Dict, List, Any, Optional, Set
from dataclasses import dataclass
from enum import Enum
import signal
import sys
from datetime import datetime
from core.config_manager import get_config_manager
from core.database_manager import get_database_manager
from core.cache_manager import get_cache_manager
from core.message_bus import get_message_bus
from core.event_system import get_event_system
from core.state_manager import get_state_manager
from core.security_manager import get_security_manager
from core.plugin_manager import get_plugin_manager
from core.scheduler import get_scheduler
from core.workflow_engine import get_workflow_engine
from core.health_check import get_health_check
from utils.logging_setup import get_logger
from utils.metrics import get_metrics_collector
from utils.tracing import AgentTracer

class ComponentState(Enum):
    STOPPED = "stopped"
    STARTING = "starting"
    RUNNING = "running"
    STOPPING = "stopping"
    ERROR = "error"

@dataclass
class Component:
    name: str
    instance: Any
    state: ComponentState = ComponentState.STOPPED
    dependencies: Set[str] = None
    startup_order: int = 0
    error: Optional[str] = None

class SystemCoordinator:
    """Coordinates system components and lifecycle"""
    
    def __init__(self):
        self.logger = get_logger("coordinator")
        self.metrics = get_metrics_collector("coordinator")
        self.tracer = AgentTracer("coordinator")
        
        # Component registry
        self._components: Dict[str, Component] = {}
        
        # Shutdown flag
        self._shutdown = False
        self._shutdown_event = asyncio.Event()
        
        # Register core components
        self._register_core_components()
        
    def _register_core_components(self):
        """Register all core system components"""
        # Config manager (no dependencies)
        self._register_component(
            "config",
            get_config_manager(),
            startup_order=0
        )
        
        # Database manager depends on config
        self._register_component(
            "database",
            get_database_manager(),
            dependencies={"config"},
            startup_order=1
        )
        
        # Cache manager depends on config
        self._register_component(
            "cache",
            get_cache_manager(),
            dependencies={"config"},
            startup_order=1
        )
        
        # Event system depends on config
        self._register_component(
            "events",
            get_event_system(),
            dependencies={"config"},
            startup_order=1
        )
        
        # Message bus depends on config
        self._register_component(
            "message_bus",
            get_message_bus(),
            dependencies={"config"},
            startup_order=1
        )
        
        # State manager depends on database and cache
        self._register_component(
            "state",
            get_state_manager(),
            dependencies={"database", "cache"},
            startup_order=2
        )
        
        # Security manager depends on config and database
        self._register_component(
            "security",
            get_security_manager(),
            dependencies={"config", "database"},
            startup_order=2
        )
        
        # Plugin manager depends on multiple components
        self._register_component(
            "plugins",
            get_plugin_manager(),
            dependencies={
                "config", "database", "events",
                "message_bus", "state"
            },
            startup_order=3
        )
        
        # Scheduler depends on multiple components
        self._register_component(
            "scheduler",
            get_scheduler(),
            dependencies={
                "config", "database", "state",
                "events"
            },
            startup_order=3
        )
        
        # Workflow engine depends on multiple components
        self._register_component(
            "workflows",
            get_workflow_engine(),
            dependencies={
                "config", "database", "state",
                "events", "message_bus"
            },
            startup_order=3
        )
        
        # Health check system depends on config
        self._register_component(
            "health_check",
            get_health_check(),
            dependencies={"config"},
            startup_order=4
        )
        
    def _register_component(
        self,
        name: str,
        instance: Any,
        dependencies: Set[str] = None,
        startup_order: int = 0
    ):
        """Register a system component"""
        self._components[name] = Component(
            name=name,
            instance=instance,
            dependencies=dependencies or set(),
            startup_order=startup_order
        )
        
    async def start(self):
        """Start all system components"""
        try:
            self.logger.info("Starting system components...")
            
            # Set up signal handlers
            self._setup_signal_handlers()
            
            # Start components in order
            components_by_order = sorted(
                self._components.values(),
                key=lambda c: c.startup_order
            )
            
            for component in components_by_order:
                await self._start_component(component)
                
            self.logger.info("All components started successfully")
            
            # Wait for shutdown signal
            await self._shutdown_event.wait()
            
            # Shutdown components
            await self.shutdown()
            
        except Exception as e:
            self.logger.error(f"Error starting system: {str(e)}")
            self.metrics.record_error("startup_error")
            self.tracer.record_exception(e)
            raise
            
    async def shutdown(self):
        """Shutdown all system components"""
        if self._shutdown:
            return
            
        self._shutdown = True
        self.logger.info("Shutting down system components...")
        
        # Stop components in reverse order
        components_by_order = sorted(
            self._components.values(),
            key=lambda c: c.startup_order,
            reverse=True
        )
        
        for component in components_by_order:
            await self._stop_component(component)
            
        self.logger.info("System shutdown complete")
        
    async def _start_component(self, component: Component):
        """Start a single component"""
        try:
            self.logger.info(f"Starting component: {component.name}")
            component.state = ComponentState.STARTING
            
            # Check dependencies
            for dep_name in component.dependencies:
                dep = self._components.get(dep_name)
                if not dep or dep.state != ComponentState.RUNNING:
                    raise RuntimeError(
                        f"Dependency {dep_name} not ready for {component.name}"
                    )
                    
            # Initialize component
            if hasattr(component.instance, 'initialize'):
                await component.instance.initialize()
                
            # Start component
            if hasattr(component.instance, 'start'):
                await component.instance.start()
                
            component.state = ComponentState.RUNNING
            self.metrics.record_operation(
                "component_start",
                "success",
                {"component": component.name}
            )
            
        except Exception as e:
            self.logger.error(
                f"Error starting component {component.name}: {str(e)}"
            )
            component.state = ComponentState.ERROR
            component.error = str(e)
            self.metrics.record_error(
                "component_start_error",
                {"component": component.name}
            )
            raise
            
    async def _stop_component(self, component: Component):
        """Stop a single component"""
        try:
            if component.state not in [
                ComponentState.RUNNING,
                ComponentState.ERROR
            ]:
                return
                
            self.logger.info(f"Stopping component: {component.name}")
            component.state = ComponentState.STOPPING
            
            # Stop component
            if hasattr(component.instance, 'stop'):
                await component.instance.stop()
                
            # Cleanup component
            if hasattr(component.instance, 'cleanup'):
                await component.instance.cleanup()
                
            component.state = ComponentState.STOPPED
            self.metrics.record_operation(
                "component_stop",
                "success",
                {"component": component.name}
            )
            
        except Exception as e:
            self.logger.error(
                f"Error stopping component {component.name}: {str(e)}"
            )
            component.state = ComponentState.ERROR
            component.error = str(e)
            self.metrics.record_error(
                "component_stop_error",
                {"component": component.name}
            )
            
    def _setup_signal_handlers(self):
        """Set up system signal handlers"""
        def signal_handler(signum, frame):
            self.logger.info(f"Received signal {signum}")
            self._shutdown_event.set()
            
        signal.signal(signal.SIGINT, signal_handler)
        signal.signal(signal.SIGTERM, signal_handler)
        
    def get_component_status(self) -> Dict[str, Dict]:
        """Get status of all components"""
        return {
            name: {
                "state": comp.state.value,
                "error": comp.error,
                "dependencies": list(comp.dependencies),
                "startup_order": comp.startup_order
            }
            for name, comp in self._components.items()
        }

# Global coordinator instance
_coordinator: Optional[SystemCoordinator] = None

def get_coordinator() -> SystemCoordinator:
    """Get global system coordinator instance"""
    global _coordinator
    if _coordinator is None:
        _coordinator = SystemCoordinator()
    return _coordinator

# Example usage
if __name__ == "__main__":
    async def main():
        coordinator = get_coordinator()
        
        try:
            # Start all components
            await coordinator.start()
        except KeyboardInterrupt:
            # Handle Ctrl+C gracefully
            await coordinator.shutdown()
            
    asyncio.run(main())