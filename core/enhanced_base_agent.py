"""
Enhanced Base Agent with advanced capabilities
"""

import os
import json
import asyncio
import logging
from typing import Dict, List, Any, Optional, Union, Callable
import uuid

logger = logging.getLogger(__name__)

class EnhancedBaseAgent:
    """Enhanced base agent class with advanced capabilities"""
    
    def __init__(self, agent_id: str, agent_name: str, agent_description: str):
        self.agent_id = agent_id or str(uuid.uuid4())
        self.agent_name = agent_name
        self.agent_description = agent_description
        self.memory = {}
        self.capabilities = []
        self.sub_agents = []
        self.parent_agent = None
        self.is_active = False
        self.logger = logging.getLogger(f"agent.{self.agent_id}")
        
    def activate(self):
        """Activate the agent"""
        self.is_active = True
        self.logger.info(f"Agent {self.agent_name} ({self.agent_id}) activated")
        return True
        
    def deactivate(self):
        """Deactivate the agent"""
        self.is_active = False
        self.logger.info(f"Agent {self.agent_name} ({self.agent_id}) deactivated")
        return True
        
    def add_capability(self, capability_name: str, capability_function: Callable):
        """Add a capability to the agent"""
        self.capabilities.append({
            "name": capability_name,
            "function": capability_function
        })
        self.logger.info(f"Added capability: {capability_name}")
        return True
        
    def has_capability(self, capability_name: str) -> bool:
        """Check if agent has a specific capability"""
        return any(cap["name"] == capability_name for cap in self.capabilities)
        
    def add_sub_agent(self, agent):
        """Add a sub-agent to this agent"""
        agent.parent_agent = self
        self.sub_agents.append(agent)
        self.logger.info(f"Added sub-agent: {agent.agent_name} ({agent.agent_id})")
        return True
        
    def remove_sub_agent(self, agent_id: str) -> bool:
        """Remove a sub-agent by ID"""
        for i, agent in enumerate(self.sub_agents):
            if agent.agent_id == agent_id:
                removed_agent = self.sub_agents.pop(i)
                removed_agent.parent_agent = None
                self.logger.info(f"Removed sub-agent: {removed_agent.agent_name} ({removed_agent.agent_id})")
                return True
        return False
        
    def get_sub_agent(self, agent_id: str):
        """Get a sub-agent by ID"""
        for agent in self.sub_agents:
            if agent.agent_id == agent_id:
                return agent
        return None
        
    def get_all_sub_agents(self):
        """Get all sub-agents"""
        return self.sub_agents
        
    def store_memory(self, key: str, value: Any):
        """Store a value in agent memory"""
        self.memory[key] = value
        return True
        
    def retrieve_memory(self, key: str, default=None) -> Any:
        """Retrieve a value from agent memory"""
        return self.memory.get(key, default)
        
    def clear_memory(self, key: str = None):
        """Clear agent memory, optionally for a specific key"""
        if key:
            if key in self.memory:
                del self.memory[key]
                return True
            return False
        else:
            self.memory = {}
            return True
            
    def to_dict(self) -> Dict[str, Any]:
        """Convert agent to dictionary representation"""
        return {
            "agent_id": self.agent_id,
            "agent_name": self.agent_name,
            "agent_description": self.agent_description,
            "is_active": self.is_active,
            "capabilities": [cap["name"] for cap in self.capabilities],
            "sub_agents": [agent.to_dict() for agent in self.sub_agents],
            "parent_agent_id": self.parent_agent.agent_id if self.parent_agent else None
        }
        
    def from_dict(self, data: Dict[str, Any]):
        """Load agent from dictionary representation"""
        self.agent_id = data.get("agent_id", self.agent_id)
        self.agent_name = data.get("agent_name", self.agent_name)
        self.agent_description = data.get("agent_description", self.agent_description)
        self.is_active = data.get("is_active", self.is_active)
        return self
        
    def save_to_file(self, filepath: str) -> bool:
        """Save agent to file"""
        try:
            with open(filepath, "w") as f:
                json.dump(self.to_dict(), f, indent=2)
            return True
        except Exception as e:
            self.logger.error(f"Error saving agent to file: {e}")
            return False
            
    @classmethod
    def load_from_file(cls, filepath: str):
        """Load agent from file"""
        try:
            with open(filepath, "r") as f:
                data = json.load(f)
            agent = cls(
                agent_id=data.get("agent_id"),
                agent_name=data.get("agent_name"),
                agent_description=data.get("agent_description")
            )
            agent.from_dict(data)
            return agent
        except Exception as e:
            logger.error(f"Error loading agent from file: {e}")
            return None
