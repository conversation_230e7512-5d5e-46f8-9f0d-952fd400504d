"""
Message Bus for agent communication
"""

import asyncio
from typing import Dict, Any, Callable, List, Optional
from datetime import datetime
import json
import uuid
from utils.logging_setup import get_logger
from utils.metrics import get_metrics_collector
from utils.tracing import get_tracer

class MessageBus:
    """Handles message routing between agents"""
    
    def __init__(self):
        self.logger = get_logger("message_bus")
        self.metrics = get_metrics_collector("message_bus")
        self.tracer = get_tracer("message_bus")
        
        # Subscribers by topic
        self._subscribers: Dict[str, List[Callable]] = {}
        
        # Message history for debugging
        self._message_history: List[Dict] = []
        self._max_history = 1000
        
        # Rate limiting
        self._rate_limits: Dict[str, List[datetime]] = {}
        self._max_messages_per_second = 100
        
        # Active subscriptions by subscriber
        self._active_subscriptions: Dict[str, List[str]] = {}
        
    async def publish(
        self,
        topic: str,
        message: Dict,
        publisher: str,
        retain: bool = False
    ):
        """Publish message to topic"""
        try:
            with self.tracer.traced_operation(
                "publish_message",
                {
                    "topic": topic,
                    "publisher": publisher
                }
            ):
                # Check rate limit
                if not self._check_rate_limit(publisher):
                    raise ValueError("Rate limit exceeded")
                    
                # Add metadata
                message_id = str(uuid.uuid4())
                timestamp = datetime.utcnow().isoformat()
                
                full_message = {
                    "id": message_id,
                    "topic": topic,
                    "publisher": publisher,
                    "timestamp": timestamp,
                    "payload": message
                }
                
                # Store in history if needed
                if retain:
                    self._add_to_history(full_message)
                    
                # Get subscribers
                subscribers = self._subscribers.get(topic, [])
                
                # Deliver to subscribers
                delivery_tasks = []
                for subscriber in subscribers:
                    task = asyncio.create_task(
                        self._deliver_message(subscriber, full_message)
                    )
                    delivery_tasks.append(task)
                    
                # Wait for deliveries
                if delivery_tasks:
                    await asyncio.gather(*delivery_tasks)
                    
                # Record metrics
                self.metrics.record_operation(
                    "message_published",
                    "success",
                    {"topic": topic}
                )
                
        except Exception as e:
            self.logger.error(f"Error publishing message: {str(e)}")
            self.metrics.record_error("publish_error")
            self.tracer.record_exception(e)
            raise
            
    async def subscribe(
        self,
        topic: str,
        callback: Callable
    ):
        """Subscribe to topic"""
        try:
            # Add subscriber
            if topic not in self._subscribers:
                self._subscribers[topic] = []
            self._subscribers[topic].append(callback)
            
            # Track subscription
            subscriber_id = str(id(callback))
            if subscriber_id not in self._active_subscriptions:
                self._active_subscriptions[subscriber_id] = []
            self._active_subscriptions[subscriber_id].append(topic)
            
            self.logger.info(f"New subscriber for topic: {topic}")
            self.metrics.record_operation(
                "subscription_added",
                "success",
                {"topic": topic}
            )
            
        except Exception as e:
            self.logger.error(f"Error subscribing to topic: {str(e)}")
            self.metrics.record_error("subscribe_error")
            self.tracer.record_exception(e)
            raise
            
    async def unsubscribe(
        self,
        topic: str,
        callback: Callable
    ):
        """Unsubscribe from topic"""
        try:
            # Remove subscriber
            if topic in self._subscribers:
                self._subscribers[topic].remove(callback)
                if not self._subscribers[topic]:
                    del self._subscribers[topic]
                    
            # Remove from active subscriptions
            subscriber_id = str(id(callback))
            if subscriber_id in self._active_subscriptions:
                self._active_subscriptions[subscriber_id].remove(topic)
                if not self._active_subscriptions[subscriber_id]:
                    del self._active_subscriptions[subscriber_id]
                    
            self.logger.info(f"Removed subscriber from topic: {topic}")
            self.metrics.record_operation(
                "subscription_removed",
                "success",
                {"topic": topic}
            )
            
        except Exception as e:
            self.logger.error(f"Error unsubscribing from topic: {str(e)}")
            self.metrics.record_error("unsubscribe_error")
            self.tracer.record_exception(e)
            raise
            
    async def _deliver_message(
        self,
        subscriber: Callable,
        message: Dict
    ):
        """Deliver message to subscriber"""
        try:
            with self.tracer.traced_operation(
                "deliver_message",
                {"subscriber": str(id(subscriber))}
            ):
                await subscriber(message)
                self.metrics.record_operation(
                    "message_delivered",
                    "success"
                )
                
        except Exception as e:
            self.logger.error(
                f"Error delivering message to subscriber: {str(e)}"
            )
            self.metrics.record_error("delivery_error")
            self.tracer.record_exception(e)
            
    def _check_rate_limit(self, publisher: str) -> bool:
        """Check if publisher has exceeded rate limit"""
        now = datetime.utcnow()
        
        # Initialize rate limit tracking
        if publisher not in self._rate_limits:
            self._rate_limits[publisher] = []
            
        # Remove old timestamps
        self._rate_limits[publisher] = [
            ts for ts in self._rate_limits[publisher]
            if (now - ts).total_seconds() <= 1
        ]
        
        # Check limit
        if len(self._rate_limits[publisher]) >= self._max_messages_per_second:
            return False
            
        # Add new timestamp
        self._rate_limits[publisher].append(now)
        return True
        
    def _add_to_history(self, message: Dict):
        """Add message to history"""
        self._message_history.append(message)
        
        # Trim history if needed
        while len(self._message_history) > self._max_history:
            self._message_history.pop(0)
            
    def get_message_history(
        self,
        topic: Optional[str] = None,
        limit: int = 100
    ) -> List[Dict]:
        """Get message history"""
        messages = self._message_history
        
        if topic:
            messages = [m for m in messages if m["topic"] == topic]
            
        return messages[-limit:]
        
    def get_active_topics(self) -> List[str]:
        """Get list of topics with active subscribers"""
        return list(self._subscribers.keys())
        
    def get_subscriber_count(self, topic: str) -> int:
        """Get number of subscribers for topic"""
        return len(self._subscribers.get(topic, []))
        
    async def health_check(self) -> Dict[str, Any]:
        """Check message bus health"""
        return {
            "active_topics": len(self._subscribers),
            "total_subscribers": sum(
                len(subs) for subs in self._subscribers.values()
            ),
            "message_history_size": len(self._message_history),
            "rate_limited_publishers": len(self._rate_limits)
        }

# Global message bus instance
_message_bus: Optional[MessageBus] = None

def get_message_bus() -> MessageBus:
    """Get global message bus instance"""
    global _message_bus
    if _message_bus is None:
        _message_bus = MessageBus()
    return _message_bus

# Example usage
if __name__ == "__main__":
    async def example():
        bus = get_message_bus()
        
        # Subscribe to topic
        async def handle_message(message):
            print(f"Received: {message}")
            
        await bus.subscribe("test_topic", handle_message)
        
        # Publish message
        await bus.publish(
            "test_topic",
            {"data": "test"},
            "example"
        )
        
        # Wait for delivery
        await asyncio.sleep(1)
        
    asyncio.run(example())