from typing import Dict, Any, Optional
from .base_agent import BaseAgent

class UIAgent(BaseAgent):
    """Base class for agents that need UI interaction capabilities"""
    
    def __init__(self, agent_name: str, enable_ui: bool = True, prefer_desktop: bool = True):
        super().__init__(agent_name)
        if enable_ui:
            self.enable_ui(prefer_desktop)

    async def initialize(self):
        await super().initialize()
        
        # Register UI-specific metrics
        self.metrics.register_counter("ui_actions_total")
        self.metrics.register_counter("ui_actions_failed")
        
    async def perform_ui_task(self, task_type: str, params: Dict[str, Any]) -> bool:
        """Perform a UI task with metrics and logging"""
        try:
            with self.tracer.traced_operation("ui_task", {"type": task_type}):
                success = self.perform_ui_action(task_type, params)
                if success:
                    self.metrics.increment_counter("ui_actions_total")
                else:
                    self.metrics.increment_counter("ui_actions_failed")
                return success
        except Exception as e:
            self.logger.error(f"Error performing UI task {task_type}: {str(e)}")
            self.metrics.increment_counter("ui_actions_failed")
            self.tracer.record_exception(e)
            return False

    async def click(self, x: int, y: int, button: str = "left") -> bool:
        """Perform a mouse click action"""
        return await self.perform_ui_task("click", {
            "x": x,
            "y": y,
            "button": button
        })

    async def type_text(self, text: str) -> bool:
        """Type text at the current cursor position"""
        return await self.perform_ui_task("type", {
            "text": text
        })

    async def scroll(self, amount: int) -> bool:
        """Scroll the current window"""
        return await self.perform_ui_task("scroll", {
            "amount": amount
        })

    async def find_element(self, description: str) -> Optional[Dict[str, Any]]:
        """Find a UI element by description"""
        try:
            with self.tracer.traced_operation("find_element"):
                result = self.perform_ui_action("find_element", {
                    "description": description
                })
                return result if result else None
        except Exception as e:
            self.logger.error(f"Error finding element '{description}': {str(e)}")
            self.tracer.record_exception(e)
            return None