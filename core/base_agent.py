"""
Base Agent class providing common functionality for all agents
"""

import os
import async<PERSON>
from typing import Optional, List, Any, Dict, Tuple

from .local_tools_manager import LocalToolsManager
from .local_llm_manager import LocalLLMManager
from .network_tools_manager import NetworkToolsManager, NetworkStats

class BaseAgent:
    """Enhanced base agent with local tool, LLM, and network capabilities."""
    
    def __init__(self, name: str):
        self.name = name
        self.local_tools = LocalToolsManager()
        self.local_llm = LocalLLMManager()
        self.network_tools = NetworkToolsManager()
        self.loaded_models: Dict[str, bool] = {}
        self.active_connections: Dict[str, str] = {}  # Map of connection name to connection ID
        
    async def initialize(self):
        """Initialize the agent with local capabilities."""
        # Initialize base components
        await self._setup_local_tools()
        await self._setup_local_models()
        await self._setup_network_tools()
        
    async def _setup_local_tools(self):
        """Set up local automation tools."""
        # No specific setup needed for tools manager
        pass
        
    async def _setup_local_models(self):
        """Set up local language models."""
        # Try to load commonly used local models
        model_paths = {
            "local-mistral": os.path.expanduser("~/.cache/huggingface/mistral-7b"),
            "local-llama": os.path.expanduser("~/.cache/huggingface/llama-2-7b"),
            "local-phi": os.path.expanduser("~/.cache/huggingface/phi-2")
        }
        
        for model_name, path in model_paths.items():
            if os.path.exists(path):
                success = await self.local_llm.load_model(model_name, path)
                self.loaded_models[model_name] = success
                
    async def _setup_network_tools(self):
        """Set up network monitoring and tools."""
        # Check internet connectivity
        is_connected = await self.network_tools.check_connectivity()
        if not is_connected:
            print(f"Warning: Agent {self.name} has no internet connectivity")
            
    async def process_with_local_llm(
        self,
        prompt: str,
        model_name: Optional[str] = None,
        max_length: int = 100,
        temperature: float = 0.7
    ) -> List[str]:
        """Process text with a local LLM if available."""
        # Use first available model if none specified
        if not model_name:
            available_models = [m for m, loaded in self.loaded_models.items() if loaded]
            if not available_models:
                raise ValueError("No local models available")
            model_name = available_models[0]
            
        return await self.local_llm.generate_text(
            model_name,
            prompt,
            max_length=max_length,
            temperature=temperature
        )
        
    async def get_local_embeddings(
        self,
        texts: List[str],
        model_name: Optional[str] = None
    ) -> List[List[float]]:
        """Get embeddings using local model."""
        if not model_name:
            available_models = [m for m, loaded in self.loaded_models.items() if loaded]
            if not available_models:
                raise ValueError("No local models available")
            model_name = available_models[0]
            
        return await self.local_llm.get_embeddings(model_name, texts)
        
    async def perform_screen_action(
        self,
        action_type: str,
        **kwargs
    ) -> Any:
        """Perform a screen-based action using local tools."""
        actions = {
            "capture": self.local_tools.capture_screen,
            "find": self.local_tools.find_on_screen,
            "type": self.local_tools.type_text,
            "click": self.local_tools.click_position,
            "drag": self.local_tools.drag_mouse,
            "press": self.local_tools.press_key
        }
        
        if action_type not in actions:
            raise ValueError(f"Unknown action type: {action_type}")
            
        return await actions[action_type](**kwargs)
        
    async def create_connection(self, name: str, url: str) -> str:
        """Create a persistent network connection."""
        conn_id = await self.network_tools.create_connection(url)
        self.active_connections[name] = conn_id
        return conn_id
        
    async def make_request(
        self,
        connection_name: str,
        method: str,
        path: str,
        **kwargs
    ) -> Tuple[int, dict, bytes]:
        """Make a network request using a persistent connection."""
        if connection_name not in self.active_connections:
            raise ValueError(f"Unknown connection: {connection_name}")
            
        return await self.network_tools.request(
            self.active_connections[connection_name],
            method,
            path,
            **kwargs
        )
        
    def get_network_stats(self, host: Optional[str] = None) -> Dict[str, NetworkStats]:
        """Get network statistics for monitored connections."""
        return self.network_tools.get_connection_stats(host)
        
    def cleanup(self):
        """Clean up resources."""
        self.local_tools.clear_cache()
        self.local_llm.clear_cache()
        
        # Cleanup models
        for model_name in list(self.loaded_models.keys()):
            if self.loaded_models[model_name]:
                self.local_llm.unload_model(model_name)
                self.loaded_models[model_name] = False
                
        # Cleanup network connections
        async def cleanup_connections():
            await self.network_tools.cleanup()
        asyncio.create_task(cleanup_connections())