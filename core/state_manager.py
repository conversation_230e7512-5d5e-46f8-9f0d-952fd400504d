"""
State Manager for handling shared state and persistence
"""

import asyncio
from typing import Dict, Any, Optional, List, Tuple
from datetime import datetime, timedelta
import json
import pickle
from core.cache_manager import get_cache_manager
from core.database_manager import get_database_manager
from utils.logging_setup import get_logger
from utils.metrics import get_metrics_collector
from utils.tracing import get_tracer

class StateManager:
    """Manages system state and persistence"""
    
    def __init__(self):
        self.logger = get_logger("state_manager")
        self.metrics = get_metrics_collector("state_manager")
        self.tracer = get_tracer("state_manager")
        
        # Core services
        self.cache = get_cache_manager()
        self.db = get_database_manager()
        
        # In-memory state
        self._memory_state: Dict[str, Any] = {}
        self._locks: Dict[str, asyncio.Lock] = {}
        
        # State change listeners
        self._listeners: Dict[str, List[callable]] = {}
        
        # Change history
        self._history: List[Dict] = []
        self._max_history = 1000
        
        # Transaction support
        self._transactions: Dict[str, List[Tuple[str, Any]]] = {}
        
    async def get(
        self,
        key: str,
        default: Any = None
    ) -> Any:
        """Get state value"""
        try:
            with self.tracer.traced_operation(
                "get_state",
                {"key": key}
            ):
                # Try cache first
                value = await self.cache.get(f"state:{key}")
                if value is not None:
                    self.metrics.record_operation(
                        "state_cache_hit",
                        "success"
                    )
                    return value
                    
                # Try memory
                if key in self._memory_state:
                    value = self._memory_state[key]
                    await self.cache.set(f"state:{key}", value)
                    return value
                    
                # Try database
                value = await self.db.get_state(key)
                if value is not None:
                    self._memory_state[key] = value
                    await self.cache.set(f"state:{key}", value)
                    return value
                    
                return default
                
        except Exception as e:
            self.logger.error(f"Error getting state: {str(e)}")
            self.metrics.record_error("state_get_error")
            self.tracer.record_exception(e)
            raise
            
    async def set(
        self,
        key: str,
        value: Any,
        source: str,
        persist: bool = True,
        ttl: Optional[int] = None
    ):
        """Set state value"""
        try:
            async with self._get_lock(key):
                with self.tracer.traced_operation(
                    "set_state",
                    {
                        "key": key,
                        "source": source
                    }
                ):
                    # Get old value
                    old_value = await self.get(key)
                    
                    # Update memory
                    self._memory_state[key] = value
                    
                    # Update cache
                    await self.cache.set(
                        f"state:{key}",
                        value,
                        ttl
                    )
                    
                    # Persist to database
                    if persist:
                        await self.db.set_state(
                            key,
                            value,
                            source
                        )
                        
                    # Record change
                    self._record_change(
                        key,
                        old_value,
                        value,
                        source
                    )
                    
                    # Notify listeners
                    await self._notify_listeners(
                        key,
                        old_value,
                        value
                    )
                    
                    self.metrics.record_operation(
                        "state_set",
                        "success"
                    )
                    
        except Exception as e:
            self.logger.error(f"Error setting state: {str(e)}")
            self.metrics.record_error("state_set_error")
            self.tracer.record_exception(e)
            raise
            
    async def delete(
        self,
        key: str,
        source: str
    ):
        """Delete state value"""
        try:
            async with self._get_lock(key):
                with self.tracer.traced_operation(
                    "delete_state",
                    {
                        "key": key,
                        "source": source
                    }
                ):
                    # Get old value
                    old_value = await self.get(key)
                    
                    # Remove from memory
                    self._memory_state.pop(key, None)
                    
                    # Remove from cache
                    await self.cache.delete(f"state:{key}")
                    
                    # Remove from database
                    await self.db.delete_state(key)
                    
                    # Record change
                    self._record_change(
                        key,
                        old_value,
                        None,
                        source
                    )
                    
                    # Notify listeners
                    await self._notify_listeners(
                        key,
                        old_value,
                        None
                    )
                    
                    self.metrics.record_operation(
                        "state_delete",
                        "success"
                    )
                    
        except Exception as e:
            self.logger.error(f"Error deleting state: {str(e)}")
            self.metrics.record_error("state_delete_error")
            self.tracer.record_exception(e)
            raise
            
    def add_listener(
        self,
        key: str,
        listener: callable
    ):
        """Add state change listener"""
        if key not in self._listeners:
            self._listeners[key] = []
        self._listeners[key].append(listener)
        
    def remove_listener(
        self,
        key: str,
        listener: callable
    ):
        """Remove state change listener"""
        if key in self._listeners:
            self._listeners[key].remove(listener)
            if not self._listeners[key]:
                del self._listeners[key]
                
    async def start_transaction(self) -> str:
        """Start new transaction"""
        transaction_id = str(len(self._transactions))
        self._transactions[transaction_id] = []
        return transaction_id
        
    async def commit_transaction(self, transaction_id: str):
        """Commit transaction"""
        if transaction_id not in self._transactions:
            raise ValueError(f"Invalid transaction ID: {transaction_id}")
            
        try:
            # Execute changes
            for key, value in self._transactions[transaction_id]:
                await self.set(key, value, f"transaction:{transaction_id}")
                
            del self._transactions[transaction_id]
            
        except Exception as e:
            # Rollback on error
            await self.rollback_transaction(transaction_id)
            raise
            
    async def rollback_transaction(self, transaction_id: str):
        """Rollback transaction"""
        if transaction_id in self._transactions:
            del self._transactions[transaction_id]
            
    def get_history(
        self,
        key: Optional[str] = None,
        limit: int = 100
    ) -> List[Dict]:
        """Get state change history"""
        history = self._history
        
        if key:
            history = [
                change for change in history
                if change["key"] == key
            ]
            
        return history[-limit:]
        
    def _get_lock(self, key: str) -> asyncio.Lock:
        """Get or create lock for key"""
        if key not in self._locks:
            self._locks[key] = asyncio.Lock()
        return self._locks[key]
        
    def _record_change(
        self,
        key: str,
        old_value: Any,
        new_value: Any,
        source: str
    ):
        """Record state change in history"""
        change = {
            "key": key,
            "old_value": old_value,
            "new_value": new_value,
            "source": source,
            "timestamp": datetime.utcnow().isoformat()
        }
        
        self._history.append(change)
        
        # Trim history if needed
        while len(self._history) > self._max_history:
            self._history.pop(0)
            
    async def _notify_listeners(
        self,
        key: str,
        old_value: Any,
        new_value: Any
    ):
        """Notify state change listeners"""
        if key not in self._listeners:
            return
            
        for listener in self._listeners[key]:
            try:
                await listener(key, old_value, new_value)
            except Exception as e:
                self.logger.error(
                    f"Error in state change listener: {str(e)}"
                )

# Global state manager instance
_state_manager: Optional[StateManager] = None

def get_state_manager() -> StateManager:
    """Get global state manager instance"""
    global _state_manager
    if _state_manager is None:
        _state_manager = StateManager()
    return _state_manager

# Example usage
if __name__ == "__main__":
    async def example():
        state = get_state_manager()
        
        # Set state
        await state.set(
            "test_key",
            {"value": 42},
            "example"
        )
        
        # Get state
        value = await state.get("test_key")
        print(f"State value: {value}")
        
        # Add listener
        async def state_changed(key, old, new):
            print(f"State changed: {key}")
            print(f"Old: {old}")
            print(f"New: {new}")
            
        state.add_listener("test_key", state_changed)
        
        # Update state
        await state.set(
            "test_key",
            {"value": 43},
            "example"
        )
        
        # Get history
        history = state.get_history("test_key")
        print(f"Change history: {history}")
        
    asyncio.run(example())