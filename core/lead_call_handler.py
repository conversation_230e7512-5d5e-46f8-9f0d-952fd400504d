"""
Lead Call Handler with Eleven<PERSON>abs and Twilio Integration
"""

import os
import json
import logging
from typing import Dict, Any, Optional
from pathlib import Path
import httpx
import pytz
from datetime import datetime
from twilio.rest import Client
from elevenlabs.client import ElevenLabs
from elevenlabs import Voice
from .anthropic_client import Anthropic<PERSON>lient
from .database_manager import DatabaseManager

logger = logging.getLogger(__name__)

class LeadCallHandler:
    """<PERSON>les lead calls with AI voice and Twilio integration"""
    
    def __init__(self):
        # Initialize services
        self.twilio_client = Client(os.getenv('TWILIO_ACCOUNT_SID'), 
                                  os.getenv('TWILIO_AUTH_TOKEN'))
        self.elevenlabs_client = ElevenLabs(api_key=os.getenv('ELEVENLABS_API_KEY'))
        self.ai_client = AnthropicClient(use_local=True)
        self.db = DatabaseManager()
        
        # Voice configuration
        self.voice = Voice(
            voice_id=os.getenv('ELEVENLABS_VOICE_ID'),
            settings=Voice.Settings(
                stability=0.7,
                similarity_boost=0.8
            )
        )
        
    async def handle_incoming_call(self, call_data: Dict[str, Any]) -> Dict[str, Any]:
        """Process an incoming Twilio call"""
        try:
            # Get lead info from database
            lead = self.db.get_lead(call_data['From'])
            
            # Generate initial greeting
            greeting = await self._generate_greeting(lead)
            audio = await self._generate_voice(greeting)
            
            # Play greeting and collect response
            response = await self._process_call_interaction(
                call_data['CallSid'],
                audio,
                lead
            )
            
            # Store call results
            self.db.log_call(
                call_sid=call_data['CallSid'],
                lead_id=lead['id'],
                transcript=response['transcript'],
                outcome=response['outcome']
            )
            
            return {
                'status': 'completed',
                'lead_id': lead['id'],
                'outcome': response['outcome']
            }
            
        except Exception as e:
            logger.error(f"Call handling error: {e}")
            return {
                'status': 'failed',
                'error': str(e)
            }
    
    async def _generate_greeting(self, lead: Dict[str, Any]) -> str:
        """Generate personalized greeting for IUL sales"""
        prompt = f"""
        You are a licensed insurance agent with Flo Faction Insurance calling {lead.get('name', 'a potential client')} about an Indexed Universal Life policy.
        Create a warm, professional greeting that:
        - Introduces you as a representative of Flo Faction Insurance
        - Mentions you're calling about their interest in financial security
        - Asks if now is a good 2-3 minute conversation
        Sound natural and friendly.
        Example: "Hi {lead.get('name', 'there')}, this is [Your Name] with Flo Faction Insurance. I'm calling about 
        your interest in financial security options - is now a good time for a quick chat?"
        """
        response = await self.ai_client.generate_text(prompt)
        return response['content'].replace("[Name]", lead.get('name', 'there'))
    
    async def _generate_voice(self, text: str) -> bytes:
        """Generate voice audio using ElevenLabs"""
        try:
            client = ElevenLabs(api_key=os.getenv('ELEVENLABS_API_KEY'))
            audio = self.elevenlabs_client.generate(
                text=text,
                voice=self.voice,
                model="eleven_monolingual_v2"
            )
            return audio
        except Exception as e:
            logger.error(f"Voice generation error: {e}")
            raise
    
    async def _process_call_interaction(
        self, 
        call_sid: str,
        initial_audio: bytes,
        lead: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Handle real IUL sales call with budget consideration"""
        try:
            # Start call with greeting
            self.twilio_client.calls(call_sid).update(
                twiml=f"""
                <Response>
                    <Play>{initial_audio}</Play>
                    <Gather action="/handle_response" method="POST" 
                            input="speech" timeout="5" speechTimeout="auto"/>
                </Response>
                """
            )
            
            # Implement full IUL sales flow
            conversation = [
                ("IUL benefits", "Explain key benefits of IUL in simple terms"),
                ("Budget fit", f"Address $200/month budget with suitable options"),
                ("Objections", "Handle common IUL objections professionally"),
                ("Close", "Suggest next steps for application")
            ]
            
            transcript = []
            for topic, prompt in conversation:
                response = await self._handle_topic(call_sid, topic, prompt)
                transcript.append(f"{topic}: {response}")
                
            return {
                'transcript': "\n".join(transcript),
                'outcome': "qualified" if "interested" in transcript[-1] else "not_interested"
            }
            
        except Exception as e:
            logger.error(f"Call interaction error: {e}")
            return {
                'transcript': str(e),
                'outcome': "failed"
            }
    
    async def persistent_outreach(self, lead: Dict[str, Any]) -> None:
        """Execute persistent outreach sequence until STOP received"""
        attempts = 0
        max_attempts = 6  # 3 sequences per day (call+text+email)
        
        while attempts < max_attempts:
            # Get current time in lead's timezone
            lead_tz = pytz.timezone(lead.get('timezone', 'America/New_York'))
            current_time = datetime.now(lead_tz)
            
            # Check if within allowed calling hours (8am-8pm)
            if 8 <= current_time.hour < 20:
                # First call attempt (no voicemail)
                call1 = self.twilio_client.calls.create(
                    to=lead['phone'],
                    from_=os.getenv('TWILIO_PHONE_NUMBER'),
                    url=f"{os.getenv('WEBHOOK_BASE_URL')}/call_handler?attempt=1",
                    machine_detection='Enable'
                )
            
            # Check first call status
            call1 = self.twilio_client.calls(call1.sid).fetch()
            if call1.status != 'completed':  # No answer
                # Second call attempt (allow voicemail)
                call2 = self.twilio_client.calls.create(
                    to=lead['phone'],
                    from_=os.getenv('TWILIO_PHONE_NUMBER'),
                    url=f"{os.getenv('WEBHOOK_BASE_URL')}/call_handler?attempt=2",
                    machine_detection='Enable'
                )
                
                # Send follow-up communications
                await self._send_follow_up(lead)
            
            # Check for STOP command
            if await self._check_for_stop(lead):
                break
                
            attempts += 1
            await asyncio.sleep(4 * 3600)  # Wait 4 hours between sequences

    async def _send_follow_up(self, lead: Dict[str, Any]) -> None:
        """Send text and email follow-up"""
        # Send text
        self.twilio_client.messages.create(
            to=lead['phone'],
            from_=os.getenv('TWILIO_PHONE_NUMBER'),
            body=f"Hi {lead['name']}, we tried reaching you about your IUL inquiry. Please call us back at {os.getenv('TWILIO_PHONE_NUMBER')}"
        )
        
        # Send email
        email_content = f"""
        Subject: Follow Up on Your IUL Inquiry
        
        Dear {lead['name']},
        
        We tried reaching you regarding your Indexed Universal Life insurance inquiry. 
        We have great options available within your $200/month budget.
        
        Please contact us at {os.getenv('TWILIO_PHONE_NUMBER')} to discuss.
        
        Best regards,
        Your Insurance Team
        """
        
        # Implement email sending logic here
        # (Would use actual email service in production)

    async def _check_for_stop(self, lead: Dict[str, Any]) -> bool:
        """Check if lead has requested to STOP"""
        # Check recent texts for STOP command
        messages = self.twilio_client.messages.list(
            to=os.getenv('TWILIO_PHONE_NUMBER'),
            from_=lead['phone']
        )
        
        for msg in messages:
            if 'stop' in msg.body.lower():
                return True
                
        # Check email for STOP (would implement in production)
        return False

    async def generate_call_summary(self, call_sid: str) -> str:
        """Generate AI summary of call"""
        call_data = self.db.get_call(call_sid)
        prompt = f"""
        Summarize this sales call:
        Lead: {call_data['lead_name']}
        Transcript: {call_data['transcript']}
        Outcome: {call_data['outcome']}
        
        Include key points and next steps.
        """
        
        response = await self.ai_client.generate_text(prompt)
        return response['content']
