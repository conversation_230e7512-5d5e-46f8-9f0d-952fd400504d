from typing import Dict, Any
from core.enhanced_base_agent import EnhancedBaseAgent

class InsuranceAgent(EnhancedBaseAgent):
    def __init__(self, agent_name: str = "insurance_agent"):
        super().__init__(agent_name, enable_ui=True)
        
    async def process_form(self, form_data: Dict[str, Any]) -> bool:
        """Process an insurance form with UI automation"""
        actions = [
            {"action": "find_element", "params": {"description": "Submit button"}},
            {"action": "click", "params": {"x": "$ELEMENT_X", "y": "$ELEMENT_Y"}},
            {"action": "type", "params": {"text": form_data.get("name", "")}},
        ]
        
        # Execute actions in optimal batch
        results = await self.batch_ui_actions(actions)
        
        # Check performance metrics
        metrics = self.get_ui_performance_metrics()
        self.logger.info(f"UI Performance: {metrics}")
        
        return all(success for success, _ in results)