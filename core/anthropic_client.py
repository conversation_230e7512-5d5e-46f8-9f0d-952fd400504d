"""
Anthropic Claude client supporting both API and local model execution
"""

import os
import json
import asyncio
import httpx
from typing import Dict, List, Any, Optional, Union
import logging
from pathlib import Path
from secure_credentials import SecureCredentialsManager

logger = logging.getLogger(__name__)

class AnthropicClient:
    """Client for Anthropic Claude with both API and local execution support"""
    
    def __init__(self, use_local: bool = True):
        self.base_url = "https://api.anthropic.com/v1"
        self.api_key = self._get_api_key()
        self.model = "claude-3-7-sonnet-20240620"
        self._client = None
        self.use_local = use_local
        self.local_models_dir = Path.home() / ".local" / "models" / "anthropic"
        self.local_model_path = None
        
    def _get_api_key(self) -> str:
        """Get API key from credentials manager"""
        try:
            credential_manager = SecureCredentialsManager()
            credentials = credential_manager.get_anthropic_credentials()
            return credentials.get("api_key", "")
        except Exception as e:
            logger.error(f"Error retrieving Anthropic API key: {e}")
            return ""

    def _get_local_model(self, model_name: str) -> Optional[Path]:
        """Check if local model exists and return its path"""
        model_path = self.local_models_dir / model_name
        if model_path.exists():
            self.local_model_path = model_path
            return model_path
        return None
            
    async def _get_client(self) -> httpx.AsyncClient:
        """Get or create HTTP client"""
        if self._client is None:
            self._client = httpx.AsyncClient(
                headers={
                    "x-api-key": self.api_key,
                    "anthropic-version": "2023-06-01",
                    "content-type": "application/json"
                },
                timeout=120.0
            )
        return self._client
        
    async def generate_text(
        self,
        prompt: str,
        system_prompt: Optional[str] = None,
        max_tokens: int = 4096,
        temperature: float = 0.7,
        top_p: float = 0.9,
        tools: Optional[List[Dict[str, Any]]] = None,
        tool_choice: Optional[str] = None
    ) -> Dict[str, Any]:
        """Generate text using either local or API Claude model"""
        # Try local execution first if enabled
        if self.use_local:
            model_path = self._get_local_model(self.model)
            if model_path:
                try:
                    return await self._generate_local(
                        prompt,
                        system_prompt,
                        max_tokens,
                        temperature,
                        top_p,
                        tools,
                        tool_choice
                    )
                except Exception as e:
                    logger.warning(f"Local execution failed: {e}, falling back to API")

        # Fall back to API if local fails or not enabled
        if not self.api_key:
            raise ValueError("Anthropic API key not found and local model unavailable")
            
        return await self._generate_api(
            prompt,
            system_prompt,
            max_tokens,
            temperature,
            top_p,
            tools,
            tool_choice
        )

    async def _generate_local(
        self,
        prompt: str,
        system_prompt: Optional[str] = None,
        max_tokens: int = 4096,
        temperature: float = 0.7,
        top_p: float = 0.9,
        tools: Optional[List[Dict[str, Any]]] = None,
        tool_choice: Optional[str] = None
    ) -> Dict[str, Any]:
        """Generate text using local model"""
        if not self.local_model_path:
            raise ValueError("Local model path not configured")

        try:
            # Prepare input for local model
            input_data = {
                "prompt": prompt,
                "system_prompt": system_prompt or "",
                "max_tokens": max_tokens,
                "temperature": temperature,
                "top_p": top_p
            }

            # Execute local model
            proc = await asyncio.create_subprocess_exec(
                str(self.local_model_path / "claude"),
                stdin=asyncio.subprocess.PIPE,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )

            # Send input and await response
            stdout, stderr = await proc.communicate(
                input=json.dumps(input_data).encode()
            )

            if proc.returncode != 0:
                error_msg = stderr.decode().strip()
                logger.error(f"Local model error: {error_msg}")
                raise RuntimeError(f"Local model failed: {error_msg}")

            # Parse and return response
            response = json.loads(stdout.decode())
            return {
                "content": response.get("text", ""),
                "model": self.model,
                "local": True,
                "usage": response.get("usage", {})
            }

        except json.JSONDecodeError as e:
            logger.error(f"Failed to parse model output: {e}")
            raise RuntimeError("Invalid model response format")
        except Exception as e:
            logger.error(f"Local model execution error: {e}")
            raise

    async def _generate_api(
        self,
        prompt: str,
        system_prompt: Optional[str] = None,
        max_tokens: int = 4096,
        temperature: float = 0.7,
        top_p: float = 0.9,
        tools: Optional[List[Dict[str, Any]]] = None,
        tool_choice: Optional[str] = None
    ) -> Dict[str, Any]:
        """Generate text using API"""
        client = await self._get_client()
        
        messages = [{"role": "user", "content": prompt}]
        
        payload = {
            "model": self.model,
            "messages": messages,
            "max_tokens": max_tokens,
            "temperature": temperature,
            "top_p": top_p
        }
        
        if system_prompt:
            payload["system"] = system_prompt
            
        if tools and tool_choice:
            payload["tools"] = tools
            payload["tool_choice"] = tool_choice
            
        try:
            response = await client.post(
                f"{self.base_url}/messages",
                json=payload
            )
            response.raise_for_status()
            return response.json()
        except Exception as e:
            logger.error(f"API call error: {e}")
            return {"error": str(e)}
            
    async def close(self):
        """Close the HTTP client"""
        if self._client:
            await self._client.aclose()
            self._client = None
            
    def set_model(self, model_name: str):
        """Set the model to use"""
        self.model = model_name
        
    async def get_embeddings(
        self,
        texts: List[str],
        model: Optional[str] = None
    ) -> List[List[float]]:
        """Get embeddings for input texts using Claude API"""
        if not self.api_key:
            raise ValueError("Anthropic API key not found. Run setup_anthropic_credentials.py first.")
            
        client = await self._get_client()
        
        embeddings = []
        for text in texts:
            try:
                response = await client.post(
                    f"{self.base_url}/embeddings",
                    json={
                        "model": model or "claude-3-sonnet-20240229",
                        "input": text
                    }
                )
                response.raise_for_status()
                result = response.json()
                embeddings.append(result.get("embedding", []))
            except Exception as e:
                logger.error(f"Error getting embeddings: {e}")
                embeddings.append([])
                
        return embeddings
