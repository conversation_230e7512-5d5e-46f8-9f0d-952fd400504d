from prometheus_client import Counter, Gauge, Histogram
from typing import Dict, Any, Optional

class UIMetricsCollector:
    """Collects and exports UI performance metrics"""
    
    def __init__(self, namespace: str = "ui_metrics"):
        # Interaction counts
        self.total_interactions = Counter(
            f"{namespace}_total_interactions",
            "Total number of UI interactions"
        )
        self.successful_interactions = Counter(
            f"{namespace}_successful_interactions",
            "Number of successful UI interactions"
        )
        self.failed_interactions = Counter(
            f"{namespace}_failed_interactions",
            "Number of failed UI interactions"
        )
        
        # Performance metrics
        self.interaction_duration = Histogram(
            f"{namespace}_interaction_duration_seconds",
            "Duration of UI interactions",
            buckets=[0.1, 0.5, 1.0, 2.0, 5.0]
        )
        self.element_recognition_time = Histogram(
            f"{namespace}_element_recognition_time_seconds",
            "Time taken to recognize UI elements",
            buckets=[0.05, 0.1, 0.2, 0.5, 1.0]
        )
        
        # Success rates
        self.success_rate = Gauge(
            f"{namespace}_success_rate",
            "Current success rate of UI interactions"
        )
        
        # Platform-specific metrics
        self.platform_interactions = Counter(
            f"{namespace}_platform_interactions_total",
            "Total interactions by platform",
            ["platform"]
        )
        self.platform_success_rate = Gauge(
            f"{namespace}_platform_success_rate",
            "Success rate by platform",
            ["platform"]
        )
        
        # Element recognition metrics
        self.element_recognition_success = Counter(
            f"{namespace}_element_recognition_success_total",
            "Successful element recognitions",
            ["element_type"]
        )
        self.element_recognition_failure = Counter(
            f"{namespace}_element_recognition_failure_total",
            "Failed element recognitions",
            ["element_type"]
        )
        
        # Cache performance
        self.cache_hits = Counter(
            f"{namespace}_cache_hits_total",
            "Number of UI element cache hits"
        )
        self.cache_misses = Counter(
            f"{namespace}_cache_misses_total",
            "Number of UI element cache misses"
        )
        self.cache_size = Gauge(
            f"{namespace}_cache_size",
            "Current size of UI element cache"
        )
        
        # Error tracking
        self.error_count = Counter(
            f"{namespace}_errors_total",
            "Total number of UI errors",
            ["error_type"]
        )
        
        # Resource usage
        self.memory_usage = Gauge(
            f"{namespace}_memory_usage_bytes",
            "Memory usage of UI components"
        )
        self.cpu_usage = Gauge(
            f"{namespace}_cpu_usage_percent",
            "CPU usage of UI components"
        )

    def record_interaction(self, duration: float, success: bool, platform: str):
        """Record a UI interaction"""
        self.total_interactions.inc()
        self.interaction_duration.observe(duration)
        self.platform_interactions.labels(platform=platform).inc()
        
        if success:
            self.successful_interactions.inc()
        else:
            self.failed_interactions.inc()
            
        # Update success rates
        total = self.successful_interactions._value.get() + self.failed_interactions._value.get()
        if total > 0:
            success_rate = self.successful_interactions._value.get() / total * 100
            self.success_rate.set(success_rate)
            self.platform_success_rate.labels(platform=platform).set(success_rate)

    def record_element_recognition(self, element_type: str, duration: float, success: bool):
        """Record element recognition attempt"""
        self.element_recognition_time.observe(duration)
        
        if success:
            self.element_recognition_success.labels(element_type=element_type).inc()
        else:
            self.element_recognition_failure.labels(element_type=element_type).inc()

    def record_cache_operation(self, hit: bool, size: int):
        """Record cache operation"""
        if hit:
            self.cache_hits.inc()
        else:
            self.cache_misses.inc()
        self.cache_size.set(size)

    def record_error(self, error_type: str):
        """Record UI error"""
        self.error_count.labels(error_type=error_type).inc()

    def update_resource_usage(self, memory_bytes: float, cpu_percent: float):
        """Update resource usage metrics"""
        self.memory_usage.set(memory_bytes)
        self.cpu_usage.set(cpu_percent)

    def get_summary(self) -> Dict[str, Any]:
        """Get summary of current metrics"""
        return {
            "total_interactions": self.total_interactions._value.get(),
            "success_rate": self.success_rate._value.get(),
            "avg_duration": self.interaction_duration._sum.get() / max(1, self.total_interactions._value.get()),
            "error_count": sum(self.error_count._metrics.values()),
            "cache_hit_rate": self.cache_hits._value.get() / max(1, (self.cache_hits._value.get() + self.cache_misses._value.get())) * 100,
        }

# Global metrics collector instance
_metrics_collector: Optional[UIMetricsCollector] = None

def get_metrics_collector(namespace: str = "ui_metrics") -> UIMetricsCollector:
    """Get or create global metrics collector"""
    global _metrics_collector
    if _metrics_collector is None:
        _metrics_collector = UIMetricsCollector(namespace)
    return _metrics_collector