import os
import asyncio
import pyautogui
import cv2
import numpy as np
from pynput import keyboard, mouse
from typing import Dict, List, Any, Optional

class LocalToolsManager:
    """Manager for local system tools and automation capabilities."""
    
    def __init__(self):
        self._keyboard_controller = keyboard.Controller()
        self._mouse_controller = mouse.Controller()
        self._screenshot_cache: Dict[str, np.ndarray] = {}
        
    async def capture_screen(self, region: Optional[tuple] = None) -> np.ndarray:
        """Capture screen or specific region."""
        screenshot = pyautogui.screenshot(region=region)
        return cv2.cvtColor(np.array(screenshot), cv2.COLOR_RGB2BGR)
    
    async def find_on_screen(self, template_path: str, confidence: float = 0.9) -> Optional[tuple]:
        """Find template image on screen."""
        screen = await self.capture_screen()
        template = cv2.imread(template_path)
        
        if template is None:
            raise ValueError(f"Could not load template image: {template_path}")
            
        result = cv2.matchTemplate(screen, template, cv2.TM_CCOEFF_NORMED)
        _, max_val, _, max_loc = cv2.minMaxLoc(result)
        
        if max_val >= confidence:
            return max_loc
        return None
        
    async def type_text(self, text: str, interval: float = 0.1):
        """Type text with natural delay."""
        for char in text:
            self._keyboard_controller.type(char)
            await asyncio.sleep(interval)
            
    async def click_position(self, x: int, y: int):
        """Click at specific coordinates."""
        self._mouse_controller.position = (x, y)
        self._mouse_controller.click(mouse.Button.left)
        
    async def drag_mouse(self, start_x: int, start_y: int, end_x: int, end_y: int, duration: float = 0.5):
        """Perform mouse drag operation."""
        self._mouse_controller.position = (start_x, start_y)
        self._mouse_controller.press(mouse.Button.left)
        
        steps = int(duration * 60)  # 60 steps per second
        for i in range(steps):
            t = i / steps
            current_x = start_x + (end_x - start_x) * t
            current_y = start_y + (end_y - start_y) * t
            self._mouse_controller.position = (current_x, current_y)
            await asyncio.sleep(duration / steps)
            
        self._mouse_controller.position = (end_x, end_y)
        self._mouse_controller.release(mouse.Button.left)
        
    async def press_key(self, key: str):
        """Press a specific keyboard key."""
        try:
            self._keyboard_controller.press(key)
            self._keyboard_controller.release(key)
        except Exception as e:
            raise ValueError(f"Invalid key: {key}. Error: {str(e)}")
            
    def clear_cache(self):
        """Clear the screenshot cache."""
        self._screenshot_cache.clear()