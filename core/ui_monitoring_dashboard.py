import asyncio
from typing import Dict, Any, List, Optional
from datetime import datetime, timedelta
from core.metrics import get_metrics_collector
from core.visual_interaction_monitor import VisualInteractionMonitor
from core.ui_anomaly_detector import UIAnomalyDetector

class UIMonitoringDashboard:
    """Centralized dashboard for monitoring UI performance across all agents"""
    
    def __init__(self):
        self.metrics = get_metrics_collector("ui_dashboard")
        self.visual_monitor = VisualInteractionMonitor()
        self.anomaly_detector = UIAnomalyDetector()
        self.agent_states = {}
        self.platform_stats = {}
        self._monitoring = False

    async def start_monitoring(self):
        """Start UI monitoring"""
        self._monitoring = True
        asyncio.create_task(self._monitor_loop())

    async def stop_monitoring(self):
        """Stop UI monitoring"""
        self._monitoring = False

    async def register_agent(self, agent_id: str, agent_type: str):
        """Register an agent for monitoring"""
        self.agent_states[agent_id] = {
            "type": agent_type,
            "status": "active",
            "last_interaction": None,
            "success_count": 0,
            "failure_count": 0,
            "performance_metrics": {
                "response_times": [],
                "success_rate": 0.0,
                "error_rate": 0.0
            }
        }

    async def record_interaction(self, agent_id: str, interaction_data: Dict[str, Any]):
        """Record an agent's UI interaction"""
        if agent_id not in self.agent_states:
            await self.register_agent(agent_id, interaction_data.get("agent_type", "unknown"))

        # Update agent state
        self.agent_states[agent_id]["last_interaction"] = datetime.now()
        if interaction_data.get("success", False):
            self.agent_states[agent_id]["success_count"] += 1
        else:
            self.agent_states[agent_id]["failure_count"] += 1

        # Update performance metrics
        metrics = self.agent_states[agent_id]["performance_metrics"]
        metrics["response_times"].append(interaction_data.get("duration", 0))
        total = self.agent_states[agent_id]["success_count"] + self.agent_states[agent_id]["failure_count"]
        metrics["success_rate"] = self.agent_states[agent_id]["success_count"] / total if total > 0 else 0
        metrics["error_rate"] = self.agent_states[agent_id]["failure_count"] / total if total > 0 else 0

        # Record platform statistics
        platform = interaction_data.get("platform", "unknown")
        if platform not in self.platform_stats:
            self.platform_stats[platform] = {
                "total_interactions": 0,
                "success_count": 0,
                "failure_count": 0,
                "response_times": []
            }
        
        stats = self.platform_stats[platform]
        stats["total_interactions"] += 1
        if interaction_data.get("success", False):
            stats["success_count"] += 1
        else:
            stats["failure_count"] += 1
        stats["response_times"].append(interaction_data.get("duration", 0))

        # Check for anomalies
        anomaly_result = await self.anomaly_detector.process_interaction(interaction_data)
        if anomaly_result["is_anomaly"]:
            await self._handle_anomaly(agent_id, anomaly_result)

        # Update visual monitoring
        if "screen_state" in interaction_data:
            await self.visual_monitor.record_screen_state(
                interaction_data["screen_state"],
                interaction_data
            )

    async def _monitor_loop(self):
        """Main monitoring loop"""
        while self._monitoring:
            try:
                await self._check_agent_health()
                await self._update_metrics()
                await asyncio.sleep(60)  # Check every minute
            except Exception as e:
                self.metrics.record_error(f"monitor_loop_error: {str(e)}")

    async def _check_agent_health(self):
        """Check health of all registered agents"""
        current_time = datetime.now()
        for agent_id, state in self.agent_states.items():
            if state["last_interaction"]:
                time_since_last = (current_time - state["last_interaction"]).total_seconds()
                if time_since_last > 300:  # 5 minutes
                    state["status"] = "inactive"
                    self.metrics.record_error(f"agent_inactive: {agent_id}")

    async def _update_metrics(self):
        """Update dashboard metrics"""
        for platform, stats in self.platform_stats.items():
            total = stats["success_count"] + stats["failure_count"]
            if total > 0:
                success_rate = stats["success_count"] / total * 100
                avg_response_time = sum(stats["response_times"]) / len(stats["response_times"])
                
                self.metrics.platform_success_rate.labels(platform=platform).set(success_rate)
                self.metrics.interaction_duration.observe(avg_response_time)

    async def _handle_anomaly(self, agent_id: str, anomaly_data: Dict[str, Any]):
        """Handle detected UI anomalies"""
        self.metrics.record_error(f"ui_anomaly_{anomaly_data['severity']}")
        
        # Update agent state
        self.agent_states[agent_id]["status"] = "warning"
        
        # If severe anomaly, trigger immediate action
        if anomaly_data["severity"] == "high":
            await self._trigger_anomaly_response(agent_id, anomaly_data)

    async def _trigger_anomaly_response(self, agent_id: str, anomaly_data: Dict[str, Any]):
        """Respond to severe UI anomalies"""
        # Record detailed anomaly data
        self.metrics.record_error(
            f"severe_anomaly_{agent_id}_{anomaly_data['details'].get('reason', 'unknown')}"
        )
        
        # Could implement automatic response actions here
        pass

    def get_dashboard_data(self) -> Dict[str, Any]:
        """Get current dashboard state"""
        return {
            "agents": {
                agent_id: {
                    "type": state["type"],
                    "status": state["status"],
                    "metrics": state["performance_metrics"]
                }
                for agent_id, state in self.agent_states.items()
            },
            "platforms": {
                platform: {
                    "total_interactions": stats["total_interactions"],
                    "success_rate": (stats["success_count"] / 
                                   (stats["success_count"] + stats["failure_count"]) * 100
                                   if stats["success_count"] + stats["failure_count"] > 0 else 0),
                    "avg_response_time": (sum(stats["response_times"]) / len(stats["response_times"])
                                        if stats["response_times"] else 0)
                }
                for platform, stats in self.platform_stats.items()
            },
            "anomalies": self.anomaly_detector.get_statistics(),
            "visual_performance": self.visual_monitor.get_performance_metrics()
        }

# Global dashboard instance
_dashboard: Optional[UIMonitoringDashboard] = None

def get_dashboard() -> UIMonitoringDashboard:
    """Get or create global UI monitoring dashboard"""
    global _dashboard
    if _dashboard is None:
        _dashboard = UIMonitoringDashboard()
    return _dashboard