"""
Dependency Injection Container for managing system components
"""

from typing import Dict, Any, Type, Optional, TypeVar, cast
import inspect
from dataclasses import dataclass
from enum import Enum
import asyncio
from utils.logging_setup import get_logger
from utils.metrics import get_metrics_collector
from utils.tracing import get_tracer

T = TypeVar('T')

class Lifecycle(Enum):
    SINGLETON = "singleton"
    TRANSIENT = "transient"
    SCOPED = "scoped"

@dataclass
class Registration:
    """Component registration details"""
    component_type: Type
    factory: Optional[callable] = None
    instance: Optional[Any] = None
    lifecycle: Lifecycle = Lifecycle.SINGLETON
    dependencies: Dict[str, Type] = None

class Container:
    """Dependency injection container"""
    
    def __init__(self):
        self.logger = get_logger("container")
        self.metrics = get_metrics_collector("container")
        self.tracer = get_tracer("container")
        
        # Component registrations
        self._registrations: Dict[Type, Registration] = {}
        
        # Scoped instances
        self._scopes: Dict[str, Dict[Type, Any]] = {}
        
        # Component graph for dependency resolution
        self._dependency_graph: Dict[Type, set] = {}
        
    def register(
        self,
        component_type: Type[T],
        factory: Optional[callable] = None,
        lifecycle: Lifecycle = Lifecycle.SINGLETON
    ) -> None:
        """Register a component"""
        try:
            # Get constructor dependencies
            if factory:
                deps = self._get_factory_dependencies(factory)
            else:
                deps = self._get_constructor_dependencies(component_type)
                
            # Create registration
            registration = Registration(
                component_type=component_type,
                factory=factory,
                lifecycle=lifecycle,
                dependencies=deps
            )
            
            # Update dependency graph
            self._update_dependency_graph(component_type, deps)
            
            # Store registration
            self._registrations[component_type] = registration
            
            self.logger.info(
                f"Registered component: {component_type.__name__}"
            )
            
        except Exception as e:
            self.logger.error(
                f"Error registering component {component_type.__name__}: {str(e)}"
            )
            self.metrics.record_error("registration_error")
            self.tracer.record_exception(e)
            raise
            
    def resolve(
        self,
        component_type: Type[T],
        scope_id: Optional[str] = None
    ) -> T:
        """Resolve component instance"""
        try:
            with self.tracer.traced_operation(
                "resolve_component",
                {"component": component_type.__name__}
            ):
                # Get registration
                registration = self._get_registration(component_type)
                
                # Check lifecycle
                if registration.lifecycle == Lifecycle.SINGLETON:
                    return self._resolve_singleton(registration)
                elif registration.lifecycle == Lifecycle.SCOPED:
                    if not scope_id:
                        raise ValueError(
                            "Scope ID required for scoped component"
                        )
                    return self._resolve_scoped(registration, scope_id)
                else:  # TRANSIENT
                    return self._resolve_transient(registration)
                    
        except Exception as e:
            self.logger.error(
                f"Error resolving component {component_type.__name__}: {str(e)}"
            )
            self.metrics.record_error("resolve_error")
            self.tracer.record_exception(e)
            raise
            
    def create_scope(self) -> str:
        """Create a new dependency scope"""
        scope_id = str(len(self._scopes))
        self._scopes[scope_id] = {}
        return scope_id
        
    def dispose_scope(self, scope_id: str):
        """Dispose of a dependency scope"""
        if scope_id in self._scopes:
            # Cleanup scope instances
            for instance in self._scopes[scope_id].values():
                if hasattr(instance, 'dispose'):
                    instance.dispose()
            del self._scopes[scope_id]
            
    def _get_registration(self, component_type: Type) -> Registration:
        """Get component registration"""
        if component_type not in self._registrations:
            raise ValueError(
                f"Component not registered: {component_type.__name__}"
            )
        return self._registrations[component_type]
        
    def _resolve_singleton(self, registration: Registration) -> Any:
        """Resolve singleton component"""
        if registration.instance is None:
            registration.instance = self._create_instance(registration)
        return registration.instance
        
    def _resolve_scoped(
        self,
        registration: Registration,
        scope_id: str
    ) -> Any:
        """Resolve scoped component"""
        if scope_id not in self._scopes:
            raise ValueError(f"Invalid scope ID: {scope_id}")
            
        scope = self._scopes[scope_id]
        if registration.component_type not in scope:
            scope[registration.component_type] = self._create_instance(
                registration
            )
        return scope[registration.component_type]
        
    def _resolve_transient(self, registration: Registration) -> Any:
        """Resolve transient component"""
        return self._create_instance(registration)
        
    def _create_instance(self, registration: Registration) -> Any:
        """Create component instance"""
        # Resolve dependencies
        deps = {}
        for name, dep_type in registration.dependencies.items():
            deps[name] = self.resolve(dep_type)
            
        # Create instance
        if registration.factory:
            return registration.factory(**deps)
        return registration.component_type(**deps)
        
    def _get_constructor_dependencies(
        self,
        component_type: Type
    ) -> Dict[str, Type]:
        """Get constructor parameter types"""
        if not hasattr(component_type, '__init__'):
            return {}
            
        sig = inspect.signature(component_type.__init__)
        deps = {}
        
        for name, param in sig.parameters.items():
            if name == 'self':
                continue
            if param.annotation == inspect.Parameter.empty:
                raise ValueError(
                    f"Missing type annotation for parameter {name} in "
                    f"{component_type.__name__}"
                )
            deps[name] = param.annotation
            
        return deps
        
    def _get_factory_dependencies(
        self,
        factory: callable
    ) -> Dict[str, Type]:
        """Get factory function parameter types"""
        sig = inspect.signature(factory)
        deps = {}
        
        for name, param in sig.parameters.items():
            if param.annotation == inspect.Parameter.empty:
                raise ValueError(
                    f"Missing type annotation for parameter {name} in factory"
                )
            deps[name] = param.annotation
            
        return deps
        
    def _update_dependency_graph(
        self,
        component_type: Type,
        dependencies: Dict[str, Type]
    ):
        """Update dependency graph"""
        if component_type not in self._dependency_graph:
            self._dependency_graph[component_type] = set()
            
        # Add dependencies
        for dep_type in dependencies.values():
            self._dependency_graph[component_type].add(dep_type)
            
        # Check for cycles
        self._check_cycles(component_type, set())
        
    def _check_cycles(
        self,
        component_type: Type,
        visited: set
    ):
        """Check for dependency cycles"""
        if component_type in visited:
            path = ' -> '.join(t.__name__ for t in visited)
            raise ValueError(
                f"Circular dependency detected: {path} -> "
                f"{component_type.__name__}"
            )
            
        visited.add(component_type)
        
        for dep_type in self._dependency_graph.get(component_type, set()):
            self._check_cycles(dep_type, visited.copy())

# Global container instance
_container: Optional[Container] = None

def get_container() -> Container:
    """Get global container instance"""
    global _container
    if _container is None:
        _container = Container()
    return _container

# Example usage
if __name__ == "__main__":
    class Database:
        def __init__(self, connection_string: str):
            self.connection_string = connection_string
            
    class UserRepository:
        def __init__(self, database: Database):
            self.database = database
            
    class UserService:
        def __init__(self, repo: UserRepository):
            self.repo = repo
            
    # Create container
    container = Container()
    
    # Register components
    container.register(
        Database,
        lambda: Database("localhost:5432")
    )
    container.register(UserRepository)
    container.register(UserService)
    
    # Resolve service
    service = container.resolve(UserService)
    print(f"Created service: {service}")
    print(f"Database connection: {service.repo.database.connection_string}")