"""
Error Handler for managing system-wide error handling and recovery
"""

import asyncio
from typing import Dict, Any, Optional, List, Type, Callable
from datetime import datetime
import traceback
import sys
from enum import Enum
from dataclasses import dataclass
from utils.logging_setup import get_logger
from utils.metrics import get_metrics_collector
from utils.tracing import get_tracer
from core.event_system import get_event_system

class ErrorSeverity(Enum):
    """Error severity levels"""
    LOW = "low"  # Non-critical errors
    MEDIUM = "medium"  # Important but non-fatal errors
    HIGH = "high"  # Serious errors requiring attention
    CRITICAL = "critical"  # System-threatening errors

@dataclass
class ErrorInfo:
    """Detailed error information"""
    error_type: str
    message: str
    severity: ErrorSeverity
    timestamp: datetime
    traceback: str
    context: Dict[str, Any]
    source: str
    correlation_id: Optional[str] = None
    handled: bool = False
    recovery_attempts: int = 0

class ErrorHandler:
    """Manages system-wide error handling"""
    
    def __init__(self):
        self.logger = get_logger("error_handler")
        self.metrics = get_metrics_collector("error_handler")
        self.tracer = get_tracer("error_handler")
        self.event_system = get_event_system()
        
        # Error registry
        self._error_history: List[ErrorInfo] = []
        self._active_errors: Dict[str, ErrorInfo] = {}
        
        # Error handlers by type
        self._handlers: Dict[Type[Exception], List[Callable]] = {}
        
        # Recovery strategies
        self._recovery_strategies: Dict[str, Callable] = {}
        
        # Error thresholds
        self._error_thresholds = {
            ErrorSeverity.LOW: 100,
            ErrorSeverity.MEDIUM: 50,
            ErrorSeverity.HIGH: 10,
            ErrorSeverity.CRITICAL: 1
        }
        
        # Error counts
        self._error_counts: Dict[ErrorSeverity, int] = {
            severity: 0 for severity in ErrorSeverity
        }
        
    def register_handler(
        self,
        error_type: Type[Exception],
        handler: Callable
    ):
        """Register error handler for specific error type"""
        if error_type not in self._handlers:
            self._handlers[error_type] = []
        self._handlers[error_type].append(handler)
        
    def register_recovery(
        self,
        error_type: str,
        strategy: Callable
    ):
        """Register recovery strategy for error type"""
        self._recovery_strategies[error_type] = strategy
        
    async def handle_error(
        self,
        error: Exception,
        severity: ErrorSeverity,
        source: str,
        context: Optional[Dict] = None,
        correlation_id: Optional[str] = None
    ):
        """Handle system error"""
        try:
            with self.tracer.traced_operation(
                "handle_error",
                {
                    "error_type": type(error).__name__,
                    "severity": severity.value,
                    "source": source
                }
            ):
                # Create error info
                error_info = ErrorInfo(
                    error_type=type(error).__name__,
                    message=str(error),
                    severity=severity,
                    timestamp=datetime.now(),
                    traceback=traceback.format_exc(),
                    context=context or {},
                    source=source,
                    correlation_id=correlation_id
                )
                
                # Store error
                self._store_error(error_info)
                
                # Update metrics
                self._update_error_metrics(error_info)
                
                # Check thresholds
                await self._check_thresholds(severity)
                
                # Find and execute handlers
                handled = await self._execute_handlers(error, error_info)
                error_info.handled = handled
                
                if not handled:
                    # Try recovery
                    await self._attempt_recovery(error_info)
                    
                # Publish error event
                await self.event_system.publish_event(
                    "system.error",
                    "error_handler",
                    {
                        "error": error_info.__dict__,
                        "handled": handled
                    }
                )
                
                return handled
                
        except Exception as e:
            self.logger.error(
                f"Error in error handler: {str(e)}"
            )
            self.metrics.record_error("error_handler_error")
            self.tracer.record_exception(e)
            return False
            
    def get_active_errors(
        self,
        severity: Optional[ErrorSeverity] = None
    ) -> List[ErrorInfo]:
        """Get active errors"""
        errors = list(self._active_errors.values())
        
        if severity:
            errors = [e for e in errors if e.severity == severity]
            
        return errors
        
    def get_error_history(
        self,
        limit: int = 100,
        severity: Optional[ErrorSeverity] = None
    ) -> List[ErrorInfo]:
        """Get error history"""
        errors = self._error_history
        
        if severity:
            errors = [e for e in errors if e.severity == severity]
            
        return errors[-limit:]
        
    def clear_error(self, error_type: str):
        """Clear active error"""
        if error_type in self._active_errors:
            del self._active_errors[error_type]
            
    async def _execute_handlers(
        self,
        error: Exception,
        error_info: ErrorInfo
    ) -> bool:
        """Execute registered error handlers"""
        handled = False
        
        # Find matching handlers
        handlers = []
        for error_type, error_handlers in self._handlers.items():
            if isinstance(error, error_type):
                handlers.extend(error_handlers)
                
        # Execute handlers
        for handler in handlers:
            try:
                result = handler(error, error_info)
                if asyncio.iscoroutine(result):
                    result = await result
                if result:
                    handled = True
            except Exception as e:
                self.logger.error(
                    f"Error in error handler: {str(e)}"
                )
                
        return handled
        
    async def _attempt_recovery(self, error_info: ErrorInfo):
        """Attempt error recovery"""
        if error_info.error_type not in self._recovery_strategies:
            return
            
        try:
            strategy = self._recovery_strategies[error_info.error_type]
            error_info.recovery_attempts += 1
            
            result = strategy(error_info)
            if asyncio.iscoroutine(result):
                result = await result
                
            if result:
                self.clear_error(error_info.error_type)
                
        except Exception as e:
            self.logger.error(
                f"Error in recovery strategy: {str(e)}"
            )
            
    def _store_error(self, error_info: ErrorInfo):
        """Store error information"""
        self._error_history.append(error_info)
        self._active_errors[error_info.error_type] = error_info
        
        # Trim history if needed
        while len(self._error_history) > 1000:
            self._error_history.pop(0)
            
    def _update_error_metrics(self, error_info: ErrorInfo):
        """Update error metrics"""
        self.metrics.record_error(
            error_info.error_type,
            {
                "severity": error_info.severity.value,
                "source": error_info.source
            }
        )
        
        self._error_counts[error_info.severity] += 1
        
    async def _check_thresholds(self, severity: ErrorSeverity):
        """Check error thresholds"""
        count = self._error_counts[severity]
        threshold = self._error_thresholds[severity]
        
        if count >= threshold:
            await self.event_system.publish_event(
                "system.error_threshold_exceeded",
                "error_handler",
                {
                    "severity": severity.value,
                    "count": count,
                    "threshold": threshold
                }
            )
            
    async def health_check(self) -> Dict[str, Any]:
        """Check error handler health"""
        return {
            "active_errors": len(self._active_errors),
            "error_counts": {
                severity.value: count
                for severity, count in self._error_counts.items()
            },
            "recovery_handlers": len(self._recovery_strategies)
        }

# Global error handler instance
_error_handler: Optional[ErrorHandler] = None

def get_error_handler() -> ErrorHandler:
    """Get global error handler instance"""
    global _error_handler
    if _error_handler is None:
        _error_handler = ErrorHandler()
    return _error_handler

# Example usage
if __name__ == "__main__":
    async def example():
        error_handler = get_error_handler()
        
        # Register error handler
        def handle_value_error(error: ValueError, info: ErrorInfo):
            print(f"Handling ValueError: {error}")
            return True
            
        error_handler.register_handler(ValueError, handle_value_error)
        
        # Register recovery strategy
        def recover_from_error(info: ErrorInfo):
            print(f"Recovering from {info.error_type}")
            return True
            
        error_handler.register_recovery(
            "ValueError",
            recover_from_error
        )
        
        # Handle error
        try:
            raise ValueError("Test error")
        except ValueError as e:
            await error_handler.handle_error(
                e,
                ErrorSeverity.MEDIUM,
                "example"
            )
            
    asyncio.run(example())