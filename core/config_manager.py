"""
Configuration Manager for handling system configuration
"""

from typing import Dict, Any, Optional, Union, List
from dataclasses import dataclass, field
import os
import yaml
from pathlib import Path
import json
from utils.logging_setup import get_logger
from utils.metrics import get_metrics_collector
from utils.tracing import get_tracer

@dataclass
class DatabaseConfig:
    """Database configuration"""
    host: str
    port: int
    database: str
    username: str
    password: str
    pool_size: int = 10
    ssl_mode: str = "disable"
    min_connections: int = 1
    max_connections: int = 20
    connection_timeout: int = 30
    idle_timeout: int = 300

@dataclass
class RedisConfig:
    """Redis configuration"""
    host: str
    port: int
    db: int = 0
    password: Optional[str] = None
    ssl: bool = False
    pool_size: int = 10
    timeout: int = 30

@dataclass
class SecurityConfig:
    """Security configuration"""
    jwt_secret: str
    token_expiry: int = 3600  # 1 hour
    refresh_token_expiry: int = 86400  # 24 hours
    password_hash_rounds: int = 12
    rate_limit_requests: int = 100
    rate_limit_period: int = 60
    allowed_origins: List[str] = field(default_factory=list)

@dataclass
class LoggingConfig:
    """Logging configuration"""
    level: str = "INFO"
    json_format: bool = True
    file_path: str = "logs"
    max_size_mb: int = 100
    backup_count: int = 5
    log_to_stdout: bool = True
    log_to_file: bool = True

@dataclass
class MonitoringConfig:
    """Monitoring configuration"""
    prometheus_port: int = 9090
    grafana_port: int = 3000
    metrics_interval: int = 60
    health_check_interval: int = 30
    tracing_enabled: bool = True
    trace_sample_rate: float = 0.1

@dataclass
class SystemConfig:
    """Complete system configuration"""
    environment: str
    database: DatabaseConfig
    redis: RedisConfig
    security: SecurityConfig
    logging: LoggingConfig
    monitoring: MonitoringConfig
    plugins: Dict[str, Any] = field(default_factory=dict)
    features: Dict[str, bool] = field(default_factory=dict)
    metadata: Dict[str, Any] = field(default_factory=dict)

class ConfigManager:
    """Manages system configuration"""
    
    def __init__(self):
        self.logger = get_logger("config_manager")
        self.metrics = get_metrics_collector("config_manager")
        self.tracer = get_tracer("config_manager")
        
        self._config: Optional[SystemConfig] = None
        self._config_path: Optional[Path] = None
        self._env_prefix = "AGENT_SYSTEM_"
        
    def load_config(self, config_path: Union[str, Path]) -> SystemConfig:
        """Load configuration from file"""
        try:
            with self.tracer.traced_operation(
                "load_config",
                {"path": str(config_path)}
            ):
                # Load config file
                config_path = Path(config_path)
                if not config_path.exists():
                    raise FileNotFoundError(
                        f"Config file not found: {config_path}"
                    )
                    
                with open(config_path) as f:
                    config_data = yaml.safe_load(f)
                    
                # Create config objects
                config = self._create_config(config_data)
                
                # Override with environment variables
                self._apply_env_overrides(config)
                
                # Store config
                self._config = config
                self._config_path = config_path
                
                self.logger.info("Configuration loaded successfully")
                self.metrics.record_operation(
                    "config_loaded",
                    "success"
                )
                
                return config
                
        except Exception as e:
            self.logger.error(f"Error loading config: {str(e)}")
            self.metrics.record_error("config_load_error")
            self.tracer.record_exception(e)
            raise
            
    def get_config(self) -> SystemConfig:
        """Get current configuration"""
        if not self._config:
            raise RuntimeError("Configuration not loaded")
        return self._config
        
    def get_value(
        self,
        key: str,
        default: Any = None
    ) -> Any:
        """Get configuration value by key path"""
        if not self._config:
            raise RuntimeError("Configuration not loaded")
            
        try:
            # Split key path
            parts = key.split('.')
            value = self._config
            
            # Traverse config
            for part in parts:
                value = getattr(value, part)
                
            return value
            
        except AttributeError:
            return default
            
    def reload_config(self) -> SystemConfig:
        """Reload configuration from file"""
        if not self._config_path:
            raise RuntimeError("No config file path set")
            
        return self.load_config(self._config_path)
        
    def _create_config(self, data: Dict) -> SystemConfig:
        """Create config object from dictionary"""
        return SystemConfig(
            environment=data.get('environment', 'development'),
            database=DatabaseConfig(**data.get('database', {})),
            redis=RedisConfig(**data.get('redis', {})),
            security=SecurityConfig(**data.get('security', {})),
            logging=LoggingConfig(**data.get('logging', {})),
            monitoring=MonitoringConfig(**data.get('monitoring', {})),
            plugins=data.get('plugins', {}),
            features=data.get('features', {}),
            metadata=data.get('metadata', {})
        )
        
    def _apply_env_overrides(self, config: SystemConfig):
        """Apply environment variable overrides"""
        for key, value in os.environ.items():
            if not key.startswith(self._env_prefix):
                continue
                
            # Remove prefix and convert to lowercase
            config_key = key[len(self._env_prefix):].lower()
            
            # Convert value type
            if value.lower() in ('true', 'false'):
                value = value.lower() == 'true'
            elif value.isdigit():
                value = int(value)
            elif value.replace('.', '').isdigit():
                value = float(value)
                
            # Apply override
            self._set_config_value(config, config_key, value)
            
    def _set_config_value(
        self,
        config: Any,
        key: str,
        value: Any
    ):
        """Set configuration value by key path"""
        parts = key.split('_')
        
        for i in range(len(parts) - 1):
            config = getattr(config, parts[i])
            
        setattr(config, parts[-1], value)
        
    def save_config(self, path: Optional[Union[str, Path]] = None):
        """Save current configuration to file"""
        if not self._config:
            raise RuntimeError("No configuration to save")
            
        path = Path(path) if path else self._config_path
        if not path:
            raise ValueError("No save path specified")
            
        try:
            # Convert config to dictionary
            config_dict = {
                'environment': self._config.environment,
                'database': self._asdict(self._config.database),
                'redis': self._asdict(self._config.redis),
                'security': self._asdict(self._config.security),
                'logging': self._asdict(self._config.logging),
                'monitoring': self._asdict(self._config.monitoring),
                'plugins': self._config.plugins,
                'features': self._config.features,
                'metadata': self._config.metadata
            }
            
            # Save to file
            with open(path, 'w') as f:
                yaml.dump(config_dict, f, default_flow_style=False)
                
            self.logger.info(f"Configuration saved to {path}")
            
        except Exception as e:
            self.logger.error(f"Error saving config: {str(e)}")
            self.metrics.record_error("config_save_error")
            self.tracer.record_exception(e)
            raise
            
    def _asdict(self, obj: Any) -> Dict:
        """Convert object to dictionary"""
        if hasattr(obj, '__dataclass_fields__'):
            return {
                k: getattr(obj, k)
                for k in obj.__dataclass_fields__
            }
        return obj

# Global config manager instance
_config_manager: Optional[ConfigManager] = None

def get_config_manager() -> ConfigManager:
    """Get global config manager instance"""
    global _config_manager
    if _config_manager is None:
        _config_manager = ConfigManager()
    return _config_manager

# Example usage
if __name__ == "__main__":
    config_manager = get_config_manager()
    
    # Load config
    config = config_manager.load_config("config/config.yml")
    
    # Access values
    print(f"Environment: {config.environment}")
    print(f"Database host: {config.database.host}")
    print(f"Log level: {config.logging.level}")
    
    # Get value by key
    redis_host = config_manager.get_value("redis.host")
    print(f"Redis host: {redis_host}")