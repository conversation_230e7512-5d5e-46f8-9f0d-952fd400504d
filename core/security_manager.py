"""
Security Manager for handling authentication and authorization
"""

import asyncio
from typing import Dict, Any, Optional, List, Set
from datetime import datetime, timedelta
import jwt
import bcrypt
from enum import Enum
import uuid
from dataclasses import dataclass
from utils.logging_setup import get_logger
from utils.metrics import get_metrics_collector
from utils.tracing import get_tracer
from core.config_manager import get_config_manager
from core.cache_manager import get_cache_manager
from core.database_manager import get_database_manager

class SecurityLevel(Enum):
    """Security clearance levels"""
    PUBLIC = 0
    BASIC = 1
    SENSITIVE = 2
    CONFIDENTIAL = 3
    SECRET = 4

@dataclass
class UserInfo:
    """User information"""
    user_id: str
    username: str
    roles: List[str]
    security_level: SecurityLevel
    metadata: Dict[str, Any]
    last_login: Optional[datetime] = None
    last_access: Optional[datetime] = None
    failed_attempts: int = 0
    locked: bool = False

class SecurityManager:
    """Manages system security"""
    
    def __init__(self):
        self.logger = get_logger("security_manager")
        self.metrics = get_metrics_collector("security_manager")
        self.tracer = get_tracer("security_manager")
        
        # Load config
        config = get_config_manager().get_config()
        self.config = config.security
        
        # Core services
        self.cache = get_cache_manager()
        self.db = get_database_manager()
        
        # Security settings
        self._token_expiry = self.config.token_expiry
        self._refresh_token_expiry = self.config.refresh_token_expiry
        self._max_failed_attempts = 5
        self._lockout_duration = 300  # 5 minutes
        
        # Active sessions
        self._active_tokens: Dict[str, str] = {}  # token -> user_id
        self._user_sessions: Dict[str, Set[str]] = {}  # user_id -> tokens
        
        # Role permissions
        self._role_permissions: Dict[str, Set[str]] = {}
        
        # Permission requirements
        self._endpoint_permissions: Dict[str, Set[str]] = {}
        
    async def register_user(
        self,
        username: str,
        password: str,
        roles: List[str],
        security_level: SecurityLevel,
        metadata: Optional[Dict] = None
    ) -> str:
        """Register new user"""
        try:
            with self.tracer.traced_operation(
                "register_user",
                {"username": username}
            ):
                # Check if user exists
                existing = await self.db.fetch_one(
                    "SELECT 1 FROM users WHERE username = $1",
                    username
                )
                if existing:
                    raise ValueError(f"User already exists: {username}")
                    
                # Generate user ID
                user_id = str(uuid.uuid4())
                
                # Hash password
                password_hash = bcrypt.hashpw(
                    password.encode(),
                    bcrypt.gensalt()
                ).decode()
                
                # Store user
                await self.db.execute(
                    """
                    INSERT INTO users (
                        id, username, password_hash, roles,
                        security_level, metadata, created_at
                    )
                    VALUES ($1, $2, $3, $4, $5, $6, $7)
                    """,
                    user_id,
                    username,
                    password_hash,
                    roles,
                    security_level.value,
                    metadata or {},
                    datetime.utcnow()
                )
                
                self.metrics.record_operation(
                    "user_registered",
                    "success"
                )
                
                return user_id
                
        except Exception as e:
            self.logger.error(f"Error registering user: {str(e)}")
            self.metrics.record_error("registration_error")
            self.tracer.record_exception(e)
            raise
            
    async def authenticate(
        self,
        username: str,
        password: str
    ) -> Optional[str]:
        """Authenticate user and return token"""
        try:
            with self.tracer.traced_operation(
                "authenticate",
                {"username": username}
            ):
                # Get user
                user = await self._get_user_by_username(username)
                if not user:
                    return None
                    
                # Check if account is locked
                if user.locked:
                    if not await self._check_unlock_user(user):
                        return None
                        
                # Verify password
                if not bcrypt.checkpw(
                    password.encode(),
                    user.password_hash.encode()
                ):
                    await self._handle_failed_login(user)
                    return None
                    
                # Generate token
                token = self._generate_token(user)
                
                # Update session tracking
                self._active_tokens[token] = user.id
                if user.id not in self._user_sessions:
                    self._user_sessions[user.id] = set()
                self._user_sessions[user.id].add(token)
                
                # Update user status
                await self._update_user_login(user)
                
                self.metrics.record_operation(
                    "user_authenticated",
                    "success"
                )
                
                return token
                
        except Exception as e:
            self.logger.error(f"Authentication error: {str(e)}")
            self.metrics.record_error("auth_error")
            self.tracer.record_exception(e)
            return None
            
    async def validate_token(self, token: str) -> Optional[UserInfo]:
        """Validate token and return user info"""
        try:
            # Check active tokens
            if token not in self._active_tokens:
                return None
                
            # Decode token
            try:
                payload = jwt.decode(
                    token,
                    self.config.jwt_secret,
                    algorithms=["HS256"]
                )
            except jwt.InvalidTokenError:
                return None
                
            # Get user
            user = await self._get_user_by_id(payload["sub"])
            if not user:
                return None
                
            # Update last access
            await self._update_user_access(user)
            
            return user
            
        except Exception as e:
            self.logger.error(f"Token validation error: {str(e)}")
            self.metrics.record_error("token_error")
            self.tracer.record_exception(e)
            return None
            
    async def invalidate_token(self, token: str):
        """Invalidate authentication token"""
        if token in self._active_tokens:
            user_id = self._active_tokens[token]
            del self._active_tokens[token]
            
            if user_id in self._user_sessions:
                self._user_sessions[user_id].remove(token)
                
    async def invalidate_user_sessions(self, user_id: str):
        """Invalidate all user sessions"""
        if user_id in self._user_sessions:
            tokens = self._user_sessions[user_id]
            for token in tokens:
                await self.invalidate_token(token)
            del self._user_sessions[user_id]
            
    def register_role_permissions(
        self,
        role: str,
        permissions: Set[str]
    ):
        """Register permissions for role"""
        self._role_permissions[role] = permissions
        
    def register_endpoint_permissions(
        self,
        endpoint: str,
        required_permissions: Set[str]
    ):
        """Register required permissions for endpoint"""
        self._endpoint_permissions[endpoint] = required_permissions
        
    async def check_permission(
        self,
        user: UserInfo,
        permission: str
    ) -> bool:
        """Check if user has permission"""
        user_permissions = set()
        for role in user.roles:
            if role in self._role_permissions:
                user_permissions.update(self._role_permissions[role])
                
        return permission in user_permissions
        
    async def check_endpoint_access(
        self,
        user: UserInfo,
        endpoint: str
    ) -> bool:
        """Check if user can access endpoint"""
        if endpoint not in self._endpoint_permissions:
            return True
            
        required = self._endpoint_permissions[endpoint]
        for permission in required:
            if not await self.check_permission(user, permission):
                return False
                
        return True
        
    async def _get_user_by_username(
        self,
        username: str
    ) -> Optional[UserInfo]:
        """Get user by username"""
        row = await self.db.fetch_one(
            "SELECT * FROM users WHERE username = $1",
            username
        )
        if not row:
            return None
            
        return self._create_user_info(row)
        
    async def _get_user_by_id(
        self,
        user_id: str
    ) -> Optional[UserInfo]:
        """Get user by ID"""
        row = await self.db.fetch_one(
            "SELECT * FROM users WHERE id = $1",
            user_id
        )
        if not row:
            return None
            
        return self._create_user_info(row)
        
    def _create_user_info(self, row: Dict) -> UserInfo:
        """Create UserInfo from database row"""
        return UserInfo(
            user_id=row["id"],
            username=row["username"],
            roles=row["roles"],
            security_level=SecurityLevel(row["security_level"]),
            metadata=row["metadata"],
            last_login=row.get("last_login"),
            last_access=row.get("last_access"),
            failed_attempts=row.get("failed_attempts", 0),
            locked=row.get("locked", False)
        )
        
    def _generate_token(self, user: UserInfo) -> str:
        """Generate JWT token"""
        payload = {
            "sub": user.user_id,
            "username": user.username,
            "roles": user.roles,
            "security_level": user.security_level.value,
            "exp": datetime.utcnow() + timedelta(
                seconds=self._token_expiry
            )
        }
        
        return jwt.encode(
            payload,
            self.config.jwt_secret,
            algorithm="HS256"
        )
        
    async def _update_user_login(self, user: UserInfo):
        """Update user login status"""
        await self.db.execute(
            """
            UPDATE users
            SET last_login = $1,
                failed_attempts = 0,
                locked = false
            WHERE id = $2
            """,
            datetime.utcnow(),
            user.user_id
        )
        
    async def _update_user_access(self, user: UserInfo):
        """Update user last access time"""
        await self.db.execute(
            """
            UPDATE users
            SET last_access = $1
            WHERE id = $2
            """,
            datetime.utcnow(),
            user.user_id
        )
        
    async def _handle_failed_login(self, user: UserInfo):
        """Handle failed login attempt"""
        failed_attempts = user.failed_attempts + 1
        locked = failed_attempts >= self._max_failed_attempts
        
        await self.db.execute(
            """
            UPDATE users
            SET failed_attempts = $1,
                locked = $2,
                locked_at = $3
            WHERE id = $4
            """,
            failed_attempts,
            locked,
            datetime.utcnow() if locked else None,
            user.user_id
        )
        
    async def _check_unlock_user(self, user: UserInfo) -> bool:
        """Check if locked user should be unlocked"""
        if not user.locked:
            return True
            
        row = await self.db.fetch_one(
            "SELECT locked_at FROM users WHERE id = $1",
            user.user_id
        )
        
        if not row or not row["locked_at"]:
            return True
            
        locked_at = row["locked_at"]
        if datetime.utcnow() - locked_at > timedelta(
            seconds=self._lockout_duration
        ):
            # Unlock user
            await self.db.execute(
                """
                UPDATE users
                SET locked = false,
                    locked_at = null,
                    failed_attempts = 0
                WHERE id = $1
                """,
                user.user_id
            )
            return True
            
        return False

# Global security manager instance
_security_manager: Optional[SecurityManager] = None

def get_security_manager() -> SecurityManager:
    """Get global security manager instance"""
    global _security_manager
    if _security_manager is None:
        _security_manager = SecurityManager()
    return _security_manager

# Example usage
if __name__ == "__main__":
    async def example():
        security = get_security_manager()
        
        # Register user
        user_id = await security.register_user(
            "testuser",
            "password123",
            ["user"],
            SecurityLevel.BASIC
        )
        
        # Authenticate
        token = await security.authenticate(
            "testuser",
            "password123"
        )
        
        # Validate token
        user = await security.validate_token(token)
        print(f"Authenticated user: {user.username}")
        
        # Register permissions
        security.register_role_permissions(
            "user",
            {"read:data", "write:data"}
        )
        
        # Check permission
        has_permission = await security.check_permission(
            user,
            "read:data"
        )
        print(f"Has permission: {has_permission}")
        
    asyncio.run(example())