import asyncio
import aiohttp
import logging
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional, Callable
import json
import os

class PackageMonitor:
    """Monitors package availability and updates"""
    
    def __init__(self):
        self.logger = logging.getLogger("package_monitor")
        self._check_interval = 3600  # 1 hour
        self._callbacks: Dict[str, List[Callable]] = {}
        self._last_check: Dict[str, datetime] = {}
        self._package_status: Dict[str, Dict[str, Any]] = {}
        self._running = False
        
    async def start_monitoring(self, packages: List[Dict[str, Any]]):
        """Start monitoring specified packages"""
        self._running = True
        self.logger.info(f"Starting package monitoring for {len(packages)} packages")
        
        while self._running:
            for package in packages:
                await self._check_package(package)
            await asyncio.sleep(self._check_interval)
            
    async def stop_monitoring(self):
        """Stop monitoring packages"""
        self._running = False
        
    def register_callback(self, package_name: str, callback: Callable):
        """Register a callback for package updates"""
        if package_name not in self._callbacks:
            self._callbacks[package_name] = []
        self._callbacks[package_name].append(callback)
        
    async def _check_package(self, package: Dict[str, Any]):
        """Check a specific package's availability"""
        name = package["name"]
        try:
            # Check PyPI
            pypi_status = await self._check_pypi(name)
            
            # Check GitHub
            if "github_repo" in package:
                github_status = await self._check_github(package["github_repo"])
            else:
                github_status = None
                
            # Check HuggingFace
            if "huggingface_model" in package:
                hf_status = await self._check_huggingface(package["huggingface_model"])
            else:
                hf_status = None
                
            status = {
                "last_check": datetime.now(),
                "pypi": pypi_status,
                "github": github_status,
                "huggingface": hf_status
            }
            
            # Detect changes
            if name in self._package_status:
                if self._has_status_changed(self._package_status[name], status):
                    await self._notify_callbacks(name, status)
            
            self._package_status[name] = status
            self._last_check[name] = datetime.now()
            
        except Exception as e:
            self.logger.error(f"Error checking package {name}: {str(e)}")
            
    async def _check_pypi(self, package_name: str) -> Dict[str, Any]:
        """Check package availability on PyPI"""
        async with aiohttp.ClientSession() as session:
            try:
                url = f"https://pypi.org/pypi/{package_name}/json"
                async with session.get(url) as response:
                    if response.status == 200:
                        data = await response.json()
                        return {
                            "available": True,
                            "version": data["info"]["version"],
                            "release_date": data["releases"][data["info"]["version"]][0]["upload_time"]
                        }
                    return {"available": False}
            except Exception:
                return {"available": False}
                
    async def _check_github(self, repo: str) -> Dict[str, Any]:
        """Check repository status on GitHub"""
        async with aiohttp.ClientSession() as session:
            try:
                url = f"https://api.github.com/repos/{repo}"
                async with session.get(url) as response:
                    if response.status == 200:
                        data = await response.json()
                        return {
                            "available": True,
                            "stars": data["stargazers_count"],
                            "last_update": data["updated_at"]
                        }
                    return {"available": False}
            except Exception:
                return {"available": False}
                
    async def _check_huggingface(self, model_id: str) -> Dict[str, Any]:
        """Check model availability on HuggingFace"""
        async with aiohttp.ClientSession() as session:
            try:
                url = f"https://huggingface.co/api/models/{model_id}"
                async with session.get(url) as response:
                    if response.status == 200:
                        data = await response.json()
                        return {
                            "available": True,
                            "downloads": data.get("downloads", 0),
                            "last_modified": data.get("lastModified", None)
                        }
                    return {"available": False}
            except Exception:
                return {"available": False}
                
    def _has_status_changed(self, old_status: Dict[str, Any], new_status: Dict[str, Any]) -> bool:
        """Check if package status has changed significantly"""
        for platform in ["pypi", "github", "huggingface"]:
            if platform not in old_status or platform not in new_status:
                continue
                
            old = old_status[platform]
            new = new_status[platform]
            
            if not old or not new:
                continue
                
            # Check availability change
            if old.get("available") != new.get("available"):
                return True
                
            # Check version change for PyPI
            if platform == "pypi" and old.get("version") != new.get("version"):
                return True
                
        return False
        
    async def _notify_callbacks(self, package_name: str, status: Dict[str, Any]):
        """Notify registered callbacks of package changes"""
        if package_name in self._callbacks:
            for callback in self._callbacks[package_name]:
                try:
                    await callback(status)
                except Exception as e:
                    self.logger.error(f"Error in callback for {package_name}: {str(e)}")
                    
    def get_status(self, package_name: Optional[str] = None) -> Dict[str, Any]:
        """Get current status of monitored packages"""
        if package_name:
            return self._package_status.get(package_name, {})
        return self._package_status

# Initialize global package monitor
_package_monitor: Optional[PackageMonitor] = None

def get_package_monitor() -> PackageMonitor:
    """Get global package monitor instance"""
    global _package_monitor
    if _package_monitor is None:
        _package_monitor = PackageMonitor()
    return _package_monitor