"""
Workflow Engine for managing agent operation sequences
"""

import asyncio
from typing import Dict, Any, Optional, List, Union, Callable
from datetime import datetime
import json
from enum import Enum
from dataclasses import dataclass
import uuid
from utils.logging_setup import get_logger
from utils.metrics import get_metrics_collector
from utils.tracing import get_tracer
from core.event_system import get_event_system

class WorkflowState(Enum):
    """Workflow execution states"""
    PENDING = "pending"
    RUNNING = "running"
    PAUSED = "paused"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"

@dataclass
class WorkflowStep:
    """Single workflow step"""
    id: str
    name: str
    handler: Callable
    dependencies: List[str] = None
    timeout: int = 300  # 5 minutes
    retry_count: int = 3
    retry_delay: int = 60
    required: bool = True
    metadata: Dict[str, Any] = None

@dataclass
class WorkflowInstance:
    """Workflow execution instance"""
    id: str
    workflow_id: str
    state: WorkflowState
    context: Dict[str, Any]
    start_time: datetime
    end_time: Optional[datetime] = None
    current_step: Optional[str] = None
    completed_steps: List[str] = None
    failed_steps: Dict[str, str] = None
    results: Dict[str, Any] = None

class WorkflowEngine:
    """Manages workflow execution"""
    
    def __init__(self):
        self.logger = get_logger("workflow_engine")
        self.metrics = get_metrics_collector("workflow_engine")
        self.tracer = get_tracer("workflow_engine")
        self.event_system = get_event_system()
        
        # Workflow registry
        self._workflows: Dict[str, Dict[str, WorkflowStep]] = {}
        
        # Active instances
        self._instances: Dict[str, WorkflowInstance] = {}
        
        # Step results cache
        self._step_results: Dict[str, Dict[str, Any]] = {}
        
    def register_workflow(
        self,
        workflow_id: str,
        steps: List[WorkflowStep]
    ):
        """Register workflow definition"""
        try:
            # Validate dependencies
            self._validate_dependencies(steps)
            
            # Store workflow
            self._workflows[workflow_id] = {
                step.id: step for step in steps
            }
            
            self.logger.info(f"Registered workflow: {workflow_id}")
            
        except Exception as e:
            self.logger.error(f"Error registering workflow: {str(e)}")
            self.metrics.record_error("workflow_registration_error")
            self.tracer.record_exception(e)
            raise
            
    async def start_workflow(
        self,
        workflow_id: str,
        context: Dict[str, Any] = None
    ) -> str:
        """Start workflow execution"""
        if workflow_id not in self._workflows:
            raise ValueError(f"Workflow not found: {workflow_id}")
            
        try:
            # Create instance
            instance_id = str(uuid.uuid4())
            instance = WorkflowInstance(
                id=instance_id,
                workflow_id=workflow_id,
                state=WorkflowState.PENDING,
                context=context or {},
                start_time=datetime.utcnow(),
                completed_steps=[],
                failed_steps={},
                results={}
            )
            
            self._instances[instance_id] = instance
            
            # Start execution
            asyncio.create_task(self._execute_workflow(instance))
            
            self.logger.info(
                f"Started workflow {workflow_id}: {instance_id}"
            )
            return instance_id
            
        except Exception as e:
            self.logger.error(f"Error starting workflow: {str(e)}")
            self.metrics.record_error("workflow_start_error")
            self.tracer.record_exception(e)
            raise
            
    async def pause_workflow(self, instance_id: str):
        """Pause workflow execution"""
        if instance_id not in self._instances:
            raise ValueError(f"Instance not found: {instance_id}")
            
        instance = self._instances[instance_id]
        if instance.state != WorkflowState.RUNNING:
            return
            
        instance.state = WorkflowState.PAUSED
        await self._publish_workflow_event(
            "workflow.paused",
            instance
        )
        
    async def resume_workflow(self, instance_id: str):
        """Resume workflow execution"""
        if instance_id not in self._instances:
            raise ValueError(f"Instance not found: {instance_id}")
            
        instance = self._instances[instance_id]
        if instance.state != WorkflowState.PAUSED:
            return
            
        instance.state = WorkflowState.RUNNING
        asyncio.create_task(
            self._execute_workflow(instance)
        )
        
        await self._publish_workflow_event(
            "workflow.resumed",
            instance
        )
        
    async def cancel_workflow(self, instance_id: str):
        """Cancel workflow execution"""
        if instance_id not in self._instances:
            raise ValueError(f"Instance not found: {instance_id}")
            
        instance = self._instances[instance_id]
        if instance.state in (
            WorkflowState.COMPLETED,
            WorkflowState.FAILED,
            WorkflowState.CANCELLED
        ):
            return
            
        instance.state = WorkflowState.CANCELLED
        instance.end_time = datetime.utcnow()
        
        await self._publish_workflow_event(
            "workflow.cancelled",
            instance
        )
        
    def get_workflow_status(
        self,
        instance_id: str
    ) -> Dict[str, Any]:
        """Get workflow execution status"""
        if instance_id not in self._instances:
            raise ValueError(f"Instance not found: {instance_id}")
            
        instance = self._instances[instance_id]
        return {
            "id": instance.id,
            "workflow_id": instance.workflow_id,
            "state": instance.state.value,
            "start_time": instance.start_time.isoformat(),
            "end_time": instance.end_time.isoformat() if instance.end_time else None,
            "current_step": instance.current_step,
            "completed_steps": instance.completed_steps,
            "failed_steps": instance.failed_steps,
            "results": instance.results
        }
        
    async def _execute_workflow(self, instance: WorkflowInstance):
        """Execute workflow steps"""
        try:
            workflow = self._workflows[instance.workflow_id]
            execution_order = self._get_execution_order(workflow)
            
            instance.state = WorkflowState.RUNNING
            await self._publish_workflow_event(
                "workflow.started",
                instance
            )
            
            # Execute steps
            for step_id in execution_order:
                if instance.state != WorkflowState.RUNNING:
                    return
                    
                step = workflow[step_id]
                instance.current_step = step_id
                
                # Check dependencies
                if not self._check_dependencies(
                    step,
                    instance.completed_steps
                ):
                    if step.required:
                        await self._handle_step_failure(
                            instance,
                            step,
                            "Dependency failure"
                        )
                        return
                    continue
                    
                # Execute step
                try:
                    result = await self._execute_step(step, instance)
                    instance.results[step_id] = result
                    instance.completed_steps.append(step_id)
                    
                    await self._publish_workflow_event(
                        "workflow.step_completed",
                        instance,
                        {"step_id": step_id}
                    )
                    
                except Exception as e:
                    await self._handle_step_failure(
                        instance,
                        step,
                        str(e)
                    )
                    if step.required:
                        return
                        
            # Complete workflow
            instance.state = WorkflowState.COMPLETED
            instance.end_time = datetime.utcnow()
            
            await self._publish_workflow_event(
                "workflow.completed",
                instance
            )
            
        except Exception as e:
            self.logger.error(f"Workflow execution error: {str(e)}")
            instance.state = WorkflowState.FAILED
            instance.end_time = datetime.utcnow()
            
            await self._publish_workflow_event(
                "workflow.failed",
                instance,
                {"error": str(e)}
            )
            
    async def _execute_step(
        self,
        step: WorkflowStep,
        instance: WorkflowInstance
    ) -> Any:
        """Execute single workflow step"""
        attempts = 0
        last_error = None
        
        while attempts < step.retry_count:
            try:
                with self.tracer.traced_operation(
                    "workflow_step",
                    {
                        "workflow": instance.workflow_id,
                        "step": step.id
                    }
                ):
                    # Execute handler
                    result = step.handler(
                        instance.context,
                        instance.results
                    )
                    if asyncio.iscoroutine(result):
                        result = await asyncio.wait_for(
                            result,
                            timeout=step.timeout
                        )
                        
                    return result
                    
            except Exception as e:
                attempts += 1
                last_error = e
                
                if attempts < step.retry_count:
                    await asyncio.sleep(step.retry_delay)
                    
        raise last_error
        
    def _validate_dependencies(self, steps: List[WorkflowStep]):
        """Validate workflow dependencies"""
        step_ids = {step.id for step in steps}
        
        for step in steps:
            if not step.dependencies:
                continue
                
            for dep in step.dependencies:
                if dep not in step_ids:
                    raise ValueError(
                        f"Invalid dependency {dep} for step {step.id}"
                    )
                    
        # Check for cycles
        dependencies = {
            step.id: set(step.dependencies or [])
            for step in steps
        }
        self._check_dependency_cycles(dependencies)
        
    def _check_dependency_cycles(
        self,
        dependencies: Dict[str, Set[str]]
    ):
        """Check for dependency cycles"""
        visited = set()
        path = []
        
        def visit(step_id: str):
            if step_id in path:
                cycle = ' -> '.join(
                    path[path.index(step_id):] + [step_id]
                )
                raise ValueError(f"Dependency cycle detected: {cycle}")
                
            if step_id in visited:
                return
                
            visited.add(step_id)
            path.append(step_id)
            
            for dep in dependencies.get(step_id, []):
                visit(dep)
                
            path.pop()
            
        for step_id in dependencies:
            visit(step_id)
            
    def _get_execution_order(
        self,
        workflow: Dict[str, WorkflowStep]
    ) -> List[str]:
        """Get step execution order"""
        # Build dependency graph
        dependencies = {
            step_id: set(step.dependencies or [])
            for step_id, step in workflow.items()
        }
        
        # Find execution order
        execution_order = []
        remaining = set(workflow.keys())
        
        while remaining:
            # Find steps with no remaining dependencies
            available = {
                step_id for step_id in remaining
                if not dependencies[step_id]
            }
            
            if not available:
                raise ValueError("Could not resolve step order")
                
            # Add steps to execution order
            execution_order.extend(sorted(available))
            remaining -= available
            
            # Remove executed steps from dependencies
            for deps in dependencies.values():
                deps -= available
                
        return execution_order
        
    def _check_dependencies(
        self,
        step: WorkflowStep,
        completed: List[str]
    ) -> bool:
        """Check if step dependencies are satisfied"""
        if not step.dependencies:
            return True
            
        return all(dep in completed for dep in step.dependencies)
        
    async def _handle_step_failure(
        self,
        instance: WorkflowInstance,
        step: WorkflowStep,
        error: str
    ):
        """Handle step execution failure"""
        instance.failed_steps[step.id] = error
        instance.state = WorkflowState.FAILED
        instance.end_time = datetime.utcnow()
        
        await self._publish_workflow_event(
            "workflow.step_failed",
            instance,
            {
                "step_id": step.id,
                "error": error
            }
        )
        
    async def _publish_workflow_event(
        self,
        event_type: str,
        instance: WorkflowInstance,
        data: Dict[str, Any] = None
    ):
        """Publish workflow event"""
        event_data = {
            "workflow_id": instance.workflow_id,
            "instance_id": instance.id,
            "state": instance.state.value
        }
        if data:
            event_data.update(data)
            
        await self.event_system.publish_event(
            event_type,
            "workflow_engine",
            event_data
        )

# Global workflow engine instance
_workflow_engine: Optional[WorkflowEngine] = None

def get_workflow_engine() -> WorkflowEngine:
    """Get global workflow engine instance"""
    global _workflow_engine
    if _workflow_engine is None:
        _workflow_engine = WorkflowEngine()
    return _workflow_engine

# Example usage
if __name__ == "__main__":
    async def example():
        engine = get_workflow_engine()
        
        # Define steps
        async def step1(context, results):
            return "Step 1 result"
            
        async def step2(context, results):
            return f"Step 2 with {results['step1']}"
            
        steps = [
            WorkflowStep(
                id="step1",
                name="First Step",
                handler=step1
            ),
            WorkflowStep(
                id="step2",
                name="Second Step",
                handler=step2,
                dependencies=["step1"]
            )
        ]
        
        # Register workflow
        engine.register_workflow("example", steps)
        
        # Start workflow
        instance_id = await engine.start_workflow(
            "example",
            {"input": "test"}
        )
        
        # Wait for completion
        await asyncio.sleep(2)
        
        # Check status
        status = engine.get_workflow_status(instance_id)
        print(f"Workflow status: {status}")
        
    asyncio.run(example())