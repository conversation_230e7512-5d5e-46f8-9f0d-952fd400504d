import os
import asyncio
import socket
import subprocess
from typing import Dict, <PERSON>, Optional, Tuple
from dataclasses import dataclass
import httpx

@dataclass
class NetworkStats:
    latency: float
    packet_loss: float
    bandwidth: Optional[float] = None

class NetworkToolsManager:
    """Manager for local network operations and monitoring."""
    
    def __init__(self):
        self._active_connections: Dict[str, httpx.AsyncClient] = {}
        self._connection_stats: Dict[str, NetworkStats] = {}
        
    async def check_connectivity(self, host: str = "*******", port: int = 53, timeout: float = 3.0) -> bool:
        """Check if there is an active internet connection."""
        try:
            socket.create_connection((host, port), timeout=timeout)
            return True
        except OSError:
            return False
            
    async def measure_latency(self, host: str = "*******") -> NetworkStats:
        """Measure network latency and packet loss to a host."""
        try:
            # Use ping command to measure latency
            if os.name == "nt":  # Windows
                cmd = f"ping -n 4 {host}"
            else:  # Unix/Linux/MacOS
                cmd = f"ping -c 4 {host}"
                
            process = await asyncio.create_subprocess_shell(
                cmd,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            stdout, _ = await process.communicate()
            output = stdout.decode()
            
            # Parse ping statistics
            if "packet loss" in output.lower():
                packet_loss = float(output.split("%")[0].split()[-1])
            else:
                packet_loss = 100.0
                
            if "time=" in output.lower():
                # Extract average latency
                latencies = [
                    float(line.split("time=")[1].split()[0])
                    for line in output.splitlines()
                    if "time=" in line.lower()
                ]
                latency = sum(latencies) / len(latencies) if latencies else float('inf')
            else:
                latency = float('inf')
                
            stats = NetworkStats(latency=latency, packet_loss=packet_loss)
            self._connection_stats[host] = stats
            return stats
            
        except Exception as e:
            print(f"Error measuring latency: {str(e)}")
            return NetworkStats(latency=float('inf'), packet_loss=100.0)
            
    async def create_connection(self, url: str) -> str:
        """Create a persistent connection for repeated requests."""
        conn_id = f"conn_{len(self._active_connections)}"
        self._active_connections[conn_id] = httpx.AsyncClient(
            base_url=url,
            timeout=30.0,
            follow_redirects=True
        )
        return conn_id
        
    async def close_connection(self, conn_id: str):
        """Close a persistent connection."""
        if conn_id in self._active_connections:
            await self._active_connections[conn_id].aclose()
            del self._active_connections[conn_id]
            
    async def request(
        self,
        conn_id: str,
        method: str,
        path: str,
        **kwargs
    ) -> Tuple[int, dict, bytes]:
        """Make a request using a persistent connection."""
        if conn_id not in self._active_connections:
            raise ValueError(f"Unknown connection ID: {conn_id}")
            
        client = self._active_connections[conn_id]
        response = await client.request(method, path, **kwargs)
        return response.status_code, dict(response.headers), response.content
        
    def get_connection_stats(self, host: Optional[str] = None) -> Dict[str, NetworkStats]:
        """Get network statistics for monitored connections."""
        if host:
            return {host: self._connection_stats.get(host)}
        return dict(self._connection_stats)
        
    async def cleanup(self):
        """Close all active connections."""
        for conn_id in list(self._active_connections.keys()):
            await self.close_connection(conn_id)
        self._connection_stats.clear()