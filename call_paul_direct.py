"""
Call <PERSON> Directly

This script makes a direct call to <PERSON> using Twi<PERSON>'s REST API.
"""

import os
from twilio.rest import Client
from dotenv import load_dotenv
import time

# Load environment variables from .env file
load_dotenv()

# <PERSON> contact information
PAUL_EDWARDS = {
    "first_name": os.getenv("PAUL_FIRST_NAME", "<PERSON>"),
    "last_name": os.getenv("PAUL_LAST_NAME", "<PERSON>"),
    "email": os.getenv("PAUL_EMAIL", "<EMAIL>"),
    "primary_phone": os.getenv("PAUL_PRIMARY_PHONE", "+***********"),
    "secondary_phone": os.getenv("PAUL_SECONDARY_PHONE", "+***********")
}

# Twilio credentials
TWILIO_ACCOUNT_SID = os.getenv("TWILIO_ACCOUNT_SID", "AC187c871afa232bbbc978caf33f3e25d9")
TWILIO_AUTH_TOKEN = os.getenv("TWILIO_AUTH_TOKEN", "CqpVewwter1BEMdFIFHrN2XmUyt22wBP")
TWILIO_PHONE_NUMBER = os.getenv("TWILIO_PHONE_NUMBER", "+***********")

def make_call():
    """Make a call to Paul Edwards using Twilio"""
    print("=" * 80)
    print("MAKING CALL TO PAUL EDWARDS")
    print("=" * 80)
    
    try:
        # Initialize Twilio client
        client = Client(TWILIO_ACCOUNT_SID, TWILIO_AUTH_TOKEN)
        
        # Create TwiML for the call
        twiml = f"""
        <Response>
            <Say voice="woman">
                Hello {PAUL_EDWARDS['first_name']}, this is Sandra Smith from Flo Faction Insurance. 
                
                I'm calling because I help people like you create tax-free retirement income without the risk of the stock market. 
                
                Based on your profile, I've found that you might qualify for a strategy that could provide around $5,000 per month in tax-free retirement income.
                
                I'd love to share a personalized analysis I've prepared for you. Please give me a call back when you have a moment.
                
                Thank you and have a great day!
            </Say>
            <Pause length="2"/>
        </Response>
        """
        
        # Make the call
        call = client.calls.create(
            twiml=twiml,
            to=PAUL_EDWARDS["primary_phone"],
            from_=TWILIO_PHONE_NUMBER
        )
        
        print(f"Call initiated with SID: {call.sid}")
        print(f"Calling {PAUL_EDWARDS['primary_phone']} from {TWILIO_PHONE_NUMBER}")
        
        # Wait for call to complete
        print("Waiting for call to complete...")
        time.sleep(5)
        
        # Check call status
        call = client.calls(call.sid).fetch()
        print(f"Call status: {call.status}")
        
        return call.sid
    except Exception as e:
        print(f"Error making call: {str(e)}")
        return None

def send_text_message():
    """Send a text message to Paul Edwards using Twilio"""
    print("=" * 80)
    print("SENDING TEXT MESSAGE TO PAUL EDWARDS")
    print("=" * 80)
    
    try:
        # Initialize Twilio client
        client = Client(TWILIO_ACCOUNT_SID, TWILIO_AUTH_TOKEN)
        
        # Create message content
        message_body = f"""
Hi {PAUL_EDWARDS['first_name']}, this is Sandra Smith with Flo Faction Insurance. I help people create tax-free retirement income without stock market risk. I've prepared a personalized analysis for you. Would you be interested in seeing this analysis? Please call me back when you have a moment. Thank you!
        """
        
        # Send the message
        message = client.messages.create(
            body=message_body,
            to=PAUL_EDWARDS["primary_phone"],
            from_=TWILIO_PHONE_NUMBER
        )
        
        print(f"Text message sent with SID: {message.sid}")
        print(f"Message sent to {PAUL_EDWARDS['primary_phone']} from {TWILIO_PHONE_NUMBER}")
        
        return message.sid
    except Exception as e:
        print(f"Error sending text message: {str(e)}")
        return None

def leave_voicemail():
    """Leave a voicemail for Paul Edwards using Twilio"""
    print("=" * 80)
    print("LEAVING VOICEMAIL FOR PAUL EDWARDS")
    print("=" * 80)
    
    try:
        # Initialize Twilio client
        client = Client(TWILIO_ACCOUNT_SID, TWILIO_AUTH_TOKEN)
        
        # Create TwiML for the voicemail
        twiml = f"""
        <Response>
            <Pause length="2"/>
            <Say voice="woman">
                Hi {PAUL_EDWARDS['first_name']}, this is Sandra Smith with Flo Faction Insurance. 
                
                I'm calling because I help people like you create tax-free retirement income without the risk of the stock market.
                
                Based on your profile, I've found that you might qualify for a strategy that could provide significant tax-free retirement income while also protecting your family with a death benefit.
                
                I'd love to share a personalized analysis I've prepared for you. Please give me a call back when you have a moment.
                
                Thank you and have a great day!
            </Say>
            <Hangup/>
        </Response>
        """
        
        # Make the call
        call = client.calls.create(
            twiml=twiml,
            to=PAUL_EDWARDS["primary_phone"],
            from_=TWILIO_PHONE_NUMBER,
            send_digits="1"  # This might help to skip to voicemail on some systems
        )
        
        print(f"Voicemail call initiated with SID: {call.sid}")
        print(f"Calling {PAUL_EDWARDS['primary_phone']} from {TWILIO_PHONE_NUMBER}")
        
        return call.sid
    except Exception as e:
        print(f"Error leaving voicemail: {str(e)}")
        return None

def main():
    """Execute all communication methods"""
    print("=" * 80)
    print("CONTACTING PAUL EDWARDS")
    print("=" * 80)
    
    # Ask which method to use
    print("Which communication method would you like to use?")
    print("1. Make a phone call")
    print("2. Send a text message")
    print("3. Leave a voicemail")
    print("4. All of the above")
    
    choice = input("Enter your choice (1-4): ")
    
    if choice == "1":
        call_sid = make_call()
        print(f"Call SID: {call_sid}")
    elif choice == "2":
        message_sid = send_text_message()
        print(f"Message SID: {message_sid}")
    elif choice == "3":
        voicemail_sid = leave_voicemail()
        print(f"Voicemail SID: {voicemail_sid}")
    elif choice == "4":
        call_sid = make_call()
        print(f"Call SID: {call_sid}")
        
        time.sleep(5)  # Wait a bit between communications
        
        message_sid = send_text_message()
        print(f"Message SID: {message_sid}")
        
        time.sleep(5)  # Wait a bit between communications
        
        voicemail_sid = leave_voicemail()
        print(f"Voicemail SID: {voicemail_sid}")
    else:
        print("Invalid choice. Please run the script again.")
    
    print("=" * 80)
    print("CONTACT OPERATIONS COMPLETED")
    print("=" * 80)

if __name__ == "__main__":
    main()
