import os
import requests
import logging
import time
from elevenlabs import generate as generate_voice
from twilio.rest import Client
from moviepy.editor import VideoFileClip, AudioFileClip, TextClip, CompositeVideoClip

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class ContentCreationAgent:
    """Agent specialized in content creation including voice generation and video creation."""

    def __init__(self, name, config=None):
        self.name = name
        self.config = config or {}
        self._init_apis()

    def _init_apis(self):
        """Initialize API clients."""
        # ElevenLabs Voice API
        self.elevenlabs_api_key = self.config.get('elevenlabs_api_key')
        
        # Twilio API
        twilio_account_sid = self.config.get('twilio_account_sid')
        twilio_auth_token = self.config.get('twilio_auth_token')
        if twilio_account_sid and twilio_auth_token:
            self.twilio_client = Client(twilio_account_sid, twilio_auth_token)
        else:
            self.twilio_client = None

        # D-ID API
        self.did_api_key = self.config.get('did_api_key')
        self.did_api_url = "https://api.d-id.com"

    def generate_voice(self, text, voice_id="default"):
        """Generate voice using ElevenLabs API."""
        if not self.elevenlabs_api_key:
            logger.error("ElevenLabs API key not configured.")
            return None
        
        try:
            audio = generate_voice(
                text=text,
                voice=voice_id,
                api_key=self.elevenlabs_api_key
            )
            output_path = "generated_voice.mp3"
            with open(output_path, "wb") as f:
                f.write(audio)
            logger.info(f"[{self.name}]: Voice generated successfully: {output_path}")
            return output_path
        except Exception as e:
            logger.error(f"[{self.name}]: Failed to generate voice: {e}")
            return None

    def create_video(self, script, voice_file=None):
        """Create video using D-ID API or local generation."""
        try:
            if self.did_api_key:
                return self._create_did_video(script, voice_file)
            else:
                return self._create_local_video(script, voice_file)
        except Exception as e:
            logger.error(f"[{self.name}]: Failed to create video: {e}")
            return None

    def _create_did_video(self, script, voice_file=None):
        """Create video using D-ID API."""
        try:
            # First, create the talk
            headers = {
                "Authorization": f"Basic {self.did_api_key}",
                "Content-Type": "application/json"
            }
            
            payload = {
                "script": {
                    "type": "text",
                    "input": script
                },
                "config": {
                    "fluent": "true",
                    "pad_audio": "0.0"
                },
                "source_url": "https://create-images-results.d-id.com/DefaultPresenters/Emma_f"
            }

            # If we have a voice file, use it instead of text
            if voice_file:
                with open(voice_file, 'rb') as f:
                    audio_data = f.read()
                payload["script"] = {
                    "type": "audio",
                    "audio_url": audio_data
                }

            response = requests.post(
                f"{self.did_api_url}/talks",
                json=payload,
                headers=headers
            )
            
            if response.status_code != 201:
                raise Exception(f"Failed to create talk: {response.text}")

            talk_id = response.json()["id"]
            
            # Wait for the video to be ready
            while True:
                status_response = requests.get(
                    f"{self.did_api_url}/talks/{talk_id}",
                    headers=headers
                )
                
                if status_response.json()["status"] == "done":
                    video_url = status_response.json()["result_url"]
                    
                    # Download the video
                    video_response = requests.get(video_url)
                    output_path = "generated_video.mp4"
                    with open(output_path, "wb") as f:
                        f.write(video_response.content)
                        
                    logger.info(f"[{self.name}]: Video created successfully: {output_path}")
                    return output_path
                    
                elif status_response.json()["status"] == "failed":
                    raise Exception("Video creation failed")
                    
                time.sleep(5)

        except Exception as e:
            logger.error(f"[{self.name}]: Failed to create D-ID video: {e}")
            return None

    def _create_local_video(self, script, voice_file=None):
        """Create video locally using MoviePy."""
        try:
            # If no voice file provided, generate one
            if not voice_file:
                voice_file = self.generate_voice(script)
                if not voice_file:
                    return None

            # Create text clip
            text_clip = TextClip(
                script, 
                fontsize=30, 
                color='white',
                bg_color='rgba(0,0,0,0.5)',
                size=(640, 480)
            )

            # Load audio
            audio_clip = AudioFileClip(voice_file)
            
            # Set duration of text clip to match audio
            text_clip = text_clip.set_duration(audio_clip.duration)
            
            # Create final video
            video = CompositeVideoClip([text_clip.set_pos('center')])
            video = video.set_audio(audio_clip)
            
            output_path = "generated_video.mp4"
            video.write_videofile(output_path, fps=24)
            
            logger.info(f"[{self.name}]: Video created locally: {output_path}")
            return output_path

        except Exception as e:
            logger.error(f"[{self.name}]: Failed to create local video: {e}")
            return None

    def send_sms(self, to_number, message):
        """Send SMS using Twilio."""
        if not self.twilio_client:
            logger.error("Twilio client not configured")
            return None
            
        try:
            message = self.twilio_client.messages.create(
                body=message,
                from_=self.config.get('twilio_phone_number'),
                to=to_number
            )
            logger.info(f"[{self.name}]: SMS sent successfully: {message.sid}")
            return message.sid
        except Exception as e:
            logger.error(f"[{self.name}]: Failed to send SMS: {e}")
            return None

    def make_call(self, to_number, voice_file=None, message=None):
        """Make phone call using Twilio with either a voice file or text message."""
        if not self.twilio_client:
            logger.error("Twilio client not configured")
            return None
            
        try:
            if voice_file:
                # Upload voice file to Twilio
                with open(voice_file, 'rb') as f:
                    media_url = self._upload_to_twilio(f.read())
            else:
                # Use text-to-speech
                media_url = None
                
            call = self.twilio_client.calls.create(
                twiml=f'<Response><Play>{media_url}</Play></Response>' if media_url else f'<Response><Say>{message}</Say></Response>',
                to=to_number,
                from_=self.config.get('twilio_phone_number')
            )
            
            logger.info(f"[{self.name}]: Call initiated successfully: {call.sid}")
            return call.sid
            
        except Exception as e:
            logger.error(f"[{self.name}]: Failed to make call: {e}")
            return None

    def execute_task(self, task, **kwargs):
        """Execute content creation task."""
        logger.info(f"[{self.name}]: Executing task '{task}'")
        
        task_map = {
            "generate_voice": self.generate_voice,
            "create_video": self.create_video,
            "send_sms": self.send_sms,
            "make_call": self.make_call
        }
        
        if task in task_map:
            return task_map[task](**kwargs)
        else:
            logger.warning(f"[{self.name}]: Unknown task '{task}'")
            return None

if __name__ == "__main__":
    # Example configuration
    config = {
        "elevenlabs_api_key": os.getenv("ELEVENLABS_API_KEY"),
        "twilio_account_sid": os.getenv("TWILIO_ACCOUNT_SID"),
        "twilio_auth_token": os.getenv("TWILIO_AUTH_TOKEN"),
        "twilio_phone_number": os.getenv("TWILIO_PHONE_NUMBER"),
        "did_api_key": os.getenv("DID_API_KEY")
    }
    
    agent = ContentCreationAgent("Test Agent", config=config)
    
    # Test voice generation
    voice_file = agent.execute_task(
        "generate_voice", 
        text="Welcome to FloFaction Insurance. We're here to protect what matters most."
    )
    
    if voice_file:
        # Test video creation with generated voice
        video_file = agent.execute_task(
            "create_video",
            script="Welcome to FloFaction Insurance",
            voice_file=voice_file
        )
