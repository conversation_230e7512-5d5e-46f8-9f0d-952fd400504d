"""
Social Media Integration for Flo Faction

This script demonstrates how to integrate with Facebook and Instagram
for the Flo Faction insurance agency.
"""

import os
import requests
import json
import time
from datetime import datetime

# Facebook credentials
FACEBOOK_PAGE_ID = os.environ.get("FACEBOOK_PAGE_ID", "your_page_id")
FACEBOOK_ACCESS_TOKEN = os.environ.get("FACEBOOK_ACCESS_TOKEN", "your_access_token")

# Instagram credentials (connected to Facebook)
INSTAGRAM_ACCOUNT_ID = os.environ.get("INSTAGRAM_ACCOUNT_ID", "your_instagram_account_id")

# Flo Faction branding
FLO_FACTION = {
    "name": "Flo Faction Insurance",
    "website": "https://www.flofaction.com",
    "facebook": "https://www.facebook.com/flofaction",
    "instagram": "https://www.instagram.com/flofaction",
    "phone": "************",
    "address": "123 Insurance Way, Insurance City, FL 12345",
    "logo_path": "assets/flo_faction_logo.png"  # Path to logo image file
}

# ======================== FACEBOOK FUNCTIONS ========================
def post_to_facebook_page(message, link=None, image_url=None):
    """
    Post a message to the Flo Faction Facebook page
    
    Args:
        message: The message to post
        link: Optional link to include
        image_url: Optional image URL to include
        
    Returns:
        Dictionary with post ID or error message
    """
    url = f"https://graph.facebook.com/v16.0/{FACEBOOK_PAGE_ID}/feed"
    
    params = {
        "access_token": FACEBOOK_ACCESS_TOKEN,
        "message": message
    }
    
    if link:
        params["link"] = link
    
    if image_url:
        params["picture"] = image_url
    
    try:
        response = requests.post(url, params=params)
        data = response.json()
        
        if "id" in data:
            print(f"Successfully posted to Facebook with ID: {data['id']}")
            return {"success": True, "post_id": data["id"]}
        else:
            print(f"Error posting to Facebook: {data.get('error', {}).get('message', 'Unknown error')}")
            return {"success": False, "error": data.get("error", {})}
    except Exception as e:
        print(f"Exception posting to Facebook: {str(e)}")
        return {"success": False, "error": str(e)}

def send_facebook_message(user_id, message):
    """
    Send a private message to a Facebook user
    
    Args:
        user_id: Facebook user ID
        message: Message to send
        
    Returns:
        Dictionary with message ID or error message
    """
    url = f"https://graph.facebook.com/v16.0/me/messages"
    
    params = {
        "access_token": FACEBOOK_ACCESS_TOKEN
    }
    
    data = {
        "recipient": {"id": user_id},
        "message": {"text": message}
    }
    
    try:
        response = requests.post(url, params=params, json=data)
        result = response.json()
        
        if "message_id" in result:
            print(f"Successfully sent Facebook message with ID: {result['message_id']}")
            return {"success": True, "message_id": result["message_id"]}
        else:
            print(f"Error sending Facebook message: {result.get('error', {}).get('message', 'Unknown error')}")
            return {"success": False, "error": result.get("error", {})}
    except Exception as e:
        print(f"Exception sending Facebook message: {str(e)}")
        return {"success": False, "error": str(e)}

def create_facebook_lead_form():
    """
    Create a lead form on the Flo Faction Facebook page
    
    Returns:
        Dictionary with form ID or error message
    """
    # Create a form
    form_url = f"https://graph.facebook.com/v16.0/{FACEBOOK_PAGE_ID}/leadgen_forms"
    
    form_params = {
        "access_token": FACEBOOK_ACCESS_TOKEN,
        "name": "Insurance Quote Request",
        "follow_up_action_url": f"{FLO_FACTION['website']}/quote-thank-you",
        "privacy_policy_url": f"{FLO_FACTION['website']}/privacy-policy",
        "questions": json.dumps([
            {
                "type": "FULL_NAME",
                "label": "Full Name"
            },
            {
                "type": "EMAIL",
                "label": "Email"
            },
            {
                "type": "PHONE",
                "label": "Phone Number"
            },
            {
                "type": "CUSTOM",
                "label": "What type of insurance are you interested in?",
                "options": ["Life Insurance", "Health Insurance", "Medicare", "Final Expense", "Other"]
            }
        ])
    }
    
    try:
        form_response = requests.post(form_url, params=form_params)
        form_data = form_response.json()
        
        if "id" in form_data:
            form_id = form_data["id"]
            print(f"Successfully created Facebook lead form with ID: {form_id}")
            return {"success": True, "form_id": form_id}
        else:
            print(f"Error creating Facebook lead form: {form_data.get('error', {}).get('message', 'Unknown error')}")
            return {"success": False, "error": form_data.get("error", {})}
    except Exception as e:
        print(f"Exception creating Facebook lead form: {str(e)}")
        return {"success": False, "error": str(e)}

# ======================== INSTAGRAM FUNCTIONS ========================
def post_to_instagram(caption, image_url):
    """
    Post an image with caption to Instagram
    
    Args:
        caption: The caption for the post
        image_url: URL of the image to post
        
    Returns:
        Dictionary with post ID or error message
    """
    # First, create a container
    container_url = f"https://graph.facebook.com/v16.0/{INSTAGRAM_ACCOUNT_ID}/media"
    
    container_params = {
        "access_token": FACEBOOK_ACCESS_TOKEN,
        "image_url": image_url,
        "caption": caption
    }
    
    try:
        container_response = requests.post(container_url, params=container_params)
        container_data = container_response.json()
        
        if "id" in container_data:
            container_id = container_data["id"]
            
            # Then publish the container
            publish_url = f"https://graph.facebook.com/v16.0/{INSTAGRAM_ACCOUNT_ID}/media_publish"
            
            publish_params = {
                "access_token": FACEBOOK_ACCESS_TOKEN,
                "creation_id": container_id
            }
            
            publish_response = requests.post(publish_url, params=publish_params)
            publish_data = publish_response.json()
            
            if "id" in publish_data:
                print(f"Successfully posted to Instagram with ID: {publish_data['id']}")
                return {"success": True, "post_id": publish_data["id"]}
            else:
                print(f"Error publishing Instagram post: {publish_data.get('error', {}).get('message', 'Unknown error')}")
                return {"success": False, "error": publish_data.get("error", {})}
        else:
            print(f"Error creating Instagram container: {container_data.get('error', {}).get('message', 'Unknown error')}")
            return {"success": False, "error": container_data.get("error", {})}
    except Exception as e:
        print(f"Exception posting to Instagram: {str(e)}")
        return {"success": False, "error": str(e)}

# ======================== CONTENT TEMPLATES ========================
def get_social_media_content():
    """
    Get content templates for social media posts
    
    Returns:
        Dictionary with content templates
    """
    return {
        "iul_introduction": {
            "facebook": {
                "message": f"""
Are you concerned about market volatility affecting your retirement savings?

Our Indexed Universal Life (IUL) policies offer:
✅ Tax-free retirement income
✅ Protection from market downturns
✅ Death benefit for your loved ones
✅ Cash value growth potential

Contact us today for a personalized analysis!
📞 {FLO_FACTION['phone']}
🌐 {FLO_FACTION['website']}
                """,
                "link": f"{FLO_FACTION['website']}/iul-retirement"
            },
            "instagram": {
                "caption": f"""
Secure your retirement without market risk! 💰

Our Indexed Universal Life policies provide tax-free retirement income while protecting you from market downturns.

Key benefits:
✅ Tax-free retirement income
✅ Protection from market losses
✅ Death benefit protection
✅ Cash value growth

Contact us for a personalized analysis!
📞 {FLO_FACTION['phone']}

#RetirementPlanning #TaxFreeIncome #FinancialSecurity #InsuranceTips #RetirementIncome #FinancialFreedom #IUL #IndexedUniversalLife
                """
            }
        },
        "client_testimonial": {
            "facebook": {
                "message": f"""
"I was worried about having enough income in retirement, but Flo Faction Insurance helped me set up an IUL policy that will provide tax-free income while protecting my principal. I couldn't be happier with their service!" - John D.

Are you concerned about your retirement income? We can help!
📞 {FLO_FACTION['phone']}
🌐 {FLO_FACTION['website']}
                """
            },
            "instagram": {
                "caption": f"""
"I was worried about having enough income in retirement, but Flo Faction Insurance helped me set up an IUL policy that will provide tax-free income while protecting my principal. I couldn't be happier with their service!" - John D.

We love helping our clients secure their financial future! 💙

Contact us to see how we can help you too!
📞 {FLO_FACTION['phone']}

#ClientTestimonial #RetirementPlanning #FinancialSecurity #InsuranceAgency #CustomerSatisfaction #FinancialPlanning #TaxFreeIncome
                """
            }
        },
        "insurance_tip": {
            "facebook": {
                "message": f"""
💡 INSURANCE TIP: Did you know that the cash value in an Indexed Universal Life policy can be accessed tax-free through policy loans?

This makes IUL policies a powerful tool for retirement planning, especially if you're concerned about future tax rates.

Want to learn more about tax-free retirement strategies?
📞 {FLO_FACTION['phone']}
🌐 {FLO_FACTION['website']}
                """
            },
            "instagram": {
                "caption": f"""
💡 INSURANCE TIP 💡

Did you know that the cash value in an Indexed Universal Life policy can be accessed tax-free through policy loans?

This makes IUL policies a powerful tool for retirement planning, especially if you're concerned about future tax rates.

Contact us to learn more about tax-free retirement strategies!
📞 {FLO_FACTION['phone']}

#InsuranceTip #TaxFreeRetirement #FinancialPlanning #RetirementStrategy #IUL #CashValue #FinancialFreedom #RetirementIncome
                """
            }
        }
    }

# ======================== PAUL EDWARDS SPECIFIC FUNCTIONS ========================
def create_paul_edwards_social_media_campaign():
    """
    Create a social media campaign for Paul Edwards
    
    Returns:
        Dictionary with campaign results
    """
    # Get content templates
    content = get_social_media_content()
    
    # Create personalized content for Paul
    paul_facebook_message = f"""
We're excited to welcome Paul Edwards to the Flo Faction Insurance family!

Paul recently started his journey toward tax-free retirement income with our IUL strategy.

Are you interested in learning how you can create tax-free retirement income without market risk? Contact us today!
📞 {FLO_FACTION['phone']}
🌐 {FLO_FACTION['website']}
    """
    
    paul_instagram_caption = f"""
Welcome to the Flo Faction family, Paul! 🎉

We're excited to help Paul on his journey toward tax-free retirement income with our IUL strategy.

Want to learn how you can create tax-free retirement income without market risk? Contact us today!
📞 {FLO_FACTION['phone']}

#NewClient #RetirementPlanning #TaxFreeIncome #FinancialSecurity #IUL #RetirementStrategy #FinancialFreedom
    """
    
    # Results dictionary
    results = {
        "facebook": None,
        "instagram": None
    }
    
    # Post to Facebook (commented out to prevent actual posting)
    # results["facebook"] = post_to_facebook_page(paul_facebook_message, link=f"{FLO_FACTION['website']}/iul-retirement")
    
    # Post to Instagram (commented out to prevent actual posting)
    # results["instagram"] = post_to_instagram(paul_instagram_caption, "https://example.com/welcome_image.jpg")
    
    # Simulate results for demonstration
    results["facebook"] = {"success": True, "post_id": "simulation_fb_123456"}
    results["instagram"] = {"success": True, "post_id": "simulation_ig_123456"}
    
    return results

# ======================== SETUP INSTRUCTIONS ========================
def setup_social_media_instructions():
    """Print instructions for setting up social media integration"""
    print("=" * 80)
    print("SOCIAL MEDIA INTEGRATION SETUP INSTRUCTIONS")
    print("=" * 80)
    
    print("""
To set up social media integration for Flo Faction Insurance:

1. Facebook Page Setup:
   a. Create a Facebook Business Page for Flo Faction Insurance if you don't have one
   b. Go to https://developers.facebook.com/ and create a developer account
   c. Create a new app for your business
   d. Add the "Facebook Login" and "Instagram Graph API" products to your app
   e. Generate a Page Access Token with the following permissions:
      - pages_manage_posts
      - pages_read_engagement
      - pages_show_list
      - instagram_basic
      - instagram_content_publish
   f. Note your Page ID and Access Token

2. Instagram Business Account Setup:
   a. Convert your Instagram account to a Business account if it's not already
   b. Link your Instagram Business account to your Facebook Page
   c. Note your Instagram Business Account ID

3. Update the Script Credentials:
   a. Set the following environment variables:
      export FACEBOOK_PAGE_ID="your_page_id"
      export FACEBOOK_ACCESS_TOKEN="your_access_token"
      export INSTAGRAM_ACCOUNT_ID="your_instagram_account_id"
   b. Or update the variables directly in the script

4. Create Content for Social Media:
   a. Design graphics for your posts (use Canva or similar tools)
   b. Write engaging captions and messages
   c. Plan a content calendar for regular posting

5. Integrate with Wix Website:
   a. In your Wix dashboard, go to "Settings" > "Social"
   b. Connect your Facebook and Instagram accounts
   c. Enable automatic posting of new content

6. Set Up Facebook Lead Ads:
   a. In Facebook Business Manager, go to "Ads Manager"
   b. Create a new campaign with the "Lead Generation" objective
   c. Set up your audience targeting (age, location, interests)
   d. Create a lead form using the questions in this script
   e. Set your budget and schedule

7. Connect Facebook Leads to Your CRM:
   a. Use Zapier or Integromat to connect Facebook Lead Ads to your CRM
   b. Create a zap that triggers when a new lead is submitted
   c. Map the lead fields to your CRM fields
   d. Add an action to send an automated email to the lead

8. Set Up Instagram Shopping:
   a. In Instagram, go to Settings > Business > Shopping
   b. Connect to your Facebook catalog
   c. Create "products" for your insurance offerings
   d. Tag these products in your Instagram posts

9. Implement Facebook Pixel:
   a. In Facebook Business Manager, go to "Events Manager"
   b. Set up a Facebook Pixel for your website
   c. Add the pixel code to your Wix website
   d. Set up conversion events for quote requests and contact form submissions

10. Create a Social Media Calendar:
    a. Plan your content 1-2 months in advance
    b. Mix educational content, testimonials, and promotional posts
    c. Schedule posts using Facebook Creator Studio or a tool like Buffer
    d. Monitor engagement and adjust your strategy accordingly
    """)
    
    print("=" * 80)

# ======================== MAIN FUNCTION ========================
if __name__ == "__main__":
    print("This script demonstrates social media integration for Flo Faction Insurance.")
    print("It can create posts on Facebook and Instagram for clients like Paul Edwards.")
    
    # Check if credentials are set
    if FACEBOOK_ACCESS_TOKEN == "your_access_token":
        print("Warning: Facebook credentials not set")
        print("Set the environment variables or update the script")
    
    action = input("What would you like to do? (create_campaign/show_instructions): ")
    
    if action.lower() == "create_campaign":
        if FACEBOOK_ACCESS_TOKEN == "your_access_token":
            print("Cannot create campaign: Credentials not set")
            print("This is a simulation only - no actual posts will be made")
            results = create_paul_edwards_social_media_campaign()
            print("Simulation results:")
            print(json.dumps(results, indent=2))
        else:
            print("Creating social media campaign for Paul Edwards...")
            results = create_paul_edwards_social_media_campaign()
            print("Campaign results:")
            print(json.dumps(results, indent=2))
    elif action.lower() == "show_instructions":
        setup_social_media_instructions()
    else:
        print("Invalid action. Please choose 'create_campaign' or 'show_instructions'.")
