import logging
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import yfinance as yf
from trading_agent import TradingAgent, TradingParameters

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_market_conditions():
    """Test trading agent performance in different market conditions"""
    
    test_scenarios = [
        {
            "name": "Bull Market",
            "symbol": "SPY",  # S&P 500 ETF
            "period": "2023-01-01",  # Strong bull market period
            "description": "Testing performance in upward trending market"
        },
        {
            "name": "Tech Growth",
            "symbol": "QQQ",  # NASDAQ ETF
            "period": "2023-01-01",
            "description": "Testing performance in tech-heavy growth market"
        },
        {
            "name": "Volatile Market",
            "symbol": "UVXY",  # Volatility ETF
            "period": "2024-01-01",
            "description": "Testing performance in high volatility conditions"
        }
    ]
    
    results = {}
    
    for scenario in test_scenarios:
        logger.info(f"\n=== Testing {scenario['name']} ===")
        logger.info(f"Description: {scenario['description']}")
        
        # Initialize trading parameters
        params = TradingParameters(
            symbol=scenario['symbol'],
            timeframe="1d",
            initial_capital=100000,
            risk_per_trade=0.02,
            stop_loss=0.02,
            take_profit=0.04,
            strategy="hybrid"
        )
        
        # Create trading agent
        agent = TradingAgent(params)
        
        # Get historical data
        data = yf.download(
            scenario['symbol'],
            start=scenario['period'],
            end=datetime.now().strftime('%Y-%m-%d')
        )
        
        # Train model
        logger.info(f"Training model on {len(data)} days of {scenario['symbol']} data")
        agent.train_model(data)
        
        # Simulate trading
        logger.info("Running trading simulation...")
        for i in range(len(data)-1):
            current_data = data.iloc[:i+1]
            analysis = agent.analyze_market(current_data)
            
            # Execute trades based on analysis
            agent.execute_trade(analysis['final_signal'], current_data)
            
            # Update positions
            current_price = current_data['Close'].iloc[-1]
            agent.update_positions(current_price)
        
        # Get performance metrics
        metrics = agent.get_performance_metrics()
        results[scenario['name']] = metrics
        
        logger.info(f"\nPerformance Metrics for {scenario['name']}:")
        for metric, value in metrics.items():
            if isinstance(value, float):
                logger.info(f"{metric}: {value:.2f}")
            else:
                logger.info(f"{metric}: {value}")
                
    return results

def test_risk_management():
    """Test trading agent's risk management capabilities"""
    
    logger.info("\n=== Testing Risk Management ===")
    
    # Test different risk levels
    risk_scenarios = [
        {"risk_per_trade": 0.01, "stop_loss": 0.01, "take_profit": 0.02},
        {"risk_per_trade": 0.02, "stop_loss": 0.02, "take_profit": 0.04},
        {"risk_per_trade": 0.03, "stop_loss": 0.03, "take_profit": 0.06}
    ]
    
    results = {}
    symbol = "SPY"  # Use S&P 500 for consistent testing
    
    for scenario in risk_scenarios:
        risk_level = f"Risk {scenario['risk_per_trade']*100}%"
        logger.info(f"\nTesting {risk_level}")
        
        params = TradingParameters(
            symbol=symbol,
            timeframe="1d",
            initial_capital=100000,
            risk_per_trade=scenario['risk_per_trade'],
            stop_loss=scenario['stop_loss'],
            take_profit=scenario['take_profit'],
            strategy="hybrid"
        )
        
        agent = TradingAgent(params)
        
        # Get 6 months of data
        end_date = datetime.now()
        start_date = end_date - timedelta(days=180)
        data = yf.download(symbol, start=start_date, end=end_date)
        
        # Train and run simulation
        agent.train_model(data)
        
        for i in range(len(data)-1):
            current_data = data.iloc[:i+1]
            analysis = agent.analyze_market(current_data)
            agent.execute_trade(analysis['final_signal'], current_data)
            agent.update_positions(current_data['Close'].iloc[-1])
            
        metrics = agent.get_performance_metrics()
        results[risk_level] = metrics
        
        logger.info("Performance Metrics:")
        logger.info(f"Total PnL: ${metrics['total_pnl']:.2f}")
        logger.info(f"Win Rate: {metrics['win_rate']*100:.1f}%")
        logger.info(f"Largest Loss: ${metrics['largest_loss']:.2f}")
        
    return results

def test_trading_strategies():
    """Test different trading strategies"""
    
    logger.info("\n=== Testing Trading Strategies ===")
    
    strategies = ["ml", "technical", "hybrid"]
    results = {}
    symbol = "AAPL"  # Use Apple stock for testing
    
    for strategy in strategies:
        logger.info(f"\nTesting {strategy.upper()} strategy")
        
        params = TradingParameters(
            symbol=symbol,
            timeframe="1d",
            initial_capital=100000,
            risk_per_trade=0.02,
            stop_loss=0.02,
            take_profit=0.04,
            strategy=strategy
        )
        
        agent = TradingAgent(params)
        
        # Get 1 year of data
        end_date = datetime.now()
        start_date = end_date - timedelta(days=365)
        data = yf.download(symbol, start=start_date, end=end_date)
        
        # Train and run simulation
        agent.train_model(data)
        
        for i in range(len(data)-1):
            current_data = data.iloc[:i+1]
            analysis = agent.analyze_market(current_data)
            agent.execute_trade(analysis['final_signal'], current_data)
            agent.update_positions(current_data['Close'].iloc[-1])
            
        metrics = agent.get_performance_metrics()
        results[strategy] = metrics
        
        logger.info("Performance Metrics:")
        logger.info(f"Total PnL: ${metrics['total_pnl']:.2f}")
        logger.info(f"Total Trades: {metrics['total_trades']}")
        logger.info(f"Win Rate: {metrics['win_rate']*100:.1f}%")
        
    return results

def main():
    """Run all trading agent tests"""
    try:
        # Test market conditions
        market_results = test_market_conditions()
        
        # Test risk management
        risk_results = test_risk_management()
        
        # Test trading strategies
        strategy_results = test_trading_strategies()
        
        # Save results to CSV
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # Market conditions results
        market_df = pd.DataFrame(market_results).T
        market_df.to_csv(f"test_results/market_conditions_{timestamp}.csv")
        
        # Risk management results
        risk_df = pd.DataFrame(risk_results).T
        risk_df.to_csv(f"test_results/risk_management_{timestamp}.csv")
        
        # Strategy results
        strategy_df = pd.DataFrame(strategy_results).T
        strategy_df.to_csv(f"test_results/strategies_{timestamp}.csv")
        
        logger.info("\nAll tests completed successfully!")
        logger.info(f"Results saved in test_results directory")
        
    except Exception as e:
        logger.error(f"Error during testing: {str(e)}")
        raise

if __name__ == "__main__":
    main()