"""
Enhanced IUL Calculator

This module provides more accurate IUL quote calculations using industry-standard
actuarial methods and realistic assumptions for cash value growth and income projections.
"""

from typing import Dict, List, Optional, Tuple
import math
import uuid
from datetime import datetime, date
from dateutil.relativedelta import relativedelta

class EnhancedIULCalculator:
    """Enhanced calculator for IUL quotes with more accurate projections"""
    
    def __init__(self):
        """Initialize the calculator with industry-standard parameters"""
        
        # Mortality cost factors by age and gender (simplified)
        self.mortality_factors = {
            "Male": {
                # Age: Annual cost per $1,000 of insurance
                20: 0.43, 25: 0.48, 30: 0.52, 35: 0.77, 40: 1.03, 
                45: 1.53, 50: 2.30, 55: 3.42, 60: 5.30, 65: 8.42,
                70: 13.48, 75: 21.68, 80: 34.63, 85: 55.41, 90: 88.66
            },
            "Female": {
                # Age: Annual cost per $1,000 of insurance
                20: 0.34, 25: 0.38, 30: 0.41, 35: 0.61, 40: 0.82, 
                45: 1.22, 50: 1.84, 55: 2.74, 60: 4.24, 65: 6.74,
                70: 10.78, 75: 17.34, 80: 27.70, 85: 44.33, 90: 70.93
            }
        }
        
        # Policy fee and expense factors
        self.policy_fees = {
            "annual_policy_fee": 120,  # Annual policy fee
            "premium_load_year_1_10": 0.08,  # Premium load years 1-10
            "premium_load_year_11_plus": 0.04,  # Premium load years 11+
            "admin_charge_per_1000": 0.06,  # Monthly admin charge per $1,000 of face amount
            "surrender_charge_years": 15,  # Years with surrender charges
            "surrender_charge_initial": 0.12,  # Initial surrender charge as percentage of target premium
            "surrender_charge_reduction": 0.008  # Annual reduction in surrender charge
        }
        
        # Index crediting parameters
        self.index_crediting = {
            "cap_rate": 0.095,  # 9.5% cap rate
            "floor_rate": 0.00,  # 0% floor
            "participation_rate": 1.00,  # 100% participation
            "historical_average": 0.068,  # 6.8% historical average index credit
            "conservative_average": 0.055,  # 5.5% conservative projection
            "optimistic_average": 0.075,  # 7.5% optimistic projection
            "guaranteed_min_rate": 0.01  # 1% guaranteed minimum interest
        }
        
        # Loan parameters
        self.loan_parameters = {
            "max_loan_percentage": 0.90,  # Maximum percentage of cash value available for loans
            "loan_interest_rate": 0.045,  # Loan interest rate
            "loan_crediting_rate": 0.04,  # Crediting rate on loaned funds
            "wash_loan_available_year": 11  # Year when wash loans become available
        }
        
        # Health rating factors
        self.health_rating_factors = {
            "Preferred Plus": 0.85,  # 15% discount on mortality costs
            "Preferred": 1.00,  # Standard preferred rates
            "Standard Plus": 1.15,  # 15% increase on mortality costs
            "Standard": 1.30,  # 30% increase on mortality costs
            "Table 2": 1.50,  # 50% increase on mortality costs
            "Table 4": 2.00  # 100% increase on mortality costs
        }
    
    def calculate_iul_quote(self, client_data: Dict, projection_years: int = 40) -> Dict:
        """
        Calculate detailed IUL quote with accurate projections
        
        Args:
            client_data: Dictionary with client information
            projection_years: Number of years to project (default 40)
            
        Returns:
            Dictionary with detailed quote information
        """
        # Extract client data
        age = client_data.get("age", 35)
        gender = client_data.get("gender", "Male")
        health_class = client_data.get("health_class", "Preferred")
        income = client_data.get("income", 75000)
        budget = client_data.get("budget", 200)  # Monthly premium budget
        
        # Calculate death benefit based on income multiple approach
        income_multiple = 10  # Standard multiple of income
        target_death_benefit = min(income * income_multiple, 2000000)  # Cap at $2M
        
        # Calculate annual premium
        annual_premium = budget * 12
        
        # Adjust death benefit based on premium
        death_benefit = self._calculate_death_benefit_from_premium(
            age, gender, health_class, annual_premium, target_death_benefit
        )
        
        # Calculate detailed projections
        projections = self._calculate_projections(
            age, gender, health_class, death_benefit, annual_premium, projection_years
        )
        
        # Calculate retirement income
        retirement_age = max(65, age + 20)  # Retirement at 65 or 20 years from now
        retirement_year = retirement_age - age
        retirement_income = self._calculate_retirement_income(
            projections, retirement_year, projection_years
        )
        
        # Generate quote ID
        quote_id = str(uuid.uuid4())
        
        # Build the quote summary
        quote_summary = {
            "quote_id": quote_id,
            "quote_date": datetime.now().strftime("%Y-%m-%d"),
            "client": {
                "name": f"{client_data.get('first_name', '')} {client_data.get('last_name', '')}",
                "age": age,
                "gender": gender,
                "health_class": health_class,
                "income": income,
                "budget": budget
            },
            "product_type": "IUL",
            "recommended_plan": {
                "carrier": client_data.get("recommended_carrier", "Mutual of Omaha"),
                "name": client_data.get("recommended_product", "Income Advantage IUL"),
                "death_benefit": round(death_benefit, 2),
                "monthly_premium": round(budget, 2),
                "annual_premium": round(annual_premium, 2),
                "death_benefit_option": "Level",  # Level death benefit
                "cash_value_projection": {
                    "year_10": round(projections[10]["cash_value"], 2) if 10 in projections else 0,
                    "year_20": round(projections[20]["cash_value"], 2) if 20 in projections else 0,
                    "at_retirement": round(projections[retirement_year]["cash_value"], 2) if retirement_year in projections else 0
                },
                "retirement_income": retirement_income,
                "detailed_projections": projections
            }
        }
        
        return quote_summary
    
    def _calculate_death_benefit_from_premium(
        self, age: int, gender: str, health_class: str, annual_premium: float, target_death_benefit: float
    ) -> float:
        """
        Calculate appropriate death benefit based on premium and client details
        
        Args:
            age: Client age
            gender: Client gender
            health_class: Health classification
            annual_premium: Annual premium amount
            target_death_benefit: Target death benefit based on income multiple
            
        Returns:
            Calculated death benefit
        """
        # Get mortality factor for age/gender
        mortality_factor = self._get_mortality_factor(age, gender)
        
        # Apply health rating factor
        health_factor = self.health_rating_factors.get(health_class, 1.0)
        adjusted_mortality = mortality_factor * health_factor
        
        # Calculate premium available for insurance after expenses
        first_year_expenses = annual_premium * self.policy_fees["premium_load_year_1_10"] + self.policy_fees["annual_policy_fee"]
        net_premium = annual_premium - first_year_expenses
        
        # Calculate death benefit (simplified)
        # This is a simplified calculation - actual carrier software uses more complex formulas
        insurance_per_premium_dollar = 1000 / (adjusted_mortality + self.policy_fees["admin_charge_per_1000"] * 12)
        calculated_death_benefit = net_premium * insurance_per_premium_dollar
        
        # Adjust to meet TAMRA/MEC guidelines (simplified)
        tamra_factor = 2.5 if age < 40 else 2.0 if age < 60 else 1.5
        min_death_benefit = annual_premium * tamra_factor
        
        # Return the appropriate death benefit
        if calculated_death_benefit < min_death_benefit:
            return min_death_benefit
        elif calculated_death_benefit > target_death_benefit:
            return target_death_benefit
        else:
            return calculated_death_benefit
    
    def _get_mortality_factor(self, age: int, gender: str) -> float:
        """Get mortality factor for age and gender"""
        gender_table = self.mortality_factors.get(gender, self.mortality_factors["Male"])
        
        # Find exact age or interpolate
        if age in gender_table:
            return gender_table[age]
        else:
            # Find closest ages and interpolate
            ages = sorted(gender_table.keys())
            if age < ages[0]:
                return gender_table[ages[0]]
            elif age > ages[-1]:
                return gender_table[ages[-1]]
            else:
                for i in range(len(ages) - 1):
                    if ages[i] < age < ages[i + 1]:
                        lower_age, upper_age = ages[i], ages[i + 1]
                        lower_rate, upper_rate = gender_table[lower_age], gender_table[upper_age]
                        # Linear interpolation
                        return lower_rate + (upper_rate - lower_rate) * (age - lower_age) / (upper_age - lower_age)
    
    def _calculate_projections(
        self, age: int, gender: str, health_class: str, death_benefit: float, 
        annual_premium: float, projection_years: int
    ) -> Dict[int, Dict]:
        """
        Calculate detailed year-by-year projections
        
        Args:
            age: Client age
            gender: Client gender
            health_class: Health classification
            death_benefit: Death benefit amount
            annual_premium: Annual premium
            projection_years: Number of years to project
            
        Returns:
            Dictionary with year-by-year projections
        """
        projections = {}
        
        # Initialize values
        current_age = age
        current_cash_value = 0
        current_surrender_value = 0
        current_death_benefit = death_benefit
        
        for year in range(1, projection_years + 1):
            # Calculate policy expenses
            policy_fee = self.policy_fees["annual_policy_fee"]
            premium_load = self.policy_fees["premium_load_year_1_10"] if year <= 10 else self.policy_fees["premium_load_year_11_plus"]
            premium_expense = annual_premium * premium_load
            admin_charge = (death_benefit / 1000) * self.policy_fees["admin_charge_per_1000"] * 12
            
            # Calculate mortality charge
            mortality_factor = self._get_mortality_factor(current_age, gender)
            health_factor = self.health_rating_factors.get(health_class, 1.0)
            mortality_charge = (death_benefit / 1000) * mortality_factor * health_factor
            
            # Calculate net premium after expenses
            net_premium = annual_premium - premium_expense - policy_fee - admin_charge - mortality_charge
            
            # Calculate interest credited
            interest_rate = self.index_crediting["historical_average"]
            interest_credited = (current_cash_value + net_premium) * interest_rate
            
            # Update cash value
            new_cash_value = current_cash_value + net_premium + interest_credited
            
            # Calculate surrender charge
            if year <= self.policy_fees["surrender_charge_years"]:
                surrender_charge_percent = max(
                    0, 
                    self.policy_fees["surrender_charge_initial"] - 
                    (year - 1) * self.policy_fees["surrender_charge_reduction"]
                )
                surrender_charge = annual_premium * surrender_charge_percent * (self.policy_fees["surrender_charge_years"] - year + 1) / self.policy_fees["surrender_charge_years"]
            else:
                surrender_charge = 0
            
            # Calculate surrender value
            new_surrender_value = max(0, new_cash_value - surrender_charge)
            
            # Store projection for this year
            projections[year] = {
                "age": current_age,
                "annual_premium": annual_premium,
                "death_benefit": current_death_benefit,
                "cash_value": new_cash_value,
                "surrender_value": new_surrender_value,
                "policy_expenses": {
                    "policy_fee": policy_fee,
                    "premium_load": premium_expense,
                    "admin_charge": admin_charge,
                    "mortality_charge": mortality_charge,
                    "total_expenses": policy_fee + premium_expense + admin_charge + mortality_charge
                },
                "interest_credited": interest_credited,
                "net_cash_value_growth": net_premium + interest_credited,
                "surrender_charge": surrender_charge
            }
            
            # Update values for next year
            current_age += 1
            current_cash_value = new_cash_value
            current_surrender_value = new_surrender_value
        
        return projections
    
    def _calculate_retirement_income(
        self, projections: Dict[int, Dict], retirement_year: int, projection_years: int
    ) -> Dict:
        """
        Calculate retirement income details
        
        Args:
            projections: Year-by-year projections
            retirement_year: Year to start retirement income
            projection_years: Total projection years
            
        Returns:
            Dictionary with retirement income details
        """
        if retirement_year not in projections:
            return {
                "annual_amount": 0,
                "monthly_amount": 0,
                "years": 0,
                "total_amount": 0,
                "start_age": 0,
                "end_age": 0
            }
        
        # Get cash value at retirement
        retirement_cash_value = projections[retirement_year]["cash_value"]
        
        # Calculate available loan amount (90% of cash value)
        available_loan = retirement_cash_value * self.loan_parameters["max_loan_percentage"]
        
        # Calculate sustainable annual withdrawal (using 5% rule of thumb)
        # In a real implementation, this would use more sophisticated calculations
        sustainable_rate = 0.05  # 5% withdrawal rate
        annual_income = available_loan * sustainable_rate
        
        # Calculate income period
        income_years = min(20, projection_years - retirement_year)
        
        # Calculate total income
        total_income = annual_income * income_years
        
        return {
            "annual_amount": round(annual_income, 2),
            "monthly_amount": round(annual_income / 12, 2),
            "years": income_years,
            "total_amount": round(total_income, 2),
            "start_age": projections[retirement_year]["age"],
            "end_age": projections[retirement_year]["age"] + income_years - 1
        }

# Example usage
if __name__ == "__main__":
    calculator = EnhancedIULCalculator()
    
    # Test client
    test_client = {
        "first_name": "Paul",
        "last_name": "Edwards",
        "age": 32,
        "gender": "Male",
        "health_class": "Preferred",
        "income": 85000,
        "budget": 200,
        "recommended_carrier": "American General",
        "recommended_product": "Max Accumulator+ IUL"
    }
    
    # Calculate quote
    quote = calculator.calculate_iul_quote(test_client)
    
    # Print summary
    print(f"IUL Quote for {test_client['first_name']} {test_client['last_name']}")
    print(f"Age: {test_client['age']}, Gender: {test_client['gender']}, Health: {test_client['health_class']}")
    print(f"Carrier: {quote['recommended_plan']['carrier']}")
    print(f"Product: {quote['recommended_plan']['name']}")
    print(f"Death Benefit: ${quote['recommended_plan']['death_benefit']:,.2f}")
    print(f"Monthly Premium: ${quote['recommended_plan']['monthly_premium']:.2f}")
    print(f"Annual Premium: ${quote['recommended_plan']['annual_premium']:,.2f}")
    
    print("\nCash Value Projections:")
    print(f"Year 10: ${quote['recommended_plan']['cash_value_projection']['year_10']:,.2f}")
    print(f"Year 20: ${quote['recommended_plan']['cash_value_projection']['year_20']:,.2f}")
    
    print("\nRetirement Income:")
    retirement = quote['recommended_plan']['retirement_income']
    print(f"Annual Income: ${retirement['annual_amount']:,.2f}")
    print(f"Monthly Income: ${retirement['monthly_amount']:,.2f}")
    print(f"Income Period: {retirement['years']} years (ages {retirement['start_age']}-{retirement['end_age']})")
    print(f"Total Income: ${retirement['total_amount']:,.2f}")
