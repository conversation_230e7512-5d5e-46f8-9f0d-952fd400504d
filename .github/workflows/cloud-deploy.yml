name: Cloud Build, Test, and Deploy (Universal)

on:
  push:
    branches: [ main, master ]
  pull_request:
    branches: [ main, master ]
  workflow_dispatch:

jobs:
  build-test-deploy:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up Python
        uses: actions/setup-python@v5
        with:
          python-version: '3.11'

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Set up QEMU (for multi-arch)
        uses: docker/setup-qemu-action@v3

      - name: Log in to GitHub Container Registry
        uses: docker/login-action@v3
        with:
          registry: ghcr.io
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}

      - name: Build Docker image (with GPU/NVIDIA support)
        uses: docker/build-push-action@v5
        with:
          context: .
          file: ./Dockerfile
          push: true
          tags: ghcr.io/${{ github.repository }}:latest
          platforms: linux/amd64,linux/arm64

      - name: Run tests in container
        run: |
          docker run --rm ghcr.io/${{ github.repository }}:latest pytest || true

      - name: Deploy to cloud (manual step)
        if: github.ref == 'refs/heads/main'
        run: |
          echo "Ready for deployment. Use this image in any cloud, server, or GPU environment."
          echo "You can pull with: docker pull ghcr.io/${{ github.repository }}:latest"
          echo "For NVIDIA NIM or similar, follow their deployment docs."
