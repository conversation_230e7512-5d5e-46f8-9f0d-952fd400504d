name: Deploy FastAPI Application

on:
  push:
    branches:
      - main

jobs:
  deploy:
    runs-on: ubuntu-latest

    steps:
    - name: Checkout code
      uses: actions/checkout@v3

    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: 3.11

    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements.txt

    - name: Authenticate with Google Cloud
      uses: google-github-actions/auth@v1
      with:
        credentials_json: ${{ secrets.GCP_CREDENTIALS }}

    - name: Configure gcloud
      run: |
        gcloud config set project ${{ secrets.GCP_PROJECT_ID }}
        gcloud config set compute/region ${{ secrets.GCP_REGION }}

    - name: Build Docker image
      run: |
        gcloud builds submit --tag gcr.io/${{ secrets.GCP_PROJECT_ID }}/fastapi-app

    - name: Deploy to Cloud Run
      run: |
        gcloud run deploy fastapi-app \
          --image gcr.io/${{ secrets.GCP_PROJECT_ID }}/fastapi-app \
          --platform managed \
          --region ${{ secrets.GCP_REGION }} \
          --allow-unauthenticated