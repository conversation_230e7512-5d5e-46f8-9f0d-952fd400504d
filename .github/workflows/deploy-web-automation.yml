name: Deploy Web Automation Dashboard

on:
  push:
    branches: [ main, develop ]
    paths:
      - 'web_ui_component.py'
      - 'webrover_component.py'
      - 'midscene_integration.py'
      - 'unified_web_automation_dashboard.py'
      - 'deploy_to_gcloud.py'
      - 'requirements.txt'
      - '.github/workflows/deploy-web-automation.yml'
  pull_request:
    branches: [ main ]
  workflow_dispatch:
    inputs:
      deployment_type:
        description: 'Deployment type'
        required: true
        default: 'cloud_run'
        type: choice
        options:
        - cloud_run
        - app_engine
      environment:
        description: 'Environment to deploy to'
        required: true
        default: 'staging'
        type: choice
        options:
        - staging
        - production

env:
  PROJECT_ID: ${{ secrets.GCP_PROJECT_ID }}
  REGION: us-central1
  SERVICE_NAME: web-automation-dashboard

jobs:
  test:
    name: Run Tests
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.9'
        
    - name: Set up Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '18'
        
    - name: Cache Python dependencies
      uses: actions/cache@v3
      with:
        path: ~/.cache/pip
        key: ${{ runner.os }}-pip-${{ hashFiles('**/requirements.txt') }}
        restore-keys: |
          ${{ runner.os }}-pip-
          
    - name: Install Python dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements.txt
        pip install pytest pytest-asyncio pytest-cov
        
    - name: Install Node.js dependencies
      run: |
        npm install
        
    - name: Install system dependencies
      run: |
        sudo apt-get update
        sudo apt-get install -y wget gnupg
        
    - name: Install Chrome for testing
      run: |
        wget -q -O - https://dl-ssl.google.com/linux/linux_signing_key.pub | sudo apt-key add -
        echo "deb [arch=amd64] http://dl.google.com/linux/chrome/deb/ stable main" | sudo tee /etc/apt/sources.list.d/google.list
        sudo apt-get update
        sudo apt-get install -y google-chrome-stable
        
    - name: Install Playwright
      run: |
        pip install playwright
        playwright install chromium
        
    - name: Run Python tests
      run: |
        python -m pytest tests/ -v --cov=. --cov-report=xml
        
    - name: Upload coverage to Codecov
      uses: codecov/codecov-action@v3
      with:
        file: ./coverage.xml
        flags: unittests
        name: codecov-umbrella
        
  lint:
    name: Code Quality
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.9'
        
    - name: Install linting tools
      run: |
        python -m pip install --upgrade pip
        pip install flake8 black isort mypy
        
    - name: Run Black
      run: black --check --diff .
      
    - name: Run isort
      run: isort --check-only --diff .
      
    - name: Run flake8
      run: flake8 . --count --select=E9,F63,F7,F82 --show-source --statistics
      
    - name: Run mypy
      run: mypy . --ignore-missing-imports
      
  security:
    name: Security Scan
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Run Bandit security scan
      uses: securecodewarrior/github-action-bandit@v1
      with:
        path: "."
        
    - name: Run Safety check
      run: |
        pip install safety
        safety check --json
        
  build:
    name: Build Docker Image
    runs-on: ubuntu-latest
    needs: [test, lint, security]
    if: github.event_name == 'push' || github.event_name == 'workflow_dispatch'
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v3
      
    - name: Authenticate to Google Cloud
      uses: google-github-actions/auth@v1
      with:
        credentials_json: ${{ secrets.GCP_SA_KEY }}
        
    - name: Set up Cloud SDK
      uses: google-github-actions/setup-gcloud@v1
      
    - name: Configure Docker for GCR
      run: gcloud auth configure-docker
      
    - name: Create deployment files
      run: |
        python deploy_to_gcloud.py --project-id ${{ env.PROJECT_ID }} --region ${{ env.REGION }} --type cloud_run --build-only
        
    - name: Build and push Docker image
      run: |
        docker build -t gcr.io/${{ env.PROJECT_ID }}/${{ env.SERVICE_NAME }}:${{ github.sha }} .
        docker push gcr.io/${{ env.PROJECT_ID }}/${{ env.SERVICE_NAME }}:${{ github.sha }}
        
        # Also tag as latest for main branch
        if [ "${{ github.ref }}" = "refs/heads/main" ]; then
          docker tag gcr.io/${{ env.PROJECT_ID }}/${{ env.SERVICE_NAME }}:${{ github.sha }} gcr.io/${{ env.PROJECT_ID }}/${{ env.SERVICE_NAME }}:latest
          docker push gcr.io/${{ env.PROJECT_ID }}/${{ env.SERVICE_NAME }}:latest
        fi
        
  deploy-staging:
    name: Deploy to Staging
    runs-on: ubuntu-latest
    needs: [build]
    if: github.ref == 'refs/heads/develop' || (github.event_name == 'workflow_dispatch' && github.event.inputs.environment == 'staging')
    environment: staging
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Authenticate to Google Cloud
      uses: google-github-actions/auth@v1
      with:
        credentials_json: ${{ secrets.GCP_SA_KEY }}
        
    - name: Set up Cloud SDK
      uses: google-github-actions/setup-gcloud@v1
      
    - name: Deploy to Cloud Run (Staging)
      run: |
        gcloud run deploy ${{ env.SERVICE_NAME }}-staging \
          --image gcr.io/${{ env.PROJECT_ID }}/${{ env.SERVICE_NAME }}:${{ github.sha }} \
          --platform managed \
          --region ${{ env.REGION }} \
          --allow-unauthenticated \
          --memory 2Gi \
          --cpu 2 \
          --max-instances 5 \
          --min-instances 1 \
          --port 8000 \
          --timeout 300 \
          --concurrency 80 \
          --set-env-vars ENVIRONMENT=staging,PORT=8000
          
    - name: Get staging URL
      id: staging-url
      run: |
        URL=$(gcloud run services describe ${{ env.SERVICE_NAME }}-staging --platform managed --region ${{ env.REGION }} --format 'value(status.url)')
        echo "url=$URL" >> $GITHUB_OUTPUT
        
    - name: Run smoke tests
      run: |
        sleep 30  # Wait for service to be ready
        curl -f ${{ steps.staging-url.outputs.url }}/api/status || exit 1
        
    - name: Comment PR with staging URL
      if: github.event_name == 'pull_request'
      uses: actions/github-script@v6
      with:
        script: |
          github.rest.issues.createComment({
            issue_number: context.issue.number,
            owner: context.repo.owner,
            repo: context.repo.repo,
            body: '🚀 Staging deployment successful!\n\n📊 Dashboard: ${{ steps.staging-url.outputs.url }}\n🔍 Health Check: ${{ steps.staging-url.outputs.url }}/api/status'
          })
          
  deploy-production:
    name: Deploy to Production
    runs-on: ubuntu-latest
    needs: [build]
    if: github.ref == 'refs/heads/main' || (github.event_name == 'workflow_dispatch' && github.event.inputs.environment == 'production')
    environment: production
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Authenticate to Google Cloud
      uses: google-github-actions/auth@v1
      with:
        credentials_json: ${{ secrets.GCP_SA_KEY }}
        
    - name: Set up Cloud SDK
      uses: google-github-actions/setup-gcloud@v1
      
    - name: Deploy to Cloud Run (Production)
      run: |
        DEPLOYMENT_TYPE="${{ github.event.inputs.deployment_type || 'cloud_run' }}"
        
        if [ "$DEPLOYMENT_TYPE" = "cloud_run" ]; then
          gcloud run deploy ${{ env.SERVICE_NAME }} \
            --image gcr.io/${{ env.PROJECT_ID }}/${{ env.SERVICE_NAME }}:${{ github.sha }} \
            --platform managed \
            --region ${{ env.REGION }} \
            --allow-unauthenticated \
            --memory 2Gi \
            --cpu 2 \
            --max-instances 10 \
            --min-instances 1 \
            --port 8000 \
            --timeout 300 \
            --concurrency 80 \
            --set-env-vars ENVIRONMENT=production,PORT=8000
        elif [ "$DEPLOYMENT_TYPE" = "app_engine" ]; then
          python deploy_to_gcloud.py --project-id ${{ env.PROJECT_ID }} --region ${{ env.REGION }} --type app_engine
        fi
        
    - name: Get production URL
      id: production-url
      run: |
        DEPLOYMENT_TYPE="${{ github.event.inputs.deployment_type || 'cloud_run' }}"
        
        if [ "$DEPLOYMENT_TYPE" = "cloud_run" ]; then
          URL=$(gcloud run services describe ${{ env.SERVICE_NAME }} --platform managed --region ${{ env.REGION }} --format 'value(status.url)')
        else
          URL="https://${{ env.PROJECT_ID }}.appspot.com"
        fi
        
        echo "url=$URL" >> $GITHUB_OUTPUT
        
    - name: Run production smoke tests
      run: |
        sleep 60  # Wait for service to be ready
        curl -f ${{ steps.production-url.outputs.url }}/api/status || exit 1
        
    - name: Create GitHub release
      if: github.ref == 'refs/heads/main'
      uses: actions/create-release@v1
      env:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
      with:
        tag_name: v${{ github.run_number }}
        release_name: Web Automation Dashboard v${{ github.run_number }}
        body: |
          🚀 **Web Automation Dashboard Deployment**
          
          **Production URL:** ${{ steps.production-url.outputs.url }}
          
          **Components Deployed:**
          - ✅ Unified Web Automation Dashboard
          - ✅ WebRover (Autonomous Navigation)
          - ✅ MidScene.js Integration (AI Automation)
          - ✅ Web-UI Component (React Interface)
          - ✅ Web Vision Agent (Visual AI)
          
          **Deployment Details:**
          - **Commit:** ${{ github.sha }}
          - **Environment:** Production
          - **Type:** ${{ github.event.inputs.deployment_type || 'cloud_run' }}
          - **Region:** ${{ env.REGION }}
          
          **Health Check:** ${{ steps.production-url.outputs.url }}/api/status
        draft: false
        prerelease: false
        
  notify:
    name: Notify Deployment
    runs-on: ubuntu-latest
    needs: [deploy-production]
    if: always()
    
    steps:
    - name: Notify success
      if: needs.deploy-production.result == 'success'
      run: |
        echo "🎉 Production deployment successful!"
        echo "📊 Dashboard URL: ${{ needs.deploy-production.outputs.url }}"
        
    - name: Notify failure
      if: needs.deploy-production.result == 'failure'
      run: |
        echo "💥 Production deployment failed!"
        exit 1
