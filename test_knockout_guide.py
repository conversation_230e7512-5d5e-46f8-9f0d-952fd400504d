#!/usr/bin/env python3
"""
Test script for carrier knockout guide system with <PERSON>' profile
"""

from carrier_knockout_guides import find_best_product_type_for_client, get_best_product_matches

def test_knockout_guide_for_paul():
    """Test the knockout guide system with <PERSON>' profile"""
    
    # <PERSON> data
    paul_data = {
        'age': 32,
        'gender': 'Male',
        'tobacco_use': False,
        'bmi': 24.5,
        'health_conditions': [],
        'medications': [],
        'income': 85000,
        'has_mortgage': True,
        'mortgage_amount': 250000,
        'retirement_focus': True,
        'risk_tolerance': 'moderate',
        'health_status': 'good',
        'budget': 200
    }
    
    # Find best product type
    recommended_type = find_best_product_type_for_client(paul_data)
    print(f"Recommended product type for <PERSON>: {recommended_type}\n")
    
    # Find specific product matches
    matches = get_best_product_matches(paul_data, recommended_type)
    print(f"Found {len(matches)} potential matches. Top 3 recommendations:")
    
    for i, match in enumerate(matches[:3], 1):
        print(f"\n{i}. {match['carrier'].title()} - {match['product'].replace('_', ' ').title()}")
        print(f"   Match score: {match['match_score']:.2f}")
        print(f"   Age range: {match['age_range'][0]}-{match['age_range'][1]}")
        print(f"   Best for: {match['preferred_for']}")
        print(f"   Notes: {match['notes']}")

if __name__ == "__main__":
    print("=== Carrier Knockout Guide Test ===")
    test_knockout_guide_for_paul()
    print("=== Test Complete ===")
