"""
Simplified trading agent using scikit-learn instead of TensorFlow
"""

import logging
import pandas as pd
import numpy as np
from typing import Dict, List, Optional
from dataclasses import dataclass
from datetime import datetime
import yfinance as yf
from sklearn.preprocessing import StandardScaler
from sklearn.ensemble import RandomForestRegressor
from sklearn.model_selection import train_test_split

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@dataclass
class TradingParameters:
    symbol: str
    timeframe: str
    initial_capital: float
    risk_per_trade: float
    stop_loss: float
    take_profit: float
    strategy: str

class SimpleTradingAgent:
    """Trading agent using random forest for predictions"""
    
    def __init__(self, params: TradingParameters):
        self.params = params
        self.capital = params.initial_capital
        self.positions = {}
        self.trade_history = []
        self.model = RandomForestRegressor(n_estimators=100, random_state=42)
        self.scaler = StandardScaler()
        
    def prepare_features(self, data: pd.DataFrame) -> pd.DataFrame:
        """Prepare features for ML model"""
        df = data.copy()
        
        # Technical indicators
        df['MA20'] = df['Close'].rolling(window=20).mean()
        df['MA50'] = df['Close'].rolling(window=50).mean()
        df['MA_ratio'] = df['MA20'] / df['MA50']
        
        # Price changes
        df['Returns'] = df['Close'].pct_change()
        df['Volatility'] = df['Returns'].rolling(window=20).std()
        
        # Volume indicators
        df['Volume_MA'] = df['Volume'].rolling(window=20).mean()
        df['Volume_ratio'] = df['Volume'] / df['Volume_MA']
        
        # Momentum
        df['RSI'] = self.calculate_rsi(df['Close'])
        df['MACD'] = self.calculate_macd(df['Close'])
        
        # Target variable (next day's return)
        df['Target'] = df['Returns'].shift(-1)
        
        # Drop NaN values
        df.dropna(inplace=True)
        
        return df
        
    def train_model(self, data: pd.DataFrame):
        """Train the ML model"""
        logger.info("Preparing data for training...")
        
        # Prepare features
        df = self.prepare_features(data)
        
        # Select features
        features = ['MA_ratio', 'Returns', 'Volatility', 
                   'Volume_ratio', 'RSI', 'MACD']
        
        X = df[features]
        y = df['Target']
        
        # Scale features
        X_scaled = self.scaler.fit_transform(X)
        
        # Split data
        X_train, X_test, y_train, y_test = train_test_split(
            X_scaled, y, test_size=0.2, shuffle=False
        )
        
        # Train model
        logger.info("Training model...")
        self.model.fit(X_train, y_train)
        
        # Calculate training metrics
        train_score = self.model.score(X_train, y_train)
        test_score = self.model.score(X_test, y_test)
        
        logger.info(f"Training R2: {train_score:.3f}")
        logger.info(f"Testing R2: {test_score:.3f}")
        
    def calculate_rsi(self, prices: pd.Series, period: int = 14) -> pd.Series:
        """Calculate Relative Strength Index"""
        delta = prices.diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
        rs = gain / loss
        return 100 - (100 / (1 + rs))
        
    def calculate_macd(self, prices: pd.Series) -> pd.Series:
        """Calculate MACD indicator"""
        exp1 = prices.ewm(span=12, adjust=False).mean()
        exp2 = prices.ewm(span=26, adjust=False).mean()
        return exp1 - exp2
        
    def analyze_market(self, data: pd.DataFrame) -> Dict:
        """Analyze market conditions and generate trading signal"""
        df = self.prepare_features(data)
        
        if len(df) < 2:
            return {"signal": 0}
            
        # Get latest features
        features = ['MA_ratio', 'Returns', 'Volatility', 
                   'Volume_ratio', 'RSI', 'MACD']
        current = df[features].iloc[-1:]
        
        # Scale features
        current_scaled = self.scaler.transform(current)
        
        # Get prediction
        pred = self.model.predict(current_scaled)[0]
        
        # Combine with technical signals
        tech_signal = self._get_technical_signal(df)
        
        if self.params.strategy == "ml":
            final_signal = np.sign(pred)
        elif self.params.strategy == "technical":
            final_signal = tech_signal
        else:  # hybrid
            final_signal = (np.sign(pred) + tech_signal) / 2
            
        return {
            "signal": final_signal,
            "prediction": pred,
            "technical_signal": tech_signal,
            "rsi": df['RSI'].iloc[-1],
            "macd": df['MACD'].iloc[-1]
        }
        
    def _get_technical_signal(self, data: pd.DataFrame) -> float:
        """Generate signal from technical indicators"""
        signal = 0
        
        # Moving average crossover
        if data['MA20'].iloc[-1] > data['MA50'].iloc[-1]:
            signal += 0.3
        else:
            signal -= 0.3
            
        # RSI
        rsi = data['RSI'].iloc[-1]
        if rsi > 70:
            signal -= 0.4
        elif rsi < 30:
            signal += 0.4
            
        # MACD
        if data['MACD'].iloc[-1] > 0:
            signal += 0.3
        else:
            signal -= 0.3
            
        return max(min(signal, 1.0), -1.0)
        
    def execute_trade(self, signal: float, data: pd.DataFrame):
        """Execute trade based on signal"""
        if abs(signal) < 0.5:  # Minimum signal threshold
            return
            
        price = data['Close'].iloc[-1]
        position_size = self.capital * self.params.risk_per_trade
        
        if signal > 0 and not self.positions:  # Buy signal
            shares = position_size / price
            stop_loss = price * (1 - self.params.stop_loss)
            take_profit = price * (1 + self.params.take_profit)
            
            self.positions[self.params.symbol] = {
                "type": "long",
                "shares": shares,
                "entry": price,
                "stop_loss": stop_loss,
                "take_profit": take_profit
            }
            
            logger.info(f"Opening LONG position: {shares:.2f} shares at {price:.2f}")
            
        elif signal < 0 and not self.positions:  # Sell signal
            shares = position_size / price
            stop_loss = price * (1 + self.params.stop_loss)
            take_profit = price * (1 - self.params.take_profit)
            
            self.positions[self.params.symbol] = {
                "type": "short",
                "shares": shares,
                "entry": price,
                "stop_loss": stop_loss,
                "take_profit": take_profit
            }
            
            logger.info(f"Opening SHORT position: {shares:.2f} shares at {price:.2f}")
            
    def update_positions(self, current_price: float):
        """Update and manage open positions"""
        for symbol, position in list(self.positions.items()):
            pnl = 0
            
            if position["type"] == "long":
                pnl = (current_price - position["entry"]) * position["shares"]
                if (current_price <= position["stop_loss"] or 
                    current_price >= position["take_profit"]):
                    self.close_position(symbol, current_price)
                    
            else:  # short position
                pnl = (position["entry"] - current_price) * position["shares"]
                if (current_price >= position["stop_loss"] or 
                    current_price <= position["take_profit"]):
                    self.close_position(symbol, current_price)
                    
    def close_position(self, symbol: str, price: float):
        """Close an open position and record the trade"""
        position = self.positions[symbol]
        pnl = 0
        
        if position["type"] == "long":
            pnl = (price - position["entry"]) * position["shares"]
        else:
            pnl = (position["entry"] - price) * position["shares"]
            
        self.capital += pnl
        
        self.trade_history.append({
            "symbol": symbol,
            "type": position["type"],
            "entry": position["entry"],
            "exit": price,
            "shares": position["shares"],
            "pnl": pnl,
            "timestamp": datetime.now()
        })
        
        logger.info(f"Closing {position['type']} position: PnL = ${pnl:.2f}")
        del self.positions[symbol]
        
    def get_performance_metrics(self) -> Dict:
        """Calculate trading performance metrics"""
        if not self.trade_history:
            return {}
            
        trades = pd.DataFrame(self.trade_history)
        
        return {
            "total_trades": len(trades),
            "winning_trades": len(trades[trades['pnl'] > 0]),
            "total_pnl": trades['pnl'].sum(),
            "win_rate": len(trades[trades['pnl'] > 0]) / len(trades),
            "avg_win": trades[trades['pnl'] > 0]['pnl'].mean(),
            "avg_loss": trades[trades['pnl'] < 0]['pnl'].mean(),
            "largest_win": trades['pnl'].max(),
            "largest_loss": trades['pnl'].min(),
            "final_capital": self.capital
        }

def main():
    """Test trading agent functionality"""
    params = TradingParameters(
        symbol="AAPL",
        timeframe="1d",
        initial_capital=100000,
        risk_per_trade=0.02,
        stop_loss=0.02,
        take_profit=0.04,
        strategy="hybrid"
    )
    
    agent = SimpleTradingAgent(params)
    
    # Get historical data
    data = yf.download(params.symbol, start="2023-01-01", end="2024-04-27")
    
    # Train model
    agent.train_model(data)
    
    # Simulate trading
    for i in range(len(data)-1):
        current_data = data.iloc[:i+1]
        analysis = agent.analyze_market(current_data)
        
        # Execute trades
        agent.execute_trade(analysis['signal'], current_data)
        
        # Update positions
        current_price = current_data['Close'].iloc[-1]
        agent.update_positions(current_price)
    
    # Get performance metrics
    metrics = agent.get_performance_metrics()
    logger.info("\nTrading Performance:")
    for metric, value in metrics.items():
        logger.info(f"{metric}: {value}")

if __name__ == "__main__":
    main()