#!/usr/bin/env python3
"""
Insurance Agent Access System

This is the main entry point for the Insurance Agent Access System, which provides:
1. Secure credential management for insurance portals and email accounts
2. Automated login to insurance carrier portals and email accounts
3. Integration with UI Tars 1.5 for enhanced UI automation
4. Portal quoting and form filling capabilities
5. Email management and communication

IMPORTANT: This system uses proper authentication methods and does not employ
password cracking or any activities that might violate terms of service.
"""

import os
import sys
import argparse
import logging
from datetime import datetime

# Import our modules
from credential_manager import SecureCredentialManager, InsurancePortalManager, EmailAccountManager, setup_credential_system
from portal_automation import PortalAutomator, EmailAutomator, BrowserSession
from tars_integration import TarsIntegration, setup_tars_integration

# Configure logging
log_dir = "logs"
os.makedirs(log_dir, exist_ok=True)
log_file = os.path.join(log_dir, f"agent_system_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log")

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(log_file),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)


class AgentAccessSystem:
    """Main class that integrates all system components"""
    
    def __init__(self):
        """Initialize the agent access system"""
        # Set up credential system
        logger.info("Initializing credential system...")
        self.managers = setup_credential_system()
        self.credential_manager = self.managers["credential_manager"]
        self.portal_manager = self.managers["portal_manager"]
        self.email_manager = self.managers["email_manager"]
        
        # Initialize automation components
        logger.info("Initializing automation components...")
        self.portal_automator = PortalAutomator(self.credential_manager, self.portal_manager)
        self.email_automator = EmailAutomator(self.credential_manager, self.email_manager)
        
        # Initialize UI Tars integration
        logger.info("Initializing UI Tars integration...")
        self.tars = setup_tars_integration()
        
    def show_available_portals(self):
        """Show available insurance portals"""
        portals = self.portal_manager.portal_urls.keys()
        print("\nAvailable Insurance Portals:")
        print("===========================")
        for i, portal in enumerate(portals, 1):
            print(f"{i}. {portal.replace('_', ' ').title()}")
            
    def show_available_emails(self):
        """Show available email accounts"""
        emails = self.email_manager.email_accounts
        print("\nAvailable Email Accounts:")
        print("=======================")
        for i, email in enumerate(emails, 1):
            print(f"{i}. {email}")
            
    def access_portal(self, portal_name, headless=False):
        """Access an insurance portal"""
        logger.info(f"Accessing portal: {portal_name}")
        print(f"\nAccessing {portal_name.replace('_', ' ').title()} portal...")
        
        self.portal_automator.start_browser(headless=headless)
        success = self.portal_automator.login_to_portal(portal_name)
        
        if success:
            print(f"Successfully logged into {portal_name.replace('_', ' ').title()} portal")
            print("Browser session is active. Type 'exit' to close the browser when done.")
            
            # Simple command loop for the browser session
            while True:
                cmd = input("> ").strip().lower()
                if cmd == 'exit':
                    break
                elif cmd == 'quote':
                    zip_code = input("Enter ZIP code for quote: ").strip()
                    self.portal_automator.start_quote(portal_name, zip_code)
                elif cmd == 'screenshot':
                    filename = f"{portal_name}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.png"
                    self.portal_automator.browser.take_screenshot(filename)
                    print(f"Screenshot saved to {filename}")
                elif cmd == 'help':
                    print("Available commands:")
                    print("  exit - Close browser and return to main menu")
                    print("  quote - Start a new quote")
                    print("  screenshot - Take a screenshot")
                    print("  help - Show this help message")
                else:
                    print("Unknown command. Type 'help' for available commands.")
                    
            # Close browser when done
            self.portal_automator.stop_browser()
        else:
            print(f"Failed to log into {portal_name.replace('_', ' ').title()} portal")
            self.portal_automator.stop_browser()
            
    def access_email(self, email_address, headless=False):
        """Access an email account"""
        logger.info(f"Accessing email: {email_address}")
        print(f"\nAccessing {email_address} email account...")
        
        self.email_automator.start_browser(headless=headless)
        success = self.email_automator.login_to_email(email_address)
        
        if success:
            print(f"Successfully logged into {email_address}")
            print("Browser session is active. Type 'exit' to close the browser when done.")
            
            # Simple command loop for the browser session
            while True:
                cmd = input("> ").strip().lower()
                if cmd == 'exit':
                    break
                elif cmd == 'compose':
                    to = input("To: ").strip()
                    subject = input("Subject: ").strip()
                    print("Body (type END on a new line to finish):")
                    body_lines = []
                    while True:
                        line = input()
                        if line.strip() == "END":
                            break
                        body_lines.append(line)
                    body = "\n".join(body_lines)
                    
                    self.email_automator.send_email(email_address, to, subject, body)
                elif cmd == 'screenshot':
                    filename = f"{email_address.split('@')[0]}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.png"
                    self.email_automator.browser.take_screenshot(filename)
                    print(f"Screenshot saved to {filename}")
                elif cmd == 'help':
                    print("Available commands:")
                    print("  exit - Close browser and return to main menu")
                    print("  compose - Compose and send a new email")
                    print("  screenshot - Take a screenshot")
                    print("  help - Show this help message")
                else:
                    print("Unknown command. Type 'help' for available commands.")
                    
            # Close browser when done
            self.email_automator.stop_browser()
        else:
            print(f"Failed to log into {email_address}")
            self.email_automator.stop_browser()
            
    def configure_tars(self):
        """Configure UI Tars integration"""
        logger.info("Configuring UI Tars integration")
        print("\nConfiguring UI Tars 1.5 integration...")
        
        if not self.tars:
            print("UI Tars integration is not available. Please make sure UI Tars 1.5 is installed.")
            return
            
        if self.tars.update_tars_configuration():
            print("UI Tars configuration updated successfully.")
            
            launch = input("Would you like to launch UI Tars now? (y/n): ").strip().lower()
            if launch == 'y':
                if self.tars.launch_tars():
                    print("UI Tars launched successfully.")
                else:
                    print("Failed to launch UI Tars.")
        else:
            print("Failed to update UI Tars configuration.")
            
    def run_interactive(self):
        """Run the system in interactive mode"""
        print("\nInsurance Agent Access System")
        print("============================")
        print("This system provides secure access to insurance portals and email accounts.")
        
        while True:
            print("\nMain Menu:")
            print("1. Access Insurance Portal")
            print("2. Access Email Account")
            print("3. Configure UI Tars Integration")
            print("4. View Available Portals")
            print("5. View Available Email Accounts")
            print("6. Exit")
            
            choice = input("\nEnter your choice (1-6): ").strip()
            
            if choice == '1':
                self.show_available_portals()
                portal = input("\nEnter portal name (or number): ").strip()
                
                # Convert number to portal name if needed
                if portal.isdigit():
                    portals = list(self.portal_manager.portal_urls.keys())
                    idx = int(portal) - 1
                    if 0 <= idx < len(portals):
                        portal = portals[idx]
                    else:
                        print("Invalid portal number")
                        continue
                        
                # Check if portal exists
                if portal not in self.portal_manager.portal_urls:
                    print(f"Portal '{portal}' not found")
                    continue
                    
                headless = input("Run in headless mode? (y/n): ").strip().lower() == 'y'
                self.access_portal(portal, headless)
                
            elif choice == '2':
                self.show_available_emails()
                email = input("\nEnter email address (or number): ").strip()
                
                # Convert number to email if needed
                if email.isdigit():
                    emails = self.email_manager.email_accounts
                    idx = int(email) - 1
                    if 0 <= idx < len(emails):
                        email = list(emails)[idx]
                    else:
                        print("Invalid email number")
                        continue
                        
                # Check if email exists
                if email not in self.email_manager.email_accounts:
                    print(f"Email '{email}' not found")
                    continue
                    
                headless = input("Run in headless mode? (y/n): ").strip().lower() == 'y'
                self.access_email(email, headless)
                
            elif choice == '3':
                self.configure_tars()
                
            elif choice == '4':
                self.show_available_portals()
                
            elif choice == '5':
                self.show_available_emails()
                
            elif choice == '6':
                print("Exiting system...")
                break
                
            else:
                print("Invalid choice. Please try again.")
                
    def browser_takeover(self, browser_type: str = "opera-neon", url: str = None):
        """
        Take over a running browser (e.g., Opera Neon) using Playwright for automation.
        Args:
            browser_type: The browser to control (default: opera-neon)
            url: Optional URL to navigate to after takeover
        Returns:
            True if successful, False otherwise
        """
        import asyncio
        from playwright.async_api import async_playwright
        async def run():
            try:
                if browser_type == "opera-neon":
                    cdp_url = "http://localhost:9223"  # User must launch Opera Neon with --remote-debugging-port=9223
                else:
                    cdp_url = "http://localhost:9222"  # Default for Chrome/Edge
                async with async_playwright() as p:
                    browser = await p.chromium.connect_over_cdp(cdp_url)
                    page = await browser.new_page()
                    if url:
                        await page.goto(url)
                    # Example: take screenshot and return path
                    screenshot_path = "takeover_screenshot.png"
                    await page.screenshot(path=screenshot_path)
                    print(f"Screenshot saved: {screenshot_path}")
                    await browser.close()
                return True
            except Exception as e:
                print(f"Browser takeover failed: {e}")
                return False
        return asyncio.run(run())

    def visual_gui_action(self, screenshot_path: str, prompt: str):
        """
        Use UI-TARS 1.5 to predict and execute a GUI action based on a screenshot and prompt.
        Args:
            screenshot_path: Path to the screenshot image
            prompt: Instruction for the agent (e.g., 'Click the Login button')
        Returns:
            Action result dict
        """
        from PIL import Image
        img = Image.open(screenshot_path)
        result = self.tars.predict_action(img, prompt)
        print(f"UI-TARS action: {result}")
        return result
                

def main():
    # Parse command line arguments
    parser = argparse.ArgumentParser(description="Insurance Agent Access System")
    parser.add_argument('--portal', help='Access a specific insurance portal')
    parser.add_argument('--email', help='Access a specific email account')
    parser.add_argument('--config-tars', action='store_true', help='Configure UI Tars integration')
    parser.add_argument('--headless', action='store_true', help='Run browser in headless mode')
    
    args = parser.parse_args()
    
    # Initialize the system
    system = AgentAccessSystem()
    
    # Process command line arguments
    if args.portal:
        system.access_portal(args.portal, args.headless)
    elif args.email:
        system.access_email(args.email, args.headless)
    elif args.config_tars:
        system.configure_tars()
    else:
        # Run in interactive mode
        system.run_interactive()
        

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\nOperation cancelled by user")
    except Exception as e:
        logger.exception("Unhandled exception")
        print(f"\nAn error occurred: {e}")
        print("Check the log file for details.")