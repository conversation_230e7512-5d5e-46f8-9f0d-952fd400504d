#!/bin/bash

# Run OpenAI Agent for <PERSON>
# This script runs the paul_edwards_openai_agent.py script to communicate with <PERSON>

echo "========================================================================"
echo "RUNNING OPENAI AGENT FOR PAUL EDWARDS"
echo "========================================================================"

# Check if Python is installed
if ! command -v python3 &> /dev/null; then
    echo "Python 3 is not installed. Please install Python 3 and try again."
    exit 1
fi

# Check if the required Python packages are installed
echo "Checking required Python packages..."
python3 -c "import agents, numpy, soundfile" 2>/dev/null
if [ $? -ne 0 ]; then
    echo "Installing required Python packages..."
    pip3 install -r openai_agent_requirements.txt
fi

# Check if OPENAI_API_KEY is set
if [ -z "$OPENAI_API_KEY" ]; then
    if grep -q "OPENAI_API_KEY" .env; then
        echo "Loading OPENAI_API_KEY from .env file..."
        export $(grep -v '^#' .env | grep OPENAI_API_KEY)
    else
        echo "OPENAI_API_KEY is not set. Please set it in your environment or .env file."
        exit 1
    fi
fi

# Run the OpenAI Agent script
echo "Running paul_edwards_openai_agent.py..."
python3 paul_edwards_openai_agent.py

# Check if the script ran successfully
if [ $? -eq 0 ]; then
    echo "========================================================================"
    echo "OPENAI AGENT PROCESS COMPLETED"
    echo "========================================================================"
else
    echo "========================================================================"
    echo "ERROR: OPENAI AGENT PROCESS FAILED"
    echo "========================================================================"
fi
