import subprocess
import sys
import logging
import os

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def install_requirements():
    """Install required packages for trading agent"""
    
    requirements = [
        'pandas',
        'numpy',
        'yfinance',
        'tensorflow',
        'scikit-learn',
        'matplotlib',
        'seaborn'
    ]
    
    logger.info("Installing trading dependencies...")
    
    for package in requirements:
        try:
            subprocess.check_call([
                sys.executable, 
                "-m", 
                "pip", 
                "install", 
                package
            ])
            logger.info(f"Successfully installed {package}")
        except Exception as e:
            logger.error(f"Error installing {package}: {str(e)}")
            return False
            
    return True

def setup_directories():
    """Create necessary directories"""
    directories = [
        'test_results',
        'models',
        'data'
    ]
    
    for directory in directories:
        try:
            if not os.path.exists(directory):
                os.makedirs(directory)
                logger.info(f"Created directory: {directory}")
        except Exception as e:
            logger.error(f"Error creating directory {directory}: {str(e)}")
            return False
            
    return True

def main():
    """Run setup process"""
    logger.info("Setting up trading environment...")
    
    # Install requirements
    if not install_requirements():
        logger.error("Failed to install requirements")
        return False
        
    # Create directories
    if not setup_directories():
        logger.error("Failed to create directories")
        return False
        
    logger.info("\nTrading environment setup completed!")
    logger.info("You can now run trading tests with:")
    logger.info("python test_trading_agent.py")
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)