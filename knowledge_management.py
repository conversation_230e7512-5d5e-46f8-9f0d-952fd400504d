import torch
import numpy as np
from typing import List, Dict, Any, Optional
from dataclasses import dataclass
import faiss
import json
import logging
from datetime import datetime
from pathlib import Path

logger = logging.getLogger(__name__)

@dataclass
class Knowledge:
    """Structure for storing knowledge entries"""
    content: str
    source: str
    timestamp: str
    embedding: np.ndarray
    metadata: Dict[str, Any]
    confidence: float
    verification_status: str
    agent_id: str
    interaction_id: str

class VectorStore:
    """Manages vector embeddings for efficient similarity search"""
    
    def __init__(self, dimension: int = 768):
        self.dimension = dimension
        self.index = faiss.IndexFlatL2(dimension)
        self.knowledge_store = []
        
    def add_embedding(self, embedding: np.ndarray, knowledge: Knowledge):
        """Add new embedding and associated knowledge"""
        self.index.add(embedding.reshape(1, -1))
        self.knowledge_store.append(knowledge)
        
    def search(self, query_embedding: np.ndarray, k: int = 5) -> List[Knowledge]:
        """Search for similar knowledge entries"""
        distances, indices = self.index.search(query_embedding.reshape(1, -1), k)
        return [self.knowledge_store[i] for i in indices[0]]

class KnowledgeManager:
    """Manages knowledge storage, retrieval, and organization"""
    
    def __init__(self, base_path: str = "./knowledge_base"):
        self.base_path = Path(base_path)
        self.vector_store = VectorStore()
        self.knowledge_domains = {}
        self.interaction_history = []
        self.initialize_storage()
        
    def initialize_storage(self):
        """Initialize knowledge base storage"""
        self.base_path.mkdir(parents=True, exist_ok=True)
        domains = ['insurance', 'content', 'email', 'support', 'finance']
        
        for domain in domains:
            domain_path = self.base_path / domain
            domain_path.mkdir(exist_ok=True)
            self.knowledge_domains[domain] = {
                'path': domain_path,
                'vector_store': VectorStore(),
                'metadata': self._load_domain_metadata(domain)
            }
    
    def add_knowledge(self, domain: str, content: str, metadata: Dict, agent_id: str):
        """Add new knowledge to the system"""
        embedding = self._generate_embedding(content)
        
        knowledge = Knowledge(
            content=content,
            source=metadata.get('source', 'agent_learning'),
            timestamp=datetime.now().isoformat(),
            embedding=embedding,
            metadata=metadata,
            confidence=metadata.get('confidence', 1.0),
            verification_status='unverified',
            agent_id=agent_id,
            interaction_id=metadata.get('interaction_id', '')
        )
        
        # Add to domain-specific store
        self.knowledge_domains[domain]['vector_store'].add_embedding(embedding, knowledge)
        
        # Save to disk
        self._persist_knowledge(domain, knowledge)
        
    def query_knowledge(self, domain: str, query: str, k: int = 5) -> List[Knowledge]:
        """Query domain-specific knowledge"""
        query_embedding = self._generate_embedding(query)
        return self.knowledge_domains[domain]['vector_store'].search(query_embedding, k)
    
    def learn_from_interaction(self, interaction: Dict):
        """Learn from agent interactions"""
        self.interaction_history.append(interaction)
        
        # Extract knowledge from interaction
        knowledge = self._extract_knowledge(interaction)
        
        # Add to appropriate domain
        domain = interaction.get('domain', 'general')
        self.add_knowledge(
            domain=domain,
            content=knowledge['content'],
            metadata={
                'source': 'interaction',
                'interaction_type': interaction['type'],
                'outcome': interaction.get('outcome'),
                'confidence': knowledge['confidence']
            },
            agent_id=interaction['agent_id']
        )
    
    def _generate_embedding(self, text: str) -> np.ndarray:
        """Generate embedding for text using simple hash-based approach"""
        import hashlib
        # Simple hash-based embedding for basic functionality
        hash_obj = hashlib.md5(text.encode())
        hash_hex = hash_obj.hexdigest()
        # Convert to numpy array (simplified)
        embedding = np.array([float(int(hash_hex[i:i+2], 16)) for i in range(0, min(32, len(hash_hex)), 2)])
        # Pad or truncate to 768 dimensions
        if len(embedding) < 768:
            embedding = np.pad(embedding, (0, 768 - len(embedding)), 'constant')
        else:
            embedding = embedding[:768]
        return embedding
    
    def _persist_knowledge(self, domain: str, knowledge: Knowledge):
        """Save knowledge to disk"""
        domain_path = self.knowledge_domains[domain]['path']
        
        # Save knowledge entry
        knowledge_file = domain_path / f"{knowledge.interaction_id}.json"
        with open(knowledge_file, 'w') as f:
            json.dump({
                'content': knowledge.content,
                'source': knowledge.source,
                'timestamp': knowledge.timestamp,
                'metadata': knowledge.metadata,
                'confidence': knowledge.confidence,
                'verification_status': knowledge.verification_status,
                'agent_id': knowledge.agent_id,
                'interaction_id': knowledge.interaction_id
            }, f)
        
        # Save embedding
        embedding_file = domain_path / f"{knowledge.interaction_id}.npy"
        np.save(embedding_file, knowledge.embedding)
    
    def _load_domain_metadata(self, domain: str) -> Dict:
        """Load domain-specific metadata"""
        metadata_file = self.base_path / domain / "metadata.json"
        if metadata_file.exists():
            with open(metadata_file) as f:
                return json.load(f)
        return {}
    
    def _extract_knowledge(self, interaction: Dict) -> Dict:
        """Extract knowledge from interaction"""
        content = interaction.get('content', {})
        if isinstance(content, dict):
            response = content.get('agent_response', str(content))
        else:
            response = str(content)

        return {
            'content': response,
            'confidence': 0.8
        }

class DomainKnowledgeBase:
    """Domain-specific knowledge management"""
    
    def __init__(self, domain: str, manager: KnowledgeManager):
        self.domain = domain
        self.manager = manager
        self.specializations = self._load_specializations()
        
    def add_specialized_knowledge(self, content: str, specialization: str, metadata: Dict):
        """Add specialized domain knowledge"""
        metadata['specialization'] = specialization
        self.manager.add_knowledge(self.domain, content, metadata, f"{self.domain}_{specialization}")
        
    def query_specialized(self, query: str, specialization: str, k: int = 5) -> List[Knowledge]:
        """Query specialized knowledge"""
        results = self.manager.query_knowledge(self.domain, query, k)
        return [r for r in results if r.metadata.get('specialization') == specialization]
        
    def _load_specializations(self) -> Dict:
        """Load domain specializations"""
        spec_file = self.manager.base_path / self.domain / "specializations.json"
        if spec_file.exists():
            with open(spec_file) as f:
                return json.load(f)
        return {}

# Example Usage
if __name__ == "__main__":
    # Initialize knowledge management system
    knowledge_manager = KnowledgeManager()
    
    # Create domain-specific knowledge bases
    insurance_kb = DomainKnowledgeBase("insurance", knowledge_manager)
    content_kb = DomainKnowledgeBase("content", knowledge_manager)
    email_kb = DomainKnowledgeBase("email", knowledge_manager)
    
    # Example: Add specialized insurance knowledge
    insurance_kb.add_specialized_knowledge(
        content="High performance vehicles typically require comprehensive coverage with specialized risk assessment.",
        specialization="auto_insurance",
        metadata={
            'confidence': 0.95,
            'source': 'expert_review',
            'tags': ['auto', 'high_performance', 'risk']
        }
    )
    
    # Example: Query insurance knowledge
    results = insurance_kb.query_specialized(
        query="What coverage is needed for sports cars?",
        specialization="auto_insurance"
    )
    
    # Example: Learn from interaction
    knowledge_manager.learn_from_interaction({
        'type': 'quote_generation',
        'domain': 'insurance',
        'agent_id': 'quote_agent_1',
        'interaction_id': 'quote_123',
        'content': {
            'customer_request': 'Sports car insurance quote',
            'agent_response': 'Recommended comprehensive coverage with additional provisions',
            'outcome': 'success'
        }
    })