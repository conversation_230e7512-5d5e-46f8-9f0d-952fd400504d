#!/usr/bin/env python3
"""
Web-UI Component
================

Modern React-based web interface for browser automation control and monitoring.
Provides a comprehensive dashboard for managing web automation tasks.
"""

import asyncio
import json
import logging
import os
import time
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Any, Optional
from dataclasses import dataclass, asdict
import aiohttp
from aiohttp import web, WSMsgType
import aiofiles

logger = logging.getLogger(__name__)

@dataclass
class AutomationTask:
    id: str
    name: str
    url: str
    actions: List[Dict[str, Any]]
    status: str = "pending"
    created_at: str = ""
    completed_at: Optional[str] = None
    result: Optional[Dict[str, Any]] = None
    error: Optional[str] = None

class WebUIComponent:
    """Web-UI component for browser automation control"""
    
    def __init__(self, host='localhost', port=3000):
        self.host = host
        self.port = port
        self.app = web.Application()
        self.setup_routes()
        
        # State management
        self.tasks: Dict[str, AutomationTask] = {}
        self.active_sessions: Dict[str, Dict] = {}
        self.websockets: List[web.WebSocketResponse] = []
        
        # Configuration
        self.config = {
            'max_concurrent_tasks': 5,
            'task_timeout': 300,
            'enable_screenshots': True,
            'enable_recording': False
        }
        
    def setup_routes(self):
        """Setup HTTP routes"""
        # Static files
        self.app.router.add_get('/', self.serve_index)
        self.app.router.add_static('/static/', path='web_ui/static', name='static')
        
        # API routes
        self.app.router.add_get('/api/tasks', self.get_tasks)
        self.app.router.add_post('/api/tasks', self.create_task)
        self.app.router.add_get('/api/tasks/{task_id}', self.get_task)
        self.app.router.add_delete('/api/tasks/{task_id}', self.delete_task)
        self.app.router.add_post('/api/tasks/{task_id}/start', self.start_task)
        self.app.router.add_post('/api/tasks/{task_id}/stop', self.stop_task)
        
        # WebSocket for real-time updates
        self.app.router.add_get('/ws', self.websocket_handler)
        
        # System status
        self.app.router.add_get('/api/status', self.get_status)
        self.app.router.add_get('/api/config', self.get_config)
        self.app.router.add_post('/api/config', self.update_config)
        
    async def serve_index(self, request):
        """Serve the main index.html"""
        return web.FileResponse('web_ui/index.html')
        
    async def get_tasks(self, request):
        """Get all automation tasks"""
        tasks_data = [asdict(task) for task in self.tasks.values()]
        return web.json_response({'tasks': tasks_data})
        
    async def create_task(self, request):
        """Create a new automation task"""
        try:
            data = await request.json()
            
            task = AutomationTask(
                id=f"task_{int(time.time())}_{len(self.tasks)}",
                name=data.get('name', 'Unnamed Task'),
                url=data.get('url', ''),
                actions=data.get('actions', []),
                created_at=datetime.now().isoformat()
            )
            
            self.tasks[task.id] = task
            
            # Notify connected clients
            await self.broadcast_update('task_created', asdict(task))
            
            return web.json_response({
                'success': True,
                'task': asdict(task)
            })
            
        except Exception as e:
            logger.error(f"Error creating task: {e}")
            return web.json_response({
                'success': False,
                'error': str(e)
            }, status=400)
            
    async def get_task(self, request):
        """Get a specific task"""
        task_id = request.match_info['task_id']
        
        if task_id not in self.tasks:
            return web.json_response({
                'success': False,
                'error': 'Task not found'
            }, status=404)
            
        return web.json_response({
            'success': True,
            'task': asdict(self.tasks[task_id])
        })
        
    async def delete_task(self, request):
        """Delete a task"""
        task_id = request.match_info['task_id']
        
        if task_id not in self.tasks:
            return web.json_response({
                'success': False,
                'error': 'Task not found'
            }, status=404)
            
        del self.tasks[task_id]
        
        await self.broadcast_update('task_deleted', {'task_id': task_id})
        
        return web.json_response({'success': True})
        
    async def start_task(self, request):
        """Start executing a task"""
        task_id = request.match_info['task_id']
        
        if task_id not in self.tasks:
            return web.json_response({
                'success': False,
                'error': 'Task not found'
            }, status=404)
            
        task = self.tasks[task_id]
        task.status = 'running'
        
        # Start task execution in background
        asyncio.create_task(self.execute_task(task))
        
        await self.broadcast_update('task_started', asdict(task))
        
        return web.json_response({
            'success': True,
            'task': asdict(task)
        })
        
    async def stop_task(self, request):
        """Stop a running task"""
        task_id = request.match_info['task_id']
        
        if task_id not in self.tasks:
            return web.json_response({
                'success': False,
                'error': 'Task not found'
            }, status=404)
            
        task = self.tasks[task_id]
        task.status = 'stopped'
        
        await self.broadcast_update('task_stopped', asdict(task))
        
        return web.json_response({
            'success': True,
            'task': asdict(task)
        })
        
    async def execute_task(self, task: AutomationTask):
        """Execute an automation task"""
        try:
            logger.info(f"Executing task: {task.name}")
            
            # Simulate task execution
            # In real implementation, this would use WebRover or UI TARS
            await asyncio.sleep(2)  # Simulate work
            
            task.status = 'completed'
            task.completed_at = datetime.now().isoformat()
            task.result = {
                'success': True,
                'message': f'Task {task.name} completed successfully',
                'data': {'url': task.url, 'actions_completed': len(task.actions)}
            }
            
            await self.broadcast_update('task_completed', asdict(task))
            
        except Exception as e:
            logger.error(f"Task execution failed: {e}")
            task.status = 'failed'
            task.error = str(e)
            task.completed_at = datetime.now().isoformat()
            
            await self.broadcast_update('task_failed', asdict(task))
            
    async def websocket_handler(self, request):
        """Handle WebSocket connections for real-time updates"""
        ws = web.WebSocketResponse()
        await ws.prepare(request)
        
        self.websockets.append(ws)
        logger.info("WebSocket client connected")
        
        try:
            async for msg in ws:
                if msg.type == WSMsgType.TEXT:
                    try:
                        data = json.loads(msg.data)
                        await self.handle_websocket_message(ws, data)
                    except json.JSONDecodeError:
                        await ws.send_str(json.dumps({
                            'type': 'error',
                            'message': 'Invalid JSON'
                        }))
                elif msg.type == WSMsgType.ERROR:
                    logger.error(f'WebSocket error: {ws.exception()}')
                    
        except Exception as e:
            logger.error(f"WebSocket error: {e}")
        finally:
            if ws in self.websockets:
                self.websockets.remove(ws)
            logger.info("WebSocket client disconnected")
            
        return ws
        
    async def handle_websocket_message(self, ws, data):
        """Handle incoming WebSocket messages"""
        msg_type = data.get('type')
        
        if msg_type == 'ping':
            await ws.send_str(json.dumps({'type': 'pong'}))
        elif msg_type == 'subscribe':
            # Client wants to subscribe to updates
            await ws.send_str(json.dumps({
                'type': 'subscribed',
                'message': 'Successfully subscribed to updates'
            }))
            
    async def broadcast_update(self, event_type: str, data: Any):
        """Broadcast update to all connected WebSocket clients"""
        if not self.websockets:
            return
            
        message = json.dumps({
            'type': event_type,
            'data': data,
            'timestamp': datetime.now().isoformat()
        })
        
        # Remove closed connections
        active_websockets = []
        for ws in self.websockets:
            if not ws.closed:
                try:
                    await ws.send_str(message)
                    active_websockets.append(ws)
                except Exception as e:
                    logger.error(f"Error sending WebSocket message: {e}")
                    
        self.websockets = active_websockets
        
    async def get_status(self, request):
        """Get system status"""
        return web.json_response({
            'status': 'running',
            'tasks': {
                'total': len(self.tasks),
                'running': len([t for t in self.tasks.values() if t.status == 'running']),
                'completed': len([t for t in self.tasks.values() if t.status == 'completed']),
                'failed': len([t for t in self.tasks.values() if t.status == 'failed'])
            },
            'websockets': len(self.websockets),
            'uptime': time.time() - getattr(self, 'start_time', time.time())
        })
        
    async def get_config(self, request):
        """Get current configuration"""
        return web.json_response(self.config)
        
    async def update_config(self, request):
        """Update configuration"""
        try:
            data = await request.json()
            self.config.update(data)
            
            await self.broadcast_update('config_updated', self.config)
            
            return web.json_response({
                'success': True,
                'config': self.config
            })
            
        except Exception as e:
            return web.json_response({
                'success': False,
                'error': str(e)
            }, status=400)
            
    async def start_server(self):
        """Start the web server"""
        self.start_time = time.time()
        
        # Create web_ui directory if it doesn't exist
        os.makedirs('web_ui/static', exist_ok=True)
        
        # Create basic HTML file if it doesn't exist
        if not os.path.exists('web_ui/index.html'):
            await self.create_default_html()
            
        runner = web.AppRunner(self.app)
        await runner.setup()
        
        site = web.TCPSite(runner, self.host, self.port)
        await site.start()
        
        logger.info(f"Web-UI server started at http://{self.host}:{self.port}")
        
    async def create_default_html(self):
        """Create a default HTML interface"""
        html_content = """<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Web Automation Dashboard</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 0; padding: 20px; background: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; }
        .header { background: #2c3e50; color: white; padding: 20px; border-radius: 8px; margin-bottom: 20px; }
        .card { background: white; padding: 20px; border-radius: 8px; margin-bottom: 20px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .btn { background: #3498db; color: white; padding: 10px 20px; border: none; border-radius: 4px; cursor: pointer; }
        .btn:hover { background: #2980b9; }
        .status { padding: 4px 8px; border-radius: 4px; font-size: 12px; }
        .status.running { background: #f39c12; color: white; }
        .status.completed { background: #27ae60; color: white; }
        .status.failed { background: #e74c3c; color: white; }
        .status.pending { background: #95a5a6; color: white; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🌐 Web Automation Dashboard</h1>
            <p>Control and monitor your web automation tasks</p>
        </div>
        
        <div class="card">
            <h2>System Status</h2>
            <div id="status">Loading...</div>
        </div>
        
        <div class="card">
            <h2>Automation Tasks</h2>
            <button class="btn" onclick="createSampleTask()">Create Sample Task</button>
            <div id="tasks" style="margin-top: 20px;">Loading...</div>
        </div>
    </div>
    
    <script>
        // WebSocket connection for real-time updates
        const ws = new WebSocket(`ws://${window.location.host}/ws`);
        
        ws.onopen = function() {
            console.log('WebSocket connected');
            ws.send(JSON.stringify({type: 'subscribe'}));
        };
        
        ws.onmessage = function(event) {
            const data = JSON.parse(event.data);
            console.log('WebSocket message:', data);
            
            if (data.type === 'task_created' || data.type === 'task_updated') {
                loadTasks();
            }
        };
        
        // Load system status
        async function loadStatus() {
            try {
                const response = await fetch('/api/status');
                const data = await response.json();
                document.getElementById('status').innerHTML = `
                    <p><strong>Status:</strong> ${data.status}</p>
                    <p><strong>Total Tasks:</strong> ${data.tasks.total}</p>
                    <p><strong>Running:</strong> ${data.tasks.running}</p>
                    <p><strong>Completed:</strong> ${data.tasks.completed}</p>
                    <p><strong>Failed:</strong> ${data.tasks.failed}</p>
                `;
            } catch (error) {
                console.error('Error loading status:', error);
            }
        }
        
        // Load tasks
        async function loadTasks() {
            try {
                const response = await fetch('/api/tasks');
                const data = await response.json();
                
                const tasksHtml = data.tasks.map(task => `
                    <div style="border: 1px solid #ddd; padding: 10px; margin: 10px 0; border-radius: 4px;">
                        <h4>${task.name} <span class="status ${task.status}">${task.status}</span></h4>
                        <p><strong>URL:</strong> ${task.url}</p>
                        <p><strong>Actions:</strong> ${task.actions.length}</p>
                        <p><strong>Created:</strong> ${new Date(task.created_at).toLocaleString()}</p>
                        ${task.status === 'pending' ? `<button class="btn" onclick="startTask('${task.id}')">Start</button>` : ''}
                        <button class="btn" style="background: #e74c3c;" onclick="deleteTask('${task.id}')">Delete</button>
                    </div>
                `).join('');
                
                document.getElementById('tasks').innerHTML = tasksHtml || '<p>No tasks found</p>';
            } catch (error) {
                console.error('Error loading tasks:', error);
            }
        }
        
        // Create sample task
        async function createSampleTask() {
            try {
                const response = await fetch('/api/tasks', {
                    method: 'POST',
                    headers: {'Content-Type': 'application/json'},
                    body: JSON.stringify({
                        name: 'Sample Web Automation',
                        url: 'https://example.com',
                        actions: [
                            {type: 'navigate', url: 'https://example.com'},
                            {type: 'screenshot'},
                            {type: 'extract_text'}
                        ]
                    })
                });
                
                if (response.ok) {
                    loadTasks();
                }
            } catch (error) {
                console.error('Error creating task:', error);
            }
        }
        
        // Start task
        async function startTask(taskId) {
            try {
                const response = await fetch(`/api/tasks/${taskId}/start`, {method: 'POST'});
                if (response.ok) {
                    loadTasks();
                }
            } catch (error) {
                console.error('Error starting task:', error);
            }
        }
        
        // Delete task
        async function deleteTask(taskId) {
            if (confirm('Are you sure you want to delete this task?')) {
                try {
                    const response = await fetch(`/api/tasks/${taskId}`, {method: 'DELETE'});
                    if (response.ok) {
                        loadTasks();
                    }
                } catch (error) {
                    console.error('Error deleting task:', error);
                }
            }
        }
        
        // Initial load
        loadStatus();
        loadTasks();
        
        // Refresh every 5 seconds
        setInterval(() => {
            loadStatus();
        }, 5000);
    </script>
</body>
</html>"""
        
        os.makedirs('web_ui', exist_ok=True)
        async with aiofiles.open('web_ui/index.html', 'w') as f:
            await f.write(html_content)

if __name__ == "__main__":
    async def main():
        web_ui = WebUIComponent()
        await web_ui.start_server()
        
        try:
            # Keep the server running
            while True:
                await asyncio.sleep(1)
        except KeyboardInterrupt:
            logger.info("Shutting down Web-UI server")
            
    asyncio.run(main())
