#!/usr/bin/env python3
"""
Google AI Studio API Key Setup Script

This script securely stores your Google AI Studio API key using the credential manager.
"""

import os
import sys
import logging
from credential_manager import SecureCredentialManager

logging.basicConfig(level=logging.INFO, 
                    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def setup_google_ai_credentials():
    """Set up Google AI Studio API credentials"""
    print("Google AI Studio API Key Setup")
    print("=============================")
    print("This script will securely store your Google AI Studio API key.")
    print("Your API key will be encrypted and stored locally.")
    print()
    
    # Get API key from user or environment variable
    api_key = os.environ.get("GOOGLE_AI_API_KEY")
    if not api_key:
        api_key = input("Enter your Google AI Studio API key: ")
        
    if not api_key:
        logger.error("No API key provided. Exiting.")
        return False
        
    try:
        # Initialize credential manager
        credential_manager = SecureCredentialManager()
        
        # Store API key
        credential_manager.add_google_ai_credentials(api_key)
        
        print()
        print("✅ Google AI Studio API key has been securely stored.")
        print("You can now use it in your applications without exposing the key in code.")
        return True
        
    except Exception as e:
        logger.error(f"Error storing API key: {e}")
        return False
        
def get_google_ai_key():
    """Retrieve the Google AI Studio API key"""
    try:
        credential_manager = SecureCredentialManager()
        credentials = credential_manager.get_google_ai_credentials()
        
        if credentials and "api_key" in credentials:
            return credentials["api_key"]
        else:
            logger.warning("Google AI Studio API key not found.")
            print("Google AI Studio API key not found. Run setup_google_ai_credentials.py to set it up.")
            return None
            
    except Exception as e:
        logger.error(f"Error retrieving API key: {e}")
        return None

if __name__ == "__main__":
    setup_google_ai_credentials()