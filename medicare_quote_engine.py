import logging
import os
import json
import datetime
import random
from typing import Dict, List, Optional, Any, Tuple
from pydantic import BaseModel, Field, validator

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger("MedicareQuoteEngine")

class PlanBenefit(BaseModel):
    """Model for a benefit offered by a Medicare plan"""
    name: str
    description: str
    covered: bool = True
    coverage_percentage: Optional[float] = None
    copay: Optional[float] = None
    notes: Optional[str] = None

class MedicarePlan(BaseModel):
    """Model for a Medicare plan"""
    id: str
    name: str
    carrier: str
    type: str  # "Part C", "Part D", "Supplement", "Advantage"
    monthly_premium: float
    annual_deductible: float
    max_out_of_pocket: Optional[float] = None
    drug_coverage: bool = False
    dental_coverage: bool = False
    vision_coverage: bool = False
    hearing_coverage: bool = False
    gym_membership: bool = False
    transportation: bool = False
    meal_delivery: bool = False
    telehealth: bool = False
    star_rating: Optional[float] = None
    benefits: List[PlanBenefit] = Field(default_factory=list)
    
    @validator('star_rating')
    def star_rating_must_be_valid(cls, v):
        if v is None:
            return v
        if v < 0 or v > 5:
            raise ValueError('Star rating must be between 0 and 5')
        return v

class InsuranceQuote(BaseModel):
    """Model for an insurance quote"""
    id: str
    lead_id: str
    quote_date: str
    expires_on: str
    status: str = "active"  # "active", "expired", "accepted", "declined"
    plan: MedicarePlan
    monthly_premium: float
    annual_premium: float
    recommended: bool = False
    notes: List[str] = Field(default_factory=list)
    
    @validator('quote_date', 'expires_on')
    def date_must_be_valid(cls, v):
        try:
            datetime.datetime.fromisoformat(v)
        except ValueError:
            raise ValueError('Invalid date format. Use ISO format')
        return v

class MedicareQuoteEngine:
    """Engine for generating Medicare quotes"""
    
    def __init__(self, carriers_file: str = "medicare_carriers.json", plans_file: str = "medicare_plans.json"):
        self.carriers_file = carriers_file
        self.plans_file = plans_file
        self.carriers: List[Dict] = []
        self.plans: List[MedicarePlan] = []
        self._load_carriers()
        self._load_plans()
        
    def _load_carriers(self) -> None:
        """Load carriers from file or create default ones"""
        if os.path.exists(self.carriers_file):
            try:
                with open(self.carriers_file, "r") as f:
                    self.carriers = json.load(f)
            except Exception as e:
                logger.error(f"Error loading carriers: {e}")
                self._create_default_carriers()
        else:
            self._create_default_carriers()
            
    def _create_default_carriers(self) -> None:
        """Create default carriers"""
        self.carriers = [
            {"id": "aetna", "name": "Aetna", "website": "https://www.aetna.com", "phone": "1-************"},
            {"id": "bcbs", "name": "Blue Cross Blue Shield", "website": "https://www.bcbs.com", "phone": "1-************"},
            {"id": "cigna", "name": "Cigna", "website": "https://www.cigna.com", "phone": "1-************"},
            {"id": "humana", "name": "Humana", "website": "https://www.humana.com", "phone": "1-************"},
            {"id": "uhc", "name": "UnitedHealthcare", "website": "https://www.uhc.com", "phone": "**************"},
            {"id": "kaiser", "name": "Kaiser Permanente", "website": "https://www.kaiserpermanente.org", "phone": "**************"},
            {"id": "anthem", "name": "Anthem", "website": "https://www.anthem.com", "phone": "1-************"}
        ]
        self._save_carriers()
        
    def _save_carriers(self) -> None:
        """Save carriers to file"""
        try:
            with open(self.carriers_file, "w") as f:
                json.dump(self.carriers, f, indent=2)
        except Exception as e:
            logger.error(f"Error saving carriers: {e}")
            
    def _load_plans(self) -> None:
        """Load plans from file or create default ones"""
        if os.path.exists(self.plans_file):
            try:
                with open(self.plans_file, "r") as f:
                    plans_data = json.load(f)
                    # Create MedicarePlan objects from JSON data
                    self.plans = []
                    for plan_data in plans_data:
                        benefits = []
                        for benefit_data in plan_data.get("benefits", []):
                            benefits.append(PlanBenefit(**benefit_data))
                            
                        plan_data["benefits"] = benefits
                        self.plans.append(MedicarePlan(**plan_data))
            except Exception as e:
                logger.error(f"Error loading plans: {e}")
                self._create_default_plans()
        else:
            self._create_default_plans()
            
    def _create_default_plans(self) -> None:
        """Create default Medicare plans"""
        default_plans = [
            # UnitedHealthcare Medicare Advantage Plan
            MedicarePlan(
                id="uhc-advantage-1",
                name="AARP Medicare Advantage Choice",
                carrier="UnitedHealthcare",
                type="Advantage",
                monthly_premium=25.00,
                annual_deductible=100.00,
                max_out_of_pocket=5000.00,
                drug_coverage=True,
                dental_coverage=True,
                vision_coverage=True,
                hearing_coverage=True,
                gym_membership=True,
                transportation=True,
                meal_delivery=False,
                telehealth=True,
                star_rating=4.5,
                benefits=[
                    PlanBenefit(
                        name="Primary Care Visit",
                        description="Visit to primary care physician",
                        covered=True,
                        copay=15.00,
                    ),
                    PlanBenefit(
                        name="Specialist Visit",
                        description="Visit to specialist",
                        covered=True,
                        copay=45.00,
                    ),
                    PlanBenefit(
                        name="Emergency Room",
                        description="Emergency room services",
                        covered=True,
                        copay=90.00,
                    ),
                    PlanBenefit(
                        name="Urgent Care",
                        description="Urgent care services",
                        covered=True,
                        copay=40.00,
                    ),
                    PlanBenefit(
                        name="Annual Physical",
                        description="Annual physical exam",
                        covered=True,
                        copay=0.00,
                    ),
                ]
            ),
            
            # Humana Medicare Advantage Plan
            MedicarePlan(
                id="humana-advantage-1",
                name="Humana Gold Plus",
                carrier="Humana",
                type="Advantage",
                monthly_premium=0.00,
                annual_deductible=125.00,
                max_out_of_pocket=4500.00,
                drug_coverage=True,
                dental_coverage=True,
                vision_coverage=True,
                hearing_coverage=True,
                gym_membership=True,
                transportation=False,
                meal_delivery=False,
                telehealth=True,
                star_rating=4.0,
                benefits=[
                    PlanBenefit(
                        name="Primary Care Visit",
                        description="Visit to primary care physician",
                        covered=True,
                        copay=10.00,
                    ),
                    PlanBenefit(
                        name="Specialist Visit",
                        description="Visit to specialist",
                        covered=True,
                        copay=40.00,
                    ),
                    PlanBenefit(
                        name="Emergency Room",
                        description="Emergency room services",
                        covered=True,
                        copay=85.00,
                    ),
                    PlanBenefit(
                        name="Urgent Care",
                        description="Urgent care services",
                        covered=True,
                        copay=35.00,
                    ),
                    PlanBenefit(
                        name="Annual Physical",
                        description="Annual physical exam",
                        covered=True,
                        copay=0.00,
                    ),
                ]
            ),
            
            # Aetna Medicare Advantage Plan
            MedicarePlan(
                id="aetna-advantage-1",
                name="Aetna Medicare Elite",
                carrier="Aetna",
                type="Advantage",
                monthly_premium=19.00,
                annual_deductible=150.00,
                max_out_of_pocket=4800.00,
                drug_coverage=True,
                dental_coverage=True,
                vision_coverage=True,
                hearing_coverage=True,
                gym_membership=True,
                transportation=True,
                meal_delivery=True,
                telehealth=True,
                star_rating=4.2,
                benefits=[
                    PlanBenefit(
                        name="Primary Care Visit",
                        description="Visit to primary care physician",
                        covered=True,
                        copay=5.00,
                    ),
                    PlanBenefit(
                        name="Specialist Visit",
                        description="Visit to specialist",
                        covered=True,
                        copay=35.00,
                    ),
                    PlanBenefit(
                        name="Emergency Room",
                        description="Emergency room services",
                        covered=True,
                        copay=90.00,
                    ),
                    PlanBenefit(
                        name="Urgent Care",
                        description="Urgent care services",
                        covered=True,
                        copay=30.00,
                    ),
                    PlanBenefit(
                        name="Annual Physical",
                        description="Annual physical exam",
                        covered=True,
                        copay=0.00,
                    ),
                ]
            ),
            
            # BCBS Medicare Supplement Plan
            MedicarePlan(
                id="bcbs-supplement-f",
                name="Blue Cross Blue Shield Medicare Supplement Plan F",
                carrier="Blue Cross Blue Shield",
                type="Supplement",
                monthly_premium=180.00,
                annual_deductible=0.00,
                max_out_of_pocket=0.00,
                drug_coverage=False,
                dental_coverage=False,
                vision_coverage=False,
                hearing_coverage=False,
                gym_membership=False,
                transportation=False,
                meal_delivery=False,
                telehealth=False,
                star_rating=None,
                benefits=[
                    PlanBenefit(
                        name="Part A Deductible",
                        description="Medicare Part A Deductible",
                        covered=True,
                        coverage_percentage=100.0,
                    ),
                    PlanBenefit(
                        name="Part B Deductible",
                        description="Medicare Part B Deductible",
                        covered=True,
                        coverage_percentage=100.0,
                    ),
                    PlanBenefit(
                        name="Part B Excess Charges",
                        description="Medicare Part B Excess Charges",
                        covered=True,
                        coverage_percentage=100.0,
                    ),
                    PlanBenefit(
                        name="Foreign Travel Emergency",
                        description="Foreign Travel Emergency",
                        covered=True,
                        coverage_percentage=80.0,
                        notes="Up to plan limits",
                    ),
                ]
            ),
            
            # BCBS Medicare Supplement Plan
            MedicarePlan(
                id="bcbs-supplement-g",
                name="Blue Cross Blue Shield Medicare Supplement Plan G",
                carrier="Blue Cross Blue Shield",
                type="Supplement",
                monthly_premium=145.00,
                annual_deductible=226.00,  # 2023 Part B Deductible
                max_out_of_pocket=226.00,  # 2023 Part B Deductible
                drug_coverage=False,
                dental_coverage=False,
                vision_coverage=False,
                hearing_coverage=False,
                gym_membership=False,
                transportation=False,
                meal_delivery=False,
                telehealth=False,
                star_rating=None,
                benefits=[
                    PlanBenefit(
                        name="Part A Deductible",
                        description="Medicare Part A Deductible",
                        covered=True,
                        coverage_percentage=100.0,
                    ),
                    PlanBenefit(
                        name="Part B Deductible",
                        description="Medicare Part B Deductible",
                        covered=False,
                    ),
                    PlanBenefit(
                        name="Part B Excess Charges",
                        description="Medicare Part B Excess Charges",
                        covered=True,
                        coverage_percentage=100.0,
                    ),
                    PlanBenefit(
                        name="Foreign Travel Emergency",
                        description="Foreign Travel Emergency",
                        covered=True,
                        coverage_percentage=80.0,
                        notes="Up to plan limits",
                    ),
                ]
            ),
            
            # UnitedHealthcare Part D Plan
            MedicarePlan(
                id="uhc-part-d-1",
                name="AARP MedicareRx Preferred",
                carrier="UnitedHealthcare",
                type="Part D",
                monthly_premium=75.00,
                annual_deductible=0.00,
                drug_coverage=True,
                dental_coverage=False,
                vision_coverage=False,
                hearing_coverage=False,
                gym_membership=False,
                transportation=False,
                meal_delivery=False,
                telehealth=False,
                star_rating=4.0,
                benefits=[
                    PlanBenefit(
                        name="Tier 1 Drugs",
                        description="Preferred Generic drugs",
                        covered=True,
                        copay=5.00,
                    ),
                    PlanBenefit(
                        name="Tier 2 Drugs",
                        description="Generic drugs",
                        covered=True,
                        copay=10.00,
                    ),
                    PlanBenefit(
                        name="Tier 3 Drugs",
                        description="Preferred Brand drugs",
                        covered=True,
                        copay=40.00,
                    ),
                    PlanBenefit(
                        name="Tier 4 Drugs",
                        description="Non-Preferred drugs",
                        covered=True,
                        copay=85.00,
                    ),
                    PlanBenefit(
                        name="Tier 5 Drugs",
                        description="Specialty drugs",
                        covered=True,
                        coverage_percentage=33.0,
                    ),
                ]
            ),
            
            # Cigna Medicare Advantage Plan
            MedicarePlan(
                id="cigna-advantage-1",
                name="Cigna Preferred Medicare",
                carrier="Cigna",
                type="Advantage",
                monthly_premium=29.00,
                annual_deductible=100.00,
                max_out_of_pocket=4900.00,
                drug_coverage=True,
                dental_coverage=True,
                vision_coverage=True,
                hearing_coverage=True,
                gym_membership=True,
                transportation=False,
                meal_delivery=False,
                telehealth=True,
                star_rating=3.8,
                benefits=[
                    PlanBenefit(
                        name="Primary Care Visit",
                        description="Visit to primary care physician",
                        covered=True,
                        copay=15.00,
                    ),
                    PlanBenefit(
                        name="Specialist Visit",
                        description="Visit to specialist",
                        covered=True,
                        copay=45.00,
                    ),
                    PlanBenefit(
                        name="Emergency Room",
                        description="Emergency room services",
                        covered=True,
                        copay=90.00,
                    ),
                    PlanBenefit(
                        name="Urgent Care",
                        description="Urgent care services",
                        covered=True,
                        copay=45.00,
                    ),
                    PlanBenefit(
                        name="Annual Physical",
                        description="Annual physical exam",
                        covered=True,
                        copay=0.00,
                    ),
                ]
            ),
            
            # Anthem Medicare Advantage Plan
            MedicarePlan(
                id="anthem-advantage-1",
                name="Anthem MediBlue Plus",
                carrier="Anthem",
                type="Advantage",
                monthly_premium=15.00,
                annual_deductible=150.00,
                max_out_of_pocket=5200.00,
                drug_coverage=True,
                dental_coverage=True,
                vision_coverage=True,
                hearing_coverage=True,
                gym_membership=True,
                transportation=False,
                meal_delivery=False,
                telehealth=True,
                star_rating=4.1,
                benefits=[
                    PlanBenefit(
                        name="Primary Care Visit",
                        description="Visit to primary care physician",
                        covered=True,
                        copay=20.00,
                    ),
                    PlanBenefit(
                        name="Specialist Visit",
                        description="Visit to specialist",
                        covered=True,
                        copay=50.00,
                    ),
                    PlanBenefit(
                        name="Emergency Room",
                        description="Emergency room services",
                        covered=True,
                        copay=95.00,
                    ),
                    PlanBenefit(
                        name="Urgent Care",
                        description="Urgent care services",
                        covered=True,