"""
Direct Contact with <PERSON>

This script provides instructions for directly contacting <PERSON>
using your own phone number instead of <PERSON><PERSON><PERSON>.
"""

# <PERSON> contact information
PAUL_EDWARDS = {
    "first_name": "<PERSON>",
    "last_name": "<PERSON>",
    "email": "<EMAIL>",
    "primary_phone": "7722089646",
    "secondary_phone": "7725395908"
}

# Agent information
AGENT_INFO = {
    "name": "<PERSON>",
    "agency": "Flo Faction Insurance",
    "website": "https://www.flofaction.com",
}

def generate_sms_script():
    """Generate SMS script for <PERSON>"""
    sms = f"""
Hi {PAUL_EDWARDS['first_name']}, this is {AGENT_INFO['name']} with {AGENT_INFO['agency']}. I help people create tax-free retirement income without stock market risk. I've prepared a personalized analysis for you. Would you be interested in seeing this analysis? Visit {AGENT_INFO['website']} for more information. Thanks!
    """
    return sms.strip()

def generate_call_script():
    """Generate call script for <PERSON>"""
    script = f"""
CALL SCRIPT FOR PAUL EDWARDS

Introduction:
"Hi {PAUL_EDWARDS['first_name']}, this is {AGENT_INFO['name']} with {AGENT_INFO['agency']}. How are you doing today? [PAUSE FOR RESPONSE]

I'm reaching out because I help people like you create tax-free retirement income without the risk of the stock market. Based on your profile, I've found that you might qualify for a strategy that could provide around $5,000 per month in tax-free retirement income. Would that be something you'd be interested in learning more about? [PAUSE FOR RESPONSE]

Great! I've prepared a personalized analysis for you that shows how an Indexed Universal Life policy could help you build tax-free retirement income while also providing a death benefit of around $720,000 to protect your family.

I'd like to schedule about 30 minutes to walk you through this analysis and answer any questions you might have. Would Tuesday at 2:00 PM or Thursday at 4:00 PM work better for your schedule? [PAUSE FOR RESPONSE]

Excellent! I'll send you a calendar invitation with all the details. Before we wrap up, do you have any initial questions about how this strategy works? [PAUSE FOR RESPONSE]

[ADDRESS ANY QUESTIONS]

I'm looking forward to our conversation on [CONFIRMED DATE/TIME]. In the meantime, I'll send you a brief overview via email so you can get familiar with the concept. Thank you for your time today, {PAUL_EDWARDS['first_name']}, and have a great day!"

Key Talking Points:
- Tax-free retirement income potential
- Protection from market downturns
- Death benefit protection for family
- Cash value growth potential
- Flexibility of the policy

Objection Handling:
- "I need to think about it" → "I understand completely. This is an important decision. The meeting is just to provide information so you can make an informed decision. There's no obligation, and I won't be asking you to make any decisions during our call."
- "I already have retirement plans" → "That's great! Many of my clients use this strategy to complement their existing retirement plans, especially to create a tax-free income stream. Would it be worth 30 minutes to see if this could enhance what you're already doing?"
- "Is this whole life insurance?" → "No, this is Indexed Universal Life, which is quite different. It provides both death benefit protection and cash value growth potential linked to market indexes, but without the risk of market losses. I'd be happy to explain the differences in our meeting."
    """
    return script.strip()

def generate_voicemail_script():
    """Generate voicemail script for Paul Edwards"""
    script = f"""
VOICEMAIL SCRIPT FOR PAUL EDWARDS

"Hi {PAUL_EDWARDS['first_name']}, this is {AGENT_INFO['name']} with {AGENT_INFO['agency']}. I'm calling because I help people like you create tax-free retirement income without the risk of the stock market.

Based on your profile, I've found that you might qualify for a strategy that could provide significant tax-free retirement income while also protecting your family with a death benefit.

I'd love to share a personalized analysis I've prepared for you. Please give me a call back when you have a moment.

Again, this is {AGENT_INFO['name']}. I look forward to speaking with you soon. Thank you!"
    """
    return script.strip()

def direct_contact_instructions():
    """Provide instructions for directly contacting Paul Edwards"""
    print("=" * 80)
    print("DIRECT CONTACT INSTRUCTIONS FOR PAUL EDWARDS")
    print("=" * 80)
    
    print("\n1. SMS INSTRUCTIONS")
    print("-" * 80)
    print("Send the following text message to Paul Edwards:")
    print("\nTo: " + PAUL_EDWARDS["primary_phone"])
    print("\nMessage:")
    print(generate_sms_script())
    
    print("\n2. CALL INSTRUCTIONS")
    print("-" * 80)
    print("Call Paul Edwards and use the following script:")
    print("\nCall: " + PAUL_EDWARDS["primary_phone"])
    print("\nScript:")
    print(generate_call_script())
    
    print("\n3. VOICEMAIL INSTRUCTIONS")
    print("-" * 80)
    print("If Paul doesn't answer, leave the following voicemail:")
    print("\nVoicemail Script:")
    print(generate_voicemail_script())
    
    print("\n" + "=" * 80)
    print("END OF DIRECT CONTACT INSTRUCTIONS")
    print("=" * 80)

if __name__ == "__main__":
    print("This script provides instructions for directly contacting Paul Edwards.")
    direct_contact_instructions()
