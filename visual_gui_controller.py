#!/usr/bin/env python3
"""
Visual GUI Controller - UI-TARS Style
=====================================

Advanced visual GUI control system that provides:
- Screen analysis and element detection
- Visual-based interaction (like UI-TARS)
- Computer vision for UI automation
- Real-time screen monitoring
"""

import asyncio
import json
import logging
import time
import base64
import cv2
import numpy as np
from typing import Dict, List, Any, Optional, Tuple
from pathlib import Path
import pyautogui
import subprocess
import os
from dataclasses import dataclass

try:
    import pytesseract
    OCR_AVAILABLE = True
except ImportError:
    OCR_AVAILABLE = False

try:
    from PIL import Image, ImageDraw, ImageFont
    PIL_AVAILABLE = True
except ImportError:
    PIL_AVAILABLE = False

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Disable pyautogui failsafe
pyautogui.FAILSAFE = False

@dataclass
class UIElement:
    """Represents a UI element detected on screen"""
    type: str
    bbox: Tuple[int, int, int, int]  # x1, y1, x2, y2
    center: Tuple[int, int]
    confidence: float
    text: str = ""
    attributes: Dict[str, Any] = None

class ScreenAnalyzer:
    """Analyzes screen content and detects UI elements"""
    
    def __init__(self):
        self.last_screenshot = None
        self.element_cache = {}
        self.screen_size = pyautogui.size()
        
    async def capture_screen(self, region: Optional[Tuple[int, int, int, int]] = None) -> np.ndarray:
        """Capture screen with optional region"""
        try:
            if region:
                screenshot = pyautogui.screenshot(region=region)
            else:
                screenshot = pyautogui.screenshot()
            
            self.last_screenshot = cv2.cvtColor(np.array(screenshot), cv2.COLOR_RGB2BGR)
            return self.last_screenshot
        except Exception as e:
            logger.error(f"Screen capture failed: {e}")
            return np.array([])
    
    async def detect_buttons(self, screenshot: Optional[np.ndarray] = None) -> List[UIElement]:
        """Detect button-like elements using computer vision"""
        if screenshot is None:
            screenshot = await self.capture_screen()
        
        if screenshot.size == 0:
            return []
        
        elements = []
        gray = cv2.cvtColor(screenshot, cv2.COLOR_BGR2GRAY)
        
        # Method 1: Edge detection for rectangular buttons
        edges = cv2.Canny(gray, 50, 150)
        contours, _ = cv2.findContours(edges, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        
        for contour in contours:
            area = cv2.contourArea(contour)
            if 500 < area < 50000:  # Reasonable button sizes
                x, y, w, h = cv2.boundingRect(contour)
                aspect_ratio = w / h
                
                if 0.3 < aspect_ratio < 8:  # Button-like aspect ratio
                    elements.append(UIElement(
                        type="button",
                        bbox=(x, y, x + w, y + h),
                        center=(x + w // 2, y + h // 2),
                        confidence=0.7,
                        attributes={"area": area, "aspect_ratio": aspect_ratio}
                    ))
        
        # Method 2: Template matching for common button patterns
        button_templates = self._get_button_templates()
        for template_name, template in button_templates.items():
            matches = cv2.matchTemplate(gray, template, cv2.TM_CCOEFF_NORMED)
            locations = np.where(matches >= 0.6)
            
            for pt in zip(*locations[::-1]):
                h, w = template.shape
                elements.append(UIElement(
                    type="button",
                    bbox=(pt[0], pt[1], pt[0] + w, pt[1] + h),
                    center=(pt[0] + w // 2, pt[1] + h // 2),
                    confidence=matches[pt[1], pt[0]],
                    attributes={"template": template_name}
                ))
        
        return self._filter_overlapping_elements(elements)
    
    async def detect_text_elements(self, screenshot: Optional[np.ndarray] = None) -> List[UIElement]:
        """Detect text elements using OCR"""
        if not OCR_AVAILABLE:
            return []
        
        if screenshot is None:
            screenshot = await self.capture_screen()
        
        if screenshot.size == 0:
            return []
        
        elements = []
        
        try:
            # Use OCR to detect text
            data = pytesseract.image_to_data(screenshot, output_type=pytesseract.Output.DICT)
            
            for i in range(len(data['text'])):
                text = data['text'][i].strip()
                if text and int(data['conf'][i]) > 30:
                    x, y, w, h = data['left'][i], data['top'][i], data['width'][i], data['height'][i]
                    
                    elements.append(UIElement(
                        type="text",
                        bbox=(x, y, x + w, y + h),
                        center=(x + w // 2, y + h // 2),
                        confidence=data['conf'][i] / 100.0,
                        text=text,
                        attributes={"font_size": h}
                    ))
        
        except Exception as e:
            logger.error(f"OCR detection failed: {e}")
        
        return elements
    
    async def detect_input_fields(self, screenshot: Optional[np.ndarray] = None) -> List[UIElement]:
        """Detect input fields and text boxes"""
        if screenshot is None:
            screenshot = await self.capture_screen()
        
        if screenshot.size == 0:
            return []
        
        elements = []
        gray = cv2.cvtColor(screenshot, cv2.COLOR_BGR2GRAY)
        
        # Detect rectangular regions that might be input fields
        edges = cv2.Canny(gray, 30, 100)
        contours, _ = cv2.findContours(edges, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        
        for contour in contours:
            area = cv2.contourArea(contour)
            if 1000 < area < 100000:  # Input field size range
                x, y, w, h = cv2.boundingRect(contour)
                aspect_ratio = w / h
                
                # Input fields are typically wider than they are tall
                if aspect_ratio > 2 and h > 20:
                    elements.append(UIElement(
                        type="input",
                        bbox=(x, y, x + w, y + h),
                        center=(x + w // 2, y + h // 2),
                        confidence=0.6,
                        attributes={"area": area, "aspect_ratio": aspect_ratio}
                    ))
        
        return elements
    
    async def analyze_full_screen(self) -> Dict[str, Any]:
        """Perform comprehensive screen analysis"""
        screenshot = await self.capture_screen()
        
        if screenshot.size == 0:
            return {"error": "Failed to capture screen"}
        
        # Detect all element types
        buttons = await self.detect_buttons(screenshot)
        text_elements = await self.detect_text_elements(screenshot)
        input_fields = await self.detect_input_fields(screenshot)
        
        analysis = {
            "timestamp": time.time(),
            "screen_size": self.screen_size,
            "elements": {
                "buttons": [self._element_to_dict(elem) for elem in buttons],
                "text": [self._element_to_dict(elem) for elem in text_elements],
                "inputs": [self._element_to_dict(elem) for elem in input_fields]
            },
            "total_elements": len(buttons) + len(text_elements) + len(input_fields),
            "screenshot_base64": base64.b64encode(cv2.imencode('.png', screenshot)[1]).decode()
        }
        
        return analysis
    
    def _get_button_templates(self) -> Dict[str, np.ndarray]:
        """Get button templates for template matching"""
        # Create simple button templates
        templates = {}
        
        # Standard button template
        button_template = np.zeros((30, 100), dtype=np.uint8)
        cv2.rectangle(button_template, (2, 2), (98, 28), 255, 2)
        templates["standard_button"] = button_template
        
        # Round button template
        round_template = np.zeros((40, 40), dtype=np.uint8)
        cv2.circle(round_template, (20, 20), 18, 255, 2)
        templates["round_button"] = round_template
        
        return templates
    
    def _filter_overlapping_elements(self, elements: List[UIElement]) -> List[UIElement]:
        """Filter out overlapping elements, keeping the one with highest confidence"""
        if not elements:
            return []
        
        # Sort by confidence (highest first)
        elements.sort(key=lambda x: x.confidence, reverse=True)
        
        filtered = []
        for elem in elements:
            overlap = False
            for existing in filtered:
                if self._elements_overlap(elem, existing):
                    overlap = True
                    break
            
            if not overlap:
                filtered.append(elem)
        
        return filtered
    
    def _elements_overlap(self, elem1: UIElement, elem2: UIElement, threshold: float = 0.5) -> bool:
        """Check if two elements overlap significantly"""
        x1_1, y1_1, x2_1, y2_1 = elem1.bbox
        x1_2, y1_2, x2_2, y2_2 = elem2.bbox
        
        # Calculate intersection
        x1_i = max(x1_1, x1_2)
        y1_i = max(y1_1, y1_2)
        x2_i = min(x2_1, x2_2)
        y2_i = min(y2_1, y2_2)
        
        if x2_i <= x1_i or y2_i <= y1_i:
            return False
        
        intersection_area = (x2_i - x1_i) * (y2_i - y1_i)
        area1 = (x2_1 - x1_1) * (y2_1 - y1_1)
        area2 = (x2_2 - x1_2) * (y2_2 - y1_2)
        
        overlap_ratio = intersection_area / min(area1, area2)
        return overlap_ratio > threshold
    
    def _element_to_dict(self, element: UIElement) -> Dict[str, Any]:
        """Convert UIElement to dictionary"""
        return {
            "type": element.type,
            "bbox": element.bbox,
            "center": element.center,
            "confidence": element.confidence,
            "text": element.text,
            "attributes": element.attributes or {}
        }

class VisualGUIController:
    """Main controller for visual GUI interactions"""
    
    def __init__(self):
        self.analyzer = ScreenAnalyzer()
        self.action_history = []
        self.is_monitoring = False
        self.monitor_task = None
        
    async def click_at_coordinates(self, x: int, y: int) -> bool:
        """Click at specific coordinates"""
        try:
            pyautogui.click(x, y)
            self.action_history.append({
                "action": "click",
                "coordinates": (x, y),
                "timestamp": time.time()
            })
            logger.info(f"✅ Clicked at ({x}, {y})")
            return True
        except Exception as e:
            logger.error(f"Click failed: {e}")
            return False
    
    async def click_element_by_text(self, text: str) -> bool:
        """Click an element containing specific text"""
        try:
            elements = await self.analyzer.detect_text_elements()
            
            for element in elements:
                if text.lower() in element.text.lower():
                    return await self.click_at_coordinates(*element.center)
            
            logger.warning(f"Element with text '{text}' not found")
            return False
            
        except Exception as e:
            logger.error(f"Click by text failed: {e}")
            return False
    
    async def type_text(self, text: str, delay: float = 0.1) -> bool:
        """Type text with natural delay"""
        try:
            pyautogui.typewrite(text, interval=delay)
            self.action_history.append({
                "action": "type",
                "text": text,
                "timestamp": time.time()
            })
            logger.info(f"✅ Typed: {text}")
            return True
        except Exception as e:
            logger.error(f"Type failed: {e}")
            return False
    
    async def scroll(self, direction: str = "down", clicks: int = 3) -> bool:
        """Scroll in specified direction"""
        try:
            if direction == "down":
                pyautogui.scroll(-clicks)
            elif direction == "up":
                pyautogui.scroll(clicks)
            
            self.action_history.append({
                "action": "scroll",
                "direction": direction,
                "clicks": clicks,
                "timestamp": time.time()
            })
            logger.info(f"✅ Scrolled {direction}")
            return True
        except Exception as e:
            logger.error(f"Scroll failed: {e}")
            return False
    
    async def press_key(self, key: str) -> bool:
        """Press a keyboard key"""
        try:
            pyautogui.press(key)
            self.action_history.append({
                "action": "key_press",
                "key": key,
                "timestamp": time.time()
            })
            logger.info(f"✅ Pressed key: {key}")
            return True
        except Exception as e:
            logger.error(f"Key press failed: {e}")
            return False
    
    async def execute_visual_command(self, command: str) -> Dict[str, Any]:
        """Execute a visual command using natural language"""
        command_lower = command.lower().strip()
        
        try:
            # Click commands
            if "click" in command_lower:
                if "button" in command_lower or "link" in command_lower:
                    # Find and click buttons
                    analysis = await self.analyzer.analyze_full_screen()
                    buttons = analysis["elements"]["buttons"]
                    
                    if buttons:
                        # Click the first button found
                        button = buttons[0]
                        success = await self.click_at_coordinates(*button["center"])
                        return {
                            "success": success,
                            "action": "click_button",
                            "message": f"Clicked button at {button['center']}" if success else "Failed to click button"
                        }
                    else:
                        return {"success": False, "action": "click_button", "message": "No buttons found"}
                
                # Extract text to click
                words = command.split()
                for i, word in enumerate(words):
                    if word.lower() == "click" and i + 1 < len(words):
                        text_to_click = " ".join(words[i + 1:])
                        success = await self.click_element_by_text(text_to_click)
                        return {
                            "success": success,
                            "action": "click_text",
                            "text": text_to_click,
                            "message": f"Clicked '{text_to_click}'" if success else f"Failed to click '{text_to_click}'"
                        }
            
            # Type commands
            elif "type" in command_lower or "enter" in command_lower:
                # Extract text to type
                if '"' in command:
                    start = command.find('"') + 1
                    end = command.find('"', start)
                    if end > start:
                        text = command[start:end]
                        success = await self.type_text(text)
                        return {
                            "success": success,
                            "action": "type",
                            "text": text,
                            "message": f"Typed: {text}" if success else f"Failed to type: {text}"
                        }
            
            # Scroll commands
            elif "scroll" in command_lower:
                direction = "down"
                if "up" in command_lower:
                    direction = "up"
                
                success = await self.scroll(direction)
                return {
                    "success": success,
                    "action": "scroll",
                    "direction": direction,
                    "message": f"Scrolled {direction}" if success else f"Failed to scroll {direction}"
                }
            
            # Key press commands
            elif "press" in command_lower:
                keys = ["enter", "escape", "tab", "space", "backspace", "delete"]
                for key in keys:
                    if key in command_lower:
                        success = await self.press_key(key)
                        return {
                            "success": success,
                            "action": "key_press",
                            "key": key,
                            "message": f"Pressed {key}" if success else f"Failed to press {key}"
                        }
            
            # Analyze screen command
            elif "analyze" in command_lower or "scan" in command_lower:
                analysis = await self.analyzer.analyze_full_screen()
                return {
                    "success": True,
                    "action": "analyze",
                    "analysis": analysis,
                    "message": f"Found {analysis['total_elements']} UI elements"
                }
            
            else:
                return {
                    "success": False,
                    "action": "unknown",
                    "message": f"Unknown visual command: {command}"
                }
                
        except Exception as e:
            logger.error(f"Visual command execution failed: {e}")
            return {
                "success": False,
                "action": "error",
                "message": f"Error executing visual command: {str(e)}"
            }
    
    async def start_monitoring(self, interval: float = 1.0):
        """Start monitoring screen for changes"""
        if self.is_monitoring:
            return
        
        self.is_monitoring = True
        self.monitor_task = asyncio.create_task(self._monitor_loop(interval))
        logger.info("✅ Started screen monitoring")
    
    async def stop_monitoring(self):
        """Stop screen monitoring"""
        self.is_monitoring = False
        if self.monitor_task:
            self.monitor_task.cancel()
            try:
                await self.monitor_task
            except asyncio.CancelledError:
                pass
        logger.info("✅ Stopped screen monitoring")
    
    async def _monitor_loop(self, interval: float):
        """Monitor loop for screen changes"""
        last_analysis = None
        
        while self.is_monitoring:
            try:
                current_analysis = await self.analyzer.analyze_full_screen()
                
                if last_analysis and self._significant_change(last_analysis, current_analysis):
                    logger.info("🔄 Significant screen change detected")
                
                last_analysis = current_analysis
                await asyncio.sleep(interval)
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Monitor loop error: {e}")
                await asyncio.sleep(interval)
    
    def _significant_change(self, old_analysis: Dict, new_analysis: Dict) -> bool:
        """Detect if there's a significant change between analyses"""
        old_count = old_analysis.get("total_elements", 0)
        new_count = new_analysis.get("total_elements", 0)
        
        # Consider it significant if element count changes by more than 20%
        if old_count > 0:
            change_ratio = abs(new_count - old_count) / old_count
            return change_ratio > 0.2
        
        return new_count > 0

# Global instance for API access
visual_controller = VisualGUIController()

async def initialize_visual_gui():
    """Initialize visual GUI controller"""
    try:
        logger.info("✅ Visual GUI controller initialized")
        return True
    except Exception as e:
        logger.error(f"Visual GUI initialization error: {e}")
        return False

async def execute_visual_command(command: str) -> Dict[str, Any]:
    """Execute a visual GUI command"""
    return await visual_controller.execute_visual_command(command)

if __name__ == "__main__":
    async def test_visual_gui():
        """Test visual GUI system"""
        print("🧪 Testing Visual GUI Controller")
        
        if await initialize_visual_gui():
            print("✅ Visual GUI initialized")
            
            # Test commands
            commands = [
                "analyze screen",
                "click button",
                "type \"hello world\"",
                "scroll down",
                "press enter"
            ]
            
            for cmd in commands:
                print(f"\n🔄 Executing: {cmd}")
                result = await execute_visual_command(cmd)
                print(f"Result: {result['message']}")
                await asyncio.sleep(2)
        else:
            print("❌ Visual GUI initialization failed")
    
    asyncio.run(test_visual_gui())
