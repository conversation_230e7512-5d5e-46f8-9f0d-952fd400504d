"""
Send Audio Email to <PERSON>

This script sends an email to <PERSON> with the Eleven Labs audio file attached.
"""

import os
import smtplib
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
from email.mime.audio import MIMEAudio
import ssl
import getpass
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# <PERSON> contact information
PAUL_EDWARDS = {
    "first_name": "<PERSON>",
    "last_name": "<PERSON>",
    "email": "<EMAIL>",
    "primary_phone": "+17722089646",
    "secondary_phone": "+17725395908"
}

# Agent information
AGENT_INFO = {
    "name": "<PERSON>",
    "agency": "Flo Faction Insurance",
    "email": "<EMAIL>",
    "website": "https://www.flofaction.com/insurance",
}

def send_audio_email():
    """Send an email to <PERSON> with the audio file attached"""
    print("=" * 80)
    print("SENDING AUDIO EMAIL TO PAUL EDWARDS")
    print("=" * 80)
    
    # Check if the audio file exists
    audio_file = "paul_edwards_voicemail.mp3"
    if not os.path.exists(audio_file):
        print(f"Error: Audio file '{audio_file}' not found.")
        print("Please run 'generate_elevenlabs_audio.py' first to generate the audio file.")
        return False
    
    # Get email credentials
    sender_email = input("Enter your email address: ")
    password = getpass.getpass("Enter your email password: ")
    
    # Create email content
    subject = "Personal Message from Sandra Smith - Flo Faction Insurance"
    body = f"""
Dear {PAUL_EDWARDS['first_name']},

I hope this email finds you well. My name is {AGENT_INFO['name']} with {AGENT_INFO['agency']}, and I specialize in helping people like you create tax-free retirement income without the risk of the stock market.

I've attached a personal audio message for you. Please take a moment to listen to it when you have a chance.

Based on the information I have, I've prepared a personalized analysis that shows how you could potentially:

1. Generate approximately $5,000 per month in tax-free retirement income
2. Build cash value that grows without the risk of market losses
3. Provide a death benefit of around $720,000 to protect your family
4. Create a financial strategy that offers both protection and growth potential

Would you be interested in a brief 30-minute conversation to review this analysis? If so, please call me or reply to this email with a good time to connect.

You can also learn more about our services by visiting our website at {AGENT_INFO['website']}.

Thank you for your time, and I look forward to speaking with you soon.

Best regards,
{AGENT_INFO['name']}
{AGENT_INFO['agency']}
{AGENT_INFO['website']}
    """
    
    # Create message
    message = MIMEMultipart()
    message["From"] = sender_email
    message["To"] = PAUL_EDWARDS["email"]
    message["Subject"] = subject
    
    # Attach body
    message.attach(MIMEText(body, "plain"))
    
    # Attach audio file
    with open(audio_file, "rb") as f:
        audio_attachment = MIMEAudio(f.read(), _subtype="mp3")
    
    audio_attachment.add_header(
        "Content-Disposition",
        f"attachment; filename={audio_file}"
    )
    
    message.attach(audio_attachment)
    
    try:
        # Create secure connection and send email
        context = ssl.create_default_context()
        
        # Determine SMTP server based on email domain
        if "@gmail.com" in sender_email.lower():
            smtp_server = "smtp.gmail.com"
            smtp_port = 587
        elif "@outlook.com" in sender_email.lower() or "@hotmail.com" in sender_email.lower():
            smtp_server = "smtp.office365.com"
            smtp_port = 587
        elif "@yahoo.com" in sender_email.lower():
            smtp_server = "smtp.mail.yahoo.com"
            smtp_port = 587
        else:
            smtp_server = input("Enter your SMTP server (e.g., smtp.gmail.com): ")
            smtp_port = int(input("Enter your SMTP port (e.g., 587): "))
        
        with smtplib.SMTP(smtp_server, smtp_port) as server:
            server.starttls(context=context)
            server.login(sender_email, password)
            server.sendmail(sender_email, PAUL_EDWARDS["email"], message.as_string())
        
        print(f"Email with audio attachment sent successfully to {PAUL_EDWARDS['email']}")
        return True
    except Exception as e:
        print(f"Error sending email: {str(e)}")
        return False

if __name__ == "__main__":
    print("This script sends an email to Paul Edwards with the Eleven Labs audio file attached.")
    print(f"Recipient: {PAUL_EDWARDS['email']}")
    print("Note: You need to generate the audio file first using 'generate_elevenlabs_audio.py'.")
    
    proceed = input("Do you want to proceed? (yes/no): ")
    
    if proceed.lower() == "yes":
        send_audio_email()
    else:
        print("Operation cancelled.")
