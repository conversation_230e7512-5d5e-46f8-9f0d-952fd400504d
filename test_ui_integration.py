import asyncio
from core.ui_agent import UIAgent

class TestUIAgent(UIAgent):
    async def process_message(self, message: Dict) -> Dict:
        if message.get("action") == "test_ui":
            # Test UI capabilities
            element = await self.find_element("Submit button")
            if element:
                await self.click(element["x"], element["y"])
                await self.type_text("Test input")
                await self.scroll(100)
                return {"status": "success", "message": "UI test completed"}
        return {"status": "error", "message": "Unknown action"}

async def main():
    # Create and start UI test agent
    agent = TestUIAgent("ui_test")
    await agent.start()
    
    # Send test message
    await agent.send_message(
        "ui_test",
        {"action": "test_ui"}
    )
    
    # Wait for processing
    await asyncio.sleep(2)
    
    # Stop agent
    await agent.stop()

if __name__ == "__main__":
    asyncio.run(main())