"""
Documentation and classification of insurance products offered by carriers
"""

from dataclasses import dataclass
from typing import List, Dict, Optional

@dataclass
class InsuranceProduct:
    name: str
    carrier: str
    product_type: str
    description: str
    features: List[str]
    requirements: Dict[str, str]
    age_range: Dict[str, int]

# Mutual of Omaha Products
MOO_PRODUCTS = {
    "term_life_express": InsuranceProduct(
        name="Term Life Express",
        carrier="Mutual of Omaha",
        product_type="Index Universal Life Insurance",
        description="Indexed Universal Life Insurance that provides both death benefit protection and potential cash value accumulation",
        features=[
            "Flexible premium payments",
            "Cash value accumulation potential",
            "Death benefit protection",
            "Index crediting options",
            "Multiple index options available"
        ],
        requirements={
            "age": "18-65",
            "health_questions": "Yes",
            "medical_exam": "No"
        },
        age_range={"min": 18, "max": 65}
    ),
    
    "guaranteed_advantage": InsuranceProduct(
        name="Guaranteed Advantage",
        carrier="Mutual of Omaha",
        product_type="Medicare Supplement Insurance",
        description="Medicare Supplement insurance that helps cover out-of-pocket costs not paid by Original Medicare",
        features=[
            "Guaranteed renewable",
            "Nationwide coverage",
            "No network restrictions",
            "Standardized benefits"
        ],
        requirements={
            "age": "65+",
            "medicare": "Must have Medicare Parts A & B",
            "medical_exam": "No"
        },
        age_range={"min": 65, "max": 99}
    ),
    
    "income_advantage": InsuranceProduct(
        name="Income Advantage",
        carrier="Mutual of Omaha",
        product_type="Index Universal Life Insurance",
        description="Index Universal Life Insurance focused on income potential and death benefit protection",
        features=[
            "Tax-advantaged cash value growth",
            "Flexible premium payments",
            "Multiple index options",
            "Death benefit protection",
            "Optional riders available"
        ],
        requirements={
            "age": "18-85",
            "health_questions": "Yes",
            "medical_exam": "Yes"
        },
        age_range={"min": 18, "max": 85}
    )
}

# FFL/Trident Life Products
FFL_PRODUCTS = {
    "mortgage_protection": InsuranceProduct(
        name="Mortgage Protection",
        carrier="FFL Trident",
        product_type="Term Life Insurance",
        description="Term life insurance specifically designed to protect mortgage payments",
        features=[
            "Level death benefit",
            "Level premiums",
            "Coverage matches mortgage term",
            "No medical exam options available"
        ],
        requirements={
            "age": "18-65",
            "mortgage": "Active mortgage",
            "medical_exam": "Varies"
        },
        age_range={"min": 18, "max": 65}
    ),
    
    "final_expense": InsuranceProduct(
        name="Final Expense",
        carrier="FFL Trident",
        product_type="Whole Life Insurance",
        description="Whole life insurance designed to cover funeral and final expenses",
        features=[
            "Guaranteed level premiums",
            "Builds cash value",
            "Coverage never expires",
            "Quick approval process"
        ],
        requirements={
            "age": "50-85",
            "health_questions": "Yes",
            "medical_exam": "No"
        },
        age_range={"min": 50, "max": 85}
    )
}

# IUL Products
IUL_PRODUCTS = {
    "life_protection_advantage": {
        "carrier": "Mutual of Omaha",
        "name": "Life Protection Advantage IUL",
        "description": "Index Universal Life insurance designed for death benefit protection with cash value growth potential",
        "min_age": 18,
        "max_age": 85,
        "features": [
            "Guaranteed death benefit protection (subject to premium requirements)",
            "Multiple index interest crediting strategies",
            "Tax-advantaged cash value accumulation",
            "Access to cash value via policy loans"
        ],
        "rating": 9.2,
        "target_market": "Young to middle-aged adults seeking protection and growth"
    },
    "income_advantage_iul": {
        "carrier": "Mutual of Omaha",
        "name": "Income Advantage IUL",
        "description": "Index Universal Life insurance focusing on cash value accumulation and income potential",
        "min_age": 18,
        "max_age": 80,
        "features": [
            "Enhanced cash value accumulation",
            "Tax-free retirement income potential (via policy loans)",
            "Multiple index options",
            "Chronic illness and terminal illness benefits"
        ],
        "rating": 9.5,
        "target_market": "Young professionals seeking tax-efficient retirement income"
    },
    "max_accumulator_plus": {
        "carrier": "American General",
        "name": "Max Accumulator+ IUL",
        "description": "High cash value accumulation IUL with strong index crediting potential",
        "min_age": 18,
        "max_age": 75,
        "features": [
            "Flexible premium design",
            "Multiple index account options",
            "Strong accumulation potential",
            "Income for Life rider available"
        ],
        "rating": 9.3,
        "target_market": "Growth-oriented individuals seeking maximum cash value"
    },
    "premier_index_universal_life": {
        "carrier": "Pacific Life",
        "name": "Pacific Discovery Premier IUL",
        "description": "Flexible premium IUL balancing protection and cash value growth",
        "min_age": 18,
        "max_age": 85,
        "features": [
            "No-lapse guarantee options",
            "Diversified index account options",
            "Tax-advantaged cash accumulation",
            "Optional return of premium death benefit"
        ],
        "rating": 8.9,
        "target_market": "Balanced approach for both protection and accumulation"
    },
    "secure_lifetime_iul": {
        "carrier": "Nationwide",
        "name": "Nationwide YourLife Secure Lifetime IUL",
        "description": "IUL focusing on guaranteed protection with moderate growth potential",
        "min_age": 20,
        "max_age": 80,
        "features": [
            "Strong death benefit guarantees",
            "Moderate cash value growth potential",
            "Simple index strategy options",
            "Overloan protection feature"
        ],
        "rating": 8.7,
        "target_market": "Conservative individuals prioritizing guarantees"
    }
}

def get_product_info(carrier: str, product_name: str) -> Optional[InsuranceProduct]:
    """Get detailed information about a specific insurance product"""
    products = {
        "mutual_of_omaha": MOO_PRODUCTS,
        "ffl_trident": FFL_PRODUCTS
    }
    
    carrier = carrier.lower().replace(" ", "_")
    product_name = product_name.lower().replace(" ", "_")
    
    if carrier in products:
        return products[carrier].get(product_name)
    return None

def list_carrier_products(carrier: str) -> List[str]:
    """List all products available from a carrier"""
    products = {
        "mutual_of_omaha": MOO_PRODUCTS,
        "ffl_trident": FFL_PRODUCTS
    }
    
    carrier = carrier.lower().replace(" ", "_")
    if carrier in products:
        return list(products[carrier].keys())
    return []

def get_suitable_products(age: int, coverage_type: str) -> List[InsuranceProduct]:
    """Find suitable products based on age and coverage type"""
    suitable = []
    
    # Check MOO products
    for product in MOO_PRODUCTS.values():
        if (product.age_range['min'] <= age <= product.age_range['max'] and
            coverage_type.lower() in product.product_type.lower()):
            suitable.append(product)
            
    # Check FFL products
    for product in FFL_PRODUCTS.values():
        if (product.age_range['min'] <= age <= product.age_range['max'] and
            coverage_type.lower() in product.product_type.lower()):
            suitable.append(product)
            
    return suitable

def get_iul_products():
    """Get all IUL products"""
    return IUL_PRODUCTS

def get_iul_carriers():
    """Get all carriers offering IUL products with their product offerings"""
    carriers = {}
    for product_id, product in IUL_PRODUCTS.items():
        carrier = product["carrier"]
        if carrier not in carriers:
            carriers[carrier] = []
        carriers[carrier].append(product_id)
    return carriers

def find_suitable_iul_products(age, budget=None, health_class="Preferred"):
    """Find suitable IUL products based on age, budget, and health class"""
    suitable = []
    
    for product_id, product in IUL_PRODUCTS.items():
        if product["min_age"] <= age <= product["max_age"]:
            suitable.append({
                "product_id": product_id,
                "carrier": product["carrier"],
                "name": product["name"],
                "description": product["description"],
                "rating": product["rating"],
                "features": product["features"]
            })
    
    # Sort by rating
    suitable.sort(key=lambda p: p["rating"], reverse=True)
    
    return suitable

if __name__ == "__main__":
    # Example usage
    print("\nMutual of Omaha Products:")
    moo_products = list_carrier_products("mutual_of_omaha")
    for product in moo_products:
        info = get_product_info("mutual_of_omaha", product)
        print(f"\n{info.name}:")
        print(f"Type: {info.product_type}")
        print(f"Description: {info.description}")
        print("Features:")
        for feature in info.features:
            print(f"- {feature}")
    
    print("\nFFL/Trident Products:")
    ffl_products = list_carrier_products("ffl_trident")
    for product in ffl_products:
        info = get_product_info("ffl_trident", product)
        print(f"\n{info.name}:")
        print(f"Type: {info.product_type}")
        print(f"Description: {info.description}")
        print("Features:")
        for feature in info.features:
            print(f"- {feature}")
            
    # Example: Find suitable products
    print("\nSuitable products for age 70 looking for Medicare Supplement:")
    suitable = get_suitable_products(70, "Medicare Supplement")
    for product in suitable:
        print(f"\n{product.carrier} - {product.name}")
        print(f"Type: {product.product_type}")