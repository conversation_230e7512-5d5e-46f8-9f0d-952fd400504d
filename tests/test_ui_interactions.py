import pytest
import asyncio
from datetime import datetime
from typing import Dict, Any
from core.social_media_agent import SocialMediaAgent
from core.customer_interaction_agent import CustomerInteractionAgent
from core.adaptive_ui_system import AdaptiveUISystem

@pytest.fixture
async def social_agent():
    agent = SocialMediaAgent("test_social_agent")
    await agent.initialize()
    yield agent
    # Cleanup
    await agent.stop()

@pytest.fixture
async def customer_agent():
    agent = CustomerInteractionAgent("test_customer_agent")
    await agent.initialize()
    yield agent
    await agent.stop()

@pytest.fixture
def adaptive_ui():
    return AdaptiveUISystem()

@pytest.mark.asyncio
async def test_social_media_interactions(social_agent):
    """Test social media interaction capabilities"""
    test_interaction = {
        "platform": "twitter",
        "type": "post",
        "content": "Test post content",
        "sentiment": "positive"
    }
    
    result = await social_agent.handle_social_interaction(
        test_interaction["platform"],
        test_interaction
    )
    assert result is True
    
    # Verify metrics
    metrics = social_agent.get_ui_performance_metrics()
    assert metrics["total_actions"] > 0
    assert metrics["success_rate_percent"] > 0

@pytest.mark.asyncio
async def test_customer_interaction_flow(customer_agent):
    """Test customer interaction handling"""
    test_request = {
        "customer_id": "test123",
        "type": "support",
        "platform": "web",
        "description": "Test support request"
    }
    
    result = await customer_agent.handle_customer_request(test_request)
    assert result is True
    
    # Verify customer context
    customer_type = await customer_agent._get_customer_type("test123")
    assert customer_type in ["new", "returning", "frequent"]

@pytest.mark.asyncio
async def test_ui_adaptation(adaptive_ui):
    """Test UI adaptation behavior"""
    test_context = {
        "platform": "web",
        "interaction_type": "form",
        "user_type": "new",
        "time_of_day": 12
    }
    
    base_actions = [
        {"action": "click", "params": {"x": 100, "y": 100}},
        {"action": "type", "params": {"text": "test"}}
    ]
    
    # Record a few interactions
    for success in [True, False, True]:
        await adaptive_ui.record_interaction(test_context, base_actions, success)
    
    # Get adapted actions
    adapted_actions = await adaptive_ui.get_adapted_actions(test_context, base_actions)
    assert len(adapted_actions) >= len(base_actions)

@pytest.mark.asyncio
async def test_error_recovery(customer_agent):
    """Test error recovery in UI interactions"""
    test_request = {
        "customer_id": "test456",
        "type": "purchase",
        "platform": "mobile",
        "product_id": "invalid_id"
    }
    
    # Force an error by using invalid data
    result = await customer_agent.handle_customer_request(test_request)
    assert result is False
    
    # Verify error handling
    metrics = customer_agent.get_ui_performance_metrics()
    assert metrics["error_count"] > 0

@pytest.mark.asyncio
async def test_multi_platform_support(social_agent):
    """Test handling of multiple social media platforms"""
    platforms = ["twitter", "facebook", "linkedin"]
    
    for platform in platforms:
        result = await social_agent.handle_social_interaction(
            platform,
            {
                "type": "post",
                "content": f"Test post on {platform}",
                "platform": platform
            }
        )
        assert result is True
        
        # Verify platform-specific metrics
        metrics = social_agent.get_ui_performance_metrics()
        assert metrics["total_actions"] > 0

@pytest.mark.asyncio
async def test_concurrent_interactions(customer_agent):
    """Test handling of concurrent customer interactions"""
    test_requests = [
        {
            "customer_id": f"test_{i}",
            "type": "support",
            "platform": "web",
            "description": f"Test request {i}"
        }
        for i in range(5)
    ]
    
    # Process requests concurrently
    tasks = [
        customer_agent.handle_customer_request(request)
        for request in test_requests
    ]
    
    results = await asyncio.gather(*tasks)
    assert all(results)
    
    # Verify system handled concurrent load
    metrics = customer_agent.get_ui_performance_metrics()
    assert metrics["total_actions"] >= len(test_requests)