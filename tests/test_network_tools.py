import pytest
import asyncio
from unittest.mock import Mo<PERSON>, AsyncMock, patch
from core.network_tools_manager import NetworkToolsManager, NetworkStats
from core.base_agent import BaseAgent

@pytest.fixture
def network_tools():
    return NetworkToolsManager()

@pytest.fixture
def base_agent():
    agent = BaseAgent("TestAgent")
    return agent

@pytest.mark.asyncio
async def test_connectivity_check(network_tools):
    with patch('socket.create_connection') as mock_connect:
        mock_connect.return_value = True
        result = await network_tools.check_connectivity()
        assert result is True
        
        mock_connect.side_effect = OSError()
        result = await network_tools.check_connectivity()
        assert result is False

@pytest.mark.asyncio
async def test_latency_measurement(network_tools):
    mock_ping_output = """
    PING ******* (*******): 56 data bytes
    64 bytes from *******: icmp_seq=0 ttl=116 time=12.934 ms
    64 bytes from *******: icmp_seq=1 ttl=116 time=11.372 ms
    64 bytes from *******: icmp_seq=2 ttl=116 time=10.231 ms
    64 bytes from *******: icmp_seq=3 ttl=116 time=11.892 ms
    
    --- ******* ping statistics ---
    4 packets transmitted, 4 packets received, 0.0% packet loss
    round-trip min/avg/max/stddev = 10.231/11.607/12.934/0.998 ms
    """
    
    mock_proc = AsyncMock()
    mock_proc.communicate.return_value = (mock_ping_output.encode(), b"")
    
    with patch('asyncio.create_subprocess_shell', return_value=mock_proc) as mock_create:
        stats = await network_tools.measure_latency()
        assert isinstance(stats, NetworkStats)
        assert stats.packet_loss == 0.0
        assert 10.0 <= stats.latency <= 13.0

@pytest.mark.asyncio
async def test_connection_management(network_tools):
    mock_client = AsyncMock()
    mock_client.aclose = AsyncMock()
    
    with patch('httpx.AsyncClient', return_value=mock_client):
        # Create connection
        conn_id = await network_tools.create_connection("http://example.com")
        assert conn_id in network_tools._active_connections
        
        # Close connection
        await network_tools.close_connection(conn_id)
        assert conn_id not in network_tools._active_connections
        mock_client.aclose.assert_awaited_once()

@pytest.mark.asyncio
async def test_agent_network_integration(base_agent):
    mock_client = AsyncMock()
    mock_response = Mock(
        status_code=200,
        headers={"Content-Type": "text/plain"},
        content=b"Hello World"
    )
    mock_client.request = AsyncMock(return_value=mock_response)
    
    with patch('httpx.AsyncClient', return_value=mock_client):
        # Create connection through agent
        conn_name = "test_conn"
        await base_agent.create_connection(conn_name, "http://example.com")
        assert conn_name in base_agent.active_connections
        
        # Make request through agent
        status, headers, content = await base_agent.make_request(
            conn_name,
            "GET",
            "/test"
        )
        assert status == 200
        assert headers["Content-Type"] == "text/plain"
        assert content == b"Hello World"
        mock_client.request.assert_awaited_once()