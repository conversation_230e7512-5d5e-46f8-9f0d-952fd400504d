import pytest
import asyncio
from unittest.mock import Mock, patch
import numpy as np
from core.local_tools_manager import LocalToolsManager
from core.local_llm_manager import LocalLLMManager
from core.base_agent import BaseAgent

@pytest.fixture
def local_tools():
    return LocalToolsManager()

@pytest.fixture
def local_llm():
    return LocalLLMManager()

@pytest.fixture
def base_agent():
    agent = BaseAgent("TestAgent")
    return agent

@pytest.mark.asyncio
async def test_screen_capture(local_tools):
    mock_array = np.zeros((100, 100, 3), dtype=np.uint8)
    with patch('pyautogui.screenshot') as mock_screenshot:
        mock_screenshot.return_value = Mock(
            __array__=Mock(return_value=mock_array)
        )
        
        result = await local_tools.capture_screen()
        assert isinstance(result, np.ndarray)
        assert result.shape == (100, 100, 3)

@pytest.mark.asyncio
async def test_find_on_screen(local_tools):
    mock_array = np.zeros((100, 100, 3), dtype=np.uint8)
    with patch('pyautogui.screenshot') as mock_screenshot:
        mock_screenshot.return_value = Mock(
            __array__=Mock(return_value=mock_array)
        )
        
        with patch('cv2.imread') as mock_imread:
            mock_imread.return_value = np.zeros((10, 10, 3), dtype=np.uint8)
            
            result = await local_tools.find_on_screen("dummy_template.png")
            assert result is None  # Should not find match in zero array

@pytest.mark.asyncio
async def test_local_llm_loading(local_llm):
    # Test with a dummy model path
    success = await local_llm.load_model("test-model", "dummy/path")
    assert not success  # Should fail as path doesn't exist

@pytest.mark.asyncio
async def test_base_agent_initialization(base_agent):
    assert isinstance(base_agent.local_tools, LocalToolsManager)
    assert isinstance(base_agent.local_llm, LocalLLMManager)
    
@pytest.mark.asyncio
async def test_screen_actions(base_agent):
    mock_array = np.zeros((100, 100, 3), dtype=np.uint8)
    with patch('core.local_tools_manager.LocalToolsManager.capture_screen') as mock_capture:
        mock_capture.return_value = mock_array
        
        result = await base_agent.perform_screen_action("capture")
        assert isinstance(result, np.ndarray)

@pytest.mark.asyncio
async def test_agent_cleanup(base_agent):
    # Add a mock model to test cleanup
    base_agent.loaded_models["test-model"] = True
    
    base_agent.cleanup()
    assert not base_agent.loaded_models["test-model"]