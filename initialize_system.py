import asyncio
import logging
import os
import json
from datetime import datetime
from pathlib import Path
import sys

from secure_credentials import SecureCredentialsManager
from client_template import ClientManager
from insurance_carriers import CarrierManager
from agent_coordinator import AgentCoordinator
from knowledge_management import KnowledgeManager

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('initialization.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class SystemInitializer:
    """Initialize and verify all system components"""
    
    def __init__(self):
        self.base_path = Path.cwd()
        self.required_directories = [
            'data/knowledge_base',
            'data/client_records',
            'data/credentials',
            'logs',
            'templates'
        ]
        self.status = {}

    async def initialize(self):
        """Run all initialization steps"""
        try:
            logger.info("Starting system initialization...")
            
            # Create directory structure
            self._create_directories()
            
            # Initialize credential manager
            await self._init_credentials()
            
            # Initialize knowledge base
            await self._init_knowledge_base()
            
            # Initialize client manager
            await self._init_client_manager()
            
            # Initialize carrier manager
            await self._init_carrier_manager()
            
            # Initialize agent coordinator
            await self._init_agent_coordinator()
            
            # Save initialization status
            self._save_status()
            
            logger.info("System initialization completed successfully!")
            return True
            
        except Exception as e:
            logger.error(f"Initialization failed: {str(e)}")
            return False

    def _create_directories(self):
        """Create required directory structure"""
        logger.info("Creating directory structure...")
        try:
            for directory in self.required_directories:
                dir_path = self.base_path / directory
                dir_path.mkdir(parents=True, exist_ok=True)
            self.status['directories'] = 'created'
        except Exception as e:
            logger.error(f"Failed to create directories: {str(e)}")
            self.status['directories'] = 'failed'

    async def _init_credentials(self):
        """Initialize credential manager and verify access"""
        logger.info("Initializing credential manager...")
        try:
            creds_manager = SecureCredentialsManager()
            
            # Test credential storage and retrieval
            test_result = await self._test_credentials(creds_manager)
            
            self.status['credentials'] = {
                'status': 'initialized' if test_result else 'failed',
                'carriers_configured': test_result
            }
            
        except Exception as e:
            logger.error(f"Failed to initialize credentials: {str(e)}")
            self.status['credentials'] = {'status': 'failed', 'error': str(e)}

    async def _init_knowledge_base(self):
        """Initialize knowledge base system"""
        logger.info("Initializing knowledge base...")
        try:
            knowledge_manager = KnowledgeManager()
            
            # Add test knowledge entry
            test_entry = {
                "content": "Test initialization entry",
                "source": "system_init",
                "timestamp": datetime.now().isoformat()
            }
            
            knowledge_manager.add_knowledge(
                domain="system",
                content=test_entry["content"],
                metadata={"source": "initialization"},
                agent_id="system"
            )
            
            self.status['knowledge_base'] = 'initialized'
            
        except Exception as e:
            logger.error(f"Failed to initialize knowledge base: {str(e)}")
            self.status['knowledge_base'] = 'failed'

    async def _init_client_manager(self):
        """Initialize client management system"""
        logger.info("Initializing client manager...")
        try:
            client_manager = ClientManager()
            
            # Verify template system
            template = client_manager.get_template()
            if template:
                self.status['client_manager'] = 'initialized'
            else:
                raise Exception("Failed to get client template")
                
        except Exception as e:
            logger.error(f"Failed to initialize client manager: {str(e)}")
            self.status['client_manager'] = 'failed'

    async def _init_carrier_manager(self):
        """Initialize carrier management system"""
        logger.info("Initializing carrier manager...")
        try:
            carrier_manager = CarrierManager()
            
            # Test carrier connections
            carrier_status = await self._test_carrier_connections(carrier_manager)
            
            self.status['carrier_manager'] = {
                'status': 'initialized',
                'carrier_connections': carrier_status
            }
            
        except Exception as e:
            logger.error(f"Failed to initialize carrier manager: {str(e)}")
            self.status['carrier_manager'] = {'status': 'failed', 'error': str(e)}

    async def _init_agent_coordinator(self):
        """Initialize agent coordination system"""
        logger.info("Initializing agent coordinator...")
        try:
            coordinator = AgentCoordinator()
            
            # Verify coordinator initialization
            self.status['agent_coordinator'] = 'initialized'
            
        except Exception as e:
            logger.error(f"Failed to initialize agent coordinator: {str(e)}")
            self.status['agent_coordinator'] = 'failed'

    async def _test_credentials(self, creds_manager):
        """Test credential manager functionality"""
        carriers = [
            "uhc", "mutual_of_omaha", "americo", 
            "aetna", "cigna", "healthsherpa"
        ]
        
        results = {}
        for carrier in carriers:
            try:
                # Test credential retrieval for both agents
                justine_creds = creds_manager.verify_agent_access("justine", carrier)
                sandra_creds = creds_manager.verify_agent_access("sandra", carrier)
                
                results[carrier] = {
                    "justine": "configured" if justine_creds else "not_configured",
                    "sandra": "configured" if sandra_creds else "not_configured"
                }
            except Exception as e:
                results[carrier] = {"error": str(e)}
                
        return results

    async def _test_carrier_connections(self, carrier_manager):
        """Test carrier portal connections"""
        carriers = [
            ("uhc", "https://jarvys.com"),
            ("mutual_of_omaha", "https://www.mutualofomaha.com"),
            ("americo", "https://www.americo.com"),
            ("aetna", "https://producerworld.com"),
            ("cigna", "https://cignaforbrokers.com")
        ]
        
        results = {}
        for carrier, url in carriers:
            try:
                connection = await carrier_manager.carriers[carrier].test_connection()
                results[carrier] = "connected" if connection else "failed"
            except Exception as e:
                results[carrier] = f"error: {str(e)}"
                
        return results

    def _save_status(self):
        """Save initialization status to file"""
        status_file = self.base_path / 'data' / 'initialization_status.json'
        
        status_data = {
            "timestamp": datetime.now().isoformat(),
            "status": self.status
        }
        
        with open(status_file, 'w') as f:
            json.dump(status_data, f, indent=2)

    def verify_environment(self):
        """Verify all required environment variables"""
        required_vars = [
            'ELEVENLABS_API_KEY',
            'TWILIO_ACCOUNT_SID',
            'TWILIO_AUTH_TOKEN'
        ]
        
        missing_vars = [var for var in required_vars if not os.getenv(var)]
        
        if missing_vars:
            logger.error(f"Missing required environment variables: {missing_vars}")
            return False
            
        return True

async def main():
    """Run system initialization"""
    initializer = SystemInitializer()
    
    # Verify environment first
    if not initializer.verify_environment():
        logger.error("Environment verification failed. Please check .env file.")
        sys.exit(1)
    
    # Run initialization
    success = await initializer.initialize()
    
    if success:
        logger.info("\nSystem initialization completed successfully!")
        logger.info("You can now run the system using: python test_live_system.py")
    else:
        logger.error("\nSystem initialization failed. Please check the logs.")
        sys.exit(1)

if __name__ == "__main__":
    asyncio.run(main())