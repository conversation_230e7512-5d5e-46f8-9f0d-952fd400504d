"""
Twilio Configuration and Helper Functions

This module provides validation and helper functions for Twilio integration.
"""

import os
import json
import re
import requests
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

def validate_twilio_credentials():
    """Validate Twilio credentials from environment variables"""
    account_sid = os.getenv("TWILIO_ACCOUNT_SID")
    auth_token = os.getenv("TWILIO_AUTH_TOKEN")
    phone_number = os.getenv("TWILIO_PHONE_NUMBER")
    
    # Check if all required environment variables are set
    if not account_sid:
        return False, "TWILIO_ACCOUNT_SID environment variable not set"
    
    if not auth_token:
        return False, "TWILIO_AUTH_TOKEN environment variable not set"
    
    if not phone_number:
        return False, "TWILIO_PHONE_NUMBER environment variable not set"
    
    # Validate account_sid format
    if not re.match(r'^AC[0-9a-fA-F]{32}$', account_sid):
        return False, "Invalid TWILIO_ACCOUNT_SID format. Should start with 'AC' followed by 32 hex characters"
    
    # Validate auth_token format (simple length check)
    if len(auth_token) < 32:
        return False, "TWILIO_AUTH_TOKEN appears too short. Should be at least 32 characters"
    
    # Validate phone number format
    if not re.match(r'^\+[1-9]\d{1,14}$', phone_number):
        return False, "Invalid TWILIO_PHONE_NUMBER format. Should be in E.164 format (e.g., +***********)"
    
    # Attempt to verify credentials with Twilio API
    try:
        url = f"https://api.twilio.com/2010-04-01/Accounts/{account_sid}.json"
        response = requests.get(url, auth=(account_sid, auth_token))
        
        if response.status_code != 200:
            error_message = format_error_message(response)
            return False, f"Twilio API validation failed: {error_message}"
        
        return True, "Credentials successfully validated"
    except Exception as e:
        return False, f"Error validating credentials: {str(e)}"

def get_account_info():
    """Get Twilio account information"""
    account_sid = os.getenv("TWILIO_ACCOUNT_SID")
    auth_token = os.getenv("TWILIO_AUTH_TOKEN")
    
    try:
        url = f"https://api.twilio.com/2010-04-01/Accounts/{account_sid}.json"
        response = requests.get(url, auth=(account_sid, auth_token))
        
        if response.status_code == 200:
            return response.json()
        else:
            print(f"Error getting account info: {response.status_code}")
            return {}
    except Exception as e:
        print(f"Exception getting account info: {str(e)}")
        return {}

def format_error_message(response):
    """Format error message from Twilio API response"""
    try:
        error_data = response.json()
        error_code = error_data.get('code', 'Unknown')
        error_message = error_data.get('message', 'Unknown error')
        error_more_info = error_data.get('more_info', 'No additional information')
        request_id = response.headers.get('Twilio-Request-Id', 'Unknown')
        
        return f"Error {error_code}: {error_message}\nMore info: {error_more_info}\nRequest ID: {request_id}"
    except:
        return f"HTTP {response.status_code}: {response.text}\nRequest ID: {response.headers.get('Twilio-Request-Id', 'Unknown')}"

def is_trial_account():
    """Check if the Twilio account is a trial account"""
    account_info = get_account_info()
    account_type = account_info.get('type', '').lower()
    
    return account_type == 'trial'

def get_verified_numbers():
    """Get list of verified numbers for trial accounts"""
    account_sid = os.getenv("TWILIO_ACCOUNT_SID")
    auth_token = os.getenv("TWILIO_AUTH_TOKEN")
    
    try:
        url = f"https://api.twilio.com/2010-04-01/Accounts/{account_sid}/OutgoingCallerIds.json"
        response = requests.get(url, auth=(account_sid, auth_token))
        
        if response.status_code == 200:
            data = response.json()
            verified_numbers = [item.get('phone_number') for item in data.get('caller_ids', [])]
            return verified_numbers
        else:
            print(f"Error getting verified numbers: {response.status_code}")
            return []
    except Exception as e:
        print(f"Exception getting verified numbers: {str(e)}")
        return []

def get_from_number(to_number):
    """Determine which number to use as the 'From' number
    
    For trial accounts, this needs to be handled differently
    """
    phone_number = os.getenv("TWILIO_PHONE_NUMBER")
    
    # For trial accounts, we need to check if we're using a verified number
    if is_trial_account():
        verified_numbers = get_verified_numbers()
        
        # If the to_number is a verified number and matches our TWILIO_PHONE_NUMBER
        if to_number in verified_numbers and to_number == phone_number:
            # For trial accounts sending to their own verified number,
            # we need to use a Twilio test number
            return "+***********"  # Twilio test number for trial accounts
        
        # If the to_number is verified but not our TWILIO_PHONE_NUMBER
        elif to_number in verified_numbers:
            # We can use our TWILIO_PHONE_NUMBER
            return phone_number
        else:
            # If the to_number is not verified, we use a test number
            # but this will likely fail for non-verified numbers on trial accounts
            return "+***********"  # Twilio test number for trial accounts
    else:
        # For paid accounts, we always use our TWILIO_PHONE_NUMBER
        return phone_number

def test_connection():
    """Test the Twilio connection and credentials
    
    Returns:
        tuple: (success, details)
    """
    print("\n" + "=" * 80)
    print("TESTING TWILIO CONNECTION")
    print("=" * 80)
    
    # Validate credentials
    is_valid, error_message = validate_twilio_credentials()
    
    if not is_valid:
        return False, error_message
    
    # Get account information
    account_info = get_account_info()
    
    if not account_info:
        return False, "Failed to retrieve account information"
    
    account_status = account_info.get('status')
    if account_status.lower() != 'active':
        return False, f"Account status is not active: {account_status}"
    
    success_message = (
        f"✓ Connection successful!\n"
        f"Account Name: {account_info.get('friendly_name')}\n"
        f"Account Type: {account_info.get('type')}\n"
        f"Account Status: {account_info.get('status')}"
    )
    
    return True, success_message

def mask_credential(credential, show_chars=4):
    """Mask a credential for display purposes, showing only the first few characters"""
    if not credential:
        return "Not set"
    
    visible = credential[:show_chars]
    masked = '*' * (len(credential) - show_chars)
    return visible + masked

if __name__ == "__main__":
    success, message = test_connection()
    
    if success:
        print("\n✓ " + message)
        print("\nThe 400 Bad Request error should now be resolved")
    else:
        print("\n❌ " + message)
        print("\nPlease check your .env file and ensure it contains:")
        print("TWILIO_ACCOUNT_SID=your_account_sid")
        print("TWILIO_AUTH_TOKEN=your_auth_token")
        print("TWILIO_PHONE_NUMBER=your_phone_number")