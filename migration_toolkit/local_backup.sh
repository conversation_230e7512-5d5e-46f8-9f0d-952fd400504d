#!/bin/bash
# ==============================================================
# PaulEdwardsAI Local Migration Script
# Created: May 5, 2025
# Complete backup solution for local migration
# ==============================================================

# Exit on error
set -e

# Create colorful header for better visibility
echo -e "\033[1;34m======================================================\033[0m"
echo -e "\033[1;32m        PaulEdwardsAI Local Migration Tool            \033[0m"
echo -e "\033[1;34m======================================================\033[0m"
echo ""
echo "🔄 Migration Start: $(date)"

# Create a unique backup directory with timestamp
TIMESTAMP=$(date +%Y%m%d_%H%M%S)
BACKUP_DIR="PaulEdwardsAI_Backup_${TIMESTAMP}"
mkdir -p "$BACKUP_DIR"
echo "📁 Created backup directory: $BACKUP_DIR"

# Function to show progress
show_progress() {
  local msg="$1"
  local progress="$2"
  local total_width=50
  local filled_width=$((progress * total_width / 100))
  local empty_width=$((total_width - filled_width))
  
  printf "\r[%s%s] %3d%% %s" \
         "$(printf '#%.0s' $(seq 1 $filled_width))" \
         "$(printf ' %.0s' $(seq 1 $empty_width))" \
         "$progress" \
         "$msg"
}

# 1. Copy all project files with visual progress
echo "📦 Step 1/5: Copying project files..."
total_files=$(find . -type f -not -path "./${BACKUP_DIR}/*" -not -path "./venv/*" -not -path "./.git/*" -not -path "./node_modules/*" | wc -l)
current_file=0

find . -type f -not -path "./${BACKUP_DIR}/*" -not -path "./venv/*" -not -path "./.git/*" -not -path "./node_modules/*" | while read file; do
  target_dir="${BACKUP_DIR}/$(dirname "$file")"
  mkdir -p "$target_dir"
  cp "$file" "$target_dir/" 2>/dev/null || true
  current_file=$((current_file + 1))
  progress=$((current_file * 100 / total_files))
  show_progress "Copying files" $progress
done
echo -e "\n✅ Files copied successfully"

# 2. Create inventory of installed packages
echo ""
echo "📋 Step 2/5: Creating package inventory..."
mkdir -p "${BACKUP_DIR}/environment"

# Python packages
if command -v pip &>/dev/null; then
  echo "🐍 Python packages:" > "${BACKUP_DIR}/environment/python_packages.txt"
  pip freeze >> "${BACKUP_DIR}/environment/python_packages.txt"
  echo "✅ Python packages inventoried"
fi

# Docker images related to the project
if command -v docker &>/dev/null; then
  echo "🐳 Docker images:" > "${BACKUP_DIR}/environment/docker_images.txt"
  docker images --format "{{.Repository}}:{{.Tag}}" | grep "pauledwardsai" >> "${BACKUP_DIR}/environment/docker_images.txt" 2>/dev/null || echo "No matching Docker images found" >> "${BACKUP_DIR}/environment/docker_images.txt"
  echo "✅ Docker images inventoried"
  
  # Save Docker compose configuration if it exists
  if [ -f "docker-compose.yml" ]; then
    mkdir -p "${BACKUP_DIR}/docker_config"
    cp docker-compose.yml "${BACKUP_DIR}/docker_config/"
    if [ -f "Dockerfile" ]; then
      cp Dockerfile "${BACKUP_DIR}/docker_config/"
    fi
    echo "✅ Docker configuration backed up"
  fi
fi

# 3. Backup credentials with encryption
echo ""
echo "🔐 Step 3/5: Securing credentials and configuration files..."
mkdir -p "${BACKUP_DIR}/secure_configs"

# Generate secure backup password
read -s -p "🔑 Enter password to encrypt sensitive files (or press Enter to skip encryption): " BACKUP_PASSWORD
echo ""

# Find and secure credential files
if [ ! -z "$BACKUP_PASSWORD" ]; then
  # Export password to temporary file to use with openssl
  echo "$BACKUP_PASSWORD" > .temp_pw_file
  
  # Find potential credential files
  find . -type f \( -name "*credential*" -o -name "*.key" -o -name "*.pem" -o -name "*.env" -o -name "*secret*" -o -name "*token*" -o -name "*fernet*" \) \
    -not -path "./${BACKUP_DIR}/*" -not -path "./venv/*" -not -path "./.git/*" | while read file; do
    if [ -f "$file" ]; then
      encrypted_file="${BACKUP_DIR}/secure_configs/$(basename "$file").enc"
      # Encrypt with password
      openssl enc -aes-256-cbc -salt -in "$file" -out "$encrypted_file" -pass file:.temp_pw_file 2>/dev/null
      echo "🔒 Encrypted: $file"
    fi
  done
  
  # Save password hint to help user remember
  first_char=$(echo "$BACKUP_PASSWORD" | cut -c1)
  length=${#BACKUP_PASSWORD}
  echo "Password hint: starts with '$first_char' and has $length characters" > "${BACKUP_DIR}/secure_configs/password_hint.txt"
  
  # Delete temporary password file
  rm .temp_pw_file
  
  echo "✅ Credentials secured with encryption"
else
  echo "⚠️ Encryption skipped - credentials will be backed up without encryption"
  
  # Copy credential files without encryption
  find . -type f \( -name "*credential*" -o -name "*.key" -o -name "*.pem" -o -name "*.env" -o -name "*secret*" -o -name "*token*" -o -name "*fernet*" \) \
    -not -path "./${BACKUP_DIR}/*" -not -path "./venv/*" -not -path "./.git/*" | while read file; do
    if [ -f "$file" ]; then
      target_dir="${BACKUP_DIR}/secure_configs/$(dirname "$file")"
      mkdir -p "$target_dir"
      cp "$file" "$target_dir/" 2>/dev/null
      echo "📄 Copied: $file"
    fi
  done
fi

# 4. Collect system information
echo ""
echo "💻 Step 4/5: Collecting system information..."
mkdir -p "${BACKUP_DIR}/system_info"

# OS information
echo "🖥️ Operating System:" > "${BACKUP_DIR}/system_info/system_details.txt"
uname -a >> "${BACKUP_DIR}/system_info/system_details.txt"

# Python information
if command -v python &>/dev/null; then
  echo -e "\n🐍 Python Version:" >> "${BACKUP_DIR}/system_info/system_details.txt"
  python --version 2>&1 >> "${BACKUP_DIR}/system_info/system_details.txt"
fi

# Node.js information
if command -v node &>/dev/null; then
  echo -e "\n📦 Node.js Version:" >> "${BACKUP_DIR}/system_info/system_details.txt"
  node --version 2>&1 >> "${BACKUP_DIR}/system_info/system_details.txt"
fi

# Security tools information
echo -e "\n🔒 Security Tools:" >> "${BACKUP_DIR}/system_info/system_details.txt"
if [ -f "install_security_tools.sh" ]; then
  grep "apt-get install\|brew install" install_security_tools.sh >> "${BACKUP_DIR}/system_info/system_details.txt" 2>/dev/null || echo "No security tools found in install_security_tools.sh" >> "${BACKUP_DIR}/system_info/system_details.txt"
elif [ -f "install_security_tools_macos.sh" ]; then
  grep "brew install" install_security_tools_macos.sh >> "${BACKUP_DIR}/system_info/system_details.txt" 2>/dev/null || echo "No security tools found in install_security_tools_macos.sh" >> "${BACKUP_DIR}/system_info/system_details.txt"
fi

echo "✅ System information collected"

# 5. Create comprehensive migration guide
echo ""
echo "📝 Step 5/5: Creating migration guide..."

# Create HTML guide for better readability
cat > "${BACKUP_DIR}/MIGRATION_GUIDE.html" << 'EOL'
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>PaulEdwardsAI Migration Guide</title>
    <style>
        body { font-family: Arial, sans-serif; max-width: 800px; margin: 0 auto; padding: 20px; }
        h1 { color: #2c3e50; text-align: center; }
        h2 { color: #3498db; border-bottom: 1px solid #3498db; padding-bottom: 5px; }
        .step { background-color: #f8f9fa; padding: 15px; margin: 15px 0; border-radius: 5px; }
        .icon { font-size: 24px; margin-right: 10px; }
        code { background-color: #f1f1f1; padding: 2px 5px; border-radius: 3px; font-family: monospace; display: block; overflow-x: auto; }
        .warning { background-color: #fff3cd; padding: 10px; border-left: 5px solid #ffc107; }
        .success { background-color: #d4edda; padding: 10px; border-left: 5px solid #28a745; }
    </style>
</head>
<body>
    <h1>🚀 PaulEdwardsAI Migration Guide</h1>
    
    <div class="step">
        <h2>📋 Step 1: Transfer Files</h2>
        <p>Transfer the entire backup folder to your new device using your preferred method:</p>
        <code>
# Using secure copy (SCP)<br>
scp -r PaulEdwardsAI_Backup_* user@new-device:/destination/path<br><br>

# Or compress first for easier transfer<br>
tar -czf PaulEdwardsAI_Backup.tar.gz PaulEdwardsAI_Backup_*<br>
scp PaulEdwardsAI_Backup.tar.gz user@new-device:/destination/path
        </code>
    </div>
    
    <div class="step">
        <h2>🔧 Step 2: Setup Environment</h2>
        <p><strong>Python Setup:</strong></p>
        <code>
# Create virtual environment<br>
python -m venv venv<br><br>

# Activate virtual environment<br>
# On macOS/Linux:<br>
source venv/bin/activate<br>
# On Windows:<br>
venv\Scripts\activate<br><br>

# Install dependencies<br>
pip install -r environment/python_packages.txt
        </code>
        
        <p><strong>Docker Setup (if needed):</strong></p>
        <code>
# Navigate to the docker configuration directory<br>
cd docker_config<br><br>

# Build and start containers<br>
docker-compose up -d
        </code>
    </div>
    
    <div class="step">
        <h2>🔐 Step 3: Restore Credentials</h2>
        <p>If you encrypted your sensitive files during backup:</p>
        <code>
# Decrypt a file (you'll need the password you used during backup)<br>
openssl enc -aes-256-cbc -d -salt -in secure_configs/filename.enc -out filename
        </code>
        <p class="warning">⚠️ See password hint in secure_configs/password_hint.txt</p>
        
        <p>Important credential files to restore:</p>
        <ul>
            <li>fernet_config.txt - Required for encryption/decryption</li>
            <li>Any API keys or authentication tokens</li>
            <li>Environment variables (.env files)</li>
        </ul>
    </div>
    
    <div class="step">
        <h2>🔒 Step 4: Install Security Tools</h2>
        <p>Install security tools based on your OS:</p>
        <code>
# On macOS:<br>
bash install_security_tools_macos.sh<br><br>

# On Linux:<br>
bash install_security_tools.sh
        </code>
    </div>
    
    <div class="step">
        <h2>📱 Step 5: Configure Communication Channels</h2>
        <p>Update the following configuration files with settings for your new device:</p>
        <ul>
            <li><strong>Email Integration:</strong> Update gmail_integration.py with your new device settings</li>
            <li><strong>Phone Integration:</strong> Update phone_utils.py with new device paths</li>
            <li><strong>Voicemail:</strong> Verify paul_edwards_voicemail.mp3 and other audio files transferred correctly</li>
        </ul>
    </div>
    
    <div class="step">
        <h2>🧪 Step 6: Test Functionality</h2>
        <p>Test each component to verify successful migration:</p>
        <code>
# Core functionality<br>
python main.py<br><br>

# Email integration<br>
python email_integration_test.py<br><br>

# Security<br>
python agent_security_integration.py<br><br>

# MCP Server Status<br>
python -c "import asyncio; from start_mcp_servers import MCPServerActivator; activator = MCPServerActivator(); asyncio.run(activator.activate_all_servers()); activator.print_server_status()"
        </code>
    </div>
    
    <div class="step">
        <h2>📊 Step 7: Communication Data Transfer</h2>
        <p>For communication data not included in the project files:</p>
        <ul>
            <li><strong>Email Data:</strong> Set up access to your email account on the new system</li>
            <li><strong>Phone/SMS Data:</strong> Configure phone app connections</li>
            <li><strong>Voicemail:</strong> Ensure audio playback works correctly</li>
        </ul>
    </div>
    
    <div class="success">
        <p>✅ Your PaulEdwardsAI system should now be fully migrated!</p>
        <p>If you encounter issues, check system_info/system_details.txt to verify your environment meets all requirements.</p>
    </div>
</body>
</html>
EOL

# Also create a plain text version for broader compatibility
cat > "${BACKUP_DIR}/MIGRATION_GUIDE.txt" << 'EOL'
==============================================================
                PaulEdwardsAI Migration Guide
==============================================================

Step 1: Transfer Files
---------------------
Transfer the entire backup folder to your new device.
- scp -r PaulEdwardsAI_Backup_* user@new-device:/destination/path
- Or use any file transfer method (USB drive, cloud storage, etc.)

Step 2: Setup Environment
------------------------
Python Setup:
# Create virtual environment
python -m venv venv

# Activate virtual environment
# On macOS/Linux:
source venv/bin/activate
# On Windows:
venv\Scripts\activate

# Install dependencies
pip install -r environment/python_packages.txt

Docker Setup (if needed):
# Navigate to docker config directory
cd docker_config

# Build and start containers
docker-compose up -d

Step 3: Restore Credentials
--------------------------
If you encrypted your sensitive files:
# Decrypt a file
openssl enc -aes-256-cbc -d -salt -in secure_configs/filename.enc -out filename

You'll be prompted for the password you used during backup.
See password hint in secure_configs/password_hint.txt

Important credential files to restore:
- fernet_config.txt
- API keys and tokens
- Environment variables (.env files)

Step 4: Install Security Tools
----------------------------
Install security tools based on your OS:
# On macOS:
bash install_security_tools_macos.sh

# On Linux:
bash install_security_tools.sh

Step 5: Configure Communication Channels
--------------------------------------
- Email: Update gmail_integration.py with your new device settings
- Phone: Update phone_utils.py with new device paths
- Voicemail: Verify paul_edwards_voicemail.mp3 transferred correctly

Step 6: Test Functionality
------------------------
Test each component:
# Core functionality
python main.py

# Email integration
python email_integration_test.py

# Security
python agent_security_integration.py

# MCP Server Status
python -c "import asyncio; from start_mcp_servers import MCPServerActivator; activator = MCPServerActivator(); asyncio.run(activator.activate_all_servers()); activator.print_server_status()"

Step 7: Communication Data Transfer
---------------------------------
For communication data not included in project files:
- Email: Set up access to your email account
- Phone/SMS: Configure phone app connections
- Voicemail: Test audio playback

Your PaulEdwardsAI system should now be fully migrated!
If you encounter issues, check system_info/system_details.txt to verify your environment.
EOL

# Create compressed archive
echo ""
echo "📦 Creating compressed archive..."
tar -czf "${BACKUP_DIR}.tar.gz" "$BACKUP_DIR"

# Print summary
echo ""
echo -e "\033[1;32m✅ Migration backup completed successfully!\033[0m"
echo -e "\033[1;34m======================================================\033[0m"
echo "📁 Backup directory: $BACKUP_DIR"
echo "🗜️ Compressed archive: ${BACKUP_DIR}.tar.gz"
echo ""
echo "📱 Next steps:"
echo "  1️⃣ Transfer the ${BACKUP_DIR}.tar.gz file to your new device"
echo "  2️⃣ Extract: tar -xzf ${BACKUP_DIR}.tar.gz"
echo "  3️⃣ Follow the instructions in MIGRATION_GUIDE.html or MIGRATION_GUIDE.txt"
echo -e "\033[1;34m======================================================\033[0m"