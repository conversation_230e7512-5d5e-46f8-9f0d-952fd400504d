#!/usr/bin/env python3
"""
PaulEdwardsAI Azure Cloud Migration Tool
Created: May 5, 2025

This script implements Azure best practices for backing up and migrating
your PaulEdwardsAI system to Azure Blob Storage.

Features:
- Parallel uploads for files over 100MB
- AES-256 encryption for sensitive files
- Azure managed identity support when available
- Exponential backoff retry logic for resilience
- Multi-container security isolation
- Cross-platform compatibility
"""

import os
import sys
import logging
import argparse
import tempfile
import subprocess
import json
import time
import shutil
import datetime
import glob
import getpass
from pathlib import Path
import base64
from concurrent.futures import ThreadPoolExecutor
import hashlib
import uuid

# Configure logging with both file and console output
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("azure_backup.log"),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger("azure_migration")

# Constants
VERSION = "1.0.0"
TIMESTAMP = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
BACKUP_DIR = f"PaulEdwardsAI_Azure_{TIMESTAMP}"
MAX_RETRIES = 5
INITIAL_RETRY_DELAY = 1  # seconds
LARGE_FILE_THRESHOLD = 100 * 1024 * 1024  # 100MB threshold for parallel upload
MAX_PARALLEL_UPLOADS = 4
CHUNK_SIZE = 4 * 1024 * 1024  # 4MB chunks for large file uploads

# ANSI color codes for terminal output
BLUE = "\033[1;34m"
GREEN = "\033[1;32m"
YELLOW = "\033[1;33m"
RED = "\033[1;31m"
RESET = "\033[0m"

class AzureBackupManager:
    """Handles backup and migration to Azure Blob Storage according to best practices"""
    
    def __init__(self, storage_account=None, resource_group=None):
        """Initialize the backup manager with Azure settings"""
        self.storage_account = storage_account
        self.resource_group = resource_group
        self.backup_dir = BACKUP_DIR
        self.main_container = "pauledwardsai-backup"
        self.secure_container = "pauledwardsai-secure"
        
        # Check for Azure environment
        self.in_azure_environment = self._check_in_azure_environment()
        
        # Check Azure CLI installation
        self._check_az_cli()
        
        # Create backup directory
        os.makedirs(self.backup_dir, exist_ok=True)
        logger.info(f"Created backup directory: {self.backup_dir}")
    
    def _check_in_azure_environment(self):
        """Check if running in Azure environment with managed identity available"""
        azure_env_vars = ['IDENTITY_ENDPOINT', 'IDENTITY_HEADER', 'MSI_ENDPOINT', 'MSI_SECRET']
        return any(env_var in os.environ for env_var in azure_env_vars)
    
    def _check_az_cli(self):
        """Verify Azure CLI is installed and available"""
        try:
            result = subprocess.run(['az', '--version'], capture_output=True, text=True, check=False)
            if result.returncode != 0:
                logger.error("Azure CLI not installed")
                print(f"{RED}❌ Azure CLI is not installed or not in PATH{RESET}")
                print("🌐 Please install Azure CLI: https://docs.microsoft.com/en-us/cli/azure/install-azure-cli")
                sys.exit(1)
        except FileNotFoundError:
            logger.error("Azure CLI not found")
            print(f"{RED}❌ Azure CLI not found{RESET}")
            print("🌐 Please install Azure CLI: https://docs.microsoft.com/en-us/cli/azure/install-azure-cli")
            sys.exit(1)
    
    def _run_command(self, cmd, check=True, capture_output=True):
        """Run a command with proper error handling"""
        try:
            logger.debug(f"Running command: {cmd}")
            if capture_output:
                result = subprocess.run(cmd, shell=True, check=check, text=True, capture_output=True)
                return result.stdout
            else:
                subprocess.run(cmd, shell=True, check=check)
                return None
        except subprocess.CalledProcessError as e:
            logger.error(f"Command failed: {e}")
            raise
    
    def _run_command_with_retry(self, cmd, capture_output=True):
        """Run a command with exponential backoff retry logic"""
        retry_count = 0
        last_exception = None
        
        while retry_count < MAX_RETRIES:
            try:
                if capture_output:
                    return self._run_command(cmd, check=True, capture_output=True)
                else:
                    self._run_command(cmd, check=True, capture_output=False)
                    return None
            except subprocess.CalledProcessError as e:
                last_exception = e
                retry_count += 1
                
                if retry_count >= MAX_RETRIES:
                    logger.error(f"Command failed after {MAX_RETRIES} retries: {e}")
                    raise
                
                # Calculate exponential backoff with jitter
                delay = min(INITIAL_RETRY_DELAY * (2 ** (retry_count - 1)), 60)
                jitter = delay * 0.2 * (hash(str(time.time())) % 100) / 100  # Add up to 20% random jitter
                delay += jitter
                
                logger.warning(f"Command failed (attempt {retry_count}/{MAX_RETRIES}), retrying in {delay:.2f}s: {e}")
                time.sleep(delay)
        
        if last_exception:
            raise last_exception
    
    def check_azure_login(self):
        """Check if logged in to Azure and prompt if needed"""
        print(f"{BLUE}🔍 Checking Azure login status...{RESET}")
        try:
            self._run_command("az account show --query name -o tsv")
            account_info = self._run_command("az account show --query name -o tsv").strip()
            print(f"{GREEN}✅ Logged in to Azure as: {account_info}{RESET}")
        except subprocess.CalledProcessError:
            print(f"{YELLOW}🔑 You need to log in to Azure{RESET}")
            self._run_command("az login", capture_output=False)
        
        # Get storage account if not provided
        if not self.storage_account:
            self._select_storage_account()
    
    def _select_storage_account(self):
        """List available storage accounts and let user select one"""
        print(f"{BLUE}\n📋 Available storage accounts:{RESET}")
        storage_accounts = json.loads(self._run_command("az storage account list --query \"[].{Name:name, ResourceGroup:resourceGroup, Location:location}\" -o json"))
        
        if not storage_accounts:
            print(f"{YELLOW}⚠️ No storage accounts found in your subscription{RESET}")
            create_new = input("Would you like to create a new storage account? (y/n): ").lower() == 'y'
            if create_new:
                self._create_storage_account()
            else:
                sys.exit(1)
        else:
            print(f"{'#':<4}{'Name':<30}{'Resource Group':<30}{'Location':<20}")
            print("-" * 80)
            for i, account in enumerate(storage_accounts, 1):
                print(f"{i:<4}{account['Name']:<30}{account['ResourceGroup']:<30}{account['Location']:<20}")
            
            while True:
                try:
                    selection = int(input("\nSelect a storage account (number): "))
                    if 1 <= selection <= len(storage_accounts):
                        self.storage_account = storage_accounts[selection-1]["Name"]
                        self.resource_group = storage_accounts[selection-1]["ResourceGroup"]
                        break
                    else:
                        print(f"{YELLOW}Invalid selection. Please try again.{RESET}")
                except ValueError:
                    print(f"{YELLOW}Please enter a number.{RESET}")
    
    def _create_storage_account(self):
        """Create a new Azure Storage account for backup"""
        name = input("Enter a unique name for your new storage account: ")
        location = input("Enter location (e.g., eastus, westus): ")
        resource_group = input("Enter resource group name (or press Enter to create a new one): ")
        
        if not resource_group:
            resource_group = f"pauledwardsai-backup-{str(uuid.uuid4())[:8]}"
            print(f"Creating resource group: {resource_group}")
            self._run_command(f"az group create --name {resource_group} --location {location}", capture_output=False)
        
        print(f"Creating storage account {name} in {location}...")
        try:
            self._run_command(
                f"az storage account create --name {name} --resource-group {resource_group} "
                "--kind StorageV2 --sku Standard_LRS --enable-hierarchical-namespace false "
                "--allow-shared-key-access true",
                capture_output=False
            )
            self.storage_account = name
            self.resource_group = resource_group
            print(f"{GREEN}✅ Storage account created successfully{RESET}")
        except subprocess.CalledProcessError:
            print(f"{RED}Failed to create storage account. Please try again with a different name.{RESET}")
            sys.exit(1)
    
    def create_containers(self):
        """Create containers for backup with appropriate security settings"""
        logger.info("Creating Azure storage containers")
        
        # Determine authentication method
        auth_params = "--auth-mode login"
        if self.in_azure_environment:
            auth_params = "--auth-mode login --identity"
        
        # Create main container
        print(f"{BLUE}Creating main backup container...{RESET}")
        try:
            self._run_command_with_retry(
                f"az storage container create --name {self.main_container} "
                f"--account-name {self.storage_account} {auth_params}"
            )
            logger.info(f"Main container {self.main_container} created or exists")
        except Exception as e:
            logger.error(f"Failed to create main container: {e}")
            raise
        
        # Create secure container with restricted access
        print(f"{BLUE}Creating secure container for sensitive data...{RESET}")
        try:
            self._run_command_with_retry(
                f"az storage container create --name {self.secure_container} "
                f"--account-name {self.storage_account} {auth_params} "
                "--public-access off"
            )
            logger.info(f"Secure container {self.secure_container} created or exists")
        except Exception as e:
            logger.error(f"Failed to create secure container: {e}")
            raise
        
        print(f"{GREEN}✅ Azure containers ready{RESET}")
        return True
    
    def backup_files(self):
        """Backup all project files to local backup directory"""
        print(f"{BLUE}📦 Step 1/6: Copying project files...{RESET}")
        
        # Find all project files excluding common excludes
        excludes = [
            "./venv", 
            "./.git", 
            "./node_modules", 
            f"./{self.backup_dir}", 
            "./.venv", 
            "./env"
        ]
        
        exclude_args = " ".join([f"--exclude='{ex}'" for ex in excludes])
        all_files_cmd = f"find . -type f {exclude_args} | wc -l"
        total_files = int(self._run_command(all_files_cmd).strip())
        
        print(f"Found {total_files} files to backup")
        rsync_cmd = f"rsync -a --info=progress2 {exclude_args} . {self.backup_dir}/"
        
        try:
            # Use rsync for efficient copying with progress display
            subprocess.run(rsync_cmd, shell=True, check=True)
            print(f"{GREEN}✅ Files copied successfully{RESET}")
            logger.info(f"Successfully backed up {total_files} files to {self.backup_dir}")
            return True
        except subprocess.CalledProcessError as e:
            logger.error(f"Failed to copy files: {e}")
            print(f"{RED}❌ Error copying files{RESET}")
            return False
    
    def inventory_packages(self):
        """Create inventory of installed packages and dependencies"""
        print(f"{BLUE}📋 Step 2/6: Creating package inventory...{RESET}")
        env_dir = os.path.join(self.backup_dir, "environment")
        os.makedirs(env_dir, exist_ok=True)
        
        # Python packages
        if shutil.which("pip"):
            print("Inventorying Python packages...")
            try:
                packages = self._run_command("pip freeze")
                with open(os.path.join(env_dir, "python_packages.txt"), "w") as f:
                    f.write("🐍 Python packages:\n")
                    f.write(packages)
                print(f"{GREEN}✅ Python packages inventoried{RESET}")
            except Exception as e:
                logger.error(f"Failed to inventory Python packages: {e}")
        
        # Docker assets
        if shutil.which("docker"):
            print("Inventorying Docker images...")
            docker_dir = os.path.join(self.backup_dir, "docker_config")
            os.makedirs(docker_dir, exist_ok=True)
            
            try:
                # Docker images
                images = self._run_command("docker images --format \"{{.Repository}}:{{.Tag}}\" | grep pauledwardsai || echo \"No matching Docker images found\"")
                with open(os.path.join(env_dir, "docker_images.txt"), "w") as f:
                    f.write("🐳 Docker images:\n")
                    f.write(images)
                
                # Docker config files
                for config_file in ["docker-compose.yml", "Dockerfile", "docker-entrypoint.sh"]:
                    if os.path.exists(config_file):
                        shutil.copy(config_file, docker_dir)
                
                print(f"{GREEN}✅ Docker assets inventoried{RESET}")
            except Exception as e:
                logger.error(f"Failed to inventory Docker assets: {e}")
        
        return True
    
    def secure_credentials(self):
        """Secure credentials with encryption"""
        print(f"{BLUE}🔐 Step 3/6: Securing credentials...{RESET}")
        secure_dir = os.path.join(self.backup_dir, "secure_configs")
        os.makedirs(secure_dir, exist_ok=True)
        
        # Generate a secure random encryption key
        encryption_key = base64.b64encode(os.urandom(32)).decode('utf-8')
        key_file = os.path.join(secure_dir, "encryption_key.txt")
        with open(key_file, "w") as f:
            f.write(encryption_key)
        logger.info("Generated encryption key")
        
        # Patterns for sensitive files
        sensitive_patterns = [
            "*credential*", "*.key", "*.pem", "*.env", 
            "*secret*", "*token*", "*fernet*", "*.pfx",
            "*password*", "*api_key*", "*auth*"
        ]
        
        # Find and encrypt sensitive files
        encrypted_files = 0
        for pattern in sensitive_patterns:
            files = glob.glob(pattern, recursive=True)
            for file in files:
                if os.path.isfile(file) and not any(ex in file for ex in ['/venv/', '/.git/', f'/{self.backup_dir}/']):
                    encrypted_file = os.path.join(secure_dir, f"{os.path.basename(file)}.enc")
                    try:
                        # Encrypt using AES-256-CBC
                        cmd = f"openssl enc -aes-256-cbc -salt -in '{file}' -out '{encrypted_file}' -pass pass:{encryption_key}"
                        self._run_command(cmd)
                        encrypted_files += 1
                        logger.info(f"Encrypted {file}")
                    except Exception as e:
                        logger.error(f"Failed to encrypt {file}: {e}")
        
        print(f"{GREEN}✅ {encrypted_files} credential files secured with encryption{RESET}")
        return True
    
    def collect_system_info(self):
        """Collect system information for migration reference"""
        print(f"{BLUE}💻 Step 4/6: Collecting system information...{RESET}")
        info_dir = os.path.join(self.backup_dir, "system_info")
        os.makedirs(info_dir, exist_ok=True)
        
        system_details = []
        
        # OS information
        system_details.append("🖥️ Operating System:")
        try:
            system_details.append(self._run_command("uname -a"))
        except Exception:
            system_details.append("Unable to determine OS info")
        
        # Python information
        if shutil.which("python"):
            system_details.append("\n🐍 Python Version:")
            try:
                system_details.append(self._run_command("python --version 2>&1"))
            except Exception:
                system_details.append("Unable to determine Python version")
        
        # Node.js information
        if shutil.which("node"):
            system_details.append("\n📦 Node.js Version:")
            try:
                system_details.append(self._run_command("node --version"))
            except Exception:
                system_details.append("Unable to determine Node.js version")
        
        # Azure CLI information
        if shutil.which("az"):
            system_details.append("\n☁️ Azure CLI Version:")
            try:
                system_details.append(self._run_command("az --version | head -n 1"))
            except Exception:
                system_details.append("Unable to determine Azure CLI version")
        
        # Docker information
        if shutil.which("docker"):
            system_details.append("\n🐳 Docker Version:")
            try:
                system_details.append(self._run_command("docker --version"))
            except Exception:
                system_details.append("Unable to determine Docker version")
        
        # Security tools
        system_details.append("\n🔒 Security Tools:")
        for tool_file in ["install_security_tools.sh", "install_security_tools_macos.sh"]:
            if os.path.exists(tool_file):
                try:
                    tools = self._run_command(f"grep 'apt-get install\\|brew install' {tool_file} || echo 'No security tools found in {tool_file}'")
                    system_details.append(tools)
                except Exception:
                    pass
        
        # Write to file
        with open(os.path.join(info_dir, "system_details.txt"), "w") as f:
            f.write("\n".join(system_details))
        
        print(f"{GREEN}✅ System information collected{RESET}")
        return True
    
    def create_migration_guide(self):
        """Create comprehensive HTML migration guide with Azure-specific instructions"""
        print(f"{BLUE}📝 Step 5/6: Creating migration guide...{RESET}")
        
        # Content for the migration guide HTML
        html_content = """<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>PaulEdwardsAI Azure Migration Guide</title>
    <style>
        body { font-family: Arial, sans-serif; max-width: 800px; margin: 0 auto; padding: 20px; }
        h1 { color: #2c3e50; text-align: center; }
        h2 { color: #3498db; border-bottom: 1px solid #3498db; padding-bottom: 5px; }
        .step { background-color: #f8f9fa; padding: 15px; margin: 15px 0; border-radius: 5px; }
        .icon { font-size: 24px; margin-right: 10px; }
        code { background-color: #f1f1f1; padding: 2px 5px; border-radius: 3px; font-family: monospace; display: block; overflow-x: auto; }
        .warning { background-color: #fff3cd; padding: 10px; border-left: 5px solid #ffc107; }
        .success { background-color: #d4edda; padding: 10px; border-left: 5px solid #28a745; }
        .azure { background-color: #e6f3ff; padding: 10px; border-left: 5px solid #0078d4; }
    </style>
</head>
<body>
    <h1>🚀 PaulEdwardsAI Azure Migration Guide</h1>
    
    <div class="step">
        <h2>📋 Step 1: Download from Azure</h2>
        <p>Download your backup from Azure Blob Storage:</p>
        <code>
# Configure Azure CLI<br>
az login<br><br>

# Download main backup archive<br>
az storage blob download \\<br>
  --account-name STORAGE_ACCOUNT \\<br>
  --container-name CONTAINER_NAME \\<br>
  --name BACKUP_FILE.tar.gz \\<br>
  --file BACKUP_FILE.tar.gz \\<br>
  --auth-mode login
        </code>
        
        <div class="azure">
            <p><strong>Azure Best Practice:</strong> For large files (≥100MB), use Azure Storage Explorer or AzCopy for parallel downloads with better performance.</p>
        </div>
    </div>
    
    <div class="step">
        <h2>🔐 Step 2: Download Encryption Key</h2>
        <p>Download the encryption key from the secure container:</p>
        <code>
# Download encryption key<br>
az storage blob download \\<br>
  --account-name STORAGE_ACCOUNT \\<br>
  --container-name pauledwardsai-secure \\<br>
  --name encryption_key.txt \\<br>
  --file encryption_key.txt \\<br>
  --auth-mode login
        </code>
        
        <div class="azure">
            <p><strong>Azure Best Practice:</strong> Consider using Azure Key Vault for managing sensitive keys in a production environment.</p>
        </div>
    </div>
    
    <div class="step">
        <h2>📦 Step 3: Extract Files</h2>
        <p>Extract the backup archive:</p>
        <code>
# Extract the archive<br>
tar -xzf BACKUP_FILE.tar.gz
        </code>
    </div>
    
    <div class="step">
        <h2>🔧 Step 4: Setup Environment</h2>
        <p><strong>Python Setup:</strong></p>
        <code>
# Create virtual environment<br>
python -m venv venv<br><br>

# Activate virtual environment<br>
# On macOS/Linux:<br>
source venv/bin/activate<br>
# On Windows:<br>
venv\Scripts\activate<br><br>

# Install dependencies<br>
pip install -r environment/python_packages.txt
        </code>
        
        <p><strong>Docker Setup (if needed):</strong></p>
        <code>
# Navigate to docker configuration directory<br>
cd docker_config<br><br>

# Build and start containers<br>
docker-compose up -d
        </code>
        
        <div class="azure">
            <p><strong>Azure Alternative:</strong> Consider using Azure Container Registry and Azure Container Instances or App Service for hosting containerized applications.</p>
        </div>
    </div>
    
    <div class="step">
        <h2>🔓 Step 5: Decrypt Credentials</h2>
        <p>Decrypt sensitive files using the encryption key:</p>
        <code>
# Decrypt a specific file<br>
openssl enc -aes-256-cbc -d -salt -in secure_configs/filename.enc -out filename -pass file:encryption_key.txt
        </code>
        
        <div class="azure">
            <p><strong>Azure Best Practice:</strong> If connecting to Azure services in your application, consider using Managed Identity instead of connection strings and keys.</p>
        </div>
    </div>
    
    <div class="step">
        <h2>🔒 Step 6: Install Security Tools</h2>
        <p>Install security tools based on your OS:</p>
        <code>
# On macOS:<br>
bash install_security_tools_macos.sh<br><br>

# On Linux:<br>
bash install_security_tools.sh
        </code>
    </div>
    
    <div class="step">
        <h2>📱 Step 7: Configure Communication Channels</h2>
        <p>Update the configuration files for your communication services:</p>
        <ul>
            <li><strong>Email Integration:</strong> Update gmail_integration.py with your new device settings</li>
            <li><strong>Phone Integration:</strong> Update phone_utils.py with new device paths</li>
            <li><strong>Voicemail:</strong> Verify paul_edwards_voicemail.mp3 and other audio files transferred correctly</li>
        </ul>
        
        <div class="azure">
            <p><strong>Azure Options:</strong> Consider Azure Communication Services for enhanced email, SMS, and voice capabilities.</p>
        </div>
    </div>
    
    <div class="step">
        <h2>🧪 Step 8: Test Functionality</h2>
        <p>Test each component to verify successful migration:</p>
        <code>
# Core functionality<br>
python main.py<br><br>

# Email integration<br>
python email_integration_test.py<br><br>

# Security<br>
python agent_security_integration.py<br><br>

# MCP Server Status<br>
python -c "import asyncio; from start_mcp_servers import MCPServerActivator; activator = MCPServerActivator(); asyncio.run(activator.activate_all_servers()); activator.print_server_status()"
        </code>
    </div>
    
    <div class="azure">
        <h2>☁️ Azure Integration Options</h2>
        <p>Consider these Azure services to enhance your PaulEdwardsAI system:</p>
        <ul>
            <li><strong>Azure Functions:</strong> For serverless execution of discrete components</li>
            <li><strong>Azure App Service:</strong> For hosting web applications and APIs</li>
            <li><strong>Azure Container Apps:</strong> For containerized microservices</li>
            <li><strong>Azure Cognitive Services:</strong> For enhanced AI capabilities</li>
            <li><strong>Azure Key Vault:</strong> For secure credential management</li>
        </ul>
        <p>For all Azure service connections, use Managed Identity where possible instead of connection strings and access keys.</p>
    </div>
    
    <div class="success">
        <p>✅ Your PaulEdwardsAI system should now be fully migrated!</p>
        <p>If you encounter issues, check system_info/system_details.txt to verify your environment meets all requirements.</p>
    </div>
</body>
</html>
"""
        
        # Write HTML guide
        with open(os.path.join(self.backup_dir, "AZURE_MIGRATION_GUIDE.html"), "w") as f:
            f.write(html_content)
        
        # Create plain text version
        text_content = """==============================================================
                PaulEdwardsAI Azure Migration Guide
==============================================================

Step 1: Download from Azure
-------------------------
Download your backup from Azure Blob Storage:

# Configure Azure CLI
az login

# Download main backup archive
az storage blob download --account-name STORAGE_ACCOUNT --container-name CONTAINER_NAME --name BACKUP_FILE.tar.gz --file BACKUP_FILE.tar.gz --auth-mode login

Azure Best Practice: For large files (≥100MB), use Azure Storage Explorer or AzCopy for parallel downloads.

Step 2: Download Encryption Key
----------------------------
Download the encryption key from the secure container:

# Download encryption key
az storage blob download --account-name STORAGE_ACCOUNT --container-name pauledwardsai-secure --name encryption_key.txt --file encryption_key.txt --auth-mode login

Step 3: Extract Files
------------------
Extract the backup archive:

# Extract the archive
tar -xzf BACKUP_FILE.tar.gz

Step 4: Setup Environment
----------------------
Python Setup:

# Create virtual environment
python -m venv venv

# Activate virtual environment
# On macOS/Linux:
source venv/bin/activate
# On Windows:
venv\Scripts\activate

# Install dependencies
pip install -r environment/python_packages.txt

Docker Setup (if needed):

# Navigate to docker configuration directory
cd docker_config

# Build and start containers
docker-compose up -d

Step 5: Decrypt Credentials
------------------------
Decrypt sensitive files using the encryption key:

# Decrypt a specific file
openssl enc -aes-256-cbc -d -salt -in secure_configs/filename.enc -out filename -pass file:encryption_key.txt

Azure Best Practice: If connecting to Azure services, consider using Managed Identity instead of connection strings and keys.

Step 6: Install Security Tools
---------------------------
Install security tools based on your OS:

# On macOS:
bash install_security_tools_macos.sh

# On Linux:
bash install_security_tools.sh

Step 7: Configure Communication Channels
------------------------------------
Update configuration files for your communication services:
- Email: Update gmail_integration.py with your new device settings
- Phone: Update phone_utils.py with new device paths
- Voicemail: Verify paul_edwards_voicemail.mp3 transferred correctly

Step 8: Test Functionality
-----------------------
Test each component:

# Core functionality
python main.py

# Email integration
python email_integration_test.py

# Security
python agent_security_integration.py

# MCP Server Status
python -c "import asyncio; from start_mcp_servers import MCPServerActivator; activator = MCPServerActivator(); asyncio.run(activator.activate_all_servers()); activator.print_server_status()"

Azure Integration Options:
- Azure Functions: For serverless execution
- Azure App Service: For hosting web applications and APIs
- Azure Container Apps: For containerized microservices
- Azure Cognitive Services: For enhanced AI capabilities
- Azure Key Vault: For secure credential management

Your PaulEdwardsAI system should now be fully migrated!
"""
        
        with open(os.path.join(self.backup_dir, "AZURE_MIGRATION_GUIDE.txt"), "w") as f:
            f.write(text_content)
        
        print(f"{GREEN}✅ Migration guides created{RESET}")
        return True
    
    def upload_to_azure(self):
        """Upload backup to Azure with best practices for handling different file sizes"""
        print(f"{BLUE}☁️ Step 6/6: Uploading to Azure...{RESET}")
        
        # Create archive
        archive_name = f"{self.backup_dir}.tar.gz"
        print(f"Creating compressed archive: {archive_name}")
        try:
            subprocess.run(f"tar -czf {archive_name} {self.backup_dir}", shell=True, check=True)
            logger.info(f"Created archive: {archive_name}")
        except subprocess.CalledProcessError as e:
            logger.error(f"Failed to create archive: {e}")
            print(f"{RED}❌ Failed to create archive{RESET}")
            return False
        
        # Upload main archive
        print("Uploading main backup to Azure...")
        
        # Determine authentication method
        auth_params = "--auth-mode login"
        if self.in_azure_environment:
            auth_params = "--auth-mode login --identity"
        
        try:
            file_size = os.path.getsize(archive_name)
            
            # Use different approach based on file size
            if file_size >= LARGE_FILE_THRESHOLD:
                print(f"Using parallel upload for large file ({file_size / (1024*1024):.1f} MB)")
                cmd = (
                    f"az storage blob upload-batch --source . "
                    f"--destination {self.main_container} "
                    f"--account-name {self.storage_account} {auth_params} "
                    f"--pattern {archive_name} "
                    f"--max-connections {MAX_PARALLEL_UPLOADS}"
                )
            else:
                cmd = (
                    f"az storage blob upload --file {archive_name} "
                    f"--name {archive_name} "
                    f"--container-name {self.main_container} "
                    f"--account-name {self.storage_account} {auth_params}"
                )
            
            self._run_command_with_retry(cmd, capture_output=False)
            logger.info(f"Successfully uploaded {archive_name} to {self.main_container}")
            print(f"{GREEN}✅ Main backup uploaded to Azure{RESET}")
        except Exception as e:
            logger.error(f"Failed to upload main archive: {e}")
            print(f"{RED}❌ Upload failed: {str(e)}{RESET}")
            return False
        
        # Upload encryption key to separate secure container
        key_file = os.path.join(self.backup_dir, "secure_configs", "encryption_key.txt")
        if os.path.exists(key_file):
            print("Uploading encryption key to secure container...")
            try:
                cmd = (
                    f"az storage blob upload --file {key_file} "
                    f"--name encryption_key.txt "
                    f"--container-name {self.secure_container} "
                    f"--account-name {self.storage_account} {auth_params}"
                )
                self._run_command_with_retry(cmd, capture_output=False)
                logger.info(f"Successfully uploaded encryption key to {self.secure_container}")
                print(f"{GREEN}✅ Encryption key uploaded to secure container{RESET}")
            except Exception as e:
                logger.error(f"Failed to upload encryption key: {e}")
                print(f"{RED}⚠️ Failed to upload encryption key: {str(e)}{RESET}")
        
        # Create summary report
        print(f"{BLUE}Creating backup summary...{RESET}")
        summary_content = f"""
======================================================
    PaulEdwardsAI Azure Backup Summary
======================================================

Date: {datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")}
Backup Directory: {self.backup_dir}
Archive: {archive_name}

Azure Storage Details:
- Storage Account: {self.storage_account}
- Resource Group: {self.resource_group}
- Main Container: {self.main_container}
- Secure Container: {self.secure_container}

To download your backup:
az storage blob download --account-name {self.storage_account} --container-name {self.main_container} --name {archive_name} --file {archive_name} --auth-mode login

To download your encryption key:
az storage blob download --account-name {self.storage_account} --container-name {self.secure_container} --name encryption_key.txt --file encryption_key.txt --auth-mode login

Azure Best Practices:
- For large files (≥100MB), use Azure Storage Explorer or AzCopy for parallel downloads
- Use Managed Identity authentication when possible
- Ensure proper RBAC permissions on containers
- Consider Azure Key Vault for managing sensitive keys in production
"""
        
        with open("azure_backup_summary.txt", "w") as f:
            f.write(summary_content)
        
        print(f"{GREEN}✅ Azure backup completed successfully!{RESET}")
        print(f"{BLUE}======================================================{RESET}")
        print(f"📁 Backup directory: {self.backup_dir}")
        print(f"🗜️ Compressed archive: {archive_name}")
        print(f"☁️ Azure storage account: {self.storage_account}")
        print(f"{BLUE}======================================================{RESET}")
        print("")
        print("📱 Next steps:")
        print("  1️⃣ Your backup is available in Azure Blob Storage")
        print("  2️⃣ Download the backup on your new device")
        print("  3️⃣ Follow instructions in AZURE_MIGRATION_GUIDE.html")
        print(f"{BLUE}======================================================{RESET}")
        
        return True

def main():
    """Main function to run the Azure backup process"""
    parser = argparse.ArgumentParser(description=f"PaulEdwardsAI Azure Cloud Migration Tool v{VERSION}")
    parser.add_argument("--account", help="Azure storage account name")
    parser.add_argument("--resource-group", help="Azure resource group name")
    args = parser.parse_args()
    
    # Print header
    print(f"{BLUE}======================================================{RESET}")
    print(f"{GREEN}     PaulEdwardsAI Azure Cloud Migration Tool       {RESET}")
    print(f"{BLUE}======================================================{RESET}")
    print("")
    
    # Initialize and run backup
    backup_manager = AzureBackupManager(args.account, args.resource_group)
    
    try:
        backup_manager.check_azure_login()
        backup_manager.create_containers()
        
        if backup_manager.backup_files():
            backup_manager.inventory_packages()
            backup_manager.secure_credentials()
            backup_manager.collect_system_info()
            backup_manager.create_migration_guide()
            backup_manager.upload_to_azure()
    except Exception as e:
        logger.error(f"Backup process failed: {e}")
        print(f"{RED}❌ Backup process failed: {str(e)}{RESET}")
        print("Please check azure_backup.log for details")
        sys.exit(1)

if __name__ == "__main__":
    main()