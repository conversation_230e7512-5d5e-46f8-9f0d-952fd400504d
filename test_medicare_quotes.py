import asyncio
import logging
from datetime import datetime
from insurance_carriers import CarrierManager
from client_template import ClientManager
from agent_coordinator import AgentCoordinator

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def test_medicare_quote_workflow():
    """Test Medicare quote generation workflow"""
    
    logger.info("Initializing Medicare quote test...")
    
    # Initialize managers
    coordinator = AgentCoordinator()
    client_manager = ClientManager()
    
    # Test client data for Medicare
    test_client = {
        "name": "John Medicare Test",
        "dob": "1955-06-15",  # Age 68
        "height": "5'10\"",
        "weight": "175",
        "phone": "************",
        "email": "<EMAIL>",
        "address": "123 Senior Way, Port St. Lucie, FL 34952",
        "ssn": "***********",
        "drivers_license": "FL*********",
        
        "medications": [
            {
                "drug_name": "Metformin",
                "dosage": "500mg",
                "frequency": "twice daily"
            }
        ],
        
        "tobacco_use": False,
        "marijuana_use": False,
        
        "bank_info": {
            "bank_name": "Wells Fargo",
            "routing_number": "*********",
            "account_number": "*********"
        },
        
        "beneficiaries": [
            {
                "name": "Mary Test",
                "dob": "1957-08-20",
                "relationship": "spouse"
            }
        ],
        
        "budget_range": "$200-$250",
        "family_health_history": "No significant issues",
        "reason_notes": "Looking for Medicare Advantage plan",
        "start_date": datetime.now().strftime("%Y-%m-%d")
    }
    
    try:
        # Create client record
        logger.info("Creating client record...")
        client = client_manager.create_client(test_client)
        
        # Process quote requests for working carriers
        working_carriers = ["mutual_of_omaha", "americo", "aetna"]
        
        results = {}
        for carrier in working_carriers:
            logger.info(f"\nRequesting quote from {carrier.upper()}...")
            
            quote_task = {
                "type": "insurance_quote",
                "carrier": carrier,
                "agent_id": "sandra",
                "client": client,
                "product": "medicare_advantage" if carrier == "aetna" else "medicare_supplement"
            }
            
            result = await coordinator.process_task(quote_task)
            results[carrier] = result
            
            if result.get("status") == "success":
                logger.info(f"Quote received from {carrier.upper()}:")
                logger.info(f"Premium: ${result.get('premium')}")
                logger.info(f"Coverage: {result.get('coverage_type', 'N/A')}")
                logger.info(f"Benefits: {result.get('benefits', [])}")
            else:
                logger.error(f"Failed to get quote from {carrier.upper()}: {result.get('error')}")
        
        return results
        
    except Exception as e:
        logger.error(f"Error in Medicare quote workflow: {str(e)}")
        return None

async def main():
    """Run Medicare quote tests"""
    try:
        logger.info("=== Starting Medicare Quote Tests ===\n")
        
        results = await test_medicare_quote_workflow()
        
        if results:
            logger.info("\nQuote Comparison:")
            logger.info("================")
            
            for carrier, quote in results.items():
                if quote.get("status") == "success":
                    logger.info(f"\n{carrier.upper()}:")
                    logger.info(f"Premium: ${quote.get('premium')}")
                    logger.info(f"Plan Type: {quote.get('plan_type', 'N/A')}")
                    logger.info(f"Coverage: {quote.get('coverage_type', 'N/A')}")
            
            logger.info("\nMedicare quote testing completed successfully!")
        else:
            logger.error("Medicare quote testing failed!")
            
    except Exception as e:
        logger.error(f"Test execution failed: {str(e)}")

if __name__ == "__main__":
    asyncio.run(main())