#!/usr/bin/env python3
"""
Insurance Carrier Access Module

This module provides a comprehensive system for accessing insurance carrier portals,
particularly with the new Anthem & Cigna PPO Private Health Insurance portals.
It integrates with the credential manager and portal automation systems.
"""

import os
import sys
import json
import logging
import requests
from typing import Dict, List, Any, Optional
from pathlib import Path

# Try importing our credential manager - will use standalone methods if not available
try:
    from credential_manager import SecureCredentialManager
    CREDENTIAL_MANAGER_AVAILABLE = True
except ImportError:
    CREDENTIAL_MANAGER_AVAILABLE = False
    print("Warning: Credential manager not available, using basic credential storage")

# Configure logging
logging.basicConfig(level=logging.INFO,
                    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class InsuranceCarrierAccess:
    """Insurance carrier portal access manager"""
    
    def __init__(self, use_secure_storage=True):
        """
        Initialize the insurance carrier access manager
        
        Args:
            use_secure_storage: Whether to use secure credential storage
        """
        self.use_secure_storage = use_secure_storage and CREDENTIAL_MANAGER_AVAILABLE
        self.credential_manager = None
        
        if self.use_secure_storage:
            self.credential_manager = SecureCredentialManager()
            
        # Portal credentials (used if secure storage not available)
        self.portal_credentials = {}
        
        # Initialize with known portals and credentials
        self._initialize_portal_credentials()
        
        # Carrier information
        self.carriers = {
            "anthem": {
                "name": "Anthem",
                "portal_url": "https://www.1enrollment.com/manage",
                "enrollment_url": "https://www.1enrollment.com/agents/signup.cfm?id=860559",
                "agent_url": "http://www.1enrollment.com/867852",
                "products": ["PPO", "Health Insurance", "Medicare Advantage", "Medicare Supplement"]
            },
            "cigna": {
                "name": "Cigna",
                "portal_url": "https://www.1enrollment.com/manage",
                "enrollment_url": "https://www.1enrollment.com/agents/signup.cfm?id=860559",
                "agent_url": "http://www.1enrollment.com/867852",
                "products": ["PPO", "Health Insurance", "Medicare Advantage", "Medicare Supplement"]
            },
            "aetna": {
                "name": "Aetna",
                "portal_url": "https://www.aetna.com/producer/Medicare/medicare_individual.html",
                "products": ["Medicare Advantage", "Medicare Supplement", "Health Insurance"]
            },
            "united_healthcare": {
                "name": "United Healthcare",
                "portal_url": "https://www.uhcjarvis.com/content/jarvis/en/secure/dashboard.html",
                "products": ["Medicare Advantage", "Medicare Supplement", "Health Insurance"]
            },
            "humana": {
                "name": "Humana",
                "portal_url": "https://www.humana.com/agent/tools/",
                "products": ["Medicare Advantage", "Medicare Supplement", "Health Insurance"]
            },
            "mutual_of_omaha": {
                "name": "Mutual of Omaha",
                "portal_url": "https://sales.mutualofomaha.com/",
                "products": ["Medicare Supplement", "Life Insurance", "Long-Term Care"]
            },
            "americo": {
                "name": "Americo",
                "portal_url": "https://agents.americo.com/login",
                "products": ["Life Insurance", "Final Expense"]
            },
            "ffl": {
                "name": "FFL",
                "portal_url": "https://fflags.com",
                "products": ["Medicare Advantage", "Medicare Supplement", "Life Insurance"]
            },
            "crump": {
                "name": "Crump",
                "portal_url": "https://agent.crump.com",
                "products": ["Life Insurance", "Annuities", "Long-Term Care"]
            },
            "healthsherpa": {
                "name": "HealthSherpa",
                "portal_url": "https://healthsherpa.com/login",
                "products": ["ACA Health Insurance", "Marketplace Plans"]
            }
        }
        
    def _initialize_portal_credentials(self):
        """Initialize portal credentials"""
        # 1Enrollment portal (Anthem/Cigna)
        enrollment_creds = {
            "username": "SG1959PE",
            "password": "GodisSoGood!777",
            "agent_id": "867852",
            "url": "https://www.1enrollment.com/manage"
        }
        
        # Save to secure storage if available
        if self.use_secure_storage:
            self.credential_manager.add_credentials("1enrollment", enrollment_creds)
            self.credential_manager.add_credentials("anthem", enrollment_creds)
            self.credential_manager.add_credentials("cigna", enrollment_creds)
        else:
            self.portal_credentials["1enrollment"] = enrollment_creds
            self.portal_credentials["anthem"] = enrollment_creds
            self.portal_credentials["cigna"] = enrollment_creds
            
        # Standard credentials used across portals
        standard_creds = {
            "username": "SG1959PE",
            "password": "GodisSoGood!777",
            "agency_name": "Flo Faction LLC",
            "agent_name": "Sandra Sencion García"
        }
        
        # Apply standard credentials to all carriers
        for carrier in ["aetna", "united_healthcare", "humana", "mutual_of_omaha", "ffl", "crump"]:
            if carrier in self.carriers:
                creds = standard_creds.copy()
                creds["url"] = self.carriers[carrier]["portal_url"]
                
                if self.use_secure_storage:
                    self.credential_manager.add_credentials(carrier, creds)
                else:
                    self.portal_credentials[carrier] = creds
                    
        logger.info("Portal credentials initialized")
        
    def get_credentials(self, carrier_name):
        """
        Get credentials for a carrier portal
        
        Args:
            carrier_name: Name of the carrier
            
        Returns:
            Dictionary with credentials or None if not found
        """
        carrier_name = carrier_name.lower().replace(" ", "_")
        
        if self.use_secure_storage:
            return self.credential_manager.get_credentials(carrier_name)
        else:
            return self.portal_credentials.get(carrier_name)
            
    def get_carrier_info(self, carrier_name):
        """
        Get information about a carrier
        
        Args:
            carrier_name: Name of the carrier
            
        Returns:
            Dictionary with carrier information or None if not found
        """
        carrier_name = carrier_name.lower().replace(" ", "_")
        return self.carriers.get(carrier_name)
        
    def list_carriers(self):
        """
        List all available carriers
        
        Returns:
            List of carrier names
        """
        return list(self.carriers.keys())
        
    def get_carriers_by_product(self, product_type):
        """
        Get carriers that offer a specific product
        
        Args:
            product_type: Type of insurance product
            
        Returns:
            List of carrier names
        """
        carriers = []
        product_type = product_type.lower()
        
        for carrier_name, carrier_info in self.carriers.items():
            products = [p.lower() for p in carrier_info.get("products", [])]
            if any(product_type in p for p in products):
                carriers.append(carrier_name)
                
        return carriers
        
    def access_portal(self, carrier_name, use_browser=False):
        """
        Access a carrier portal
        
        Args:
            carrier_name: Name of the carrier
            use_browser: Whether to open a browser or just return access info
            
        Returns:
            Dictionary with portal access details
        """
        carrier_name = carrier_name.lower().replace(" ", "_")
        
        # Get carrier info
        carrier_info = self.get_carrier_info(carrier_name)
        if not carrier_info:
            logger.error(f"Carrier not found: {carrier_name}")
            return {"error": f"Carrier not found: {carrier_name}"}
            
        # Get credentials
        credentials = self.get_credentials(carrier_name)
        if not credentials:
            logger.error(f"Credentials not found for carrier: {carrier_name}")
            return {"error": f"Credentials not found for carrier: {carrier_name}"}
            
        portal_url = carrier_info.get("portal_url")
        if not portal_url:
            logger.error(f"Portal URL not found for carrier: {carrier_name}")
            return {"error": f"Portal URL not found for carrier: {carrier_name}"}
            
        # Open browser if requested
        if use_browser:
            try:
                import webbrowser
                webbrowser.open(portal_url)
                logger.info(f"Opened browser for {carrier_name} portal")
            except Exception as e:
                logger.error(f"Error opening browser: {e}")
                return {"error": f"Error opening browser: {e}"}
                
        # Return access details
        return {
            "carrier": carrier_info["name"],
            "portal_url": portal_url,
            "credentials": {
                "username": credentials.get("username"),
                "password": credentials.get("password")
            },
            "agent_id": credentials.get("agent_id"),
            "products": carrier_info.get("products", [])
        }
        
    def test_portal_access(self, carrier_name):
        """
        Test access to a carrier portal (without actually logging in)
        
        Args:
            carrier_name: Name of the carrier
            
        Returns:
            Dictionary with test results
        """
        carrier_name = carrier_name.lower().replace(" ", "_")
        
        # Get carrier info and credentials
        carrier_info = self.get_carrier_info(carrier_name)
        credentials = self.get_credentials(carrier_name)
        
        if not carrier_info or not credentials:
            return {"success": False, "error": "Carrier info or credentials not found"}
            
        portal_url = carrier_info.get("portal_url")
        if not portal_url:
            return {"success": False, "error": "Portal URL not found"}
            
        # Check if portal is reachable
        try:
            response = requests.head(portal_url, timeout=10)
            status_code = response.status_code
            
            return {
                "success": 200 <= status_code < 400,
                "carrier": carrier_info["name"],
                "portal_url": portal_url,
                "status_code": status_code,
                "credentials_available": bool(credentials.get("username") and credentials.get("password"))
            }
        except requests.RequestException as e:
            return {
                "success": False,
                "carrier": carrier_info["name"],
                "portal_url": portal_url,
                "error": str(e),
                "credentials_available": bool(credentials.get("username") and credentials.get("password"))
            }


# Example usage
if __name__ == "__main__":
    # Initialize carrier access
    carrier_access = InsuranceCarrierAccess(use_secure_storage=CREDENTIAL_MANAGER_AVAILABLE)
    
    # Process command line arguments
    import argparse
    parser = argparse.ArgumentParser(description="Insurance Carrier Access")
    parser.add_argument("--list", action="store_true", help="List all carriers")
    parser.add_argument("--carrier", help="Carrier to access")
    parser.add_argument("--product", help="List carriers by product type")
    parser.add_argument("--open", action="store_true", help="Open portal in browser")
    parser.add_argument("--test", action="store_true", help="Test portal access")
    
    args = parser.parse_args()
    
    if args.list:
        print("Available carriers:")
        for carrier in carrier_access.list_carriers():
            carrier_info = carrier_access.get_carrier_info(carrier)
            print(f"- {carrier_info['name']}: {', '.join(carrier_info.get('products', []))}")
            
    elif args.product:
        carriers = carrier_access.get_carriers_by_product(args.product)
        print(f"Carriers offering {args.product}:")
        for carrier in carriers:
            carrier_info = carrier_access.get_carrier_info(carrier)
            print(f"- {carrier_info['name']}")
            
    elif args.carrier:
        if args.test:
            result = carrier_access.test_portal_access(args.carrier)
            print(f"Test results for {args.carrier}:")
            for key, value in result.items():
                print(f"- {key}: {value}")
        else:
            access_info = carrier_access.access_portal(args.carrier, use_browser=args.open)
            
            if "error" in access_info:
                print(f"Error: {access_info['error']}")
            else:
                print(f"Access information for {access_info['carrier']}:")
                print(f"- Portal URL: {access_info['portal_url']}")
                print(f"- Username: {access_info['credentials']['username']}")
                print(f"- Password: {'*' * len(access_info['credentials']['password'])}")
                if "agent_id" in access_info and access_info["agent_id"]:
                    print(f"- Agent ID: {access_info['agent_id']}")
                print(f"- Products: {', '.join(access_info['products'])}")
            
    else:
        parser.print_help()