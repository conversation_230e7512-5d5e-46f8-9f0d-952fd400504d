#!/usr/bin/env python3
import os
import sys
import json
import time
import logging
import requests
import argparse
from datetime import datetime, timedelta

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s [%(levelname)s] %(message)s",
    handlers=[logging.StreamHandler()]
)
logger = logging.getLogger("drip-campaign")

class DripCampaign:
    """Insurance Lead Drip Campaign Manager"""

    def __init__(self, client_name, client_phone, client_email=None, client_location=None):
        self.client_name = client_name
        self.client_phone = client_phone
        self.client_email = client_email
        self.client_location = client_location
        self.communication_server_url = "http://localhost:8084"
        self.workflow_server_url = "http://localhost:8087"

    def check_servers(self):
        """Check if required servers are running"""
        servers = [
            {"name": "Communication Server", "url": f"{self.communication_server_url}/health"},
            {"name": "Workflow Server", "url": f"{self.workflow_server_url}/health"}
        ]

        all_running = True
        for server in servers:
            try:
                response = requests.get(server["url"], timeout=5)
                if response.status_code == 200:
                    logger.info(f"✅ {server['name']} is running")
                else:
                    logger.error(f"❌ {server['name']} returned status code {response.status_code}")
                    all_running = False
            except Exception as e:
                logger.error(f"❌ {server['name']} is not running: {e}")
                all_running = False

        return all_running

    def create_workflow(self):
        """Create a drip campaign workflow"""
        logger.info(f"Creating drip campaign workflow for {self.client_name}")

        # Define the workflow steps
        steps = [
            # Day 1: Initial contact
            {
                "type": "email",
                "action": "send_email",
                "params": {
                    "to": self.client_email,
                    "subject": f"Hello from Flo Faction Insurance, {self.client_name}",
                    "body": f"Dear {self.client_name},\n\nMy name is Paul Edwards from Flo Faction Insurance. I hope this email finds you well. I wanted to reach out to introduce myself and our insurance services.\n\nWe specialize in providing comprehensive insurance solutions tailored to your specific needs. I would love to schedule a brief call to discuss how we might be able to help you.\n\nPlease let me know if you're available for a quick chat in the coming days.\n\nBest regards,\nPaul Edwards\nFlo Faction Insurance\n(*************"
                },
                "schedule": "now"
            },
            {
                "type": "sms",
                "action": "send_sms",
                "params": {
                    "to": self.client_phone,
                    "message": f"Hi {self.client_name}, this is Paul Edwards from Flo Faction Insurance. I just sent you an email introducing our services. I'd love to chat about your insurance needs when you have a moment. Feel free to call me at (************* or reply to this text."
                },
                "schedule": "+1 hour"
            },

            # Day 2: Follow-up call
            {
                "type": "call",
                "action": "make_call",
                "params": {
                    "to": self.client_phone,
                    "message": f"Hello {self.client_name}, this is Paul Edwards from Flo Faction Insurance calling. I sent you an email and text yesterday introducing our services. I'm calling to see if you have any questions or if you'd like to discuss your insurance options. If this isn't a good time, please feel free to call me back at (************* at your convenience. Thank you and have a great day!"
                },
                "schedule": "+1 day"
            },

            # Day 3: Educational email
            {
                "type": "email",
                "action": "send_email",
                "params": {
                    "to": self.client_email,
                    "subject": "Insurance Tips for Florida Residents",
                    "body": f"Dear {self.client_name},\n\nI hope this email finds you well. I wanted to share some valuable insurance tips specifically for Florida residents like yourself.\n\nLiving in Florida comes with unique insurance considerations, especially regarding:\n\n1. Hurricane coverage\n2. Flood insurance\n3. Home insurance in coastal areas\n\nI'd be happy to discuss these topics with you and provide personalized advice for your situation. Would you be available for a brief call this week?\n\nBest regards,\nPaul Edwards\nFlo Faction Insurance\n(*************"
                },
                "schedule": "+3 days"
            },

            # Day 5: Second follow-up call and voicemail
            {
                "type": "call",
                "action": "make_call",
                "params": {
                    "to": self.client_phone,
                    "message": f"Hello {self.client_name}, this is Paul Edwards from Flo Faction Insurance calling again. I wanted to follow up on the email I sent about insurance tips for Florida residents. I believe I could provide valuable insights for your specific situation. Please give me a call back at (************* when you have a moment. Thank you!"
                },
                "schedule": "+5 days"
            },

            # Day 7: Final follow-up text
            {
                "type": "sms",
                "action": "send_sms",
                "params": {
                    "to": self.client_phone,
                    "message": f"Hi {self.client_name}, Paul Edwards from Flo Faction Insurance here. I'm still available to discuss your insurance needs whenever is convenient for you. As a Florida resident, having the right coverage is crucial. Call or text me at (************* when you're ready to chat."
                },
                "schedule": "+7 days"
            }
        ]

        # Create the workflow
        workflow_data = {
            "name": f"Drip Campaign for {self.client_name}",
            "steps": steps
        }

        try:
            response = requests.post(
                f"{self.workflow_server_url}/workflow/create",
                json=workflow_data
            )

            if response.status_code == 200:
                workflow_id = response.json().get("workflow_id")
                logger.info(f"✅ Created workflow with ID: {workflow_id}")
                return workflow_id
            else:
                logger.error(f"❌ Failed to create workflow: {response.text}")
                return None
        except Exception as e:
            logger.error(f"❌ Error creating workflow: {e}")
            return None

    def start_campaign(self):
        """Start the drip campaign"""
        logger.info(f"Starting drip campaign for {self.client_name}")

        # Check if servers are running
        if not self.check_servers():
            logger.error("❌ Cannot start campaign: Required servers are not running")
            return False

        # Create the workflow
        workflow_id = self.create_workflow()
        if not workflow_id:
            logger.error("❌ Cannot start campaign: Failed to create workflow")
            return False

        # Execute the workflow
        try:
            response = requests.post(
                f"{self.workflow_server_url}/workflow/execute",
                json={"workflow_id": workflow_id}
            )

            if response.status_code == 200:
                logger.info(f"✅ Started drip campaign for {self.client_name}")
                return True
            else:
                logger.error(f"❌ Failed to start campaign: {response.text}")
                return False
        except Exception as e:
            logger.error(f"❌ Error starting campaign: {e}")
            return False

    def send_immediate_notification(self):
        """Send an immediate notification to the insurance agent"""
        logger.info("Sending immediate notification to insurance agent")

        # Send email notification
        email_data = {
            "to": "<EMAIL>",
            "subject": f"New Insurance Lead: {self.client_name}",
            "body": f"Dear Paul,\n\nA new insurance lead has been added to the system:\n\nName: {self.client_name}\nPhone: {self.client_phone}\nEmail: {self.client_email or 'Not provided'}\nLocation: {self.client_location or 'Not provided'}\n\nA drip campaign has been automatically set up for this lead. The first contact has been initiated.\n\nBest regards,\nFlo Faction Insurance System"
        }

        try:
            response = requests.post(
                f"{self.communication_server_url}/send_email",
                json=email_data
            )

            if response.status_code == 200:
                logger.info("✅ Sent email notification to insurance agent")
            else:
                logger.error(f"❌ Failed to send email notification: {response.text}")
        except Exception as e:
            logger.error(f"❌ Error sending email notification: {e}")

        # Send SMS notification
        sms_data = {
            "to": "7722089646",
            "message": f"New insurance lead: {self.client_name} ({self.client_phone}). Drip campaign started. Check your email for details."
        }

        try:
            response = requests.post(
                f"{self.communication_server_url}/send_sms",
                json=sms_data
            )

            if response.status_code == 200:
                logger.info("✅ Sent SMS notification to insurance agent")
            else:
                logger.error(f"❌ Failed to send SMS notification: {response.text}")
        except Exception as e:
            logger.error(f"❌ Error sending SMS notification: {e}")

def main():
    parser = argparse.ArgumentParser(description="Insurance Lead Drip Campaign")
    parser.add_argument("--name", required=True, help="Client name")
    parser.add_argument("--phone", required=True, help="Client phone number")
    parser.add_argument("--email", help="Client email address")
    parser.add_argument("--location", help="Client location")
    args = parser.parse_args()

    # Create and start the drip campaign
    campaign = DripCampaign(
        client_name=args.name,
        client_phone=args.phone,
        client_email=args.email,
        client_location=args.location
    )

    # Send immediate notification
    campaign.send_immediate_notification()

    # Start the campaign
    if campaign.start_campaign():
        logger.info(f"✅ Successfully started drip campaign for {args.name}")
    else:
        logger.error(f"❌ Failed to start drip campaign for {args.name}")

if __name__ == "__main__":
    main()
