"""
Agent Integration Module - Coordinates all agents to work together seamlessly
"""

import logging
import asyncio
from typing import Dict, List, Optional, Union, Any
import subprocess
import shlex
from dotenv import load_dotenv
import os

# Load environment variables from .env file
load_dotenv()
from datetime import datetime
from enum import Enum
import json

from agent_coordinator import AgentCoordinator
from content_creation_agent import ContentAgent
from insurance_carriers import CarrierManager
from knowledge_management import KnowledgeManager
from trading_agent import TradingAgent
from client_template import ClientManager
from secure_credentials import SecureCredentialsManager
from social_media_manager import SocialMediaManager
from mcp_server_registry import registry as mcp_registry

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class AgentRole(Enum):
    INSURANCE = "insurance"
    CONTENT = "content"
    TRADING = "trading"
    LEARNING = "learning"
    COMMUNICATION = "communication"
    SOCIAL_MEDIA = "social_media"
    MCP = "mcp"  # Added MCP agent role

class AgentIntegration:
    """Manages integration and coordination between all agents"""
    
    def __init__(self):
        self.coordinator = AgentCoordinator()
        self.carrier_manager = CarrierManager()
        self.knowledge_manager = KnowledgeManager()
        self.client_manager = ClientManager()
        self.content_agent = ContentAgent()
        self.trading_agent = TradingAgent()
        self.creds_manager = SecureCredentialsManager()
        self.social_media_manager = SocialMediaManager(self.creds_manager)
        
        # Track active workflows
        self.active_workflows = {}
        self.agent_states = {}
        
        # Memory systems
        self.short_term_memory = {}  # Stores in-session conversational history
        self.long_term_memory = None  # Placeholder for vector database integration
        
        # MCP server configuration
        self.mcp_servers_initialized = False
        self.default_mcp_servers = [
            {
                "server_id": "local-codegen",
                "name": "Local Code Generation",
                "url": "http://localhost:8080",
                "capabilities": ["code-completion", "text-embedding"]
            },
            {
                "server_id": "ai-agent-mcp",
                "name": "AI Agent MCP",
                "url": "http://localhost:8081",
                "capabilities": ["agent-tools", "code-execution"]
            }
        ]
        
    async def initialize_memory_systems(self):
        """Initialize short-term and long-term memory systems"""
        try:
            # Initialize short-term memory
            self.short_term_memory = {}
            
            # Initialize long-term memory with ChromaDB
            from chromadb import Client
            self.long_term_memory = Client()
            self.long_term_memory.create_collection("agent_memory")
            
            logger.info("Memory systems initialized successfully")
        except Exception as e:
            logger.error(f"Error initializing memory systems: {str(e)}")

        # API Endpoints
        self.api_endpoints = {
            "get_agent_status": self.get_agent_status,
            "get_workflow_status": self.get_workflow_status,
            "submit_workflow": self.submit_workflow
        }
        
    async def initialize_agents(self):
        """Initialize all agent systems"""
        try:
            logger.info("Initializing agent systems...")
            
            # Initialize knowledge base
            await self.knowledge_manager.initialize()
            
            # Initialize carrier connections
            await self.carrier_manager.initialize()
            
            # Initialize content agent
            await self.content_agent.initialize()
            
            # Initialize trading agent
            await self.trading_agent.initialize()
            
            # Initialize MCP servers
            await self.initialize_mcp_servers()
            
            # Set up agent states
            self.agent_states = {
                AgentRole.INSURANCE: {"status": "ready", "tasks": []},
                AgentRole.CONTENT: {"status": "ready", "tasks": []},
                AgentRole.TRADING: {"status": "ready", "tasks": []},
                AgentRole.LEARNING: {"status": "ready", "tasks": []},
                AgentRole.COMMUNICATION: {"status": "ready", "tasks": []},
                AgentRole.SOCIAL_MEDIA: {"status": "ready", "tasks": []},
                AgentRole.MCP: {"status": "ready", "tasks": []}  # Added MCP agent state
            }
            
            logger.info("All agent systems initialized successfully")
            return True
            
        except Exception as e:
            logger.error(f"Error initializing agents: {str(e)}")
            return False
    
    async def initialize_mcp_servers(self):
        """Initialize and register MCP servers"""
        try:
            if self.mcp_servers_initialized:
                return
                
            logger.info("Initializing MCP servers...")
            
            # Initialize the MCP registry
            await mcp_registry.initialize()
            
            # Register default MCP servers if no servers are registered
            if len(mcp_registry.get_all_servers()) == 0:
                for server_config in self.default_mcp_servers:
                    mcp_registry.register_server(
                        server_config["server_id"],
                        server_config["name"],
                        server_config["url"],
                        capabilities=server_config.get("capabilities", [])
                    )
                    
            # Get all active servers and log their status
            await mcp_registry.check_all_servers()
            active_servers = mcp_registry.get_active_servers()
            logger.info(f"Active MCP servers: {len(active_servers)}/{len(mcp_registry.get_all_servers())}")
            
            for server in active_servers:
                logger.info(f"  - {server.name} ({server.url}): {', '.join(server.capabilities)}")
                
            self.mcp_servers_initialized = True
            return True
                
        except Exception as e:
            logger.error(f"Error initializing MCP servers: {str(e)}")
            return False
    
    async def register_mcp_server(self, server_id: str, name: str, url: str, 
                                capabilities: Optional[List[str]] = None) -> bool:
        """Register a new MCP server"""
        try:
            server = mcp_registry.register_server(server_id, name, url, capabilities=capabilities)
            await server.ping()
            return server.status == "active"
        except Exception as e:
            logger.error(f"Error registering MCP server: {str(e)}")
            return False
            
    def get_active_mcp_servers(self) -> List[Dict]:
        """Get list of all active MCP servers"""
        try:
            return [server.to_dict() for server in mcp_registry.get_active_servers()]
        except Exception as e:
            logger.error(f"Error getting active MCP servers: {str(e)}")
            return []
    
    def get_mcp_servers_by_capability(self, capability: str) -> List[Dict]:
        """Get MCP servers with specific capability"""
        try:
            return [server.to_dict() for server in mcp_registry.get_servers_by_capability(capability)]
        except Exception as e:
            logger.error(f"Error getting MCP servers by capability: {str(e)}")
            return []
    
    async def process_mcp_workflow(self, workflow_type: str, data: Dict):
        """Process workflow involving MCP servers"""
        try:
            workflow_id = f"{workflow_type}_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            
            logger.info(f"Starting MCP workflow: {workflow_id}")
            self.active_workflows[workflow_id] = {
                "type": workflow_type,
                "status": "in_progress",
                "steps": [],
                "start_time": datetime.now()
            }
            
            # Ensure MCP servers are initialized
            if not self.mcp_servers_initialized:
                await self.initialize_mcp_servers()
                
            # Get appropriate MCP servers based on workflow type
            capability = data.get("capability", "agent-tools")
            servers = mcp_registry.get_servers_by_capability(capability)
            
            if not servers:
                logger.warning(f"No MCP servers found with capability: {capability}")
                self.active_workflows[workflow_id]["status"] = "failed"
                self.active_workflows[workflow_id]["error"] = f"No MCP servers found with capability: {capability}"
                return None
                
            # Select first available server
            server = servers[0]
            logger.info(f"Using MCP server: {server.name} for workflow: {workflow_type}")
            
            # Process the workflow with the MCP server
            result = await self._execute_step(
                workflow_id,
                AgentRole.MCP,
                "process_with_mcp",
                {
                    "server_id": server.server_id,
                    "workflow_type": workflow_type,
                    "data": data
                }
            )
            
            self.active_workflows[workflow_id]["status"] = "completed"
            self.active_workflows[workflow_id]["end_time"] = datetime.now()
            self.active_workflows[workflow_id]["result"] = result
            
            return result
            
        except Exception as e:
            logger.error(f"Error in MCP workflow {workflow_type}: {str(e)}")
            if workflow_id in self.active_workflows:
                self.active_workflows[workflow_id]["status"] = "failed"
                self.active_workflows[workflow_id]["error"] = str(e)
            return None

    async def process_workflow(self, workflow_type: str, data: Dict):
        """Process a complete workflow involving multiple agents"""
        try:
            workflow_id = f"{workflow_type}_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            
            logger.info(f"Starting workflow: {workflow_id}")
            self.active_workflows[workflow_id] = {
                "type": workflow_type,
                "status": "in_progress",
                "steps": [],
                "start_time": datetime.now()
            }
            
            # Check if this is an MCP-related workflow
            if workflow_type.startswith("mcp_"):
                return await self.process_mcp_workflow(workflow_type, data)
            
            if workflow_type == "medicare_enrollment":
                result = await self._handle_medicare_workflow(workflow_id, data)
            elif workflow_type == "trading_analysis":
                result = await self._handle_trading_workflow(workflow_id, data)
            elif workflow_type == "policy_service":
                result = await self._handle_policy_workflow(workflow_id, data)
            elif workflow_type == "social_media_campaign":
                result = await self._handle_social_media_workflow(workflow_id, data)
            else:
                raise ValueError(f"Unknown workflow type: {workflow_type}")
                
            self.active_workflows[workflow_id]["status"] = "completed"
            self.active_workflows[workflow_id]["end_time"] = datetime.now()
            self.active_workflows[workflow_id]["result"] = result
            
            return result
            
        except Exception as e:
            logger.error(f"Error in workflow {workflow_type}: {str(e)}")
            if workflow_id in self.active_workflows:
                self.active_workflows[workflow_id]["status"] = "failed"
                self.active_workflows[workflow_id]["error"] = str(e)
            return None
            
    async def _handle_medicare_workflow(self, workflow_id: str, data: Dict):
        """Handle Medicare enrollment workflow"""
        steps = []
        
        try:
            # 1. Create/update client record
            client = await self._execute_step(
                workflow_id,
                AgentRole.INSURANCE,
                "create_client",
                data
            )
            steps.append(("client_record", "success"))
            
            # 2. Get carrier quotes
            quotes = await self._execute_step(
                workflow_id,
                AgentRole.INSURANCE,
                "get_quotes",
                {"client": client}
            )
            steps.append(("quotes", "success"))
            
            # 3. Generate enrollment content
            content = await self._execute_step(
                workflow_id,
                AgentRole.CONTENT,
                "create_enrollment_content",
                {
                    "client": client,
                    "quotes": quotes
                }
            )
            steps.append(("content", "success"))
            
            # 4. Send communications
            comms = await self._execute_step(
                workflow_id,
                AgentRole.COMMUNICATION,
                "send_communications",
                {
                    "client": client,
                    "content": content,
                    "type": "enrollment"
                }
            )
            steps.append(("communications", "success"))
            
            # 5. Update knowledge base
            await self._execute_step(
                workflow_id,
                AgentRole.LEARNING,
                "update_knowledge",
                {
                    "workflow_type": "medicare_enrollment",
                    "client_data": client,
                    "outcome": "success"
                }
            )
            steps.append(("knowledge_update", "success"))
            
            return {
                "status": "success",
                "steps": steps,
                "client_id": client.id,
                "quote_ids": [q.id for q in quotes]
            }
            
        except Exception as e:
            logger.error(f"Error in Medicare workflow: {str(e)}")
            return {
                "status": "failed",
                "error": str(e),
                "steps": steps
            }
            
    async def _handle_trading_workflow(self, workflow_id: str, data: Dict):
        """Handle trading analysis and execution workflow"""
        steps = []
        
        try:
            # 1. Analyze market data
            analysis = await self._execute_step(
                workflow_id,
                AgentRole.TRADING,
                "analyze_market",
                data
            )
            steps.append(("market_analysis", "success"))
            
            # 2. Generate trading signals
            signals = await self._execute_step(
                workflow_id,
                AgentRole.TRADING,
                "generate_signals",
                {
                    "analysis": analysis,
                    "market_data": data
                }
            )
            steps.append(("signals", "success"))
            
            # 3. Execute trades if appropriate
            if signals.get("execute_trade", False):
                trades = await self._execute_step(
                    workflow_id,
                    AgentRole.TRADING,
                    "execute_trades",
                    {
                        "signals": signals,
                        "market_data": data
                    }
                )
                steps.append(("trade_execution", "success"))
            
            # 4. Generate trade reports
            reports = await self._execute_step(
                workflow_id,
                AgentRole.CONTENT,
                "create_trade_report",
                {
                    "analysis": analysis,
                    "signals": signals,
                    "trades": trades if "trades" in locals() else None
                }
            )
            steps.append(("reports", "success"))
            
            # 5. Update knowledge base
            await self._execute_step(
                workflow_id,
                AgentRole.LEARNING,
                "update_knowledge",
                {
                    "workflow_type": "trading",
                    "analysis": analysis,
                    "outcome": "success"
                }
            )
            steps.append(("knowledge_update", "success"))
            
            return {
                "status": "success",
                "steps": steps,
                "analysis_id": analysis.id,
                "signals": signals
            }
            
        except Exception as e:
            logger.error(f"Error in trading workflow: {str(e)}")
            return {
                "status": "failed",
                "error": str(e),
                "steps": steps
            }
            
    async def _handle_policy_workflow(self, workflow_id: str, data: Dict):
        """Handle policy service and updates workflow"""
        steps = []
        
        try:
            # 1. Validate policy changes
            validation = await self._execute_step(
                workflow_id,
                AgentRole.INSURANCE,
                "validate_changes",
                data
            )
            steps.append(("validation", "success"))
            
            # 2. Process policy updates
            updates = await self._execute_step(
                workflow_id,
                AgentRole.INSURANCE,
                "process_updates",
                {
                    "validation": validation,
                    "changes": data
                }
            )
            steps.append(("updates", "success"))
            
            # 3. Generate documentation
            docs = await self._execute_step(
                workflow_id,
                AgentRole.CONTENT,
                "create_policy_docs",
                {
                    "updates": updates,
                    "client": data["client"]
                }
            )
            steps.append(("documentation", "success"))
            
            # 4. Send notifications
            notifications = await self._execute_step(
                workflow_id,
                AgentRole.COMMUNICATION,
                "send_notifications",
                {
                    "client": data["client"],
                    "updates": updates,
                    "docs": docs
                }
            )
            steps.append(("notifications", "success"))
            
            # 5. Update knowledge base
            await self._execute_step(
                workflow_id,
                AgentRole.LEARNING,
                "update_knowledge",
                {
                    "workflow_type": "policy_service",
                    "updates": updates,
                    "outcome": "success"
                }
            )
            steps.append(("knowledge_update", "success"))
            
            return {
                "status": "success",
                "steps": steps,
                "update_id": updates.id
            }
            
        except Exception as e:
            logger.error(f"Error in policy workflow: {str(e)}")
            return {
                "status": "failed",
                "error": str(e),
                "steps": steps
            }
            
    async def _handle_social_media_workflow(self, workflow_id: str, data: Dict):
        """Handle social media content creation and posting workflow"""
        steps = []
        
        try:
            # 1. Generate social media content
            content = await self._execute_step(
                workflow_id,
                AgentRole.CONTENT,
                "create_social_content",
                data
            )
            steps.append(("content_creation", "success"))
            
            # 2. Format content for different platforms
            formatted_content = await self._execute_step(
                workflow_id,
                AgentRole.SOCIAL_MEDIA,
                "format_content",
                {
                    "content": content,
                    "platforms": data.get("platforms", ["instagram", "tiktok", "wix"])
                }
            )
            steps.append(("content_formatting", "success"))
            
            # 3. Create a posting schedule
            schedule = await self._execute_step(
                workflow_id,
                AgentRole.SOCIAL_MEDIA,
                "create_schedule",
                {
                    "content": formatted_content,
                    "start_date": data.get("start_date"),
                    "frequency": data.get("frequency", "weekly")
                }
            )
            steps.append(("scheduling", "success"))
            
            # 4. Post or schedule the content
            if data.get("post_now", False):
                posting = await self._execute_step(
                    workflow_id,
                    AgentRole.SOCIAL_MEDIA,
                    "post_content",
                    {
                        "content": formatted_content,
                        "platforms": data.get("platforms", ["instagram", "tiktok", "wix"])
                    }
                )
                steps.append(("posting", "success"))
            else:
                posting = await self._execute_step(
                    workflow_id,
                    AgentRole.SOCIAL_MEDIA,
                    "schedule_content",
                    {
                        "content": formatted_content,
                        "schedule": schedule
                    }
                )
                steps.append(("scheduling_posts", "success"))
            
            # 5. Update knowledge base
            await self._execute_step(
                workflow_id,
                AgentRole.LEARNING,
                "update_knowledge",
                {
                    "workflow_type": "social_media",
                    "content": content,
                    "outcome": "success"
                }
            )
            steps.append(("knowledge_update", "success"))
            
            return {
                "status": "success",
                "steps": steps,
                "content_id": content.get("id"),
                "platforms": data.get("platforms", ["instagram", "tiktok", "wix"]),
                "post_ids": posting.get("post_ids", []) if "posting" in locals() else schedule.get("schedule_ids", [])
            }
            
        except Exception as e:
            logger.error(f"Error in social media workflow: {str(e)}")
            return {
                "status": "failed",
                "error": str(e),
                "steps": steps
            }
            
    async def _execute_step(self, workflow_id: str, agent: AgentRole, 
                          action: str, data: Dict) -> Any:
        """Execute a single workflow step"""
        try:
            # Update agent state
            self.agent_states[agent]["tasks"].append({
                "workflow_id": workflow_id,
                "action": action,
                "start_time": datetime.now()
            })
            
            # Execute action through coordinator
            result = await self.coordinator.execute_action(agent, action, data)
            
            # Update workflow status
            self.active_workflows[workflow_id]["steps"].append({
                "agent": agent.value,
                "action": action,
                "status": "success",
                "timestamp": datetime.now()
            })
            
            # Update agent state
            task = self.agent_states[agent]["tasks"][-1]
            task["status"] = "completed"
            task["end_time"] = datetime.now()
            
            return result
            
        except Exception as e:
            # Update workflow status
            self.active_workflows[workflow_id]["steps"].append({
                "agent": agent.value,
                "action": action,
                "status": "failed",
                "error": str(e),
                "timestamp": datetime.now()
            })
            
            # Update agent state
            if agent in self.agent_states and self.agent_states[agent]["tasks"]:
                task = self.agent_states[agent]["tasks"][-1]
                task["status"] = "failed"
                task["error"] = str(e)
                task["end_time"] = datetime.now()
                
            raise
            
    def get_workflow_status(self, workflow_id: str) -> Dict:
        """Get current status of a workflow"""
        if workflow_id in self.active_workflows:
            return self.active_workflows[workflow_id]
        return None
        
    def get_agent_status(self, agent: AgentRole) -> Dict:
        """Get current status of an agent"""
        if agent in self.agent_states:
            return self.agent_states[agent]
        return None

async def submit_workflow(self, workflow_type: str, data: Dict) -> Dict:
    """Submit a new workflow request"""
    try:
        result = await self.process_workflow(workflow_type, data)
        return {"status": "success", "result": result}
    except Exception as e:
        logger.error(f"Error submitting workflow: {str(e)}")
        return {"status": "error", "message": str(e)}

async def main():
    """Test agent integration"""
    integration = AgentIntegration()
    
    # Initialize agents
    await integration.initialize_agents()
    
    # Test API Endpoints
    print("Testing API Endpoints:")
    print(await integration.api_endpoints["get_agent_status"](AgentRole.INSURANCE))
    print(await integration.api_endpoints["submit_workflow"]("medicare_enrollment", {
        "client_info": {
            "name": "Test Client",
            "dob": "1955-06-15",
            "phone": "************",
            "email": "<EMAIL>",
            "address": "123 Test St, Port St. Lucie, FL 34952"
        },
        "product_type": "medicare_supplement",
        "preferred_carrier": "Mutual of Omaha"
    }))

if __name__ == "__main__":
    asyncio.run(main())