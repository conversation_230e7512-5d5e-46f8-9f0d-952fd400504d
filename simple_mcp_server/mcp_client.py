#!/usr/bin/env python3

import asyncio
import json
import time
from typing import Dict, Any, List, Optional
import httpx
import argparse
import base64
from PIL import Image
from io import BytesIO

class MCPClient:
    """Simple client for interacting with MCP servers"""
    
    def __init__(self, server_name: str, host: str = "localhost", port: int = 8775):
        self.server_name = server_name
        self.host = host
        self.port = port
        self.base_url = f"http://{host}:{port}"
        self.client = httpx.AsyncClient(timeout=60.0)
    
    async def get_schema(self) -> Dict[str, Any]:
        """Get the tool schema from the MCP server"""
        url = f"{self.base_url}/schema"
        response = await self.client.get(url)
        response.raise_for_status()
        return response.json()
    
    async def list_tools(self) -> List[str]:
        """List all available tools on the MCP server"""
        schema = await self.get_schema()
        tools = []
        
        if "tools" in schema:
            for tool in schema["tools"]:
                name = tool.get("name", "Unknown")
                description = tool.get("description", "No description available")
                tools.append(f"{name}: {description}")
        
        return tools
    
    async def call_tool(self, tool_name: str, parameters: Dict[str, Any] = None) -> Dict[str, Any]:
        """Call a tool on the MCP server"""
        if parameters is None:
            parameters = {}
        
        url = f"{self.base_url}/tool/{tool_name}"
        
        # Create the request payload
        payload = {
            "inputs": parameters,
            "server_name": self.server_name
        }
        
        # Make the request
        response = await self.client.post(url, json=payload)
        response.raise_for_status()
        
        # Return the response
        return response.json()
    
    async def close(self):
        """Close the client connection"""
        await self.client.aclose()

async def display_result(result: Dict[str, Any]):
    """Display the result from the MCP server"""
    print("\n===== TOOL RESULT =====")
    
    if "outputs" in result:
        for output in result["outputs"]:
            output_type = output.get("type", "unknown")
            
            if output_type == "text":
                print(output.get("text", "No text content"))
            
            elif output_type == "image":
                image_data = output.get("image", "")
                image_format = output.get("format", "png")
                
                if image_data:
                    try:
                        # Decode base64 image
                        image_bytes = base64.b64decode(image_data)
                        img = Image.open(BytesIO(image_bytes))
                        
                        # Save the image to a file
                        filename = f"mcp_image_{int(time.time())}.{image_format}"
                        img.save(filename)
                        print(f"Image saved to {filename}")
                    except Exception as e:
                        print(f"Error displaying image: {e}")
            
            else:
                print(f"Unsupported output type: {output_type}")
    
    print("=======================\n")

async def main():
    # Parse command-line arguments
    parser = argparse.ArgumentParser(description="MCP Client")
    parser.add_argument("--server", default="github.com/Operative-Sh/web-eval-agent", help="Server name")
    parser.add_argument("--host", default="localhost", help="Server host")
    parser.add_argument("--port", type=int, default=8775, help="Server port")
    parser.add_argument("--list-tools", action="store_true", help="List available tools")
    parser.add_argument("--tool", help="Tool to call")
    parser.add_argument("--parameters", help="Tool parameters as JSON string")
    
    args = parser.parse_args()
    
    # Create the client
    client = MCPClient(args.server, args.host, args.port)
    
    try:
        if args.list_tools:
            # List available tools
            tools = await client.list_tools()
            print("\n===== AVAILABLE TOOLS =====")
            for i, tool in enumerate(tools, 1):
                print(f"{i}. {tool}")
            print("===========================\n")
        
        elif args.tool:
            # Parse parameters
            parameters = {}
            if args.parameters:
                try:
                    parameters = json.loads(args.parameters)
                except json.JSONDecodeError:
                    print("Error: Parameters must be a valid JSON string")
                    return
            
            # Call the tool
            print(f"Calling tool '{args.tool}' with parameters: {parameters}")
            result = await client.call_tool(args.tool, parameters)
            
            # Display the result
            await display_result(result)
        
        else:
            print("Interactive mode: Select an option:")
            print("1. List available tools")
            print("2. Call a tool")
            choice = input("Enter your choice (1/2): ")
            
            if choice == "1":
                tools = await client.list_tools()
                print("\n===== AVAILABLE TOOLS =====")
                for i, tool in enumerate(tools, 1):
                    print(f"{i}. {tool}")
                print("===========================\n")
            
            elif choice == "2":
                tools = await client.list_tools()
                print("\n===== AVAILABLE TOOLS =====")
                for i, tool in enumerate(tools, 1):
                    print(f"{i}. {tool}")
                print("===========================\n")
                
                tool_idx = int(input("Enter tool number: ")) - 1
                if 0 <= tool_idx < len(tools):
                    tool_name = tools[tool_idx].split(":")[0].strip()
                    
                    param_str = input("Enter parameters as JSON (or leave empty): ")
                    parameters = {}
                    if param_str:
                        try:
                            parameters = json.loads(param_str)
                        except json.JSONDecodeError:
                            print("Error: Parameters must be a valid JSON string")
                            return
                    
                    print(f"Calling tool '{tool_name}' with parameters: {parameters}")
                    result = await client.call_tool(tool_name, parameters)
                    
                    await display_result(result)
                else:
                    print("Invalid tool number")
            
            else:
                print("Invalid choice")
    
    finally:
        # Close the client
        await client.close()

if __name__ == "__main__":
    asyncio.run(main())
