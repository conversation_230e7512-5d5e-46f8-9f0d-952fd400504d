#!/usr/bin/env python3

import os
import json
import datetime
import platform
import psutil
import requests
from mcp.server.fastmcp import FastMCP, Context
from mcp.types import TextContent, ImageContent
import base64
from io import BytesIO
from PIL import Image, ImageDraw

# Create the MCP server
mcp = FastMCP("EnhancedMCP")

@mcp.tool(name="get_current_time")
async def get_current_time(ctx: Context) -> list[TextContent]:
    """Get the current time.

    This is a simple tool that returns the current time.

    Returns:
        list[TextContent]: A message with the current time.
    """
    current_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    return [TextContent(type="text", text=f"The current time is: {current_time}")]

@mcp.tool(name="echo_message")
async def echo_message(message: str, ctx: Context) -> list[TextContent]:
    """Echo a message back to the user.

    Args:
        message: The message to echo back.

    Returns:
        list[TextContent]: The echoed message.
    """
    return [TextContent(type="text", text=f"Echo: {message}")]

@mcp.tool(name="system_info")
async def system_info(ctx: Context) -> list[TextContent]:
    """Get information about the system.

    Returns:
        list[TextContent]: Information about the system's CPU, memory, disk, and network.
    """
    # Get CPU information
    cpu_count = psutil.cpu_count()
    cpu_percent = psutil.cpu_percent(interval=1)
    
    # Get memory information
    memory = psutil.virtual_memory()
    memory_total = memory.total / (1024 ** 3)  # Convert to GB
    memory_used = memory.used / (1024 ** 3)    # Convert to GB
    memory_percent = memory.percent
    
    # Get disk information
    disk = psutil.disk_usage('/')
    disk_total = disk.total / (1024 ** 3)      # Convert to GB
    disk_used = disk.used / (1024 ** 3)        # Convert to GB
    disk_percent = disk.percent
    
    # Get network information
    net_io = psutil.net_io_counters()
    net_sent = net_io.bytes_sent / (1024 ** 2)  # Convert to MB
    net_recv = net_io.bytes_recv / (1024 ** 2)  # Convert to MB
    
    # Get system platform information
    system_platform = platform.system()
    system_release = platform.release()
    system_version = platform.version()
    
    # Format the output
    output = f"""
System Information:
------------------
Platform: {system_platform} {system_release} {system_version}

CPU:
  Cores: {cpu_count}
  Usage: {cpu_percent}%

Memory:
  Total: {memory_total:.2f} GB
  Used: {memory_used:.2f} GB
  Usage: {memory_percent}%

Disk:
  Total: {disk_total:.2f} GB
  Used: {disk_used:.2f} GB
  Usage: {disk_percent}%

Network:
  Sent: {net_sent:.2f} MB
  Received: {net_recv:.2f} MB
"""
    
    return [TextContent(type="text", text=output)]

@mcp.tool(name="fetch_weather")
async def fetch_weather(city: str, ctx: Context) -> list[TextContent]:
    """Fetch weather information for a city.

    Args:
        city: The name of the city to get weather for.

    Returns:
        list[TextContent]: Weather information for the specified city.
    """
    try:
        # Free weather API that doesn't require API key
        url = f"https://wttr.in/{city}?format=j1"
        response = requests.get(url)
        
        if response.status_code == 200:
            weather_data = response.json()
            current = weather_data.get('current_condition', [{}])[0]
            location = weather_data.get('nearest_area', [{}])[0]
            
            city_name = location.get('areaName', [{}])[0].get('value', city)
            country = location.get('country', [{}])[0].get('value', 'Unknown')
            
            temp_c = current.get('temp_C', 'N/A')
            temp_f = current.get('temp_F', 'N/A')
            humidity = current.get('humidity', 'N/A')
            weather_desc = current.get('weatherDesc', [{}])[0].get('value', 'N/A')
            
            output = f"""
Weather for {city_name}, {country}:
------------------
Condition: {weather_desc}
Temperature: {temp_c}°C / {temp_f}°F
Humidity: {humidity}%
"""
            return [TextContent(type="text", text=output)]
        else:
            return [TextContent(type="text", text=f"Error fetching weather for {city}: HTTP {response.status_code}")]
    
    except Exception as e:
        return [TextContent(type="text", text=f"Error fetching weather for {city}: {str(e)}")]

@mcp.tool(name="generate_color_image")
async def generate_color_image(ctx: Context, color: str = "#FF5733", width: int = 300, height: int = 200) -> list[ImageContent]:
    """Generate a simple colored image.

    Args:
        color: The color for the image (hex code).
        width: The width of the image in pixels.
        height: The height of the image in pixels.

    Returns:
        list[ImageContent]: A colored image.
    """
    try:
        # Create a new image with the specified color
        img = Image.new('RGB', (width, height), color=color)
        
        # Add some text to the image
        draw = ImageDraw.Draw(img)
        draw.text((width//2 - 50, height//2), f"Color: {color}", fill="white")
        
        # Convert the image to base64
        buffer = BytesIO()
        img.save(buffer, format="PNG")
        img_str = base64.b64encode(buffer.getvalue()).decode('utf-8')
        
        return [ImageContent(type="image", image=img_str, format="png")]
    
    except Exception as e:
        return [TextContent(type="text", text=f"Error generating image: {str(e)}")]

@mcp.tool(name="calculate")
async def calculate(expression: str, ctx: Context) -> list[TextContent]:
    """Perform a mathematical calculation.

    Args:
        expression: The mathematical expression to evaluate.

    Returns:
        list[TextContent]: The result of the calculation.
    """
    try:
        # Using eval with safety restrictions
        allowed_names = {"abs": abs, "max": max, "min": min, "sum": sum, "round": round}
        result = eval(expression, {"__builtins__": {}}, allowed_names)
        return [TextContent(type="text", text=f"Result of {expression} = {result}")]
    
    except Exception as e:
        return [TextContent(type="text", text=f"Error calculating {expression}: {str(e)}")]

if __name__ == "__main__":
    print("Starting Enhanced MCP Server...")
    # Set environment variables for MCP server configuration
    os.environ["MCP_TRANSPORT"] = "socket"
    os.environ["MCP_PORT"] = "8776"
    os.environ["MCP_HOST"] = "0.0.0.0"
    # Run the MCP server
    mcp.run()

def main():
    print("Starting Enhanced MCP Server...")
    # Set environment variables for MCP server configuration
    os.environ["MCP_TRANSPORT"] = "socket"
    os.environ["MCP_PORT"] = "8776"
    os.environ["MCP_HOST"] = "0.0.0.0"
    # Run the MCP server
    mcp.run()
