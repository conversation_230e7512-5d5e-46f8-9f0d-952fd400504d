#!/usr/bin/env python3

import os
from mcp.server.fastmcp import Fast<PERSON>P, Context
from mcp.types import TextContent

# Create the MCP server
mcp = FastMCP("SimpleMCP")

@mcp.tool(name="get_current_time")
async def get_current_time(ctx: Context) -> list[TextContent]:
    """Get the current time.

    This is a simple tool that returns the current time.

    Returns:
        list[TextContent]: A message with the current time.
    """
    import datetime
    current_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    return [TextContent(type="text", text=f"The current time is: {current_time}")]

@mcp.tool(name="echo_message")
async def echo_message(message: str, ctx: Context) -> list[TextContent]:
    """Echo a message back to the user.

    Args:
        message: The message to echo back.

    Returns:
        list[TextContent]: The echoed message.
    """
    return [TextContent(type="text", text=f"Echo: {message}")]

if __name__ == "__main__":
    print("Starting Simple MCP Server...")
    # Set environment variables for MCP server configuration
    os.environ["MCP_TRANSPORT"] = "socket"
    os.environ["MCP_PORT"] = "8775"
    os.environ["MCP_HOST"] = "0.0.0.0"
    # Run the MCP server
    mcp.run()

def main():
    print("Starting Simple MCP Server...")
    # Set environment variables for MCP server configuration
    os.environ["MCP_TRANSPORT"] = "socket"
    os.environ["MCP_PORT"] = "8775"
    os.environ["MCP_HOST"] = "0.0.0.0"
    # Run the MCP server
    mcp.run()
