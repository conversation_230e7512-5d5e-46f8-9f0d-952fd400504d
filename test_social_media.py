import os
import json
import logging
from datetime import datetime, timed<PERSON><PERSON>
from agent_system import AgentSys<PERSON>
from social_media_manager import SocialMediaManager
from secure_credentials import SecureCredentialsManager

# Configure logging
logging.basicConfig(level=logging.INFO, 
                    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_social_media_integration():
    """
    Test the integration of the social media manager with the agent system.
    This function demonstrates how to:
    1. Initialize the social media manager
    2. Add social media accounts for an agent
    3. Create and post content
    4. Schedule posts
    5. Create a campaign
    6. Retrieve analytics
    """
    logger.info("Initializing agent system with social media capabilities...")
    
    # Initialize system
    system = AgentSystem()
    
    # Get social media agent from system
    social_agent = system.agents.get('social')
    
    if not social_agent:
        logger.error("Social media agent not found in agent system")
        return False
    
    logger.info("Social media agent successfully initialized")
    
    # Test adding social media accounts (using placeholder credentials)
    # In production, these would be securely stored and retrieved
    test_agent_id = "test_agent_001"
    
    # Example Twitter credentials (placeholder values)
    twitter_credentials = {
        "consumer_key": "YOUR_CONSUMER_KEY",
        "consumer_secret": "YOUR_CONSUMER_SECRET",
        "access_token": "YOUR_ACCESS_TOKEN",
        "access_token_secret": "YOUR_ACCESS_TOKEN_SECRET"
    }
    
    # Example Facebook credentials (placeholder values)
    facebook_credentials = {
        "access_token": "YOUR_FACEBOOK_ACCESS_TOKEN",
        "page_id": "YOUR_FACEBOOK_PAGE_ID"
    }
    
    logger.info(f"Adding Twitter account for agent {test_agent_id}...")
    # Note: In real usage, you would use actual API credentials
    # This test will not actually connect to Twitter
    try:
        # We'll skip the actual connection in this test
        # social_agent.add_social_account(test_agent_id, "twitter", twitter_credentials)
        logger.info("Twitter account would be added (skipped in test)")
    except Exception as e:
        logger.error(f"Error adding Twitter account: {e}")
    
    logger.info(f"Adding Facebook account for agent {test_agent_id}...")
    try:
        # We'll skip the actual connection in this test
        # social_agent.add_social_account(test_agent_id, "facebook", facebook_credentials)
        logger.info("Facebook account would be added (skipped in test)")
    except Exception as e:
        logger.error(f"Error adding Facebook account: {e}")
    
    # Test content creation
    logger.info("Testing content creation...")
    
    # Insurance tip content
    tip_params = {
        "tip": "Bundling home and auto insurance can save you up to 20% on premiums"
    }
    
    # Quote content
    quote_params = {
        "quote": "The best investment you can make is in yourself and your future",
        "author": "Warren Buffett"
    }
    
    # Promotion content
    promo_params = {
        "offer": "Free policy review and $50 gift card for new referrals",
        "end_date": "May 31, 2025"
    }
    
    # Test content creation
    try:
        tip_content = social_agent.create_content("insurance_tip", tip_params)
        quote_content = social_agent.create_content("quote", quote_params)
        promo_content = social_agent.create_content("promotion", promo_params)
        
        logger.info("Successfully created content:")
        logger.info(f"Insurance Tip: {tip_content}")
        logger.info(f"Quote: {quote_content}")
        logger.info(f"Promotion: {promo_content}")
    except Exception as e:
        logger.error(f"Error creating content: {e}")
    
    # Test campaign creation
    logger.info("Testing campaign creation...")
    
    # Create a sample campaign
    now = datetime.now()
    post_schedule = [
        now + timedelta(days=1),  # Tomorrow
        now + timedelta(days=3),  # 3 days from now
        now + timedelta(days=7)   # 1 week from now
    ]
    
    content_configs = [
        {
            "content_type": "insurance_tip",
            "parameters": {
                "tip": "Reviewing your policy annually can help ensure you have adequate coverage"
            }
        },
        {
            "content_type": "quote",
            "parameters": {
                "quote": "Risk comes from not knowing what you're doing",
                "author": "Warren Buffett"
            }
        },
        {
            "content_type": "promotion",
            "parameters": {
                "offer": "Schedule a consultation for a chance to win a $100 gift card",
                "end_date": "June 15, 2025"
            }
        }
    ]
    
    try:
        # Since we don't have real connections, we'll simulate the campaign creation
        # campaign = social_agent.create_campaign(
        #    test_agent_id, 
        #    "Spring Insurance Awareness", 
        #    ["twitter", "facebook"], 
        #    post_schedule, 
        #    content_configs
        # )
        
        # Mock campaign data
        mock_campaign = {
            "agent_id": test_agent_id,
            "name": "Spring Insurance Awareness",
            "platforms": ["twitter", "facebook"],
            "scheduled_posts": [
                {
                    "platform": "twitter",
                    "schedule_id": "sched_123456789",
                    "scheduled_time": post_schedule[0].isoformat(),
                    "content_config": content_configs[0]
                },
                # More scheduled posts would be here
            ],
            "status": "active"
        }
        
        logger.info("Campaign would be created (simulated in test):")
        logger.info(f"Campaign name: {mock_campaign['name']}")
        logger.info(f"Platforms: {', '.join(mock_campaign['platforms'])}")
        logger.info(f"Number of scheduled posts: {len(mock_campaign['scheduled_posts'])}")
    except Exception as e:
        logger.error(f"Error creating campaign: {e}")
    
    # Test analytics
    logger.info("Testing analytics reporting...")
    
    # Create mock analytics data
    mock_analytics = {
        "engagement": {
            "total_likes": 245,
            "total_comments": 54,
            "total_shares": 37
        },
        "by_platform": {
            "twitter": {
                "post_count": 12,
                "total_likes": 183,
                "total_comments": 32,
                "total_shares": 27
            },
            "facebook": {
                "post_count": 8,
                "total_likes": 62,
                "total_comments": 22,
                "total_shares": 10
            }
        }
    }
    
    # Simulate generating a report
    try:
        # Normally we would get real analytics
        # report = social_agent.generate_analytics_report(test_agent_id)
        
        # Create a mock report
        mock_report = f"Social Media Analytics Report for Agent: {test_agent_id}\n"
        mock_report += "=" * 50 + "\n\n"
        
        # Overall engagement
        mock_report += "Overall Engagement:\n"
        mock_report += f"Total Likes: {mock_analytics['engagement']['total_likes']}\n"
        mock_report += f"Total Comments: {mock_analytics['engagement']['total_comments']}\n"
        mock_report += f"Total Shares: {mock_analytics['engagement']['total_shares']}\n\n"
        
        # Platform breakdown
        mock_report += "Platform Breakdown:\n"
        for platform, stats in mock_analytics['by_platform'].items():
            mock_report += f"\n{platform.capitalize()}:\n"
            mock_report += f"  Post Count: {stats['post_count']}\n"
            mock_report += f"  Total Likes: {stats['total_likes']}\n"
            mock_report += f"  Total Comments: {stats['total_comments']}\n"
            mock_report += f"  Total Shares: {stats['total_shares']}\n"
            
            # Calculate engagement rate
            total_interactions = stats['total_likes'] + stats['total_comments'] + stats['total_shares']
            engagement_rate = total_interactions / stats['post_count']
            mock_report += f"  Average Engagement: {engagement_rate:.2f} per post\n"
        
        logger.info("Analytics report would look like this:")
        print("\n" + mock_report)
        
    except Exception as e:
        logger.error(f"Error generating analytics report: {e}")
    
    logger.info("Social media integration test completed")
    return True

if __name__ == "__main__":
    test_social_media_integration()