{"summary": {"total_time": 0.22851204872131348, "total_tests": 21, "successful_tests": 0, "failed_tests": 21, "success_rate": 0.0}, "test_results": [{"test_name": "System Initialization", "success": false, "duration": 1.0967254638671875e-05, "metadata": {"error": "Dashboard initialization failed"}, "timestamp": 1749596158.412489}, {"test_name": "Basic Query: What is artificial intelligenc...", "success": false, "duration": 7.152557373046875e-07, "metadata": {"confidence": 0, "processing_method": "unknown", "response_length": 37}, "timestamp": 1749596158.4124968}, {"test_name": "Basic Query: Explain machine learning in si...", "success": false, "duration": 2.1457672119140625e-06, "metadata": {"confidence": 0, "processing_method": "unknown", "response_length": 37}, "timestamp": 1749596158.412503}, {"test_name": "Basic Query: What are the benefits of autom...", "success": false, "duration": 0.0, "metadata": {"confidence": 0, "processing_method": "unknown", "response_length": 37}, "timestamp": 1749596158.412506}, {"test_name": "Insurance Query: What is term life insurance?...", "success": false, "duration": 9.5367431640625e-07, "metadata": {"confidence": 0, "processing_method": "unknown", "sources_used": []}, "timestamp": 1749596158.412512}, {"test_name": "Insurance Query: Explain the difference between...", "success": false, "duration": 0.0, "metadata": {"confidence": 0, "processing_method": "unknown", "sources_used": []}, "timestamp": 1749596158.4125152}, {"test_name": "Insurance Query: What factors affect auto insur...", "success": false, "duration": 0.0, "metadata": {"confidence": 0, "processing_method": "unknown", "sources_used": []}, "timestamp": 1749596158.412517}, {"test_name": "Insurance Query: How does disability insurance ...", "success": false, "duration": 9.5367431640625e-07, "metadata": {"confidence": 0, "processing_method": "unknown", "sources_used": []}, "timestamp": 1749596158.41252}, {"test_name": "Vision Query: Describe this image in detail...", "success": false, "duration": 3.0994415283203125e-06, "metadata": {"confidence": 0, "processing_method": "unknown", "models_used": []}, "timestamp": 1749596158.640877}, {"test_name": "Vision Query: Extract any text visible in th...", "success": false, "duration": 1.1920928955078125e-06, "metadata": {"confidence": 0, "processing_method": "unknown", "models_used": []}, "timestamp": 1749596158.640897}, {"test_name": "Vision Query: Analyze the colors and composi...", "success": false, "duration": 0.0, "metadata": {"confidence": 0, "processing_method": "unknown", "models_used": []}, "timestamp": 1749596158.6409018}, {"test_name": "Vision Query: Identify any objects or shapes...", "success": false, "duration": 9.5367431640625e-07, "metadata": {"confidence": 0, "processing_method": "unknown", "models_used": []}, "timestamp": 1749596158.640905}, {"test_name": "Web Task: Navigate to https://example.co...", "success": false, "duration": 7.152557373046875e-07, "metadata": {"processing_method": "unknown", "execution_time": 0}, "timestamp": 1749596158.640913}, {"test_name": "Web Task: Check the structure of a simpl...", "success": false, "duration": 9.5367431640625e-07, "metadata": {"processing_method": "unknown", "execution_time": 0}, "timestamp": 1749596158.6409159}, {"test_name": "Web Task: Identify interactive elements ...", "success": false, "duration": 9.5367431640625e-07, "metadata": {"processing_method": "unknown", "execution_time": 0}, "timestamp": 1749596158.640919}, {"test_name": "Complex Workflow: Research life insurance option...", "success": false, "duration": 1.1920928955078125e-06, "metadata": {"confidence": 0, "processing_method": "unknown", "models_used": [], "strategy_used": "unknown"}, "timestamp": 1749596158.640925}, {"test_name": "Complex Workflow: Analyze insurance market trend...", "success": false, "duration": 9.5367431640625e-07, "metadata": {"confidence": 0, "processing_method": "unknown", "models_used": [], "strategy_used": "unknown"}, "timestamp": 1749596158.640928}, {"test_name": "Complex Workflow: Explain different types of ins...", "success": false, "duration": 1.1920928955078125e-06, "metadata": {"confidence": 0, "processing_method": "unknown", "models_used": [], "strategy_used": "unknown"}, "timestamp": 1749596158.6409311}, {"test_name": "Multi-Agent: Use multiple sources to explai...", "success": false, "duration": 1.1920928955078125e-06, "metadata": {"confidence": 0, "processing_method": "unknown", "sources_used": [], "models_used": []}, "timestamp": 1749596158.640939}, {"test_name": "Multi-Agent: Research and verify informatio...", "success": false, "duration": 0.0, "metadata": {"confidence": 0, "processing_method": "unknown", "sources_used": [], "models_used": []}, "timestamp": 1749596158.640942}, {"test_name": "Multi-Agent: Combine traditional knowledge ...", "success": false, "duration": 0.0, "metadata": {"confidence": 0, "processing_method": "unknown", "sources_used": [], "models_used": []}, "timestamp": 1749596158.640945}]}