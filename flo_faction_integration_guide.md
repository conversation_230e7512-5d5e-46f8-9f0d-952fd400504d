# Flo Faction Integration Guide

This comprehensive guide will help you integrate all communication systems (Twilio, Gmail, Eleven Labs) with the Flo Faction website and social media platforms.

## Website Integration

### Step 1: Set Up Wix Developer Tools

1. Log in to your Wix account
2. Go to your Flo Faction website dashboard
3. Click on "Dev Mode" or "Velo by Wix" in the sidebar
4. Enable Developer Mode if not already enabled
5. This gives you access to write custom code for your site

### Step 2: Create API Connection in Wix

1. In your Wix dashboard, go to "Settings" > "API Connections"
2. Click "Add Connection" and select "Custom API"
3. Name it "Twilio Integration"
4. Add the following credentials:
   - API Key: Your Twilio Account SID
   - API Secret: Your Twilio Auth Token
5. Save the connection

### Step 3: Create Backend Files for Communication

Create the following backend files in Velo:

**twilio-service.js**
```javascript
import {fetch} from 'wix-fetch';
import {getSecret} from 'wix-secrets-backend';

// Get Twilio credentials from Wix Secrets
let accountSid, authToken, twilioNumber;

export async function initTwilio() {
  accountSid = await getSecret('TWILIO_ACCOUNT_SID');
  authToken = await getSecret('TWILIO_AUTH_TOKEN');
  twilioNumber = await getSecret('TWILIO_PHONE_NUMBER');
}

// Send SMS via Twilio
export async function sendSMS(to, message) {
  await initTwilio();
  
  const url = `https://api.twilio.com/2010-04-01/Accounts/${accountSid}/Messages.json`;
  
  const options = {
    method: 'post',
    headers: {
      'Authorization': 'Basic ' + Buffer.from(`${accountSid}:${authToken}`).toString('base64'),
      'Content-Type': 'application/x-www-form-urlencoded'
    },
    body: `From=${twilioNumber}&To=${to}&Body=${message}`
  };
  
  return fetch(url, options)
    .then(response => response.json())
    .catch(error => {
      console.error('Error sending SMS:', error);
      throw error;
    });
}

// Make a call via Twilio
export async function makeCall(to, twimlScript) {
  await initTwilio();
  
  const url = `https://api.twilio.com/2010-04-01/Accounts/${accountSid}/Calls.json`;
  
  const options = {
    method: 'post',
    headers: {
      'Authorization': 'Basic ' + Buffer.from(`${accountSid}:${authToken}`).toString('base64'),
      'Content-Type': 'application/x-www-form-urlencoded'
    },
    body: `From=${twilioNumber}&To=${to}&Twiml=${encodeURIComponent(twimlScript)}`
  };
  
  return fetch(url, options)
    .then(response => response.json())
    .catch(error => {
      console.error('Error making call:', error);
      throw error;
    });
}
```

**elevenlabs-service.js**
```javascript
import {fetch} from 'wix-fetch';
import {getSecret} from 'wix-secrets-backend';

// Get Eleven Labs credentials from Wix Secrets
let apiKey, voiceId;

export async function initElevenLabs() {
  apiKey = await getSecret('ELEVENLABS_API_KEY');
  voiceId = await getSecret('ELEVENLABS_VOICE_ID');
}

// Generate speech from text
export async function generateSpeech(text) {
  await initElevenLabs();
  
  const url = `https://api.elevenlabs.io/v1/text-to-speech/${voiceId}`;
  
  const options = {
    method: 'post',
    headers: {
      'Accept': 'audio/mpeg',
      'Content-Type': 'application/json',
      'xi-api-key': apiKey
    },
    body: JSON.stringify({
      text: text,
      model_id: "eleven_monolingual_v1",
      voice_settings: {
        stability: 0.5,
        similarity_boost: 0.5
      }
    })
  };
  
  return fetch(url, options)
    .then(response => {
      if (!response.ok) {
        throw new Error('Failed to generate speech');
      }
      return response.arrayBuffer();
    })
    .then(audioData => {
      // Return base64 encoded audio data
      return Buffer.from(audioData).toString('base64');
    })
    .catch(error => {
      console.error('Error generating speech:', error);
      throw error;
    });
}
```

**email-service.js**
```javascript
import {sendEmail} from 'wix-members-backend';

// Send email using Wix email service
export async function sendClientEmail(to, subject, body) {
  try {
    await sendEmail({
      to: to,
      subject: subject,
      body: {
        html: body
      },
      from: 'Flo Faction Insurance'
    });
    
    return {success: true};
  } catch (error) {
    console.error('Error sending email:', error);
    return {success: false, error: error.message};
  }
}
```

### Step 4: Create Form Handlers

Add this code to your contact form's backend file:

```javascript
import {sendSMS, makeCall} from 'backend/twilio-service';
import {generateSpeech} from 'backend/elevenlabs-service';
import {sendClientEmail} from 'backend/email-service';
import wixData from 'wix-data';

export function contactForm_afterSubmit(event) {
  const formData = event.submissionData;
  const name = formData.name;
  const email = formData.email;
  const phone = formData.phone;
  const message = formData.message;
  
  // Save lead to database
  wixData.insert('Leads', {
    name: name,
    email: email,
    phone: phone,
    message: message,
    source: 'Website Contact Form',
    date: new Date()
  })
  .then(results => {
    const leadId = results._id;
    
    // Send confirmation text message
    const smsText = `Hi ${name}, thank you for contacting Flo Faction Insurance. One of our agents will reach out to you shortly.`;
    sendSMS(phone, smsText);
    
    // Send confirmation email
    const emailSubject = "Thank You for Contacting Flo Faction Insurance";
    const emailBody = `
      <html>
        <body>
          <h2>Thank you for contacting Flo Faction Insurance</h2>
          <p>Dear ${name},</p>
          <p>We have received your inquiry and one of our agents will contact you shortly.</p>
          <p>In the meantime, you can learn more about our services on our website or social media:</p>
          <ul>
            <li><a href="https://www.flofaction.com">Website</a></li>
            <li><a href="https://www.facebook.com/flofaction">Facebook</a></li>
            <li><a href="https://www.instagram.com/flofaction">Instagram</a></li>
          </ul>
          <p>Best regards,<br>The Flo Faction Insurance Team</p>
        </body>
      </html>
    `;
    sendClientEmail(email, emailSubject, emailBody);
    
    // Schedule a follow-up call (optional)
    // This would typically be done through a separate scheduled task
  })
  .catch(error => {
    console.error('Error processing form submission:', error);
  });
}
```

### Step 5: Add Communication Buttons to Client Dashboard

If you have a client dashboard, add these buttons:

```html
<button id="callClientBtn" onclick="callClient()">Call Client</button>
<button id="textClientBtn" onclick="textClient()">Text Client</button>
<button id="emailClientBtn" onclick="emailClient()">Email Client</button>
```

With this JavaScript:

```javascript
import {sendSMS, makeCall} from 'backend/twilio-service';
import {sendClientEmail} from 'backend/email-service';

export function callClient() {
  const clientPhone = currentClient.phone;
  const clientName = currentClient.name;
  
  const twimlScript = `
    <Response>
      <Say>Hello ${clientName}, this is Flo Faction Insurance calling to follow up on your recent inquiry. Please call us back at your convenience.</Say>
    </Response>
  `;
  
  makeCall(clientPhone, twimlScript)
    .then(result => {
      // Show success message
      $w('#callStatus').text = "Call initiated successfully!";
    })
    .catch(error => {
      // Show error message
      $w('#callStatus').text = "Error making call: " + error.message;
    });
}

export function textClient() {
  const clientPhone = currentClient.phone;
  const clientName = currentClient.name;
  
  const message = `Hi ${clientName}, this is Flo Faction Insurance following up on your recent inquiry. Please call us at your convenience.`;
  
  sendSMS(clientPhone, message)
    .then(result => {
      // Show success message
      $w('#textStatus').text = "Text sent successfully!";
    })
    .catch(error => {
      // Show error message
      $w('#textStatus').text = "Error sending text: " + error.message;
    });
}

export function emailClient() {
  const clientEmail = currentClient.email;
  const clientName = currentClient.name;
  
  const subject = "Following Up - Flo Faction Insurance";
  const body = `
    <html>
      <body>
        <h2>Following Up</h2>
        <p>Dear ${clientName},</p>
        <p>We're following up on your recent inquiry with Flo Faction Insurance.</p>
        <p>Please let us know if you have any questions or would like to schedule a consultation.</p>
        <p>Best regards,<br>The Flo Faction Insurance Team</p>
      </body>
    </html>
  `;
  
  sendClientEmail(clientEmail, subject, body)
    .then(result => {
      // Show success message
      $w('#emailStatus').text = "Email sent successfully!";
    })
    .catch(error => {
      // Show error message
      $w('#emailStatus').text = "Error sending email: " + error.message;
    });
}
```

## Social Media Integration

### Step 1: Connect Social Media Accounts to Wix

1. In your Wix dashboard, go to "Settings" > "Social"
2. Connect your Facebook and Instagram accounts
3. Enable automatic posting of new content

### Step 2: Set Up Facebook Messenger Integration

1. Go to your Facebook page settings
2. Under "Messaging", click "Add Messenger to your website"
3. Copy the provided code
4. In Wix, go to "Add" > "Embed" and paste the code

### Step 3: Create Social Media Automation with Zapier

1. Create a Zapier account at zapier.com
2. Create a new Zap with these triggers and actions:
   - Trigger: "New Lead in Wix Database"
   - Action 1: "Send Email via Gmail"
   - Action 2: "Send SMS via Twilio"
   - Action 3: "Create Lead in CRM"
   - Action 4: "Schedule Social Media Post" (optional)

### Step 4: Set Up Facebook Lead Ads Integration

1. Create a Facebook Lead Ad campaign
2. In Zapier, create a new Zap:
   - Trigger: "New Lead from Facebook Lead Ads"
   - Action 1: "Add Lead to Wix Database"
   - Action 2: "Send Automated Email"
   - Action 3: "Send SMS via Twilio"

### Step 5: Create Instagram Shopping for Insurance Products

1. Set up an Instagram Business account if not already done
2. Connect to Facebook Catalog
3. Create "products" for your insurance offerings
4. Tag these products in your Instagram posts

## CRM Integration

### Step 1: Set Up a CRM System

1. Choose a CRM system compatible with Wix (e.g., HubSpot, Zoho)
2. Create custom fields for insurance-specific information
3. Set up lead scoring based on client needs

### Step 2: Connect Wix to CRM

1. In Wix, go to "Settings" > "Business Integrations"
2. Find your CRM provider and connect
3. Map Wix form fields to CRM fields

### Step 3: Create Automated Workflows

1. In your CRM, create these workflows:
   - New Lead Follow-up
   - Quote Request Processing
   - Policy Renewal Reminders
   - Birthday/Anniversary Greetings

### Step 4: Set Up Communication Tracking

1. Configure your CRM to track:
   - Email opens and clicks
   - Call outcomes
   - Text message responses
   - Website visits

## Testing and Monitoring

### Step 1: Test All Integrations

1. Submit a test form on your website
2. Verify that:
   - The lead is saved in your database
   - A confirmation email is sent
   - A text message is sent
   - The lead appears in your CRM

### Step 2: Set Up Monitoring

1. Create a dashboard to monitor:
   - Form submissions
   - Communication statistics
   - Conversion rates
   - Client engagement

### Step 3: Regular Maintenance

1. Schedule monthly reviews of:
   - Communication templates
   - Automation workflows
   - Integration performance
   - Client feedback

## Final Steps

1. Train your team on using the integrated system
2. Create documentation for all processes
3. Establish a feedback loop for continuous improvement
4. Schedule regular system updates and maintenance

By following this guide, you'll have a fully integrated communication system that connects your Flo Faction website, social media, and client communications through Twilio, Gmail, and Eleven Labs.
