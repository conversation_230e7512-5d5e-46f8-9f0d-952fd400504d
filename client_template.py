from dataclasses import dataclass
from typing import List, Dict, Optional
from datetime import datetime
import json
import logging

logger = logging.getLogger(__name__)

@dataclass
class Medication:
    drug_name: str
    dosage: str
    frequency: str

@dataclass
class Insurance:
    carrier_name: str
    coverage_amount: float
    current_premium: float
    policy_type: str

@dataclass
class BankInfo:
    bank_name: str
    routing_number: str
    account_number: str

@dataclass
class Beneficiary:
    name: str
    dob: str
    relationship: Optional[str] = None

@dataclass
class ClientInformation:
    # Basic Information
    name: str
    dob: str
    height: str
    weight: str
    phone: str
    email: str
    address: str
    ssn: str
    drivers_license: str
    
    # Health Information
    medications: List[Medication]
    tobacco_use: bool
    marijuana_use: bool
    
    # Financial Information
    bank_info: BankInfo
    budget_range: str
    
    # Insurance Information
    current_policies: List[Insurance]
    beneficiaries: List[Beneficiary]
    
    # Additional Information
    family_health_history: str
    reason_notes: str
    start_date: str

class ClientManager:
    """Manages client information and templates"""
    
    def __init__(self, storage_path: str = "client_data/"):
        self.storage_path = storage_path
        self.templates = {}
        self.clients = {}
        self.initialize_template()

    def initialize_template(self):
        """Initialize the client information template"""
        self.template = {
            "personal_info": {
                "name": "",
                "dob": "",
                "height": "",
                "weight": "",
                "phone": "",
                "email": "",
                "address": "",
                "ssn": "",
                "drivers_license": ""
            },
            "beneficiaries": [],
            "health_info": {
                "medications": [],
                "tobacco_use": False,
                "marijuana_use": False
            },
            "financial_info": {
                "bank_info": {
                    "bank_name": "",
                    "routing_number": "",
                    "account_number": ""
                },
                "budget_ranges": {
                    "$100": False,
                    "$150": False,
                    "$200": False,
                    "$250": False,
                    "$300": False,
                    "$350": False
                }
            },
            "insurance_info": {
                "current_policies": [],
                "family_health_history": "",
                "start_date": ""
            },
            "notes": {
                "reason": "",
                "additional_notes": ""
            }
        }

    def create_client(self, client_info: Dict) -> ClientInformation:
        """Create new client record"""
        try:
            # Convert medications
            medications = [
                Medication(m['drug_name'], m['dosage'], m['frequency'])
                for m in client_info.get('medications', [])
            ]
            
            # Convert beneficiaries
            beneficiaries = [
                Beneficiary(b['name'], b['dob'], b.get('relationship'))
                for b in client_info.get('beneficiaries', [])
            ]
            
            # Convert insurance policies
            policies = [
                Insurance(
                    p['carrier_name'],
                    p['coverage_amount'],
                    p['current_premium'],
                    p['policy_type']
                )
                for p in client_info.get('current_policies', [])
            ]
            
            # Create bank info
            bank_info = BankInfo(
                client_info['bank_info']['bank_name'],
                client_info['bank_info']['routing_number'],
                client_info['bank_info']['account_number']
            )
            
            # Create client object
            client = ClientInformation(
                name=client_info['name'],
                dob=client_info['dob'],
                height=client_info['height'],
                weight=client_info['weight'],
                phone=client_info['phone'],
                email=client_info['email'],
                address=client_info['address'],
                ssn=client_info['ssn'],
                drivers_license=client_info['drivers_license'],
                medications=medications,
                tobacco_use=client_info.get('tobacco_use', False),
                marijuana_use=client_info.get('marijuana_use', False),
                bank_info=bank_info,
                budget_range=client_info.get('budget_range', ''),
                current_policies=policies,
                beneficiaries=beneficiaries,
                family_health_history=client_info.get('family_health_history', ''),
                reason_notes=client_info.get('reason_notes', ''),
                start_date=client_info.get('start_date', '')
            )
            
            return client
            
        except KeyError as e:
            logger.error(f"Missing required field: {e}")
            raise
            
    def validate_client_info(self, client_info: Dict) -> bool:
        """Validate client information against template"""
        required_fields = [
            'name', 'dob', 'phone', 'email', 'address', 'ssn'
        ]
        
        return all(field in client_info for field in required_fields)

    def get_template(self) -> Dict:
        """Get the client information template"""
        return self.template.copy()

    def format_for_carrier(self, client: ClientInformation, carrier: str) -> Dict:
        """Format client information for specific carrier"""
        base_info = {
            "name": client.name,
            "dob": client.dob,
            "contact": {
                "phone": client.phone,
                "email": client.email,
                "address": client.address
            },
            "health": {
                "height": client.height,
                "weight": client.weight,
                "medications": [
                    {
                        "name": med.drug_name,
                        "dosage": med.dosage,
                        "frequency": med.frequency
                    }
                    for med in client.medications
                ],
                "tobacco": client.tobacco_use,
                "marijuana": client.marijuana_use
            },
            "beneficiaries": [
                {
                    "name": ben.name,
                    "dob": ben.dob,
                    "relationship": ben.relationship
                }
                for ben in client.beneficiaries
            ]
        }
        
        # Add carrier-specific formatting
        if carrier.lower() == "mutual_of_omaha":
            base_info["payment"] = {
                "bank_name": client.bank_info.bank_name,
                "routing": client.bank_info.routing_number,
                "account": client.bank_info.account_number,
                "draft_date": client.start_date
            }
            
        elif carrier.lower() == "americo":
            base_info["health_questions"] = {
                "family_history": client.family_health_history
            }
            
        return base_info

# Example usage
if __name__ == "__main__":
    client_manager = ClientManager()
    
    # Get template
    template = client_manager.get_template()
    
    # Example client info
    client_info = {
        "name": "John Doe",
        "dob": "1980-01-01",
        "height": "5'10\"",
        "weight": "180",
        "phone": "************",
        "email": "<EMAIL>",
        "address": "123 Main St",
        "ssn": "***********",
        "drivers_license": "********",
        "medications": [
            {
                "drug_name": "Lisinopril",
                "dosage": "10mg",
                "frequency": "once daily"
            }
        ],
        "tobacco_use": False,
        "marijuana_use": False,
        "bank_info": {
            "bank_name": "Chase",
            "routing_number": "*********",
            "account_number": "*********"
        },
        "budget_range": "$200-$250",
        "beneficiaries": [
            {
                "name": "Jane Doe",
                "dob": "1982-02-02",
                "relationship": "spouse"
            }
        ],
        "family_health_history": "No significant issues",
        "reason_notes": "Looking for term life insurance",
        "start_date": "2025-05-01"
    }
    
    # Create client record
    if client_manager.validate_client_info(client_info):
        client = client_manager.create_client(client_info)
        
        # Format for specific carrier
        mutual_format = client_manager.format_for_carrier(client, "mutual_of_omaha")
        print(json.dumps(mutual_format, indent=2))