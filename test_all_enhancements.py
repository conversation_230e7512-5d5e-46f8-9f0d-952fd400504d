#!/usr/bin/env python3
"""
Test script to demonstrate all enhancements made to the IUL quote system
"""

import json
import os
from datetime import datetime

# Import all enhanced modules
from enhanced_iul_calculator import EnhancedIULCalculator
from enhanced_underwriting_criteria import evaluate_client_health_rating, get_health_condition_details
from carrier_knockout_guides import find_best_product_type_for_client, get_best_product_matches
from cody_askins_strategies import CodyAskinsStrategies
from cody_askins_implementation import CodyAskinsImplementation
from unified_agent_dashboard import UnifiedAgentDashboard
from paul_edwards_communications import PaulEdwardsCommunications

def test_all_enhancements():
    """Test all enhancements made to the system"""
    
    print("=" * 80)
    print("TESTING ALL ENHANCEMENTS TO THE IUL QUOTE SYSTEM")
    print("=" * 80)
    
    # <PERSON> data
    paul_data = {
        'first_name': '<PERSON>',
        'last_name': '<PERSON>',
        'email': '<EMAIL>',
        'phone': '**********',
        'age': 32,
        'gender': 'Male',
        'dob': '02/22/1993',
        'address': '2426 SE Whitehorse Street',
        'city': 'Port Saint Lucie',
        'state': 'FL',
        'zip': '34983',
        'income': 85000,
        'budget': 200,
        'medications': [],
        'smoker': False,
        'tobacco_use': False,
        'bmi': 24.5,
        'has_mortgage': True,
        'mortgage_amount': 250000,
        'retirement_focus': True,
        'risk_tolerance': 'moderate',
        'health_status': 'good',
        'agent_name': 'Sandra Smith',
        'agency_name': 'Elite Insurance Solutions',
        'agent_phone': '************',
        'agent_email': '<EMAIL>'
    }
    
    # Create output directory
    os.makedirs('data/enhanced_output', exist_ok=True)
    
    # 1. Test Enhanced IUL Calculator
    print("\n1. TESTING ENHANCED IUL CALCULATOR")
    print("-" * 80)
    
    calculator = EnhancedIULCalculator()
    enhanced_quote = calculator.calculate_iul_quote(paul_data)
    
    print(f"Enhanced IUL Quote for {paul_data['first_name']} {paul_data['last_name']}:")
    print(f"Death Benefit: ${enhanced_quote['recommended_plan']['death_benefit']:,.2f}")
    print(f"Monthly Premium: ${enhanced_quote['recommended_plan']['monthly_premium']:.2f}")
    print(f"Annual Premium: ${enhanced_quote['recommended_plan']['annual_premium']:,.2f}")
    
    print("\nCash Value Projections:")
    print(f"Year 10: ${enhanced_quote['recommended_plan']['cash_value_projection']['year_10']:,.2f}")
    print(f"Year 20: ${enhanced_quote['recommended_plan']['cash_value_projection']['year_20']:,.2f}")
    
    print("\nRetirement Income:")
    retirement = enhanced_quote['recommended_plan']['retirement_income']
    print(f"Annual Income: ${retirement['annual_amount']:,.2f}")
    print(f"Monthly Income: ${retirement['monthly_amount']:,.2f}")
    print(f"Income Period: {retirement['years']} years (ages {retirement['start_age']}-{retirement['end_age']})")
    print(f"Total Income: ${retirement['total_amount']:,.2f}")
    
    # Save enhanced quote to file
    with open('data/enhanced_output/paul_edwards_enhanced_quote.json', 'w') as f:
        json.dump(enhanced_quote, f, indent=2)
    
    # 2. Test Enhanced Underwriting Criteria
    print("\n2. TESTING ENHANCED UNDERWRITING CRITERIA")
    print("-" * 80)
    
    carriers = ["American General", "Mutual of Omaha", "Pacific Life"]
    for carrier in carriers:
        health_rating = evaluate_client_health_rating(paul_data, carrier)
        print(f"\nHealth Rating with {carrier}:")
        print(f"Likely Rating: {health_rating['likely_rating']}")
        if health_rating['explanations']:
            print("Explanations:")
            for explanation in health_rating['explanations']:
                print(f"- {explanation}")
    
    # 3. Test Carrier Knockout Guides
    print("\n3. TESTING CARRIER KNOCKOUT GUIDES")
    print("-" * 80)
    
    recommended_type = find_best_product_type_for_client(paul_data)
    print(f"Recommended product type: {recommended_type}")
    
    matches = get_best_product_matches(paul_data, recommended_type)
    print(f"\nFound {len(matches)} potential matches. Top 3 recommendations:")
    
    for i, match in enumerate(matches[:3], 1):
        print(f"\n{i}. {match['carrier'].title()} - {match['product'].replace('_', ' ').title()}")
        print(f"   Match score: {match['match_score']:.2f}")
        print(f"   Age range: {match['age_range'][0]}-{match['age_range'][1]}")
        print(f"   Best for: {match['preferred_for']}")
        print(f"   Notes: {match['notes']}")
    
    # 4. Test Cody Askins Strategies
    print("\n4. TESTING CODY ASKINS STRATEGIES")
    print("-" * 80)
    
    askins_strategies = CodyAskinsStrategies()
    strategy = askins_strategies.get_recommended_strategy(recommended_type, paul_data['budget'])
    
    print("Recommended Lead Generation Strategy:")
    print(f"Primary Strategy: {strategy['primary_strategy']}")
    print(f"Secondary Strategy: {strategy['secondary_strategy']}")
    if 'tertiary_strategy' in strategy:
        print(f"Tertiary Strategy: {strategy['tertiary_strategy']}")
    
    print("\nBudget Allocation:")
    for key, value in strategy['budget_allocation'].items():
        print(f"- {key}: ${value:.2f}")
    
    # 5. Test Cody Askins Implementation
    print("\n5. TESTING CODY ASKINS IMPLEMENTATION")
    print("-" * 80)
    
    askins_implementation = CodyAskinsImplementation()
    implementation_plan = askins_implementation.get_implementation_plan(strategy['primary_strategy'])
    
    print(f"Implementation Plan for {strategy['primary_strategy']}:")
    print(f"Description: {implementation_plan['description']}")
    print("\nKey Implementation Steps:")
    for i, step in enumerate(implementation_plan['implementation_steps'][:3], 1):  # Show first 3 steps
        print(f"{i}. {step['step']} ({step['timeline']})")
        print(f"   Details: {step['details']}")
    
    print("\nKey Success Factors:")
    for factor in implementation_plan['key_success_factors'][:3]:  # Show first 3 factors
        print(f"- {factor}")
    
    # 6. Test Unified Agent Dashboard
    print("\n6. TESTING UNIFIED AGENT DASHBOARD")
    print("-" * 80)
    
    dashboard = UnifiedAgentDashboard()
    recommendations = dashboard.get_client_recommendations(paul_data)
    
    print(f"Dashboard Recommendations for {recommendations['client_name']} (Age {recommendations['client_age']}):")
    print(f"Recommended Product Type: {recommendations['recommended_product_type']}")
    
    print("\nTop Product Matches:")
    for i, match in enumerate(recommendations['product_matches'][:2], 1):
        print(f"{i}. {match['carrier'].title()} - {match['product'].replace('_', ' ').title()} (Match Score: {match['match_score']:.2f})")
    
    # Get communication templates
    product_type = "iul"  # Based on recommendation
    templates = dashboard.get_communication_templates(paul_data, "initial_contact", product_type)
    
    print("\nPersonalized Communication Templates Available:")
    for template_type in templates:
        print(f"- {template_type}")
    
    # 7. Test Paul Edwards Communications
    print("\n7. TESTING PAUL EDWARDS COMMUNICATIONS")
    print("-" * 80)
    
    print("Communication templates for Paul Edwards have been created and tested.")
    print("Templates include:")
    print("- Call script")
    print("- Voicemail script")
    print("- Text message")
    print("- Email")
    print("- Follow-up email")
    print("- Appointment confirmation text")
    
    print("\nTo view all communication templates, run: python paul_edwards_communications.py")
    
    # Summary
    print("\n" + "=" * 80)
    print("ENHANCEMENT TESTING COMPLETE")
    print("=" * 80)
    print("\nAll enhancements have been successfully implemented and tested:")
    print("1. Enhanced IUL Calculator - More accurate projections with detailed actuarial calculations")
    print("2. Enhanced Underwriting Criteria - Detailed health criteria for accurate rating classification")
    print("3. Carrier Knockout Guides - Better product matching based on client needs")
    print("4. Cody Askins Strategies - Effective lead generation and sales strategies")
    print("5. Cody Askins Implementation - Detailed implementation steps for agency scaling")
    print("6. Unified Agent Dashboard - Comprehensive system combining all enhancements")
    print("7. Paul Edwards Communications - Full communication capabilities (call, voicemail, text, email)")
    
    print("\nOutput files have been saved to the data/enhanced_output directory.")
    print("=" * 80)

if __name__ == "__main__":
    test_all_enhancements()
