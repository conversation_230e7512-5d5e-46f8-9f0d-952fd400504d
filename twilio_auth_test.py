"""
Twilio Authentication Test

This script tests Twilio authentication and provides detailed error information.
"""

import os
import requests
import base64
from dotenv import load_dotenv
import json

# Load environment variables from .env file
load_dotenv()

# Twilio credentials - hardcoded for testing
TWILIO_ACCOUNT_SID = "AC187c871afa232bbbc978caf33f3e25d9"
TWILIO_AUTH_TOKEN = "CqpVewwter1BEMdFIFHrN2XmUyt22wBP"
TWILIO_API_KEY = "**********************************"
TWILIO_API_SECRET = "CqpVewwter1BEMdFIFHrN2XmUyt22wBP"

# Print the actual values from environment variables for debugging
print(f"ENV TWILIO_ACCOUNT_SID: {os.getenv('TWILIO_ACCOUNT_SID')}")
print(f"ENV TWILIO_AUTH_TOKEN: {os.getenv('TWILIO_AUTH_TOKEN')}")

def test_account_info():
    """Test fetching account information from <PERSON>wi<PERSON>"""
    print("=" * 80)
    print("TESTING TWILIO ACCOUNT INFORMATION")
    print("=" * 80)

    url = f"https://api.twilio.com/2010-04-01/Accounts/{TWILIO_ACCOUNT_SID}.json"

    # Create the authentication header
    auth = base64.b64encode(f"{TWILIO_ACCOUNT_SID}:{TWILIO_AUTH_TOKEN}".encode()).decode()
    headers = {
        "Authorization": f"Basic {auth}",
        "Content-Type": "application/json"
    }

    try:
        response = requests.get(url, headers=headers)

        print(f"Status Code: {response.status_code}")
        print(f"Response: {response.text}")

        if response.status_code >= 200 and response.status_code < 300:
            print("Authentication successful!")
            return True
        else:
            print("Authentication failed.")
            return False
    except Exception as e:
        print(f"Error: {str(e)}")
        return False

def test_api_key():
    """Test authentication using API Key and Secret"""
    print("=" * 80)
    print("TESTING TWILIO API KEY AUTHENTICATION")
    print("=" * 80)

    url = f"https://api.twilio.com/2010-04-01/Accounts/{TWILIO_ACCOUNT_SID}.json"

    # Create the authentication header
    auth = base64.b64encode(f"{TWILIO_API_KEY}:{TWILIO_API_SECRET}".encode()).decode()
    headers = {
        "Authorization": f"Basic {auth}",
        "Content-Type": "application/json"
    }

    try:
        response = requests.get(url, headers=headers)

        print(f"Status Code: {response.status_code}")
        print(f"Response: {response.text}")

        if response.status_code >= 200 and response.status_code < 300:
            print("API Key authentication successful!")
            return True
        else:
            print("API Key authentication failed.")
            return False
    except Exception as e:
        print(f"Error: {str(e)}")
        return False

def test_available_phone_numbers():
    """Test fetching available phone numbers"""
    print("=" * 80)
    print("TESTING TWILIO AVAILABLE PHONE NUMBERS")
    print("=" * 80)

    url = f"https://api.twilio.com/2010-04-01/Accounts/{TWILIO_ACCOUNT_SID}/AvailablePhoneNumbers/US/Local.json"

    # Create the authentication header
    auth = base64.b64encode(f"{TWILIO_ACCOUNT_SID}:{TWILIO_AUTH_TOKEN}".encode()).decode()
    headers = {
        "Authorization": f"Basic {auth}",
        "Content-Type": "application/json"
    }

    try:
        response = requests.get(url, headers=headers)

        print(f"Status Code: {response.status_code}")
        print(f"Response: {response.text[:500]}...")  # Show only the first 500 characters

        if response.status_code >= 200 and response.status_code < 300:
            print("Available phone numbers fetched successfully!")
            return True
        else:
            print("Failed to fetch available phone numbers.")
            return False
    except Exception as e:
        print(f"Error: {str(e)}")
        return False

def test_incoming_phone_numbers():
    """Test fetching incoming phone numbers"""
    print("=" * 80)
    print("TESTING TWILIO INCOMING PHONE NUMBERS")
    print("=" * 80)

    url = f"https://api.twilio.com/2010-04-01/Accounts/{TWILIO_ACCOUNT_SID}/IncomingPhoneNumbers.json"

    # Create the authentication header
    auth = base64.b64encode(f"{TWILIO_ACCOUNT_SID}:{TWILIO_AUTH_TOKEN}".encode()).decode()
    headers = {
        "Authorization": f"Basic {auth}",
        "Content-Type": "application/json"
    }

    try:
        response = requests.get(url, headers=headers)

        print(f"Status Code: {response.status_code}")
        print(f"Response: {response.text[:500]}...")  # Show only the first 500 characters

        if response.status_code >= 200 and response.status_code < 300:
            print("Incoming phone numbers fetched successfully!")
            return True
        else:
            print("Failed to fetch incoming phone numbers.")
            return False
    except Exception as e:
        print(f"Error: {str(e)}")
        return False

def print_environment_variables():
    """Print environment variables related to Twilio"""
    print("=" * 80)
    print("TWILIO ENVIRONMENT VARIABLES")
    print("=" * 80)

    print(f"TWILIO_ACCOUNT_SID: {TWILIO_ACCOUNT_SID}")
    print(f"TWILIO_AUTH_TOKEN: {TWILIO_AUTH_TOKEN[:5]}{'*' * (len(TWILIO_AUTH_TOKEN) - 5)}")
    print(f"TWILIO_API_KEY: {TWILIO_API_KEY}")
    print(f"TWILIO_API_SECRET: {TWILIO_API_SECRET[:5]}{'*' * (len(TWILIO_API_SECRET) - 5)}")
    print(f"TWILIO_PHONE_NUMBER: {os.getenv('TWILIO_PHONE_NUMBER', 'Not set')}")

def main():
    """Run all Twilio authentication tests"""
    print("=" * 80)
    print("TWILIO AUTHENTICATION TEST")
    print("=" * 80)

    # Print environment variables
    print_environment_variables()

    # Test account information
    account_info_success = test_account_info()

    # Test API key authentication
    api_key_success = test_api_key()

    # Test available phone numbers
    if account_info_success or api_key_success:
        available_numbers_success = test_available_phone_numbers()
    else:
        available_numbers_success = False
        print("Skipping available phone numbers test due to authentication failure.")

    # Test incoming phone numbers
    if account_info_success or api_key_success:
        incoming_numbers_success = test_incoming_phone_numbers()
    else:
        incoming_numbers_success = False
        print("Skipping incoming phone numbers test due to authentication failure.")

    # Print summary
    print("=" * 80)
    print("TWILIO AUTHENTICATION TEST SUMMARY")
    print("=" * 80)

    print(f"Account Info Test: {'Success' if account_info_success else 'Failed'}")
    print(f"API Key Test: {'Success' if api_key_success else 'Failed'}")
    print(f"Available Phone Numbers Test: {'Success' if available_numbers_success else 'Failed'}")
    print(f"Incoming Phone Numbers Test: {'Success' if incoming_numbers_success else 'Failed'}")

    # Provide recommendations
    print("=" * 80)
    print("RECOMMENDATIONS")
    print("=" * 80)

    if not account_info_success and not api_key_success:
        print("1. Verify your Twilio Account SID and Auth Token are correct.")
        print("2. Check if your Twilio account is active and not suspended.")
        print("3. Make sure your Twilio account has been properly set up and verified.")
        print("4. Try regenerating your Auth Token in the Twilio Console.")
        print("5. Check if your Twilio account has any restrictions or limitations.")
    elif not available_numbers_success or not incoming_numbers_success:
        print("1. Check if your Twilio account has the necessary permissions.")
        print("2. Verify that your account has been properly set up and verified.")
        print("3. Make sure your account has the necessary funds or credits.")
    else:
        print("All tests passed! Your Twilio account is properly authenticated.")
        print("If you're still having issues, check the following:")
        print("1. Make sure your Twilio phone number is properly configured.")
        print("2. Verify that your account has the necessary funds or credits.")
        print("3. Check if there are any restrictions on your account.")

if __name__ == "__main__":
    main()
