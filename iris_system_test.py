#!/usr/bin/env python3
"""
IRIS System Verification Test
============================

Comprehensive test suite to verify all IRIS components work together seamlessly.
Tests integration between traditional agents, advanced models, vision capabilities,
web automation, and knowledge retrieval.
"""

import asyncio
import logging
import time
import json
from pathlib import Path
from typing import Dict, List, Any

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Import IRIS components
try:
    from iris_dashboard import IRISDashboard
    from enhanced_agent_interface import EnhancedAgentInterface
    IRIS_AVAILABLE = True
    print("✅ IRIS system components loaded for testing")
except ImportError as e:
    IRIS_AVAILABLE = False
    print(f"⚠️ IRIS system not available for testing: {e}")

class IRISSystemTest:
    """Comprehensive IRIS system test suite"""
    
    def __init__(self):
        self.dashboard = None
        self.test_results = []
        self.start_time = None
        
    async def run_all_tests(self):
        """Run comprehensive system tests"""
        print("🧪 IRIS SYSTEM VERIFICATION TEST SUITE")
        print("=" * 60)
        
        if not IRIS_AVAILABLE:
            print("❌ IRIS system not available for testing")
            return False
        
        self.start_time = time.time()
        
        try:
            # Initialize system
            await self._test_system_initialization()
            
            # Test core functionality
            await self._test_basic_queries()
            await self._test_insurance_queries()
            await self._test_vision_capabilities()
            await self._test_web_automation()
            await self._test_complex_workflows()
            await self._test_multi_agent_coordination()
            
            # Generate test report
            self._generate_test_report()
            
            return True
            
        except Exception as e:
            logger.error(f"System test failed: {e}")
            print(f"❌ System test failed: {e}")
            return False
        finally:
            if self.dashboard:
                await self.dashboard.cleanup()
    
    async def _test_system_initialization(self):
        """Test system initialization and component availability"""
        print("\n🔧 Testing System Initialization...")
        
        test_start = time.time()
        
        try:
            # Initialize dashboard
            self.dashboard = IRISDashboard()
            success = await self.dashboard.initialize()
            
            if success:
                # Check system status
                system_info = self.dashboard.get_system_info()
                
                self._record_test_result(
                    "System Initialization",
                    True,
                    time.time() - test_start,
                    {
                        'components_available': system_info['system_status'],
                        'capabilities_count': len(system_info['capabilities'])
                    }
                )
                
                print("✅ System initialization successful")
                
                # Display component status
                print("\n📊 Component Status:")
                for component, status in system_info['system_status'].items():
                    icon = "✅" if status else "❌"
                    name = component.replace('_', ' ').title()
                    print(f"  {icon} {name}")
                
            else:
                self._record_test_result(
                    "System Initialization",
                    False,
                    time.time() - test_start,
                    {'error': 'Dashboard initialization failed'}
                )
                print("❌ System initialization failed")
                
        except Exception as e:
            self._record_test_result(
                "System Initialization",
                False,
                time.time() - test_start,
                {'error': str(e)}
            )
            print(f"❌ System initialization error: {e}")
    
    async def _test_basic_queries(self):
        """Test basic query processing"""
        print("\n💬 Testing Basic Query Processing...")
        
        test_queries = [
            "What is artificial intelligence?",
            "Explain machine learning in simple terms",
            "What are the benefits of automation?"
        ]
        
        for query in test_queries:
            test_start = time.time()
            
            try:
                result = await self.dashboard.process_query(query)
                
                self._record_test_result(
                    f"Basic Query: {query[:30]}...",
                    result.get('success', False),
                    time.time() - test_start,
                    {
                        'confidence': result.get('confidence', 0),
                        'processing_method': result.get('processing_method', 'unknown'),
                        'response_length': len(result.get('response', ''))
                    }
                )
                
                if result.get('success'):
                    print(f"✅ Query processed: {query[:50]}...")
                else:
                    print(f"❌ Query failed: {query[:50]}...")
                    
            except Exception as e:
                self._record_test_result(
                    f"Basic Query: {query[:30]}...",
                    False,
                    time.time() - test_start,
                    {'error': str(e)}
                )
                print(f"❌ Query error: {e}")
    
    async def _test_insurance_queries(self):
        """Test insurance-specific queries"""
        print("\n🏢 Testing Insurance Query Processing...")
        
        insurance_queries = [
            "What is term life insurance?",
            "Explain the difference between HMO and PPO health plans",
            "What factors affect auto insurance premiums?",
            "How does disability insurance work?"
        ]
        
        for query in insurance_queries:
            test_start = time.time()
            
            try:
                result = await self.dashboard.process_query(query, query_type="insurance")
                
                self._record_test_result(
                    f"Insurance Query: {query[:30]}...",
                    result.get('success', False),
                    time.time() - test_start,
                    {
                        'confidence': result.get('confidence', 0),
                        'processing_method': result.get('processing_method', 'unknown'),
                        'sources_used': result.get('sources_used', [])
                    }
                )
                
                if result.get('success'):
                    print(f"✅ Insurance query processed: {query[:40]}...")
                else:
                    print(f"❌ Insurance query failed: {query[:40]}...")
                    
            except Exception as e:
                self._record_test_result(
                    f"Insurance Query: {query[:30]}...",
                    False,
                    time.time() - test_start,
                    {'error': str(e)}
                )
                print(f"❌ Insurance query error: {e}")
    
    async def _test_vision_capabilities(self):
        """Test vision and image processing capabilities"""
        print("\n🔍 Testing Vision Capabilities...")
        
        # Create test image
        test_image = self._create_test_image()
        
        vision_queries = [
            "Describe this image in detail",
            "Extract any text visible in this image",
            "Analyze the colors and composition",
            "Identify any objects or shapes"
        ]
        
        for query in vision_queries:
            test_start = time.time()
            
            try:
                result = await self.dashboard.process_query(
                    query,
                    query_type="vision",
                    image_data=test_image
                )
                
                self._record_test_result(
                    f"Vision Query: {query[:30]}...",
                    result.get('success', False),
                    time.time() - test_start,
                    {
                        'confidence': result.get('confidence', 0),
                        'processing_method': result.get('processing_method', 'unknown'),
                        'models_used': result.get('models_used', [])
                    }
                )
                
                if result.get('success'):
                    print(f"✅ Vision query processed: {query[:40]}...")
                else:
                    print(f"❌ Vision query failed: {query[:40]}...")
                    
            except Exception as e:
                self._record_test_result(
                    f"Vision Query: {query[:30]}...",
                    False,
                    time.time() - test_start,
                    {'error': str(e)}
                )
                print(f"❌ Vision query error: {e}")
    
    async def _test_web_automation(self):
        """Test web automation capabilities"""
        print("\n🌐 Testing Web Automation...")
        
        web_tasks = [
            "Navigate to https://example.com and analyze the page",
            "Check the structure of a simple webpage",
            "Identify interactive elements on a test page"
        ]
        
        for task in web_tasks:
            test_start = time.time()
            
            try:
                result = await self.dashboard.process_query(
                    task,
                    query_type="web_automation"
                )
                
                self._record_test_result(
                    f"Web Task: {task[:30]}...",
                    result.get('success', False),
                    time.time() - test_start,
                    {
                        'processing_method': result.get('processing_method', 'unknown'),
                        'execution_time': result.get('execution_time', 0)
                    }
                )
                
                if result.get('success'):
                    print(f"✅ Web task completed: {task[:40]}...")
                else:
                    print(f"❌ Web task failed: {task[:40]}...")
                    
            except Exception as e:
                self._record_test_result(
                    f"Web Task: {task[:30]}...",
                    False,
                    time.time() - test_start,
                    {'error': str(e)}
                )
                print(f"❌ Web task error: {e}")
    
    async def _test_complex_workflows(self):
        """Test complex multi-step workflows"""
        print("\n🧠 Testing Complex Workflows...")
        
        complex_queries = [
            "Research life insurance options and create a comparison summary",
            "Analyze insurance market trends and provide recommendations",
            "Explain different types of insurance and their use cases"
        ]
        
        for query in complex_queries:
            test_start = time.time()
            
            try:
                result = await self.dashboard.process_query(
                    query,
                    query_type="complex"
                )
                
                self._record_test_result(
                    f"Complex Workflow: {query[:30]}...",
                    result.get('success', False),
                    time.time() - test_start,
                    {
                        'confidence': result.get('confidence', 0),
                        'processing_method': result.get('processing_method', 'unknown'),
                        'models_used': result.get('models_used', []),
                        'strategy_used': result.get('strategy_used', 'unknown')
                    }
                )
                
                if result.get('success'):
                    print(f"✅ Complex workflow completed: {query[:40]}...")
                else:
                    print(f"❌ Complex workflow failed: {query[:40]}...")
                    
            except Exception as e:
                self._record_test_result(
                    f"Complex Workflow: {query[:30]}...",
                    False,
                    time.time() - test_start,
                    {'error': str(e)}
                )
                print(f"❌ Complex workflow error: {e}")
    
    async def _test_multi_agent_coordination(self):
        """Test coordination between multiple agents"""
        print("\n🤝 Testing Multi-Agent Coordination...")
        
        coordination_queries = [
            "Use multiple sources to explain insurance fundamentals",
            "Research and verify information about health insurance",
            "Combine traditional knowledge with AI analysis for policy recommendations"
        ]
        
        for query in coordination_queries:
            test_start = time.time()
            
            try:
                result = await self.dashboard.process_query(query)
                
                self._record_test_result(
                    f"Multi-Agent: {query[:30]}...",
                    result.get('success', False),
                    time.time() - test_start,
                    {
                        'confidence': result.get('confidence', 0),
                        'processing_method': result.get('processing_method', 'unknown'),
                        'sources_used': result.get('sources_used', []),
                        'models_used': result.get('models_used', [])
                    }
                )
                
                if result.get('success'):
                    print(f"✅ Multi-agent coordination: {query[:40]}...")
                else:
                    print(f"❌ Multi-agent coordination failed: {query[:40]}...")
                    
            except Exception as e:
                self._record_test_result(
                    f"Multi-Agent: {query[:30]}...",
                    False,
                    time.time() - test_start,
                    {'error': str(e)}
                )
                print(f"❌ Multi-agent coordination error: {e}")
    
    def _create_test_image(self) -> bytes:
        """Create a test image for vision testing"""
        try:
            from PIL import Image, ImageDraw
            import io
            
            # Create a simple test image
            img = Image.new('RGB', (400, 300), color='white')
            draw = ImageDraw.Draw(img)
            
            # Add some text and shapes
            draw.text((50, 50), "IRIS Test Image", fill='black')
            draw.text((50, 100), "Insurance Policy Test", fill='blue')
            draw.rectangle([50, 150, 200, 200], outline='red', width=2)
            draw.ellipse([250, 150, 350, 250], outline='green', width=2)
            
            # Convert to bytes
            img_bytes = io.BytesIO()
            img.save(img_bytes, format='PNG')
            return img_bytes.getvalue()
            
        except ImportError:
            # Fallback: return dummy data
            return b"TEST_IMAGE_DATA_FOR_IRIS_VISION_TESTING"
    
    def _record_test_result(self, test_name: str, success: bool, duration: float, metadata: Dict[str, Any]):
        """Record test result"""
        result = {
            'test_name': test_name,
            'success': success,
            'duration': duration,
            'metadata': metadata,
            'timestamp': time.time()
        }
        self.test_results.append(result)
    
    def _generate_test_report(self):
        """Generate comprehensive test report"""
        total_time = time.time() - self.start_time
        total_tests = len(self.test_results)
        successful_tests = sum(1 for result in self.test_results if result['success'])
        failed_tests = total_tests - successful_tests
        
        print("\n" + "=" * 60)
        print("📊 IRIS SYSTEM TEST REPORT")
        print("=" * 60)
        
        print(f"🕒 Total Test Time: {total_time:.2f} seconds")
        print(f"📋 Total Tests: {total_tests}")
        print(f"✅ Successful: {successful_tests}")
        print(f"❌ Failed: {failed_tests}")
        print(f"📈 Success Rate: {(successful_tests/total_tests)*100:.1f}%")
        
        print(f"\n📝 Test Results Summary:")
        print("-" * 40)
        
        for result in self.test_results:
            icon = "✅" if result['success'] else "❌"
            duration = result['duration']
            print(f"{icon} {result['test_name']} ({duration:.2f}s)")
        
        # Show failed tests details
        failed_results = [r for r in self.test_results if not r['success']]
        if failed_results:
            print(f"\n❌ Failed Test Details:")
            print("-" * 40)
            for result in failed_results:
                print(f"• {result['test_name']}")
                if 'error' in result['metadata']:
                    print(f"  Error: {result['metadata']['error']}")
        
        # Show performance metrics
        avg_duration = sum(r['duration'] for r in self.test_results) / total_tests
        print(f"\n⚡ Performance Metrics:")
        print(f"• Average Test Duration: {avg_duration:.2f}s")
        print(f"• Fastest Test: {min(r['duration'] for r in self.test_results):.2f}s")
        print(f"• Slowest Test: {max(r['duration'] for r in self.test_results):.2f}s")
        
        # Save detailed report
        report_file = f"iris_test_report_{int(time.time())}.json"
        with open(report_file, 'w') as f:
            json.dump({
                'summary': {
                    'total_time': total_time,
                    'total_tests': total_tests,
                    'successful_tests': successful_tests,
                    'failed_tests': failed_tests,
                    'success_rate': (successful_tests/total_tests)*100
                },
                'test_results': self.test_results
            }, f, indent=2)
        
        print(f"\n💾 Detailed report saved to: {report_file}")
        
        # Final assessment
        if successful_tests == total_tests:
            print(f"\n🎉 ALL TESTS PASSED! IRIS system is fully operational.")
        elif successful_tests >= total_tests * 0.8:
            print(f"\n✅ MOST TESTS PASSED! IRIS system is operational with minor issues.")
        else:
            print(f"\n⚠️ SOME TESTS FAILED! IRIS system has significant issues that need attention.")
        
        print("=" * 60)

async def main():
    """Main test function"""
    print("🧪 Starting IRIS System Verification...")
    
    test_suite = IRISSystemTest()
    success = await test_suite.run_all_tests()
    
    if success:
        print("\n✅ System verification completed successfully!")
    else:
        print("\n❌ System verification encountered issues!")
    
    return success

if __name__ == "__main__":
    asyncio.run(main())
