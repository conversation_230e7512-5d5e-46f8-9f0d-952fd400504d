import logging
import aiohttp
from typing import Dict, Optional
from datetime import datetime
from secure_credentials import SecureCredentialsManager
from client_template import ClientInformation
from carrier_urls import CARRIER_URLS, get_carrier_urls

logger = logging.getLogger(__name__)

class CarrierPortal:
    """Base class for carrier portal interactions"""
    
    def __init__(self, credentials_manager: SecureCredentialsManager, carrier_id: str):
        self.credentials_manager = credentials_manager
        self.carrier_id = carrier_id
        self.urls = get_carrier_urls(carrier_id)
        self.session = None
        
    async def login(self, agent_id: str) -> bool:
        """Login to carrier portal"""
        try:
            creds = self.credentials_manager.get_carrier_credentials(agent_id, self.carrier_id)
            return await self._perform_login(creds)
        except Exception as e:
            logger.error(f"Lo<PERSON> failed for {self.carrier_id}: {str(e)}")
            return False
            
    async def _perform_login(self, credentials: Dict) -> bool:
        """Implement carrier-specific login"""
        try:
            async with aiohttp.ClientSession() as session:
                async with session.post(
                    self.urls['login_url'],
                    json={
                        "username": credentials["username"],
                        "password": credentials["password"]
                    }
                ) as response:
                    if response.status == 200:
                        self.session = session
                        return True
            return False
        except Exception as e:
            logger.error(f"Login failed: {str(e)}")
            return False
            
    async def test_connection(self) -> bool:
        """Test connection to carrier portal"""
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get(self.urls['base_url']) as response:
                    return response.status == 200
        except Exception:
            return False
            
    async def get_quote(self, client_data: Dict) -> Optional[Dict]:
        """Get quote from carrier"""
        if not self.session:
            logger.error("Not logged in to carrier portal")
            return None
            
        try:
            async with self.session.post(
                self.urls['quote_url'],
                json=client_data
            ) as response:
                if response.status == 200:
                    return await response.json()
                return None
        except Exception as e:
            logger.error(f"Error getting quote: {str(e)}")
            return None

class FFLTridentPortal(CarrierPortal):
    """FFL Trident Life portal interactions"""
    
    def __init__(self, credentials_manager: SecureCredentialsManager):
        super().__init__(credentials_manager, "ffl_trident")

    async def _get_quote_parameters(self, client_data: Dict) -> Dict:
        """Format client data for FFL quote request"""
        return {
            "name": client_data["name"],
            "dob": client_data["dob"],
            "gender": client_data.get("gender", ""),
            "state": client_data["address"].split(",")[-1].strip().split()[0],
            "tobacco": "Yes" if client_data["tobacco_use"] else "No",
            "coverage_amount": client_data.get("desired_coverage", "250000"),
            "health_class": self._determine_health_class(client_data),
            "product_type": client_data.get("product_type", "Term")
        }
        
    def _determine_health_class(self, client_data: Dict) -> str:
        """Determine health class based on client data"""
        if client_data["tobacco_use"]:
            return "Tobacco"
            
        health_issues = len(client_data["medications"]) > 0
        if not health_issues and not client_data["family_health_history"]:
            return "Preferred Plus"
        elif not health_issues:
            return "Preferred"
        else:
            return "Standard"

class MutualOfOmahaPortal(CarrierPortal):
    """Mutual of Omaha portal interactions"""
    
    def __init__(self, credentials_manager: SecureCredentialsManager):
        super().__init__(credentials_manager, "mutual_of_omaha")

    async def _get_quote_parameters(self, client_data: Dict) -> Dict:
        """Format client data for MOO quote request"""
        return {
            "applicant": {
                "name": client_data["name"],
                "dob": client_data["dob"],
                "tobacco": client_data["tobacco_use"],
                "height": client_data["height"],
                "weight": client_data["weight"],
                "medications": [
                    {
                        "name": med["drug_name"],
                        "dosage": med["dosage"]
                    }
                    for med in client_data["medications"]
                ]
            },
            "product": {
                "type": client_data.get("product_type", "Medicare Supplement"),
                "state": client_data["address"].split(",")[-1].strip().split()[0]
            }
        }

class CarrierManager:
    """Manages interactions with all insurance carriers"""
    
    def __init__(self):
        self.credentials_manager = SecureCredentialsManager()
        self.carriers = {}
        
        # Only initialize portals for carriers that have credentials
        agent_id = "sandra"  # Using sandra for testing
        try:
            agent_creds = self.credentials_manager.get_agent_credentials(agent_id)
            if 'carriers' in agent_creds:
                for carrier_id in agent_creds['carriers'].keys():
                    if carrier_id == 'mutual_of_omaha':
                        self.carriers[carrier_id] = MutualOfOmahaPortal(self.credentials_manager)
                    elif carrier_id == 'aetna':
                        self.carriers[carrier_id] = AetnaPortal(self.credentials_manager)
                    elif carrier_id == 'united_healthcare':
                        self.carriers[carrier_id] = UnitedHealthcarePortal(self.credentials_manager)
        
    async def get_quotes(self, client: ClientInformation, agent_id: str) -> Dict:
        """Get quotes from all available carriers"""
        quotes = {}
        
        for carrier_id, portal in self.carriers.items():
            try:
                # Login to carrier portal
                if await portal.login(agent_id):
                    # Get quote
                    quote = await self._get_carrier_quote(portal, client, carrier_id)
                    if quote:
                        quotes[carrier_id] = quote
            except Exception as e:
                logger.error(f"Error getting quote from {carrier_id}: {str(e)}")
                
        return quotes
        
    async def _get_carrier_quote(self, portal: CarrierPortal, 
                               client: ClientInformation, 
                               carrier: str) -> Optional[Dict]:
        """Get quote from specific carrier"""
        try:
            # Format client info for carrier
            client_data = self._format_for_carrier(client, carrier)
            
            # Get quote parameters for specific carrier
            quote_params = await portal._get_quote_parameters(client_data)
            
            # Get quote from carrier portal
            quote_response = await portal.get_quote(quote_params)
            
            if quote_response:
                return {
                    'carrier': carrier,
                    'premium': quote_response.get('premium'),
                    'coverage': quote_response.get('coverage'),
                    'details': quote_response.get('details', {}),
                    'timestamp': datetime.now().isoformat()
                }
            
            return None
            
        except Exception as e:
            logger.error(f"Error getting quote from {carrier}: {str(e)}")
            return None
            
    def _format_for_carrier(self, client: ClientInformation, carrier: str) -> Dict:
        """Format client information for specific carrier"""
        base_info = {
            'name': client.name,
            'dob': client.dob,
            'tobacco_use': client.tobacco_use,
            'height': client.height,
            'weight': client.weight,
            'address': client.address,
            'medications': [
                {'drug_name': med.drug_name, 'dosage': med.dosage}
                for med in client.medications
            ],
            'product_type': 'Medicare Supplement'
        }
        
        # Add carrier-specific fields
        if carrier == 'mutual_of_omaha':
            base_info['payment_info'] = {
                'bank_name': client.bank_info.bank_name,
                'routing': client.bank_info.routing_number,
                'account': client.bank_info.account_number
            }
            
        elif carrier == 'ffl_trident':
            base_info['desired_coverage'] = "250000"  # Default coverage amount
            
        return base_info

# Example usage
if __name__ == "__main__":
    import asyncio
    
    async def test_carriers():
        manager = CarrierManager()
        
        # Test carrier connections
        for carrier_id, portal in manager.carriers.items():
            connected = await portal.test_connection()
            print(f"{carrier_id}: {'Connected' if connected else 'Failed'}")
    
    asyncio.run(test_carriers())