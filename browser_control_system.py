#!/usr/bin/env python3
"""
Browser Control System - OpenAI Operator Style
===============================================

Comprehensive browser automation and control system that provides:
- Visual element detection and interaction
- Screen capture and analysis
- Natural language to browser actions
- Real-time browser takeover capabilities
"""

import asyncio
import json
import logging
import time
import base64
import cv2
import numpy as np
from typing import Dict, List, Any, Optional, Tuple
from pathlib import Path
import pyautogui
import subprocess
import os

try:
    from playwright.async_api import async_playwright, <PERSON>, <PERSON>rows<PERSON>, BrowserContext
    PLAYWRIGHT_AVAILABLE = True
except ImportError:
    PLAYWRIGHT_AVAILABLE = False
    print("⚠️ Playwright not available. Install with: pip install playwright")

try:
    import pytesseract
    OCR_AVAILABLE = True
except ImportError:
    OCR_AVAILABLE = False
    print("⚠️ OCR not available. Install with: pip install pytesseract")

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class VisualElementDetector:
    """Detects and analyzes visual elements on screen"""
    
    def __init__(self):
        self.templates = {}
        self.last_screenshot = None
        
    async def capture_screen(self, region: Optional[Tuple[int, int, int, int]] = None) -> np.ndarray:
        """Capture screen or specific region"""
        try:
            screenshot = pyautogui.screenshot(region=region)
            self.last_screenshot = cv2.cvtColor(np.array(screenshot), cv2.COLOR_RGB2BGR)
            return self.last_screenshot
        except Exception as e:
            logger.error(f"Screen capture failed: {e}")
            return np.array([])
    
    async def find_elements_by_text(self, text: str, screenshot: Optional[np.ndarray] = None) -> List[Dict]:
        """Find elements containing specific text using OCR"""
        if not OCR_AVAILABLE:
            return []
            
        if screenshot is None:
            screenshot = await self.capture_screen()
            
        try:
            # Use OCR to find text
            data = pytesseract.image_to_data(screenshot, output_type=pytesseract.Output.DICT)
            elements = []
            
            for i, word in enumerate(data['text']):
                if text.lower() in word.lower() and int(data['conf'][i]) > 30:
                    x, y, w, h = data['left'][i], data['top'][i], data['width'][i], data['height'][i]
                    elements.append({
                        'text': word,
                        'bbox': (x, y, x + w, y + h),
                        'confidence': data['conf'][i],
                        'center': (x + w // 2, y + h // 2)
                    })
            
            return elements
        except Exception as e:
            logger.error(f"OCR text detection failed: {e}")
            return []
    
    async def find_clickable_elements(self, screenshot: Optional[np.ndarray] = None) -> List[Dict]:
        """Find clickable elements (buttons, links, etc.)"""
        if screenshot is None:
            screenshot = await self.capture_screen()
            
        elements = []
        
        # Convert to grayscale for processing
        gray = cv2.cvtColor(screenshot, cv2.COLOR_BGR2GRAY)
        
        # Find button-like rectangles
        edges = cv2.Canny(gray, 50, 150)
        contours, _ = cv2.findContours(edges, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        
        for contour in contours:
            area = cv2.contourArea(contour)
            if 100 < area < 50000:  # Filter by reasonable button sizes
                x, y, w, h = cv2.boundingRect(contour)
                aspect_ratio = w / h
                
                # Check if it looks like a button (reasonable aspect ratio)
                if 0.2 < aspect_ratio < 10:
                    elements.append({
                        'type': 'clickable',
                        'bbox': (x, y, x + w, y + h),
                        'center': (x + w // 2, y + h // 2),
                        'area': area
                    })
        
        return elements
    
    async def analyze_screen_layout(self) -> Dict[str, Any]:
        """Analyze the current screen layout and identify UI elements"""
        screenshot = await self.capture_screen()
        
        analysis = {
            'timestamp': time.time(),
            'screen_size': screenshot.shape[:2] if screenshot.size > 0 else (0, 0),
            'clickable_elements': await self.find_clickable_elements(screenshot),
            'text_elements': [],
            'layout_regions': []
        }
        
        # Add OCR text detection if available
        if OCR_AVAILABLE:
            try:
                data = pytesseract.image_to_data(screenshot, output_type=pytesseract.Output.DICT)
                for i, text in enumerate(data['text']):
                    if text.strip() and int(data['conf'][i]) > 50:
                        x, y, w, h = data['left'][i], data['top'][i], data['width'][i], data['height'][i]
                        analysis['text_elements'].append({
                            'text': text,
                            'bbox': (x, y, x + w, y + h),
                            'confidence': data['conf'][i]
                        })
            except Exception as e:
                logger.warning(f"OCR analysis failed: {e}")
        
        return analysis

class BrowserController:
    """Controls browser instances with visual feedback"""
    
    def __init__(self):
        self.playwright = None
        self.browser = None
        self.context = None
        self.page = None
        self.visual_detector = VisualElementDetector()
        self.action_history = []
        
    async def initialize(self, headless: bool = False):
        """Initialize browser controller"""
        if not PLAYWRIGHT_AVAILABLE:
            raise RuntimeError("Playwright not available. Install with: pip install playwright")
            
        try:
            self.playwright = await async_playwright().start()
            self.browser = await self.playwright.chromium.launch(
                headless=headless,
                args=['--start-maximized', '--disable-web-security']
            )
            self.context = await self.browser.new_context(
                viewport={'width': 1920, 'height': 1080}
            )
            self.page = await self.context.new_page()
            
            # Enable console logging
            self.page.on('console', lambda msg: logger.info(f"Browser console: {msg.text}"))
            
            logger.info("✅ Browser controller initialized")
            return True
            
        except Exception as e:
            logger.error(f"Browser initialization failed: {e}")
            return False
    
    async def navigate_to(self, url: str) -> bool:
        """Navigate to a URL"""
        try:
            if not self.page:
                await self.initialize()
                
            await self.page.goto(url, wait_until='networkidle')
            self.action_history.append({
                'action': 'navigate',
                'url': url,
                'timestamp': time.time()
            })
            
            logger.info(f"✅ Navigated to: {url}")
            return True
            
        except Exception as e:
            logger.error(f"Navigation failed: {e}")
            return False
    
    async def take_screenshot(self) -> str:
        """Take screenshot and return base64 encoded image"""
        try:
            if not self.page:
                return ""
                
            screenshot_bytes = await self.page.screenshot(full_page=True)
            return base64.b64encode(screenshot_bytes).decode()
            
        except Exception as e:
            logger.error(f"Screenshot failed: {e}")
            return ""
    
    async def click_element(self, selector: str = None, text: str = None, coordinates: Tuple[int, int] = None) -> bool:
        """Click an element by selector, text, or coordinates"""
        try:
            if not self.page:
                return False
                
            if coordinates:
                await self.page.mouse.click(coordinates[0], coordinates[1])
                action_type = f"click_coordinates_{coordinates}"
            elif selector:
                await self.page.click(selector)
                action_type = f"click_selector_{selector}"
            elif text:
                await self.page.click(f"text={text}")
                action_type = f"click_text_{text}"
            else:
                return False
                
            self.action_history.append({
                'action': action_type,
                'timestamp': time.time()
            })
            
            logger.info(f"✅ Clicked: {action_type}")
            return True
            
        except Exception as e:
            logger.error(f"Click failed: {e}")
            return False
    
    async def type_text(self, text: str, selector: str = None) -> bool:
        """Type text into an element"""
        try:
            if not self.page:
                return False
                
            if selector:
                await self.page.fill(selector, text)
            else:
                await self.page.keyboard.type(text)
                
            self.action_history.append({
                'action': 'type',
                'text': text,
                'selector': selector,
                'timestamp': time.time()
            })
            
            logger.info(f"✅ Typed text: {text}")
            return True
            
        except Exception as e:
            logger.error(f"Type failed: {e}")
            return False
    
    async def scroll(self, direction: str = "down", amount: int = 3) -> bool:
        """Scroll the page"""
        try:
            if not self.page:
                return False
                
            if direction == "down":
                await self.page.keyboard.press("PageDown")
            elif direction == "up":
                await self.page.keyboard.press("PageUp")
            elif direction == "bottom":
                await self.page.keyboard.press("End")
            elif direction == "top":
                await self.page.keyboard.press("Home")
                
            self.action_history.append({
                'action': 'scroll',
                'direction': direction,
                'timestamp': time.time()
            })
            
            return True
            
        except Exception as e:
            logger.error(f"Scroll failed: {e}")
            return False
    
    async def extract_page_content(self) -> Dict[str, Any]:
        """Extract comprehensive page content"""
        try:
            if not self.page:
                return {}
                
            content = {
                'url': self.page.url,
                'title': await self.page.title(),
                'text_content': await self.page.inner_text('body'),
                'links': [],
                'forms': [],
                'buttons': [],
                'inputs': []
            }
            
            # Extract links
            links = await self.page.query_selector_all('a[href]')
            for link in links:
                href = await link.get_attribute('href')
                text = await link.inner_text()
                if href and text:
                    content['links'].append({'href': href, 'text': text.strip()})
            
            # Extract buttons
            buttons = await self.page.query_selector_all('button, input[type="button"], input[type="submit"]')
            for button in buttons:
                text = await button.inner_text() or await button.get_attribute('value')
                if text:
                    content['buttons'].append(text.strip())
            
            # Extract input fields
            inputs = await self.page.query_selector_all('input, textarea, select')
            for input_elem in inputs:
                input_type = await input_elem.get_attribute('type') or 'text'
                placeholder = await input_elem.get_attribute('placeholder') or ''
                name = await input_elem.get_attribute('name') or ''
                content['inputs'].append({
                    'type': input_type,
                    'placeholder': placeholder,
                    'name': name
                })
            
            return content
            
        except Exception as e:
            logger.error(f"Content extraction failed: {e}")
            return {}
    
    async def close(self):
        """Close browser and cleanup"""
        try:
            if self.page:
                await self.page.close()
            if self.context:
                await self.context.close()
            if self.browser:
                await self.browser.close()
            if self.playwright:
                await self.playwright.stop()
                
            logger.info("✅ Browser controller closed")
            
        except Exception as e:
            logger.error(f"Browser cleanup failed: {e}")

class NaturalLanguageBrowserAgent:
    """Converts natural language commands to browser actions"""
    
    def __init__(self):
        self.browser = BrowserController()
        self.command_history = []
        
    async def initialize(self):
        """Initialize the browser agent"""
        return await self.browser.initialize(headless=False)
    
    async def execute_command(self, command: str) -> Dict[str, Any]:
        """Execute a natural language command"""
        command_lower = command.lower().strip()
        
        try:
            # Navigation commands
            if 'go to' in command_lower or 'navigate to' in command_lower or 'open' in command_lower:
                url = self._extract_url(command)
                if url:
                    success = await self.browser.navigate_to(url)
                    return {
                        'success': success,
                        'action': 'navigate',
                        'url': url,
                        'message': f"Navigated to {url}" if success else f"Failed to navigate to {url}"
                    }
            
            # Click commands
            elif 'click' in command_lower:
                target = self._extract_click_target(command)
                if target['type'] == 'text':
                    success = await self.browser.click_element(text=target['value'])
                elif target['type'] == 'selector':
                    success = await self.browser.click_element(selector=target['value'])
                else:
                    success = False
                    
                return {
                    'success': success,
                    'action': 'click',
                    'target': target,
                    'message': f"Clicked {target['value']}" if success else f"Failed to click {target['value']}"
                }
            
            # Type commands
            elif 'type' in command_lower or 'enter' in command_lower or 'fill' in command_lower:
                text = self._extract_text_to_type(command)
                success = await self.browser.type_text(text)
                return {
                    'success': success,
                    'action': 'type',
                    'text': text,
                    'message': f"Typed: {text}" if success else f"Failed to type: {text}"
                }
            
            # Scroll commands
            elif 'scroll' in command_lower:
                direction = 'down'
                if 'up' in command_lower:
                    direction = 'up'
                elif 'top' in command_lower:
                    direction = 'top'
                elif 'bottom' in command_lower:
                    direction = 'bottom'
                    
                success = await self.browser.scroll(direction)
                return {
                    'success': success,
                    'action': 'scroll',
                    'direction': direction,
                    'message': f"Scrolled {direction}" if success else f"Failed to scroll {direction}"
                }
            
            # Screenshot commands
            elif 'screenshot' in command_lower or 'capture' in command_lower:
                screenshot = await self.browser.take_screenshot()
                return {
                    'success': bool(screenshot),
                    'action': 'screenshot',
                    'screenshot': screenshot,
                    'message': "Screenshot captured" if screenshot else "Failed to capture screenshot"
                }
            
            # Extract content commands
            elif 'extract' in command_lower or 'get content' in command_lower:
                content = await self.browser.extract_page_content()
                return {
                    'success': bool(content),
                    'action': 'extract',
                    'content': content,
                    'message': "Content extracted" if content else "Failed to extract content"
                }
            
            else:
                return {
                    'success': False,
                    'action': 'unknown',
                    'message': f"Unknown command: {command}"
                }
                
        except Exception as e:
            logger.error(f"Command execution failed: {e}")
            return {
                'success': False,
                'action': 'error',
                'message': f"Error executing command: {str(e)}"
            }
    
    def _extract_url(self, command: str) -> str:
        """Extract URL from command"""
        words = command.split()
        for word in words:
            if 'http' in word or '.' in word:
                if not word.startswith('http'):
                    word = 'https://' + word
                return word
        return ""
    
    def _extract_click_target(self, command: str) -> Dict[str, str]:
        """Extract click target from command"""
        # Look for quoted text
        if '"' in command:
            start = command.find('"') + 1
            end = command.find('"', start)
            if end > start:
                return {'type': 'text', 'value': command[start:end]}
        
        # Look for common button/link text
        words = command.lower().split()
        button_words = ['button', 'link', 'submit', 'login', 'sign', 'search']
        for i, word in enumerate(words):
            if word in button_words and i + 1 < len(words):
                return {'type': 'text', 'value': words[i + 1]}
        
        return {'type': 'text', 'value': 'button'}
    
    def _extract_text_to_type(self, command: str) -> str:
        """Extract text to type from command"""
        # Look for quoted text
        if '"' in command:
            start = command.find('"') + 1
            end = command.find('"', start)
            if end > start:
                return command[start:end]
        
        # Extract text after type/enter/fill
        words = command.split()
        trigger_words = ['type', 'enter', 'fill']
        for i, word in enumerate(words):
            if word.lower() in trigger_words and i + 1 < len(words):
                return ' '.join(words[i + 1:])
        
        return ""
    
    async def close(self):
        """Close the browser agent"""
        await self.browser.close()

# Global instance for API access
browser_agent = NaturalLanguageBrowserAgent()

async def initialize_browser_control():
    """Initialize browser control system"""
    try:
        success = await browser_agent.initialize()
        if success:
            logger.info("✅ Browser control system initialized")
        else:
            logger.error("❌ Browser control system failed to initialize")
        return success
    except Exception as e:
        logger.error(f"Browser control initialization error: {e}")
        return False

async def execute_browser_command(command: str) -> Dict[str, Any]:
    """Execute a browser command"""
    return await browser_agent.execute_command(command)

async def authenticate_google_cloud():
    """Automate Google Cloud authentication."""
    try:
        success = await browser_agent.initialize()
        if success:
            logger.info("✅ Browser initialized for Google Cloud authentication")
            await browser_agent.execute_command("go to https://accounts.google.com/o/oauth2/auth")
            logger.info("✅ Navigated to Google Cloud authentication page")
        else:
            logger.error("❌ Failed to initialize browser for Google Cloud authentication")
    except Exception as e:
        logger.error(f"Error during Google Cloud authentication: {e}")

if __name__ == "__main__":
    async def test_browser_control():
        """Test browser control system"""
        print("🧪 Testing Browser Control System")
        
        # Initialize
        if await initialize_browser_control():
            print("✅ Browser initialized")
            
            # Test commands
            commands = [
                "go to https://google.com",
                "take a screenshot",
                "click search button",
                "type hello world",
                "scroll down"
            ]
            
            for cmd in commands:
                print(f"\n🔄 Executing: {cmd}")
                result = await execute_browser_command(cmd)
                print(f"Result: {result['message']}")
                await asyncio.sleep(2)
            
            await browser_agent.close()
        else:
            print("❌ Browser initialization failed")
    
    asyncio.run(test_browser_control())
