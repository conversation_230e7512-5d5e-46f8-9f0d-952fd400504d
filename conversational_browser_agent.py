#!/usr/bin/env python3
"""
Conversational Browser Agent
===========================

An AI-powered browser agent that understands natural language,
maintains conversation context, and automates browsers like OpenAI's Operator.
"""

import asyncio
import json
import time
import re
from dataclasses import dataclass, asdict
from typing import List, Dict, Any, Optional
from pathlib import Path

# Browser automation
try:
    from selenium import webdriver
    from selenium.webdriver.chrome.options import Options
    from selenium.webdriver.common.by import By
    from selenium.webdriver.support.ui import WebDriverWait
    from selenium.webdriver.support import expected_conditions as EC
    from selenium.webdriver.common.action_chains import ActionChains
    from webdriver_manager.chrome import ChromeDriverManager
    from selenium.webdriver.chrome.service import Service
    SELENIUM_AVAILABLE = True
except ImportError:
    SELENIUM_AVAILABLE = False

# Web server
try:
    from aiohttp import web, WSMsgType
    import aiohttp_cors
    AIOHTTP_AVAILABLE = True
except ImportError:
    AIOHTTP_AVAILABLE = False

# AI/NLP capabilities
try:
    import openai
    OPENAI_AVAILABLE = True
except ImportError:
    OPENAI_AVAILABLE = False

@dataclass
class ConversationMessage:
    """A message in the conversation"""
    role: str  # 'user', 'assistant', 'system'
    content: str
    timestamp: float
    metadata: Optional[Dict[str, Any]] = None

@dataclass
class BrowserState:
    """Current state of the browser"""
    url: str = ""
    title: str = ""
    page_text: str = ""
    elements: List[Dict[str, Any]] = None
    screenshot_path: str = ""
    
    def __post_init__(self):
        if self.elements is None:
            self.elements = []

class ConversationalBrowserAgent:
    """AI-powered conversational browser agent"""
    
    def __init__(self, openai_api_key: Optional[str] = None):
        self.driver = None
        self.conversation_history: List[ConversationMessage] = []
        self.browser_state = BrowserState()
        self.openai_api_key = openai_api_key
        self.session_id = f"session_{int(time.time())}"
        
        # Initialize conversation with system prompt
        self.add_message("system", self._get_system_prompt())
        
        if openai_api_key:
            openai.api_key = openai_api_key
    
    def _get_system_prompt(self) -> str:
        """Get the system prompt for the AI agent"""
        return """You are a conversational browser automation agent, similar to OpenAI's Operator. 

Your capabilities:
- Navigate websites and interact with web pages
- Understand natural language instructions
- Remember conversation context and previous actions
- Explain what you're doing and ask for clarification when needed
- Take screenshots and analyze page content
- Fill forms, click buttons, extract information
- Maintain ongoing conversations about browser tasks

When given instructions:
1. Acknowledge what you understand
2. Break down complex tasks into steps
3. Execute browser actions while explaining what you're doing
4. Ask questions if instructions are unclear
5. Remember previous context and build upon it
6. Provide helpful feedback and suggestions

Current browser state will be provided with each interaction.
Respond conversationally and helpfully, like a knowledgeable assistant."""

    def add_message(self, role: str, content: str, metadata: Optional[Dict[str, Any]] = None):
        """Add a message to conversation history"""
        message = ConversationMessage(
            role=role,
            content=content,
            timestamp=time.time(),
            metadata=metadata
        )
        self.conversation_history.append(message)
    
    def setup_browser(self, headless: bool = False) -> bool:
        """Setup Chrome browser"""
        try:
            print("🔧 Setting up conversational browser agent...")
            
            chrome_options = Options()
            if headless:
                chrome_options.add_argument("--headless")
            chrome_options.add_argument("--no-sandbox")
            chrome_options.add_argument("--disable-dev-shm-usage")
            chrome_options.add_argument("--disable-gpu")
            chrome_options.add_argument("--window-size=1920,1080")
            
            service = Service(ChromeDriverManager().install())
            self.driver = webdriver.Chrome(service=service, options=chrome_options)
            self.driver.implicitly_wait(10)
            
            print("✅ Browser agent ready for conversation!")
            return True
            
        except Exception as e:
            print(f"❌ Browser setup failed: {e}")
            return False
    
    def update_browser_state(self):
        """Update current browser state"""
        if not self.driver:
            return
        
        try:
            self.browser_state.url = self.driver.current_url
            self.browser_state.title = self.driver.title
            
            # Get visible text (limited to avoid overwhelming the AI)
            body = self.driver.find_element(By.TAG_NAME, "body")
            self.browser_state.page_text = body.text[:2000]  # Limit text length
            
            # Get interactive elements
            self.browser_state.elements = []
            for tag in ['a', 'button', 'input', 'select', 'textarea']:
                elements = self.driver.find_elements(By.TAG_NAME, tag)[:10]  # Limit elements
                for elem in elements:
                    try:
                        elem_info = {
                            'tag': tag,
                            'text': elem.text[:100] if elem.text else '',
                            'id': elem.get_attribute('id') or '',
                            'class': elem.get_attribute('class') or '',
                            'type': elem.get_attribute('type') or '',
                            'name': elem.get_attribute('name') or ''
                        }
                        self.browser_state.elements.append(elem_info)
                    except:
                        continue
                        
        except Exception as e:
            print(f"Warning: Could not update browser state: {e}")
    
    def take_screenshot(self) -> str:
        """Take a screenshot and return the path"""
        if not self.driver:
            return ""
        
        try:
            filename = f"screenshot_{self.session_id}_{int(time.time())}.png"
            self.driver.save_screenshot(filename)
            self.browser_state.screenshot_path = filename
            return filename
        except Exception as e:
            print(f"Screenshot failed: {e}")
            return ""
    
    def execute_browser_action(self, action: str, target: str = "", value: str = "") -> Dict[str, Any]:
        """Execute a browser action"""
        if not self.driver:
            return {"success": False, "error": "Browser not initialized"}
        
        try:
            if action == "navigate":
                self.driver.get(target)
                self.update_browser_state()
                return {"success": True, "message": f"Navigated to {target}"}
                
            elif action == "click":
                element = self.driver.find_element(By.CSS_SELECTOR, target)
                element.click()
                self.update_browser_state()
                return {"success": True, "message": f"Clicked {target}"}
                
            elif action == "type":
                element = self.driver.find_element(By.CSS_SELECTOR, target)
                element.clear()
                element.send_keys(value)
                return {"success": True, "message": f"Typed '{value}' into {target}"}
                
            elif action == "scroll":
                self.driver.execute_script("window.scrollBy(0, arguments[0]);", int(value) if value else 500)
                return {"success": True, "message": f"Scrolled by {value or 500} pixels"}
                
            elif action == "screenshot":
                filename = self.take_screenshot()
                return {"success": True, "message": f"Screenshot saved as {filename}", "data": filename}
                
            elif action == "get_text":
                element = self.driver.find_element(By.CSS_SELECTOR, target)
                text = element.text
                return {"success": True, "message": f"Got text: {text}", "data": text}
                
            else:
                return {"success": False, "error": f"Unknown action: {action}"}
                
        except Exception as e:
            return {"success": False, "error": str(e)}
    
    def parse_natural_language(self, user_input: str) -> List[Dict[str, Any]]:
        """Parse natural language into browser actions (enhanced version)"""
        actions = []
        user_input_lower = user_input.lower()

        # Navigation patterns
        if any(phrase in user_input_lower for phrase in ["go to", "navigate to", "visit", "open"]):
            # Extract URL
            url_match = re.search(r'https?://[^\s]+', user_input)
            if url_match:
                actions.append({"action": "navigate", "target": url_match.group(), "value": ""})
            else:
                # Look for domain names or common sites
                domain_patterns = [
                    r'(?:go to|visit|navigate to|open)\s+([a-zA-Z0-9.-]+\.[a-zA-Z]{2,})',
                    r'(?:go to|visit|navigate to|open)\s+(google|youtube|facebook|twitter|github|stackoverflow)',
                ]
                for pattern in domain_patterns:
                    domain_match = re.search(pattern, user_input_lower)
                    if domain_match:
                        domain = domain_match.group(1)
                        if '.' not in domain:
                            domain = f"{domain}.com"
                        url = f"https://{domain}"
                        actions.append({"action": "navigate", "target": url, "value": ""})
                        break

        # Click patterns
        click_patterns = [
            r'click (?:on )?(?:the )?(.+?)(?:\s|$)',
            r'press (?:the )?(.+?)(?:\s|$)',
            r'tap (?:on )?(?:the )?(.+?)(?:\s|$)'
        ]
        for pattern in click_patterns:
            if any(word in user_input_lower for word in ["click", "press", "tap"]):
                match = re.search(pattern, user_input_lower)
                if match:
                    target = match.group(1).strip()
                    # Convert common terms to CSS selectors
                    if target in ["button", "submit", "login", "sign in"]:
                        actions.append({"action": "click", "target": "button", "value": ""})
                    elif target in ["link", "first link"]:
                        actions.append({"action": "click", "target": "a", "value": ""})
                    else:
                        actions.append({"action": "click", "target": f"*:contains('{target}')", "value": ""})
                break

        # Type/input patterns
        type_patterns = [
            r'type "([^"]+)"',
            r"type '([^']+)'",
            r'enter "([^"]+)"',
            r"enter '([^']+)'",
            r'search for "([^"]+)"',
            r"search for '([^']+)'",
        ]
        for pattern in type_patterns:
            match = re.search(pattern, user_input_lower)
            if match:
                text = match.group(1)
                # Try to find appropriate input field
                if "search" in user_input_lower:
                    actions.append({"action": "type", "target": "input[type='search'], input[name*='search'], input[placeholder*='search']", "value": text})
                else:
                    actions.append({"action": "type", "target": "input", "value": text})
                break

        # Screenshot patterns
        if any(phrase in user_input_lower for phrase in ["screenshot", "take a picture", "capture", "snap"]):
            actions.append({"action": "screenshot", "target": "", "value": ""})

        # Scroll patterns
        if "scroll" in user_input_lower:
            if "up" in user_input_lower:
                actions.append({"action": "scroll", "target": "", "value": "-500"})
            elif "down" in user_input_lower:
                actions.append({"action": "scroll", "target": "", "value": "500"})
            else:
                actions.append({"action": "scroll", "target": "", "value": "500"})

        # Information extraction patterns
        if any(phrase in user_input_lower for phrase in ["get", "find", "extract", "what is", "what's", "tell me"]):
            if "title" in user_input_lower:
                actions.append({"action": "get_text", "target": "title", "value": ""})
            elif "heading" in user_input_lower or "header" in user_input_lower:
                actions.append({"action": "get_text", "target": "h1, h2, h3", "value": ""})
            elif "text" in user_input_lower:
                actions.append({"action": "get_text", "target": "body", "value": ""})

        return actions
    
    async def generate_ai_response(self, user_input: str) -> str:
        """Generate AI response using OpenAI (or fallback to rule-based)"""
        if not self.openai_api_key or not OPENAI_AVAILABLE:
            return self._generate_fallback_response(user_input)
        
        try:
            # Prepare conversation context
            messages = []
            for msg in self.conversation_history[-10:]:  # Last 10 messages for context
                messages.append({"role": msg.role, "content": msg.content})
            
            # Add current browser state
            state_info = f"""
Current Browser State:
- URL: {self.browser_state.url}
- Title: {self.browser_state.title}
- Page Text (excerpt): {self.browser_state.page_text[:500]}...
- Interactive Elements: {len(self.browser_state.elements)} found
"""
            messages.append({"role": "system", "content": state_info})
            messages.append({"role": "user", "content": user_input})
            
            response = openai.ChatCompletion.create(
                model="gpt-3.5-turbo",
                messages=messages,
                max_tokens=500,
                temperature=0.7
            )
            
            return response.choices[0].message.content
            
        except Exception as e:
            print(f"AI response generation failed: {e}")
            return self._generate_fallback_response(user_input)
    
    def _generate_fallback_response(self, user_input: str) -> str:
        """Generate a fallback response when AI is not available"""
        user_input_lower = user_input.lower()

        # Greeting responses
        if any(greeting in user_input_lower for greeting in ["hello", "hi", "hey", "start"]):
            return "Hello! I'm your conversational browser agent, similar to OpenAI's Operator. I can understand natural language and help you automate web browsing. I remember our conversation and can build on previous actions. What would you like me to do?"

        # Navigation responses
        elif any(nav in user_input_lower for nav in ["go to", "visit", "navigate", "open"]):
            url_found = re.search(r'https?://[^\s]+|[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}', user_input)
            if url_found:
                return f"I'll navigate to {url_found.group()} for you. Once the page loads, I can help you interact with it, fill forms, extract information, or perform other tasks. Just let me know what you need!"
            else:
                return "I'd be happy to navigate somewhere for you! Could you specify the website or URL you'd like me to visit?"

        # Interaction responses
        elif any(action in user_input_lower for action in ["click", "press", "tap"]):
            return "I'll look for that element on the current page and click it for you. If there are multiple similar elements, I'll try to find the most relevant one based on your description."

        # Input responses
        elif any(input_action in user_input_lower for input_action in ["type", "enter", "fill", "search"]):
            return "I'll find the appropriate input field and enter that text for you. If you're searching, I'll look for search boxes or forms on the page."

        # Information extraction responses
        elif any(extract in user_input_lower for extract in ["what", "get", "find", "extract", "tell me", "show me"]):
            if self.browser_state.url:
                return f"I'll analyze the current page ({self.browser_state.title or self.browser_state.url}) and extract that information for you. Let me examine the page content."
            else:
                return "I'd be happy to extract information for you! First, let me know which website you'd like me to visit, or if you want me to analyze the current page."

        # Screenshot responses
        elif any(capture in user_input_lower for capture in ["screenshot", "picture", "capture", "snap"]):
            return "I'll take a screenshot of the current page for you. This will capture the entire visible area of the webpage."

        # Scroll responses
        elif "scroll" in user_input_lower:
            direction = "down" if "down" in user_input_lower else "up" if "up" in user_input_lower else "down"
            return f"I'll scroll {direction} on the current page for you. This will help you see more content."

        # Help responses
        elif "help" in user_input_lower or "what can you do" in user_input_lower:
            return """I'm a conversational browser agent that understands natural language! Here's what I can do:

🌐 **Navigation**: "Go to google.com", "Visit github.com", "Open youtube"
🖱️ **Interactions**: "Click the login button", "Press submit", "Tap the menu"
⌨️ **Text Input**: "Type 'hello world'", "Search for 'AI news'", "Enter my email"
📸 **Screenshots**: "Take a screenshot", "Capture the page"
📜 **Scrolling**: "Scroll down", "Scroll up"
🔍 **Information**: "What's the page title?", "Get the main heading", "Find all links"

I remember our conversation and can build on previous actions. Just tell me what you want to do in natural language!"""

        # Context-aware responses
        elif self.browser_state.url:
            return f"I'm currently on {self.browser_state.title or self.browser_state.url}. I understand you want me to: '{user_input}'. Let me analyze the page and see how I can help you with that task."

        # Default response
        else:
            return f"I understand you want me to: '{user_input}'. I'm ready to help! If you'd like me to start by visiting a website, just say something like 'Go to [website]'. Or if you want to see what I can do, just ask 'What can you help me with?'"
    
    async def process_conversation(self, user_input: str) -> Dict[str, Any]:
        """Process a conversational input and return response with actions"""
        # Add user message to history
        self.add_message("user", user_input)
        
        # Parse for browser actions
        actions = self.parse_natural_language(user_input)
        
        # Execute actions
        action_results = []
        for action_data in actions:
            result = self.execute_browser_action(
                action_data["action"],
                action_data["target"],
                action_data["value"]
            )
            action_results.append(result)
        
        # Generate AI response
        ai_response = await self.generate_ai_response(user_input)
        
        # Add AI response to history
        self.add_message("assistant", ai_response, {
            "actions_executed": len(action_results),
            "browser_state": asdict(self.browser_state)
        })
        
        return {
            "response": ai_response,
            "actions_executed": action_results,
            "browser_state": asdict(self.browser_state),
            "conversation_length": len(self.conversation_history)
        }
    
    def get_conversation_history(self) -> List[Dict[str, Any]]:
        """Get conversation history"""
        return [asdict(msg) for msg in self.conversation_history if msg.role != "system"]
    
    def cleanup(self):
        """Cleanup resources"""
        if self.driver:
            try:
                self.driver.quit()
                print("🧹 Browser agent cleaned up")
            except:
                pass
            self.driver = None

# Web interface for conversational browser agent
class ConversationalBrowserServer:
    """Web server for conversational browser agent"""
    
    def __init__(self, host: str = "localhost", port: int = 8081, openai_api_key: Optional[str] = None):
        self.host = host
        self.port = port
        self.app = web.Application()
        self.agent = ConversationalBrowserAgent(openai_api_key)
        self.websockets = set()
        self.setup_routes()
    
    def setup_routes(self):
        """Setup web routes"""
        self.app.router.add_get('/', self.index)
        self.app.router.add_get('/ws', self.websocket_handler)
        self.app.router.add_post('/api/chat', self.chat_endpoint)
        self.app.router.add_get('/api/history', self.history_endpoint)
        self.app.router.add_get('/api/state', self.state_endpoint)
        
        # Enable CORS
        if AIOHTTP_AVAILABLE:
            cors = aiohttp_cors.setup(self.app, defaults={
                "*": aiohttp_cors.ResourceOptions(
                    allow_credentials=True,
                    expose_headers="*",
                    allow_headers="*",
                    allow_methods="*"
                )
            })
            for route in list(self.app.router.routes()):
                cors.add(route)
    
    async def index(self, request):
        """Serve conversational interface"""
        html = """
        <!DOCTYPE html>
        <html>
        <head>
            <title>🤖 Conversational Browser Agent</title>
            <style>
                body { font-family: Arial, sans-serif; margin: 0; padding: 20px; background: #f5f5f5; }
                .container { max-width: 1200px; margin: 0 auto; display: grid; grid-template-columns: 1fr 300px; gap: 20px; }
                .chat-container { background: white; border-radius: 10px; padding: 20px; height: 80vh; display: flex; flex-direction: column; }
                .sidebar { background: white; border-radius: 10px; padding: 20px; }
                .messages { flex: 1; overflow-y: auto; margin-bottom: 20px; padding: 10px; border: 1px solid #ddd; border-radius: 5px; }
                .message { margin: 10px 0; padding: 10px; border-radius: 5px; }
                .user-message { background: #007bff; color: white; margin-left: 20%; }
                .assistant-message { background: #f8f9fa; border: 1px solid #ddd; margin-right: 20%; }
                .input-area { display: flex; gap: 10px; }
                .input-area input { flex: 1; padding: 10px; border: 1px solid #ddd; border-radius: 5px; }
                .input-area button { padding: 10px 20px; background: #007bff; color: white; border: none; border-radius: 5px; cursor: pointer; }
                .status { padding: 10px; background: #e9ecef; border-radius: 5px; margin-bottom: 10px; }
                .browser-state { font-size: 12px; color: #666; }
                h1 { text-align: center; color: #333; }
                .examples { margin-top: 20px; }
                .example { background: #e9ecef; padding: 5px 10px; margin: 5px 0; border-radius: 3px; cursor: pointer; font-size: 14px; }
                .example:hover { background: #dee2e6; }
            </style>
        </head>
        <body>
            <h1>🤖 Conversational Browser Agent</h1>
            <div class="container">
                <div class="chat-container">
                    <div class="status" id="status">Initializing browser agent...</div>
                    <div class="messages" id="messages">
                        <div class="assistant-message">
                            Hello! I'm your conversational browser agent. I can help you navigate websites, interact with pages, and automate browser tasks. Just tell me what you'd like me to do in natural language!
                        </div>
                    </div>
                    <div class="input-area">
                        <input type="text" id="userInput" placeholder="Tell me what you'd like me to do..." onkeypress="handleKeyPress(event)">
                        <button onclick="sendMessage()">Send</button>
                    </div>
                </div>
                <div class="sidebar">
                    <h3>Browser State</h3>
                    <div class="browser-state" id="browserState">Not connected</div>
                    
                    <div class="examples">
                        <h4>Try these examples:</h4>
                        <div class="example" onclick="setInput('Go to google.com')">Go to google.com</div>
                        <div class="example" onclick="setInput('Take a screenshot')">Take a screenshot</div>
                        <div class="example" onclick="setInput('Visit example.com and get the page title')">Visit example.com and get the page title</div>
                        <div class="example" onclick="setInput('Scroll down the page')">Scroll down the page</div>
                        <div class="example" onclick="setInput('What can you see on this page?')">What can you see on this page?</div>
                    </div>
                </div>
            </div>
            
            <script>
                let ws = null;
                
                function connectWebSocket() {
                    ws = new WebSocket('ws://localhost:8081/ws');
                    
                    ws.onopen = function() {
                        document.getElementById('status').innerHTML = '✅ Connected to browser agent';
                        initializeBrowser();
                    };
                    
                    ws.onmessage = function(event) {
                        const data = JSON.parse(event.data);
                        if (data.type === 'response') {
                            addMessage('assistant', data.response);
                            updateBrowserState(data.browser_state);
                        }
                    };
                    
                    ws.onclose = function() {
                        document.getElementById('status').innerHTML = '❌ Disconnected from browser agent';
                        setTimeout(connectWebSocket, 3000);
                    };
                }
                
                async function initializeBrowser() {
                    try {
                        const response = await fetch('/api/state');
                        const data = await response.json();
                        updateBrowserState(data);
                    } catch (error) {
                        console.error('Failed to initialize browser state:', error);
                    }
                }
                
                async function sendMessage() {
                    const input = document.getElementById('userInput');
                    const message = input.value.trim();
                    if (!message) return;
                    
                    addMessage('user', message);
                    input.value = '';
                    
                    try {
                        const response = await fetch('/api/chat', {
                            method: 'POST',
                            headers: {'Content-Type': 'application/json'},
                            body: JSON.stringify({message: message})
                        });
                        
                        const data = await response.json();
                        addMessage('assistant', data.response);
                        updateBrowserState(data.browser_state);
                        
                    } catch (error) {
                        addMessage('assistant', 'Sorry, I encountered an error processing your request.');
                    }
                }
                
                function addMessage(role, content) {
                    const messages = document.getElementById('messages');
                    const messageDiv = document.createElement('div');
                    messageDiv.className = role + '-message message';
                    messageDiv.textContent = content;
                    messages.appendChild(messageDiv);
                    messages.scrollTop = messages.scrollHeight;
                }
                
                function updateBrowserState(state) {
                    const stateDiv = document.getElementById('browserState');
                    stateDiv.innerHTML = `
                        <strong>URL:</strong> ${state.url || 'None'}<br>
                        <strong>Title:</strong> ${state.title || 'None'}<br>
                        <strong>Elements:</strong> ${state.elements ? state.elements.length : 0}<br>
                        <strong>Screenshot:</strong> ${state.screenshot_path || 'None'}
                    `;
                }
                
                function setInput(text) {
                    document.getElementById('userInput').value = text;
                }
                
                function handleKeyPress(event) {
                    if (event.key === 'Enter') {
                        sendMessage();
                    }
                }
                
                // Initialize
                connectWebSocket();
            </script>
        </body>
        </html>
        """
        return web.Response(text=html, content_type='text/html')
    
    async def websocket_handler(self, request):
        """Handle WebSocket connections"""
        ws = web.WebSocketResponse()
        await ws.prepare(request)
        
        self.websockets.add(ws)
        
        try:
            async for msg in ws:
                if msg.type == WSMsgType.TEXT:
                    data = json.loads(msg.data)
                    if data.get('type') == 'chat':
                        result = await self.agent.process_conversation(data['message'])
                        await ws.send_text(json.dumps({
                            'type': 'response',
                            'response': result['response'],
                            'browser_state': result['browser_state']
                        }))
        finally:
            self.websockets.discard(ws)
        
        return ws
    
    async def chat_endpoint(self, request):
        """Handle chat API requests"""
        try:
            data = await request.json()
            result = await self.agent.process_conversation(data['message'])
            return web.json_response(result)
        except Exception as e:
            return web.json_response({"error": str(e)}, status=500)
    
    async def history_endpoint(self, request):
        """Get conversation history"""
        return web.json_response(self.agent.get_conversation_history())
    
    async def state_endpoint(self, request):
        """Get current browser state"""
        return web.json_response(asdict(self.agent.browser_state))
    
    async def start_server(self):
        """Start the server"""
        # Initialize browser
        if not self.agent.setup_browser():
            print("❌ Failed to setup browser")
            return None
        
        runner = web.AppRunner(self.app)
        await runner.setup()
        site = web.TCPSite(runner, self.host, self.port)
        await site.start()
        print(f"🌐 Conversational Browser Agent running at http://{self.host}:{self.port}")
        return runner

def main():
    """Main function"""
    print("🤖 Starting Conversational Browser Agent...")
    
    if not SELENIUM_AVAILABLE:
        print("❌ Selenium not available. Please install: pip install selenium webdriver-manager")
        return
    
    if not AIOHTTP_AVAILABLE:
        print("❌ aiohttp not available. Please install: pip install aiohttp aiohttp-cors")
        return
    
    # Get OpenAI API key from environment or user input
    import os
    openai_api_key = os.getenv('OPENAI_API_KEY')
    
    if not openai_api_key:
        print("💡 OpenAI API key not found. Agent will use rule-based responses.")
        print("   Set OPENAI_API_KEY environment variable for full AI capabilities.")
    
    async def run_server():
        server = ConversationalBrowserServer(openai_api_key=openai_api_key)
        runner = await server.start_server()
        
        if not runner:
            return
        
        try:
            print("\n🎯 Ready for conversation! Visit http://localhost:8081")
            print("💬 You can now chat with your browser agent in natural language!")
            while True:
                await asyncio.sleep(1)
        except KeyboardInterrupt:
            print("\n🛑 Shutting down...")
        finally:
            await runner.cleanup()
            server.agent.cleanup()
    
    asyncio.run(run_server())

if __name__ == "__main__":
    main()
