#!/bin/bash

# Send Audio Email to <PERSON>
# This script runs the automated_audio_email.py script to send an audio email to Paul <PERSON>

echo "========================================================================"
echo "SENDING AUDIO EMAIL TO PAUL EDWARDS"
echo "========================================================================"

# Check if Python is installed
if ! command -v python3 &> /dev/null; then
    echo "Python 3 is not installed. Please install Python 3 and try again."
    exit 1
fi

# Check if the required Python packages are installed
echo "Checking required Python packages..."
python3 -c "import requests, dotenv" 2>/dev/null
if [ $? -ne 0 ]; then
    echo "Installing required Python packages..."
    pip3 install requests python-dotenv
fi

# Run the automated_audio_email.py script
echo "Running automated_audio_email.py..."
python3 automated_audio_email.py

# Check if the script ran successfully
if [ $? -eq 0 ]; then
    echo "========================================================================"
    echo "AUDIO EMAIL PROCESS COMPLETED"
    echo "========================================================================"
else
    echo "========================================================================"
    echo "ERROR: AUDIO EMAIL PROCESS FAILED"
    echo "========================================================================"
fi
