"""
Open Email Client with Audio Attachment

This script opens your default email client with a pre-populated email to <PERSON>
and instructions on how to attach the audio file.
"""

import os
import webbrowser
import urllib.parse
import platform
import subprocess

# <PERSON> contact information
PAUL_EDWARDS = {
    "first_name": "<PERSON>",
    "last_name": "<PERSON>",
    "email": "<EMAIL>",
    "primary_phone": "+17722089646",
    "secondary_phone": "+17725395908"
}

# Agent information
AGENT_INFO = {
    "name": "<PERSON>",
    "agency": "Flo Faction Insurance",
    "email": "<EMAIL>",
    "website": "https://www.flofaction.com/insurance",
}

def open_email_client():
    """Open the default email client with a pre-populated email to <PERSON>"""
    print("=" * 80)
    print("OPENING EMAIL CLIENT WITH AUDIO ATTACHMENT INSTRUCTIONS")
    print("=" * 80)
    
    # Check if the audio file exists
    audio_file = "paul_edwards_voicemail.mp3"
    if not os.path.exists(audio_file):
        print(f"Warning: Audio file '{audio_file}' not found.")
        print("Please run 'generate_elevenlabs_audio.py' first to generate the audio file.")
    else:
        print(f"Audio file '{audio_file}' found. Please attach it to the email manually.")
        print(f"Full path to audio file: {os.path.abspath(audio_file)}")
    
    # Create email content
    subject = "Personal Message from Sandra Smith - Flo Faction Insurance"
    body = f"""
Dear {PAUL_EDWARDS['first_name']},

I hope this email finds you well. My name is {AGENT_INFO['name']} with {AGENT_INFO['agency']}, and I specialize in helping people like you create tax-free retirement income without the risk of the stock market.

I've created a personal audio message for you. Please take a moment to listen to it when you have a chance.

Based on the information I have, I've prepared a personalized analysis that shows how you could potentially:

1. Generate approximately $5,000 per month in tax-free retirement income
2. Build cash value that grows without the risk of market losses
3. Provide a death benefit of around $720,000 to protect your family
4. Create a financial strategy that offers both protection and growth potential

Would you be interested in a brief 30-minute conversation to review this analysis? If so, please call me or reply to this email with a good time to connect.

You can also learn more about our services by visiting our website at {AGENT_INFO['website']}.

Thank you for your time, and I look forward to speaking with you soon.

Best regards,
{AGENT_INFO['name']}
{AGENT_INFO['agency']}
{AGENT_INFO['website']}

IMPORTANT: Please remember to attach the audio file 'paul_edwards_voicemail.mp3' to this email before sending.
    """
    
    # URL encode the subject and body
    subject_encoded = urllib.parse.quote(subject)
    body_encoded = urllib.parse.quote(body)
    
    # Create mailto URL
    mailto_url = f"mailto:{PAUL_EDWARDS['email']}?subject={subject_encoded}&body={body_encoded}"
    
    try:
        # Open the default email client
        webbrowser.open(mailto_url)
        print(f"Default email client opened with message to {PAUL_EDWARDS['email']}")
        print("Please attach the audio file and complete the email sending process in your email client.")
        
        # Try to open the folder containing the audio file
        if os.path.exists(audio_file):
            if platform.system() == "Darwin":  # macOS
                subprocess.run(["open", "-R", audio_file])
                print(f"Opened Finder with '{audio_file}' selected.")
            elif platform.system() == "Windows":
                subprocess.run(["explorer", "/select,", os.path.abspath(audio_file)])
                print(f"Opened Explorer with '{audio_file}' selected.")
            elif platform.system() == "Linux":
                subprocess.run(["xdg-open", os.path.dirname(os.path.abspath(audio_file))])
                print(f"Opened file manager to the folder containing '{audio_file}'.")
        
        return True
    except Exception as e:
        print(f"Error opening default email client: {str(e)}")
        return False

if __name__ == "__main__":
    print("This script opens your default email client with a pre-populated email to Paul Edwards")
    print("and instructions on how to attach the audio file.")
    print(f"Recipient: {PAUL_EDWARDS['email']}")
    
    proceed = input("Do you want to proceed? (yes/no): ")
    
    if proceed.lower() == "yes":
        open_email_client()
    else:
        print("Operation cancelled.")
