#!/bin/bash

# Complete MCP Server Fix Script
# This script fixes all issues with the MCP servers and ensures they start automatically

echo "🔧 Complete MCP Server Fix"
echo "========================="
echo "This script will fix all issues with the MCP servers and ensure they start automatically."
echo

# Step 1: Install dependencies
echo "Step 1: Installing dependencies..."
./install_dependencies.sh
if [ $? -ne 0 ]; then
    echo "❌ Error: Failed to install dependencies."
    exit 1
fi

# Step 2: Create necessary directories
echo "Step 2: Creating necessary directories..."
mkdir -p ./config/llm_models
mkdir -p ./local/models

# Step 3: Install the launchd service
echo "Step 3: Installing launchd service..."
cp ./com.flofaction.mcpservers.plist ~/Library/LaunchAgents/
launchctl unload ~/Library/LaunchAgents/com.flofaction.mcpservers.plist 2>/dev/null
launchctl load -w ~/Library/LaunchAgents/com.flofaction.mcpservers.plist

# Check if the service was installed correctly
if launchctl list | grep -q "com.flofaction.mcpservers"; then
    echo "✅ Launchd service installed successfully"
else
    echo "⚠️ Failed to install launchd service"
fi

# Step 4: Stop any running MCP servers
echo "Step 4: Stopping any running MCP servers..."
pkill -f "python3 start_mcp_servers.py" 2>/dev/null
pkill -f "local_models_mcp_server.py" 2>/dev/null
pkill -f "data_processing_server.py" 2>/dev/null
pkill -f "knowledge_base_server.py" 2>/dev/null
pkill -f "communication_server.py" 2>/dev/null
pkill -f "workflow_server.py" 2>/dev/null
pkill -f "security_server.py" 2>/dev/null
pkill -f "integration_server.py" 2>/dev/null

# Step 5: Start the servers
echo "Step 5: Starting all MCP servers..."
./start_all_mcp_servers.sh &

# Wait a moment for servers to start
echo "Waiting for servers to start..."
sleep 15

# Step 6: Check if servers are running
echo "Step 6: Checking MCP server status..."
for port in 8080 8081 8082 8083 8084 8085 8086 8087 8088 8089; do
    if nc -z localhost $port 2>/dev/null; then
        echo "✅ Server on port $port is running"
    else
        echo "⚠️ Server on port $port is not responding"
    fi
done

echo
echo "✅ MCP server fix complete!"
echo "All 10 MCP servers should now start automatically when your system boots."
echo "If you still have issues, check the log files:"
echo "  - $(pwd)/mcp_servers_startup.log"
echo "  - $(pwd)/mcp_servers_output.log"
echo "  - $(pwd)/mcp_servers_error.log"
echo
echo "You can manually start all servers at any time by running:"
echo "  $(pwd)/start_all_mcp_servers.sh"
