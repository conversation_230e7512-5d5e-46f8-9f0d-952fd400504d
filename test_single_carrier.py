import asyncio
import logging
from datetime import datetime
from insurance_carriers import CarrierManager, MutualOfOmahaPortal
from client_template import ClientManager
from secure_credentials import SecureCredentialsManager

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def test_mutual_of_omaha():
    """Test Mutual of Omaha quote generation"""
    
    try:
        # Initialize credentials manager
        creds_manager = SecureCredentialsManager()
        
        # Test credential retrieval
        logger.info("Testing credential retrieval for Mutual of Omaha...")
        try:
            creds = creds_manager.get_carrier_credentials("sandra", "mutual_of_omaha")
            logger.info("Successfully retrieved credentials")
            logger.info(f"Username: {creds.get('username')}")
            logger.info(f"Support Phone: {creds.get('phone', '(*************')}")
        except Exception as e:
            logger.error(f"Failed to retrieve credentials: {e}")
            return False
            
        # Initialize portal
        logger.info("\nTesting portal connection...")
        portal = MutualOfOmahaPortal(creds_manager)
        
        # Test connection
        connected = await portal.test_connection()
        logger.info(f"Connection test {'successful' if connected else 'failed'}")
        
        if not connected:
            return False
            
        # Initialize test client
        client_manager = ClientManager()
        test_client = {
            "name": "John Medicare Test",
            "dob": "1955-06-15",
            "height": "5'10\"",
            "weight": "175",
            "phone": "************",
            "email": "<EMAIL>",
            "address": "123 Senior Way, Port St. Lucie, FL 34952",
            "ssn": "***********",
            "drivers_license": "FL*********",
            
            "medications": [
                {
                    "drug_name": "Metformin",
                    "dosage": "500mg",
                    "frequency": "twice daily"
                }
            ],
            
            "tobacco_use": False,
            "marijuana_use": False,
            
            "bank_info": {
                "bank_name": "Wells Fargo",
                "routing_number": "*********",
                "account_number": "*********"
            },
            
            "beneficiaries": [
                {
                    "name": "Mary Test",
                    "dob": "1957-08-20",
                    "relationship": "spouse"
                }
            ],
            
            "budget_range": "$200-$250",
            "family_health_history": "No significant issues",
            "reason_notes": "Looking for Medicare Supplement plan",
            "start_date": datetime.now().strftime("%Y-%m-%d")
        }
        
        # Create client record
        logger.info("\nCreating test client record...")
        client = client_manager.create_client(test_client)
        
        # Test login
        logger.info("\nTesting carrier login...")
        logged_in = await portal.login("mutual_of_omaha", "sandra")
        
        if not logged_in:
            logger.error("Failed to log in to carrier portal")
            return False
            
        logger.info("Successfully logged in to carrier portal")
        
        # Get quote
        logger.info("\nRequesting quote...")
        carrier_manager = CarrierManager()
        quote = await carrier_manager._get_carrier_quote(portal, client, "mutual_of_omaha")
        
        if quote:
            logger.info("Quote received successfully:")
            logger.info(f"Premium: ${quote.get('premium')}")
            logger.info(f"Coverage: ${quote.get('coverage')}")
            if 'details' in quote:
                logger.info("Plan Details:")
                for key, value in quote['details'].items():
                    logger.info(f"  {key}: {value}")
            return True
        else:
            logger.error("Failed to get quote")
            return False
            
    except Exception as e:
        logger.error(f"Test failed: {str(e)}")
        return False

async def main():
    """Run single carrier test"""
    logger.info("=== Testing Mutual of Omaha Integration ===\n")
    
    success = await test_mutual_of_omaha()
    
    if success:
        logger.info("\nTest completed successfully!")
    else:
        logger.error("\nTest failed!")

if __name__ == "__main__":
    asyncio.run(main())