"""
Send <PERSON> Text Message - Fixed Authentication

This script sends a text message to <PERSON> using Twilio with proper authentication.
"""

import os
import requests
import base64
from urllib.parse import urlencode

# <PERSON> contact information
PAUL_EDWARDS = {
    "first_name": "<PERSON>",
    "last_name": "<PERSON>",
    "email": "<EMAIL>",
    "phone": "+***********",  # Primary contact number
    "alt_phone": "+***********"  # Alternative contact number
}

# ======================== CONFIGURATION ========================
# Twilio credentials
TWILIO_ACCOUNT_SID = "AC187c871afa232bbbc978caf33f3e25d9"
TWILIO_AUTH_TOKEN = "CqpVewwter1BEMdFIFHrN2XmUyt22wBP"

# Agent information
AGENT_INFO = {
    "name": "<PERSON>",
    "agency": "Flo Faction Insurance",
    "phone": "Flo Faction AI Assistant",
    "email": "<EMAIL>",
    "website": "https://www.flofaction.com",
}

# ======================== TWILIO FUNCTIONS ========================
def send_text_message_direct(to_number, message):
    """Send a text message using Twilio REST API directly"""
    
    # Format phone number for Twilio
    if not to_number.startswith("+"):
        to_number = "+1" + to_number
    
    try:
        # Twilio API endpoint for sending messages
        url = f"https://api.twilio.com/2010-04-01/Accounts/{TWILIO_ACCOUNT_SID}/Messages.json"
        
        # Create the authentication header
        auth = base64.b64encode(f"{TWILIO_ACCOUNT_SID}:{TWILIO_AUTH_TOKEN}".encode()).decode()
        headers = {
            "Authorization": f"Basic {auth}",
            "Content-Type": "application/x-www-form-urlencoded"
        }
        
        # Prepare the request data
        data = {
            "To": to_number,
            "Body": message
        }
        
        # For testing, we'll use Twilio's default messaging service
        # This will select an appropriate sender number automatically
        if os.environ.get("TWILIO_MESSAGING_SERVICE_SID"):
            data["MessagingServiceSid"] = os.environ.get("TWILIO_MESSAGING_SERVICE_SID")
        else:
            # If no messaging service is available, we need to specify a From number
            # For testing, we'll use a Twilio test number
            data["From"] = "+***********"  # Twilio test number
        
        # Make the request
        response = requests.post(url, headers=headers, data=urlencode(data))
        
        # Check if the request was successful
        if response.status_code >= 200 and response.status_code < 300:
            response_data = response.json()
            print(f"Text message sent with SID: {response_data.get('sid')}")
            return response_data.get('sid')
        else:
            print(f"Error sending text message: {response.status_code} - {response.text}")
            return None
    except Exception as e:
        print(f"Exception sending text message: {str(e)}")
        return None

# ======================== MAIN FUNCTION ========================
def send_text_to_paul():
    """Send a text message to Paul Edwards"""
    print("=" * 80)
    print("SENDING TEXT MESSAGE TO PAUL EDWARDS")
    print("=" * 80)
    
    # Create custom text message with Flo Faction branding
    text_message = f"""
Hi {PAUL_EDWARDS['first_name']}, this is {AGENT_INFO['name']} with {AGENT_INFO['agency']}. I help people create tax-free retirement income without stock market risk. I've prepared a personalized analysis for you. Would you be interested in seeing this analysis? Visit {AGENT_INFO['website']} for more information. Thanks!
    """
    
    # Send to primary number
    print("\nSENDING TEXT MESSAGE TO PRIMARY NUMBER")
    print("-" * 80)
    primary_sid = send_text_message_direct(PAUL_EDWARDS["phone"], text_message)
    
    # Send to alternative number
    print("\nSENDING TEXT MESSAGE TO ALTERNATIVE NUMBER")
    print("-" * 80)
    alt_sid = send_text_message_direct(PAUL_EDWARDS["alt_phone"], text_message)
    
    # Summary
    print("\n" + "=" * 80)
    print("TEXT MESSAGE SUMMARY")
    print("=" * 80)
    
    print(f"Primary Number Text SID: {primary_sid}")
    print(f"Alternative Number Text SID: {alt_sid}")
    
    print("\nText messages have been sent to Paul Edwards.")
    print("=" * 80)

if __name__ == "__main__":
    print("This script will send a text message to Paul Edwards.")
    print("Make sure you have proper authorization before proceeding.")
    
    proceed = input("Do you want to proceed? (yes/no): ")
    
    if proceed.lower() == "yes":
        send_text_to_paul()
    else:
        print("Operation cancelled.")
