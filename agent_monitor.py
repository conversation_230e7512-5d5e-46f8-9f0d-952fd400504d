"""
Agent Monitor for comprehensive agent monitoring and management
"""

from typing import Dict, Any, Optional
import asyncio
import logging
from datetime import datetime
from core.metrics import get_metrics_collector
from core.ui_monitoring_dashboard import get_dashboard
from core.ui_anomaly_detector import UIAnomalyDetector
from core.visual_interaction_monitor import VisualInteractionMonitor
from utils.logging_setup import get_logger

class AgentMonitor:
    """Enhanced agent monitoring system with UI performance tracking"""
    
    def __init__(self):
        self.logger = get_logger("agent_monitor")
        self.metrics = get_metrics_collector("agent_monitor")
        self.ui_dashboard = get_dashboard()
        self.active_agents = {}
        self._monitoring = False

    async def start(self):
        """Start monitoring agents"""
        self._monitoring = True
        await self.ui_dashboard.start_monitoring()
        asyncio.create_task(self._monitor_loop())
        self.logger.info("Agent monitoring started with UI tracking enabled")

    async def stop(self):
        """Stop monitoring agents"""
        self._monitoring = False
        await self.ui_dashboard.stop_monitoring()
        self.logger.info("Agent monitoring stopped")

    async def register_agent(self, agent_id: str, agent_type: str, capabilities: Dict[str, Any]):
        """Register an agent for monitoring"""
        self.active_agents[agent_id] = {
            "type": agent_type,
            "capabilities": capabilities,
            "status": "active",
            "last_update": datetime.now(),
            "health_checks": [],
            "performance_metrics": {
                "success_rate": 0.0,
                "response_time": 0.0,
                "error_count": 0
            }
        }
        
        # Register with UI dashboard if agent has UI capabilities
        if capabilities.get("ui_enabled", False):
            await self.ui_dashboard.register_agent(agent_id, agent_type)
            self.logger.info(f"Registered UI-enabled agent: {agent_id}")

    async def update_agent_status(self, agent_id: str, status_data: Dict[str, Any]):
        """Update agent status including UI performance data"""
        if agent_id not in self.active_agents:
            self.logger.warning(f"Unknown agent {agent_id} status update")
            return

        agent = self.active_agents[agent_id]
        agent["last_update"] = datetime.now()
        agent["status"] = status_data.get("status", agent["status"])

        # Process UI interaction data if present
        if "ui_interaction" in status_data:
            await self.ui_dashboard.record_interaction(
                agent_id,
                status_data["ui_interaction"]
            )

        # Update performance metrics
        metrics = agent["performance_metrics"]
        if "success" in status_data:
            current_total = len(agent["health_checks"])
            current_success = sum(1 for check in agent["health_checks"] if check.get("success"))
            metrics["success_rate"] = ((current_success + (1 if status_data["success"] else 0)) /
                                     (current_total + 1)) * 100

        if "response_time" in status_data:
            metrics["response_time"] = (metrics["response_time"] * len(agent["health_checks"]) +
                                      status_data["response_time"]) / (len(agent["health_checks"]) + 1)

        if not status_data.get("success", True):
            metrics["error_count"] += 1

        # Add health check record
        agent["health_checks"].append({
            "timestamp": datetime.now(),
            **status_data
        })

        # Trim health check history
        if len(agent["health_checks"]) > 100:
            agent["health_checks"] = agent["health_checks"][-100:]

    async def get_agent_status(self, agent_id: str) -> Optional[Dict[str, Any]]:
        """Get comprehensive agent status including UI performance"""
        if agent_id not in self.active_agents:
            return None

        agent = self.active_agents[agent_id]
        dashboard_data = self.ui_dashboard.get_dashboard_data()
        
        return {
            "agent_info": {
                "type": agent["type"],
                "capabilities": agent["capabilities"],
                "status": agent["status"],
                "last_update": agent["last_update"]
            },
            "performance": agent["performance_metrics"],
            "ui_performance": dashboard_data["agents"].get(agent_id, {
                "status": "UI monitoring not enabled"
            }),
            "recent_health_checks": agent["health_checks"][-5:]  # Last 5 checks
        }

    async def get_system_status(self) -> Dict[str, Any]:
        """Get overall system status with UI performance metrics"""
        dashboard_data = self.ui_dashboard.get_dashboard_data()
        
        return {
            "active_agents": len(self.active_agents),
            "agent_statuses": {
                agent_id: {
                    "type": agent["type"],
                    "status": agent["status"],
                    "success_rate": agent["performance_metrics"]["success_rate"]
                }
                for agent_id, agent in self.active_agents.items()
            },
            "ui_metrics": {
                "platform_stats": dashboard_data["platforms"],
                "anomalies": dashboard_data["anomalies"],
                "visual_performance": dashboard_data["visual_performance"]
            }
        }

    async def _monitor_loop(self):
        """Main monitoring loop"""
        while self._monitoring:
            try:
                await self._check_agent_health()
                await asyncio.sleep(60)  # Check every minute
            except Exception as e:
                self.logger.error(f"Error in monitor loop: {str(e)}")

    async def _check_agent_health(self):
        """Check health of all registered agents"""
        current_time = datetime.now()
        for agent_id, agent in self.active_agents.items():
            time_since_update = (current_time - agent["last_update"]).total_seconds()
            
            if time_since_update > 300:  # 5 minutes
                agent["status"] = "inactive"
                self.logger.warning(f"Agent {agent_id} is inactive")
                self.metrics.record_error(f"agent_inactive_{agent_id}")

# Global monitor instance
_monitor: Optional[AgentMonitor] = None

def get_agent_monitor() -> AgentMonitor:
    """Get or create global agent monitor"""
    global _monitor
    if _monitor is None:
        _monitor = AgentMonitor()
    return _monitor