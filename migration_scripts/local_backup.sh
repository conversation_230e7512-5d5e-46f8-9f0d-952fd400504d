#!/bin/bash

# PaulEdwardsAI Project Migration Script
# Created: May 5, 2025
# This script creates a comprehensive backup of your project for transfer to another device

set -e  # Exit on error

echo "======================================================================"
echo "    PaulEdwardsAI Project Migration Tool - Local Backup"
echo "======================================================================"
echo "This script will create a backup of your project files, configurations,"
echo "credentials (encrypted), and generate instructions for migration."

# Create backup directory with timestamp
BACKUP_DIR="PaulEdwardsAI_Backup_$(date +%Y%m%d_%H%M%S)"
mkdir -p "$BACKUP_DIR"

# Copy all project files (including hidden files)
echo "[1/6] Copying project files..."
rsync -av --exclude "$BACKUP_DIR" --exclude "venv" --exclude "__pycache__" --exclude "*.pyc" \
      --exclude ".git" --exclude "node_modules" --exclude ".DS_Store" . "$BACKUP_DIR/"

# Create inventory of installed packages 
echo "[2/6] Creating package inventory..."
if [ -d "venv" ]; then
  source venv/bin/activate 2>/dev/null || source venv/Scripts/activate 2>/dev/null
  pip freeze > "$BACKUP_DIR/requirements.txt"
  deactivate
else
  pip freeze > "$BACKUP_DIR/requirements.txt"
fi

# Backup Docker configuration
echo "[3/6] Backing up Docker configuration..."
if [ -f "docker-compose.yml" ]; then
  mkdir -p "$BACKUP_DIR/docker_backup"
  cp docker-compose.yml "$BACKUP_DIR/docker_backup/"
  cp Dockerfile "$BACKUP_DIR/docker_backup/" 2>/dev/null || echo "Dockerfile not found"
  docker images --format "{{.Repository}}:{{.Tag}}" | grep "pauledwardsai" > "$BACKUP_DIR/docker_backup/docker_images.txt" 2>/dev/null || echo "No docker images found"
fi

# Backup security configurations and credentials (with warning)
echo "[4/6] Backing up security configurations (ENCRYPTED)..."
if [ -f "fernet_config.txt" ]; then
  mkdir -p "$BACKUP_DIR/secure_configs"
  cp fernet_config.txt "$BACKUP_DIR/secure_configs/"
  
  # Look for potential credential files but warn about security
  echo "WARNING: Credential files will be backed up but should be handled securely"
  find . -type f -name "*credential*" -o -name "*.key" -o -name "*.pem" -o -name "*.env" | 
  while read file; do
    if [ -f "$file" ]; then
      target_dir="$BACKUP_DIR/secure_configs/$(dirname "$file")"
      mkdir -p "$target_dir"
      cp "$file" "$target_dir/"
      echo "Backed up: $file"
    fi
  done
fi

# Collect system information
echo "[5/6] Collecting system information..."
mkdir -p "$BACKUP_DIR/system_info"
echo "System: $(uname -a)" > "$BACKUP_DIR/system_info/system_details.txt"
echo "Python: $(python --version 2>&1)" >> "$BACKUP_DIR/system_info/system_details.txt"
echo "Node: $(node --version 2>&1)" >> "$BACKUP_DIR/system_info/system_details.txt" 2>/dev/null || echo "Node not installed"

# List installed security tools
echo "Installed security tools:" > "$BACKUP_DIR/system_info/security_tools.txt"
if [ -f "install_security_tools.sh" ]; then
  grep "apt-get install\|brew install" install_security_tools.sh >> "$BACKUP_DIR/system_info/security_tools.txt" 2>/dev/null
fi
if [ -f "install_security_tools_macos.sh" ]; then
  grep "brew install" install_security_tools_macos.sh >> "$BACKUP_DIR/system_info/security_tools.txt" 2>/dev/null
fi

# Create detailed migration instructions
echo "[6/6] Creating migration instructions..."
cat > "$BACKUP_DIR/MIGRATION_INSTRUCTIONS.md" << 'EOL'
# PaulEdwardsAI Project Migration Instructions

This document provides comprehensive steps to migrate your AI Agent system to a new device.

## 1. Project Files

All project files have been copied to this backup directory. Transfer the entire directory to your new system.

## 2. Environment Setup

### Prerequisites
- Python 3.x (preferably the same version noted in system_info/system_details.txt)
- Docker (if using containerized deployment)
- Node.js (if required)

### Basic Setup
```bash
# Create and activate virtual environment
python -m venv venv
source venv/bin/activate  # On macOS/Linux
# OR
venv\Scripts\activate     # On Windows

# Install dependencies
pip install -r requirements.txt
```

### Security Tools Installation

The security tools need to be installed separately:

```bash
# On macOS:
bash install_security_tools_macos.sh

# On Linux:
bash install_security_tools.sh
```

## 3. Configuration & Credentials

1. **Environment Variables**: 
   - Transfer any environment variables, particularly API keys.
   - Check for any .env files that need to be moved.

2. **Credentials**:
   - The secure_configs directory contains encrypted credentials.
   - Verify that fernet_config.txt is properly transferred.

3. **API Connections**:
   - Update any API endpoints if needed.
   - Verify that authentication still works.

## 4. Communication Data Transfer

For your communications data, you'll need to use specific methods for each type:

1. **Email Data**:
   - Verify gmail_integration.py configuration
   - Ensure IMAP/SMTP settings are correct for your new device
   - Test with email_integration_test.py

2. **Phone/SMS Data**:
   - Update phone_utils.py with correct device paths
   - Test with open_phone_app.py and open_sms_app.py

3. **Voicemail**:
   - Verify paul_edwards_voicemail.mp3 and other audio files transferred correctly

## 5. Docker Setup (If Used)

If using Docker:
```bash
# Build container
docker-compose build

# Start services
docker-compose up -d
```

## 6. Testing

After migration, test the system functionality:

1. Run `python main.py` to verify core functionality
2. Test each communication channel:
   - Email: `python email_integration_test.py`
   - Phone: `python call_with_vonage.py`
   - SMS: `python open_sms_app.py`
3. Verify security: `python agent_security_integration.py`

## 7. Additional Data Transfer

For data not included in this project but relevant to your system:

1. **Browser Data & History**:
   - Use browser's export/import functionality
   - Chrome: Settings → Advanced → Export bookmarks and settings
   - Firefox: Bookmarks → Show All Bookmarks → Import and Backup

2. **System Preferences**:
   - macOS: Use Migration Assistant
   - Windows: Use Windows Easy Transfer

## 8. Troubleshooting

If you encounter issues:

1. Check logs in the project directory
2. Verify environment variables and paths
3. Ensure all dependencies are installed
4. Compare system_info between source and destination

Need assistance? Refer to the project documentation or contact your system administrator.
EOL

# Create archive
echo "Creating compressed archive..."
tar -czf "${BACKUP_DIR}.tar.gz" "$BACKUP_DIR"

echo ""
echo "======================================================================"
echo "Backup completed successfully!"
echo "======================================================================"
echo "Backup directory: $BACKUP_DIR"
echo "Compressed archive: ${BACKUP_DIR}.tar.gz"
echo ""
echo "NEXT STEPS:"
echo "1. Transfer the ${BACKUP_DIR}.tar.gz file to your new system"
echo "2. Extract the archive: tar -xzf ${BACKUP_DIR}.tar.gz"
echo "3. Follow the instructions in MIGRATION_INSTRUCTIONS.md"
echo ""