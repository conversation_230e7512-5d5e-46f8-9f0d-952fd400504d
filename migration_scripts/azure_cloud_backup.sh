#!/bin/bash

# PaulEdwardsAI Project Azure Cloud Migration Script
# Created: May 5, 2025
# This script creates a comprehensive backup of your project and uploads it to Azure Blob Storage

set -e  # Exit on error

echo "======================================================================"
echo "    PaulEdwardsAI Project Migration Tool - Azure Cloud Backup"
echo "======================================================================"
echo "This script will create a backup of your project files, configurations,"
echo "credentials (encrypted), and upload them to Azure Blob Storage."

# Check for Azure CLI
if ! command -v az &> /dev/null; then
    echo "Error: Azure CLI is not installed. Please install it first."
    echo "Visit: https://docs.microsoft.com/en-us/cli/azure/install-azure-cli"
    exit 1
fi

# Check Azure CLI login status
echo "Checking Azure login status..."
az account show &> /dev/null
if [ $? -ne 0 ]; then
    echo "You need to log in to Azure first."
    az login
fi

# Create backup directory with timestamp
TIMESTAMP=$(date +%Y%m%d_%H%M%S)
BACKUP_DIR="PaulEdwardsAI_Backup_${TIMESTAMP}"
mkdir -p "$BACKUP_DIR"

# Copy all project files (including hidden files)
echo "[1/8] Copying project files..."
rsync -av --exclude "$BACKUP_DIR" --exclude "venv" --exclude "__pycache__" --exclude "*.pyc" \
      --exclude ".git" --exclude "node_modules" --exclude ".DS_Store" . "$BACKUP_DIR/"

# Create inventory of installed packages 
echo "[2/8] Creating package inventory..."
if [ -d "venv" ]; then
  source venv/bin/activate 2>/dev/null || source venv/Scripts/activate 2>/dev/null
  pip freeze > "$BACKUP_DIR/requirements.txt"
  deactivate
else
  pip freeze > "$BACKUP_DIR/requirements.txt"
fi

# Backup Docker configuration
echo "[3/8] Backing up Docker configuration..."
if [ -f "docker-compose.yml" ]; then
  mkdir -p "$BACKUP_DIR/docker_backup"
  cp docker-compose.yml "$BACKUP_DIR/docker_backup/"
  cp Dockerfile "$BACKUP_DIR/docker_backup/" 2>/dev/null || echo "Dockerfile not found"
  docker images --format "{{.Repository}}:{{.Tag}}" | grep "pauledwardsai" > "$BACKUP_DIR/docker_backup/docker_images.txt" 2>/dev/null || echo "No docker images found"
fi

# Backup security configurations and credentials (with encryption)
echo "[4/8] Backing up security configurations (ENCRYPTED)..."
if [ -f "fernet_config.txt" ]; then
  mkdir -p "$BACKUP_DIR/secure_configs"
  cp fernet_config.txt "$BACKUP_DIR/secure_configs/"
  
  # Look for potential credential files and encrypt them
  echo "WARNING: Credential files will be backed up with additional encryption"
  
  # Generate a secure backup key (or use existing one)
  if [ ! -f "migration_backup_key.txt" ]; then
    openssl rand -base64 32 > migration_backup_key.txt
    echo "Created new migration backup key. Keep this file secure!"
  fi
  
  # Create a directory for encrypted credentials
  mkdir -p "$BACKUP_DIR/secure_configs/encrypted_credentials"
  
  # Find and encrypt credential files
  find . -type f -name "*credential*" -o -name "*.key" -o -name "*.pem" -o -name "*.env" | 
  while read file; do
    if [ -f "$file" ]; then
      encrypted_file="$BACKUP_DIR/secure_configs/encrypted_credentials/$(basename "$file").enc"
      openssl enc -aes-256-cbc -salt -in "$file" -out "$encrypted_file" -pass file:migration_backup_key.txt
      echo "Backed up and encrypted: $file"
    fi
  done
  
  # Copy encryption key (will be uploaded separately with restricted permissions)
  cp migration_backup_key.txt "$BACKUP_DIR/secure_configs/migration_backup_key.txt"
fi

# Collect system information
echo "[5/8] Collecting system information..."
mkdir -p "$BACKUP_DIR/system_info"
echo "System: $(uname -a)" > "$BACKUP_DIR/system_info/system_details.txt"
echo "Python: $(python --version 2>&1)" >> "$BACKUP_DIR/system_info/system_details.txt"
echo "Node: $(node --version 2>&1)" >> "$BACKUP_DIR/system_info/system_details.txt" 2>/dev/null || echo "Node not installed"

# List installed security tools
echo "Installed security tools:" > "$BACKUP_DIR/system_info/security_tools.txt"
if [ -f "install_security_tools.sh" ]; then
  grep "apt-get install\|brew install" install_security_tools.sh >> "$BACKUP_DIR/system_info/security_tools.txt" 2>/dev/null
fi
if [ -f "install_security_tools_macos.sh" ]; then
  grep "brew install" install_security_tools_macos.sh >> "$BACKUP_DIR/system_info/security_tools.txt" 2>/dev/null
fi

# Create detailed migration instructions
echo "[6/8] Creating migration instructions..."
cat > "$BACKUP_DIR/AZURE_MIGRATION_INSTRUCTIONS.md" << 'EOL'
# PaulEdwardsAI Project Azure Cloud Migration Instructions

This document provides comprehensive steps to migrate your AI Agent system using Azure Cloud Storage.

## 1. Azure Blob Storage Backup

Your project has been backed up to Azure Blob Storage with the following organization:
- Main project archive: Contains all project files, configurations, and documentation
- Encrypted credentials: Contains sensitive files with additional encryption layer
- Encryption key: Stored in a separate secure container with restricted access

## 2. Retrieving Your Backup

### Option 1: Using Azure Portal
1. Log in to Azure Portal (portal.azure.com)
2. Navigate to the storage account specified in the backup summary
3. Browse to the containers and download the required files

### Option 2: Using Azure CLI
```bash
# Download main project backup
az storage blob download --account-name [STORAGE_ACCOUNT_NAME] --container-name [CONTAINER_NAME] --name [BACKUP_FILENAME] --file [LOCAL_PATH]

# Download encryption key (if needed)
az storage blob download --account-name [STORAGE_ACCOUNT_NAME] --container-name secure-keys --name migration_backup_key.txt --file migration_backup_key.txt
```

## 3. Environment Setup

### Prerequisites
- Python 3.x (preferably the same version noted in system_info/system_details.txt)
- Docker (if using containerized deployment)
- Azure CLI (for cloud interactions)

### Basic Setup
```bash
# Extract the backup
tar -xzf [BACKUP_FILENAME].tar.gz

# Create and activate virtual environment
python -m venv venv
source venv/bin/activate  # On macOS/Linux
# OR
venv\Scripts\activate     # On Windows

# Install dependencies
pip install -r requirements.txt
```

### Security Tools Installation
```bash
# On macOS:
bash install_security_tools_macos.sh

# On Linux:
bash install_security_tools.sh
```

## 4. Decrypting Credentials

If you need to decrypt the credentials:
```bash
# Decrypt a file
openssl enc -aes-256-cbc -d -in [ENCRYPTED_FILE] -out [DECRYPTED_FILE] -pass file:migration_backup_key.txt
```

## 5. Azure Integration

Your project may have Azure integrations. Update the following as needed:
1. Connection strings in configuration files
2. Service Principal credentials if used
3. Storage account access keys or connection strings

## 6. Communication Data Transfer

For your communications data, you'll need to use specific methods for each type:

1. **Email Data**:
   - Verify gmail_integration.py configuration
   - Ensure IMAP/SMTP settings are correct for your new device
   - Test with email_integration_test.py

2. **Phone/SMS Data**:
   - Update phone_utils.py with correct device paths
   - Test with open_phone_app.py and open_sms_app.py

3. **Voicemail**:
   - Verify paul_edwards_voicemail.mp3 and other audio files transferred correctly

## 7. Docker Setup (If Used)

If using Docker:
```bash
# Build container
docker-compose build

# Start services
docker-compose up -d
```

## 8. Testing

After migration, test the system functionality:

1. Run `python main.py` to verify core functionality
2. Test each communication channel:
   - Email: `python email_integration_test.py`
   - Phone: `python call_with_vonage.py`
   - SMS: `python open_sms_app.py`
3. Verify security: `python agent_security_integration.py`

## 9. Azure-Based Additional Data Transfer

For other data types, consider these Azure services:

1. **Large File Storage**:
   - Azure Files for SMB-based file shares
   - Azure Blob Storage for unstructured data

2. **Database Migration**:
   - Azure Database Migration Service
   - Azure Data Factory for ETL processes

3. **Email Migration**:
   - Consider Exchange Online migration tools
   - Use Azure Logic Apps for email workflow migration

## 10. Troubleshooting

If you encounter issues:

1. Check logs in the project directory
2. Verify Azure connection strings and authentication
3. Ensure Azure resource permissions are configured correctly
4. Compare system_info between source and destination

Need assistance? Refer to the project documentation or contact your Azure administrator.
EOL

# Create archive
echo "Creating compressed archive..."
tar -czf "${BACKUP_DIR}.tar.gz" "$BACKUP_DIR"

# Setup Azure Storage for backup
echo "[7/8] Setting up Azure Storage for backup..."
echo "Please provide your Azure storage details:"
read -p "Azure Storage Account Name: " STORAGE_ACCOUNT
read -p "Azure Resource Group: " RESOURCE_GROUP
read -p "Create new container? (yes/no, default: yes): " CREATE_CONTAINER
CREATE_CONTAINER=${CREATE_CONTAINER:-yes}

CONTAINER_NAME="pauledwardsai-backup"
SECURE_CONTAINER_NAME="secure-keys"

if [ "$CREATE_CONTAINER" = "yes" ]; then
  echo "Creating containers in Azure Storage..."
  az storage container create --name "$CONTAINER_NAME" --account-name "$STORAGE_ACCOUNT" --auth-mode login
  az storage container create --name "$SECURE_CONTAINER_NAME" --account-name "$STORAGE_ACCOUNT" --auth-mode login --public-access off
fi

# Upload backup to Azure
echo "[8/8] Uploading backup to Azure Storage..."
echo "Uploading main backup archive..."
az storage blob upload --account-name "$STORAGE_ACCOUNT" --container-name "$CONTAINER_NAME" \
   --name "${BACKUP_DIR}.tar.gz" --file "${BACKUP_DIR}.tar.gz" --auth-mode login

# Upload encryption key with more restricted access
if [ -f "migration_backup_key.txt" ]; then
  echo "Uploading encryption key (with restricted access)..."
  az storage blob upload --account-name "$STORAGE_ACCOUNT" --container-name "$SECURE_CONTAINER_NAME" \
     --name "migration_backup_key.txt" --file "migration_backup_key.txt" --auth-mode login
fi

# Create a summary report
cat > "azure_backup_summary.txt" << EOL
PaulEdwardsAI Azure Backup Summary
================================
Date: $(date)
Local Backup Directory: $BACKUP_DIR
Local Archive: ${BACKUP_DIR}.tar.gz

Azure Storage Details:
- Storage Account: $STORAGE_ACCOUNT
- Resource Group: $RESOURCE_GROUP
- Container: $CONTAINER_NAME
- Blob Name: ${BACKUP_DIR}.tar.gz
- Encryption Key Container: $SECURE_CONTAINER_NAME (restricted access)

To download your backup from Azure:
az storage blob download --account-name $STORAGE_ACCOUNT --container-name $CONTAINER_NAME --name ${BACKUP_DIR}.tar.gz --file ${BACKUP_DIR}.tar.gz --auth-mode login

To download your encryption key from Azure:
az storage blob download --account-name $STORAGE_ACCOUNT --container-name $SECURE_CONTAINER_NAME --name migration_backup_key.txt --file migration_backup_key.txt --auth-mode login
EOL

echo ""
echo "======================================================================"
echo "Backup completed and uploaded to Azure successfully!"
echo "======================================================================"
echo "Backup directory: $BACKUP_DIR"
echo "Local compressed archive: ${BACKUP_DIR}.tar.gz"
echo "Azure Storage Account: $STORAGE_ACCOUNT"
echo "Azure Container: $CONTAINER_NAME"
echo ""
echo "A summary has been saved to azure_backup_summary.txt"
echo ""
echo "NEXT STEPS:"
echo "1. The backup is available in Azure Blob Storage"
echo "2. On your new device, install Azure CLI and download the backup"
echo "3. Follow the instructions in AZURE_MIGRATION_INSTRUCTIONS.md"
echo ""