"""
Main application entry point for the agent system
Now includes a FastAPI backend for API and WebSocket communication.
Enhanced with unified interface and intelligent request routing
"""

import asyncio
import sys
import argparse
import logging
from fastapi import FastAPI, WebSocket, WebSocketDisconnect
from pathlib import Path
from typing import Optional
from core.coordinator import get_coordinator
from core.config_manager import get_config_manager
from core.security_manager import get_security_manager, SecurityLevel
from core.plugin_manager import get_plugin_manager
from core.package_monitor import get_package_monitor
from agent_monitor import get_agent_monitor
from utils.logging_setup import get_logger
from unified_command_center import UnifiedCommandCenter
from intelligent_request_router import IntelligentRequestRouter
# Placeholder for LangGraph and other blueprint components
# from langchain.graphs import StateGraph

app = FastAPI()


class AgentSystem:
    """Main agent system application"""

    def __init__(self):
        self.logger = get_logger("agent_system")
        self.config_path: Optional[Path] = None
        self.plugin_path: Optional[Path] = None
        self.coordinator = get_coordinator()
        self.package_monitor = get_package_monitor()
        # self.orchestrator = self._initialize_orchestrator() # Placeholder for LangGraph

    async def _handle_package_update(self, status: dict):
        """Handle package update notifications"""
        self.logger.info(f"Package update detected: {status}")
        # Notify relevant components about package availability
        await self.coordinator.broadcast_event("package_update", status)

    # def _initialize_orchestrator(self):
    #     # Placeholder: Initialize LangGraph orchestrator here (Blueprint Section 4)
    #     # agent_state = ...
    #     # graph = StateGraph(agent_state)
    #     # ... add nodes and edges ...
    #     # return graph.compile()
    #     return None

    async def start(
        self,
        config_path: str,
        plugin_path: Optional[str] = None
    ):
        """Start the agent system"""
        self.logger.info("Initializing agent system components...")
            # Load configuration
        self.config_path = Path(config_path)
        if not self.config_path.exists():
            self.logger.error(f"Config file not found: {config_path}")
            raise FileNotFoundError(f"Config file not found: {config_path}")

            config_manager = get_config_manager()
            config = config_manager.load_config(config_path)

            # Start package monitoring
            packages_to_monitor = [
                {
                    "name": "ui-tars",
                    "github_repo": "bytedance/UI-TARS",
                    "huggingface_model": "bytedance/ui-tars-1.5-7b"
                },
                {
                    "name": "ui-tars-desktop",
                    "github_repo": "bytedance/UI-TARS-desktop"
                }
            ]

            # Register callback for package updates
            for package in packages_to_monitor:
                self.package_monitor.register_callback(
                    package["name"],
                    self._handle_package_update
                )

            # Start monitoring in background
            asyncio.create_task(
                self.package_monitor.start_monitoring(packages_to_monitor)
            )

            # Continue with normal startup
            # Load plugins if path provided
            if plugin_path:
                self.plugin_path = Path(plugin_path)
                if not self.plugin_path.exists():
                    raise FileNotFoundError(
                        f"Plugin path not found: {plugin_path}"
                    )

                plugin_manager = get_plugin_manager()
                plugin_manager.register_plugin_path(plugin_path)
                await plugin_manager.discover_plugins()

                # Load discovered plugins
                for plugin_name in plugin_manager._plugins:
                    try:
                        await plugin_manager.load_plugin(
                            plugin_name,
                            config.get("plugins", {}).get(plugin_name)
                        )
                        await plugin_manager.enable_plugin(plugin_name)
                        self.logger.info(f"Loaded plugin: {plugin_name}")
                    except Exception as e:
                        self.logger.error(
                            f"Error loading plugin {plugin_name}: {str(e)}"
                        )

            # Start agent monitor
            monitor = get_agent_monitor()
            await monitor.start()

            # Initialize security
            security = get_security_manager()
            # Create admin user if not exists
            try:
                admin_config = config.security.admin
                await security.register_user(
                    admin_config.username,
                    admin_config.password,
                    ["admin"],
                    SecurityLevel.SECRET
                )
            except Exception as e:
                self.logger.error(f"Error creating admin user: {str(e)}")

            # Start system coordinator
            await self.coordinator.start()

            self.logger.info("Agent system started successfully")

    async def process_chat_request(self, message: str) -> str:
        """
        Process a chat request using the orchestrator.
        Placeholder for LangGraph integration (Blueprint Section 1.2, 4.3).
        """
        self.logger.info(f"Processing chat request: {message}")
        # Example: await self.orchestrator.ainvoke({"input": message, "chat_history": []})
        # For now, echo back or use a simple mock response
        if "paul edwards" in message.lower():
            return await self.mock_paul_edwards_handler(message)
        return f"IRIS received: {message}"

    async def mock_paul_edwards_handler(self, message: str) -> str:
        # Simulates logic from irisInterface.handlePaulEdwardsRequest
        self.logger.info(f"Handling Paul Edwards request: {message}")
        # In a real scenario, this would trigger a LangGraph tool/agent
        return '📞 Initiating call to Paul Edwards (772-208-9646) via backend. I\'ll handle the communication.'

    async def stop(self):
        """Stop the agent system"""
        self.logger.info("Stopping agent system...")
        await self.package_monitor.stop_monitoring()
        await self.coordinator.shutdown()
        self.logger.info("Agent system stopped")


agent_system = AgentSystem()

@app.on_event("startup")
async def startup_event():
    """Handle FastAPI startup"""
    # Setup logging (can be done once)
    # setup_logging(debug=True) # Or get from config/env
    # Load config path from env or default
    # For now, assuming it might be passed differently or hardcoded for dev
    # This part needs refinement based on how config is supplied to a FastAPI app
    # For example, use environment variables for config paths.
    # await agent_system.start(config_path="config/config.yaml") # Example
    logger = get_logger("fastapi_startup")
    logger.info("FastAPI application starting up...")
    # Minimal start for now, full agent_system.start might need to be adapted
    # or parts of it called. For instance, plugin loading and coordinator start.
    # Consider what needs to run when the *server* starts vs. when a *request* comes in.
    # For now, we assume AgentSystem is mostly initialized and ready.
    # A more robust approach would be to configure AgentSystem within FastAPI's lifespan context.

@app.post("/api/chat/request")
async def handle_chat_request(request: dict):
    """Handle incoming chat requests"""
    message = request.get("message")
    if not message:
        return {"error": "No message provided"}, 400
    response = await agent_system.process_chat_request(message)
    return {"response": response}

@app.websocket("/ws/gemini_live")
async def websocket_gemini_relay(websocket: WebSocket):
    """WebSocket relay for Gemini Live, as per Blueprint Section 3.2"""
    await websocket.accept()
    logger = get_logger("websocket_gemini")
    logger.info("Gemini Live WebSocket client connected.")
    # Conceptual relay logic (needs actual Gemini SDK integration)
    # This is a simplified version of the blueprint's example
    try:
        while True:
            data = await websocket.receive_text() # Or receive_bytes for audio/video
            logger.debug(f"Received from client: {data}")
            # Forward to Gemini Live API
            # gemini_response = await call_gemini_live_api(data)
            mock_gemini_response = f"Gemini mock response to: {data}"
            await websocket.send_text(mock_gemini_response)
            logger.debug(f"Sent to client: {mock_gemini_response}")
    except WebSocketDisconnect:
        logger.info("Gemini Live WebSocket client disconnected.")
    except Exception as e:
        logger.error(f"Gemini Live WebSocket error: {e}")
    finally:
        # Clean up resources if any
        pass

async def run_cli():
    """Original CLI entry point, can be kept for other purposes if needed"""
    parser = argparse.ArgumentParser(description="Agent System CLI")
    parser.add_argument(
        "-c", "--config", required=True, help="Path to configuration file"
    )
    parser.add_argument("-p", "--plugins", help="Path to plugins directory")
    parser.add_argument(
        "-d", "--debug", action="store_true", help="Enable debug logging"
    )
    args = parser.parse_args()

    setup_logging(args.debug)
    cli_logger = get_logger("agent_system_cli")

    # Create and start system for CLI specific tasks if any
    # This might be different from the FastAPI-managed agent_system instance
    system_cli = AgentSystem()
    try:
        await system_cli.start(args.config, args.plugins)
            # Wait for shutdown
        await system_cli.coordinator._shutdown_event.wait() # Accessing protected member for CLI
    except KeyboardInterrupt:
        cli_logger.info("\nCLI Shutdown requested...")
    except Exception as e:
        cli_logger.error(f"CLI Error: {str(e)}")
        # return 1 # Cannot return from async
    finally:
        await system_cli.stop()
    # return 0

def setup_logging(debug: bool = False):
    """Configure logging"""
    level = logging.DEBUG if debug else logging.INFO
    logging.basicConfig(
        level=level,
        format='%(asctime)s [%(levelname)s] %(name)s: %(message)s'
    )

if __name__ == "__main__":
    # This main block is now for running the FastAPI app with uvicorn programmatically,
    # or for running the CLI. The Dockerfile will use `uvicorn main:app`.
    # For local development, you might run `python main.py --cli` for the CLI
    # or `uvicorn main:app --reload` for the server.

    # Default logging setup for when uvicorn runs this script directly
    # or when __main__ is executed without uvicorn for CLI.
    # Args parsing for debug flag can be added here if needed for uvicorn startup.
    setup_logging(debug=True) # Example: enable debug logging

    # If you want to keep the CLI operational:
    # if "--cli" in sys.argv:
    #     asyncio.run(run_cli())
    # else:
    #     # This part is typically handled by `uvicorn main:app` command
    #     # import uvicorn
    #     # uvicorn.run(app, host="0.0.0.0", port=8000)
    #     pass # Uvicorn will import `app` and run it.

    try:
        if sys.platform == "win32":
            # Set up proper asyncio event loop on Windows
            asyncio.set_event_loop_policy(
                asyncio.WindowsSelectorEventLoopPolicy()
            )
            
        # The asyncio.run(main()) call is removed as uvicorn handles the event loop for the FastAPI app.
        # If you need to run the CLI:
        if len(sys.argv) > 1 and sys.argv[1] == 'cli': # Example: python main.py cli -c config.yaml
            # Reconstruct CLI args parsing if run_cli is to be used
            # For simplicity, this example assumes uvicorn is the primary run method.
            get_logger("main").info("CLI mode invoked but not fully configured in this example. Run with uvicorn.")
        else:
            get_logger("main").info("To run the FastAPI server, use: uvicorn main:app --host 0.0.0.0 --port 8080")

    except KeyboardInterrupt:
        get_logger("main").info("\nApplication shutdown.")
        sys.exit(0)
    except Exception as e:
        get_logger("main").error(f"Fatal error: {str(e)}")
        sys.exit(1)