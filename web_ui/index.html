<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Web Automation Dashboard</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 0; padding: 20px; background: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; }
        .header { background: #2c3e50; color: white; padding: 20px; border-radius: 8px; margin-bottom: 20px; }
        .card { background: white; padding: 20px; border-radius: 8px; margin-bottom: 20px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .btn { background: #3498db; color: white; padding: 10px 20px; border: none; border-radius: 4px; cursor: pointer; }
        .btn:hover { background: #2980b9; }
        .status { padding: 4px 8px; border-radius: 4px; font-size: 12px; }
        .status.running { background: #f39c12; color: white; }
        .status.completed { background: #27ae60; color: white; }
        .status.failed { background: #e74c3c; color: white; }
        .status.pending { background: #95a5a6; color: white; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🌐 Web Automation Dashboard</h1>
            <p>Control and monitor your web automation tasks</p>
        </div>
        
        <div class="card">
            <h2>System Status</h2>
            <div id="status">Loading...</div>
        </div>
        
        <div class="card">
            <h2>Automation Tasks</h2>
            <button class="btn" onclick="createSampleTask()">Create Sample Task</button>
            <div id="tasks" style="margin-top: 20px;">Loading...</div>
        </div>
    </div>
    
    <script>
        // WebSocket connection for real-time updates
        const ws = new WebSocket(`ws://${window.location.host}/ws`);
        
        ws.onopen = function() {
            console.log('WebSocket connected');
            ws.send(JSON.stringify({type: 'subscribe'}));
        };
        
        ws.onmessage = function(event) {
            const data = JSON.parse(event.data);
            console.log('WebSocket message:', data);
            
            if (data.type === 'task_created' || data.type === 'task_updated') {
                loadTasks();
            }
        };
        
        // Load system status
        async function loadStatus() {
            try {
                const response = await fetch('/api/status');
                const data = await response.json();
                document.getElementById('status').innerHTML = `
                    <p><strong>Status:</strong> ${data.status}</p>
                    <p><strong>Total Tasks:</strong> ${data.tasks.total}</p>
                    <p><strong>Running:</strong> ${data.tasks.running}</p>
                    <p><strong>Completed:</strong> ${data.tasks.completed}</p>
                    <p><strong>Failed:</strong> ${data.tasks.failed}</p>
                `;
            } catch (error) {
                console.error('Error loading status:', error);
            }
        }
        
        // Load tasks
        async function loadTasks() {
            try {
                const response = await fetch('/api/tasks');
                const data = await response.json();
                
                const tasksHtml = data.tasks.map(task => `
                    <div style="border: 1px solid #ddd; padding: 10px; margin: 10px 0; border-radius: 4px;">
                        <h4>${task.name} <span class="status ${task.status}">${task.status}</span></h4>
                        <p><strong>URL:</strong> ${task.url}</p>
                        <p><strong>Actions:</strong> ${task.actions.length}</p>
                        <p><strong>Created:</strong> ${new Date(task.created_at).toLocaleString()}</p>
                        ${task.status === 'pending' ? `<button class="btn" onclick="startTask('${task.id}')">Start</button>` : ''}
                        <button class="btn" style="background: #e74c3c;" onclick="deleteTask('${task.id}')">Delete</button>
                    </div>
                `).join('');
                
                document.getElementById('tasks').innerHTML = tasksHtml || '<p>No tasks found</p>';
            } catch (error) {
                console.error('Error loading tasks:', error);
            }
        }
        
        // Create sample task
        async function createSampleTask() {
            try {
                const response = await fetch('/api/tasks', {
                    method: 'POST',
                    headers: {'Content-Type': 'application/json'},
                    body: JSON.stringify({
                        name: 'Sample Web Automation',
                        url: 'https://example.com',
                        actions: [
                            {type: 'navigate', url: 'https://example.com'},
                            {type: 'screenshot'},
                            {type: 'extract_text'}
                        ]
                    })
                });
                
                if (response.ok) {
                    loadTasks();
                }
            } catch (error) {
                console.error('Error creating task:', error);
            }
        }
        
        // Start task
        async function startTask(taskId) {
            try {
                const response = await fetch(`/api/tasks/${taskId}/start`, {method: 'POST'});
                if (response.ok) {
                    loadTasks();
                }
            } catch (error) {
                console.error('Error starting task:', error);
            }
        }
        
        // Delete task
        async function deleteTask(taskId) {
            if (confirm('Are you sure you want to delete this task?')) {
                try {
                    const response = await fetch(`/api/tasks/${taskId}`, {method: 'DELETE'});
                    if (response.ok) {
                        loadTasks();
                    }
                } catch (error) {
                    console.error('Error deleting task:', error);
                }
            }
        }
        
        // Initial load
        loadStatus();
        loadTasks();
        
        // Refresh every 5 seconds
        setInterval(() => {
            loadStatus();
        }, 5000);
    </script>
</body>
</html>