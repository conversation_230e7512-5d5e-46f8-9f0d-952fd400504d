#!/usr/bin/env python3
"""
Setup script for Anthropic Claude API credentials
"""

import os
import logging
from secure_credentials import SecureCredentialsManager

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def setup_anthropic_credentials():
    """Set up Anthropic Claude API credentials"""
    print("Anthropic Claude API Key Setup")
    print("=============================")
    print("This script will securely store your Anthropic Claude API key.")
    print("Your API key will be encrypted and stored locally.")
    print()
    
    # Get API key from user or environment variable
    api_key = os.environ.get("ANTHROPIC_API_KEY")
    if not api_key:
        api_key = input("Enter your Anthropic Claude API key: ")
        
    if not api_key:
        logger.error("No API key provided. Exiting.")
        return False
        
    try:
        # Initialize credential manager
        credential_manager = SecureCredentialsManager()
        
        # Add method to store Anthropic credentials
        if not hasattr(credential_manager, 'add_anthropic_credentials'):
            # Update the SecureCredentialsManager class with the new method
            SecureCredentialsManager.add_anthropic_credentials = lambda self, key: self.add_api_credentials("anthropic", {"api_key": key})
            SecureCredentialsManager.get_anthropic_credentials = lambda self: self.get_api_credentials("anthropic")
        
        # Store API key
        credential_manager.add_anthropic_credentials(api_key)
        
        print()
        print("✅ Anthropic Claude API key has been securely stored.")
        print("You can now use Claude 3.7 Sonnet in your agent system.")
        return True
        
    except Exception as e:
        logger.error(f"Error storing API key: {e}")
        return False

if __name__ == "__main__":
    setup_anthropic_credentials()
