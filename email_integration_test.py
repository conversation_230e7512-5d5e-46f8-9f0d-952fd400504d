#!/usr/bin/env python3
"""
Email Integration Test Script

This script demonstrates how to use the EmailCommunicationAgent to manage multiple
insurance-related email accounts, including:
- <EMAIL> (Main insurance email)
- <EMAIL> (Contracting information and requests)
- <EMAIL> (Permanent broker email)
"""

import os
import sys
import logging
from datetime import datetime, timedelta
from agent_system import AgentSystem

# Configure logging
logging.basicConfig(level=logging.INFO,
                    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


def test_email_integration():
    """Test the integration of email accounts with the agent system"""
    logger.info("Initializing agent system with email capabilities...")
    
    # Initialize system
    system = AgentSystem()
    
    # Get email agent from system
    email_agent = system.agents.get('email')
    gmail_manager = system.agents.get('gmail_manager')
    
    if not email_agent:
        logger.error("Email agent not found in agent system")
        return False
        
    logger.info("Email agent successfully initialized")
    
    # Test email accounts initialization
    logger.info("Testing email accounts are properly configured...")
    
    for email in email_agent.email_accounts:
        logger.info(f"Account: {email} - {email_agent.email_accounts[email]['description']}")
    
    # Test sending an email (simulation only)
    test_insurance_request = {
        "type": "quote_request",
        "sender_email": "<EMAIL>",
        "recipient_email": "<EMAIL>",
        "recipient_name": "John Doe",
        "insurance_type": "Medicare Advantage",
        "insurance_details": "This plan includes dental and vision coverage with a premium of $0 per month.",
        "agent_name": "Sandra",
        "agent_phone": "(*************"
    }
    
    logger.info("Testing insurance email request handling...")
    success = email_agent.handle_insurance_request(test_insurance_request)
    logger.info(f"Email request handled: {'Successfully' if success else 'Failed'}")
    
    # Test reading emails (if credentials are available)
    try:
        logger.info("Testing email reading functionality...")
        main_email = "<EMAIL>"
        
        # Read recent emails
        recent_emails = email_agent.read_recent_emails(main_email, limit=5)
        logger.info(f"Retrieved {len(recent_emails)} recent emails")
        
        # Display recent emails (summary)
        if recent_emails:
            logger.info("Recent emails summary:")
            for i, email in enumerate(recent_emails, 1):
                logger.info(f"{i}. From: {email.get('from', 'Unknown')}")
                logger.info(f"   Subject: {email.get('subject', 'No Subject')}")
                logger.info(f"   Date: {email.get('date', 'Unknown')}")
                logger.info(f"   Snippet: {email.get('snippet', '')[:50]}...")
                logger.info("---")
    except Exception as e:
        logger.error(f"Error testing email functionality: {e}")
    
    # Example: Schedule an email for future sending
    tomorrow = datetime.now() + timedelta(days=1)
    scheduled_email = {
        "sender": "<EMAIL>",
        "recipient": "<EMAIL>",
        "subject": "Follow-up on Insurance Quote",
        "content": "Dear Client,\n\nThank you for your interest in our insurance products. I'd like to follow up on the quote we discussed.\n\nBest regards,\nSandra",
        "send_time": tomorrow
    }
    
    logger.info(f"Testing email scheduling for: {tomorrow.strftime('%Y-%m-%d %H:%M:%S')}")
    email_id = email_agent.schedule_email(
        sender_email=scheduled_email["sender"],
        recipient=scheduled_email["recipient"],
        subject=scheduled_email["subject"],
        content=scheduled_email["content"],
        send_time=scheduled_email["send_time"]
    )
    logger.info(f"Email scheduled with ID: {email_id}")
    
    logger.info("Email integration test completed")
    return True


if __name__ == "__main__":
    test_email_integration()