import logging
from typing import Dict, List, Any, Optional
from datetime import datetime, timedelta
import json
import asyncio
from concurrent.futures import ThreadPoolExecutor

from rag_agent_system import (
    InsuranceMainAgent, ContentMainAgent, EmailMainAgent, SocialMediaMainAgent
)
from agent_learning_modules import SpecializedLearners
from knowledge_management import KnowledgeManager, DomainKnowledgeBase
from social_media_manager import SocialMediaManager
from mcp_coordinator import coordinator as mcp_coordinator

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class AgentCoordinator:
    """Coordinates all agents, their interactions, and learning processes"""
    
    def __init__(self):
        self.knowledge_manager = KnowledgeManager()
        self.main_agents = {}
        self.learning_modules = {}
        self.knowledge_bases = {}
        self.interaction_history = []
        self.task_queue = asyncio.Queue()
        self.executor = ThreadPoolExecutor(max_workers=10)
        
        # MCP initialized flag
        self.mcp_initialized = False
        
        self.initialize_system()

    async def initialize_system(self):
        """Initialize all system components"""
        # Initialize main agents
        self.main_agents = {
            'insurance': {
                'agent': InsuranceMainAgent(),
                'active_tasks': set(),
                'performance_metrics': {},
            },
            'content': {
                'agent': ContentMainAgent(),
                'active_tasks': set(),
                'performance_metrics': {},
            },
            'email': {
                'agent': EmailMainAgent(),
                'active_tasks': set(),
                'performance_metrics': {},
            },
            'social_media': {
                'agent': SocialMediaMainAgent(),
                'active_tasks': set(),
                'performance_metrics': {},
            },
            'mcp': {
                'agent': None,  # MCP doesn't need a specific agent
                'active_tasks': set(),
                'performance_metrics': {},
            }
        }

        # Initialize learning modules
        self.learning_modules = {
            'insurance': {
                'quote': SpecializedLearners.InsuranceQuoteLearner(),
                'policy': SpecializedLearners.InsuranceQuoteLearner(),
                'claims': SpecializedLearners.InsuranceQuoteLearner()
            },
            'content': {
                'writer': SpecializedLearners.ContentCreationLearner(),
                'editor': SpecializedLearners.ContentCreationLearner(),
                'media': SpecializedLearners.ContentCreationLearner()
            },
            'email': {
                'classifier': SpecializedLearners.EmailProcessingLearner(),
                'responder': SpecializedLearners.EmailProcessingLearner(),
                'router': SpecializedLearners.EmailProcessingLearner()
            },
            'social_media': {
                'content': SpecializedLearners.ContentCreationLearner(),
                'analytics': SpecializedLearners.SocialMediaAnalyticsLearner(),
                'scheduler': SpecializedLearners.SocialMediaSchedulingLearner()
            },
            'mcp': {}  # MCP doesn't need learning modules yet
        }

        # Initialize knowledge bases
        self.knowledge_bases = {
            'insurance': DomainKnowledgeBase("insurance", self.knowledge_manager),
            'content': DomainKnowledgeBase("content", self.knowledge_manager),
            'email': DomainKnowledgeBase("email", self.knowledge_manager),
            'social_media': DomainKnowledgeBase("social_media", self.knowledge_manager),
            'mcp': DomainKnowledgeBase("mcp", self.knowledge_manager)
        }
        
        # Initialize MCP coordinator
        await self.initialize_mcp()

    async def initialize_mcp(self):
        """Initialize MCP coordinator if not already initialized"""
        if self.mcp_initialized:
            return
            
        try:
            logger.info("Initializing MCP coordinator...")
            success = await mcp_coordinator.initialize()
            
            if success:
                logger.info("MCP coordinator initialized successfully")
                self.mcp_initialized = True
            else:
                logger.warning("MCP coordinator initialization failed")
                
        except Exception as e:
            logger.error(f"Error initializing MCP coordinator: {str(e)}")

    async def execute_action(self, agent_role, action, data):
        """Execute a specific action through an agent"""
        domain = self._map_role_to_domain(agent_role)
        
        if domain not in self.main_agents:
            raise ValueError(f"Unknown agent domain: {domain}")
        
        # Handle MCP domain differently
        if domain == 'mcp':
            return await self._handle_mcp_action(action, data)
            
        agent = self.main_agents[domain]['agent']
        
        # Prepare task data
        task = {
            "type": f"{domain}_{action}",
            "action": action,
            "data": data,
            "timestamp": datetime.now().isoformat()
        }
        
        # Add domain-specific handling
        if domain == 'social_media':
            # Special handling for social media tasks
            if action == 'format_content':
                return await self._format_social_media_content(task)
            elif action == 'post_content':
                return await self._post_social_media_content(task)
            elif action == 'create_schedule':
                return await self._create_social_media_schedule(task)
            elif action == 'schedule_content':
                return await self._schedule_social_media_content(task)
        
        # Get context
        knowledge = await self._query_relevant_knowledge(domain, task)
        
        # Process with context
        enhanced_task = {**task, "context": knowledge}
        result = await self.executor.submit(agent.process_task, enhanced_task)
        
        # Learn from interaction
        await self._learn_from_interaction(domain, task, result)
        
        return result

    async def _handle_mcp_action(self, action, data):
        """Handle MCP-specific actions"""
        try:
            # Ensure MCP is initialized
            if not self.mcp_initialized:
                await self.initialize_mcp()
                
            # Different MCP action types
            if action == "process_with_mcp":
                server_id = data.get("server_id")
                workflow_type = data.get("workflow_type")
                task_data = data.get("data", {})
                
                # Log MCP task execution
                logger.info(f"Executing MCP task: {workflow_type} with server: {server_id}")
                
                # Use MCP coordinator to process the task
                result = await mcp_coordinator.process_task(workflow_type, {
                    "server_id": server_id,
                    **task_data
                })
                
                return result
                
            elif action == "send_email":
                # Send email through MCP
                to = data.get("to")
                subject = data.get("subject", "")
                body = data.get("body", "")
                cc = data.get("cc")
                attachments = data.get("attachments")
                
                result = await mcp_coordinator.process_task("send_email", {
                    "to": to,
                    "subject": subject,
                    "body": body,
                    "cc": cc,
                    "attachments": attachments
                })
                
                return result
                
            elif action == "generate_audio":
                # Generate audio through MCP
                text = data.get("text", "")
                voice = data.get("voice")
                
                result = await mcp_coordinator.process_task("generate_audio", {
                    "text": text,
                    "voice": voice
                })
                
                return result
                
            elif action == "execute_code":
                # Execute code through MCP
                code = data.get("code", "")
                language = data.get("language", "python")
                
                result = await mcp_coordinator.process_task("execute_code", {
                    "code": code,
                    "language": language
                })
                
                return result
                
            else:
                # For any other action, pass it through to the MCP coordinator
                return await mcp_coordinator.process_task(action, data)
                
        except Exception as e:
            error_msg = f"Error handling MCP action {action}: {str(e)}"
            logger.error(error_msg)
            return {"success": False, "error": error_msg}

    async def _format_social_media_content(self, task):
        """Format content for social media platforms"""
        content = task['data']['content']
        platforms = task['data']['platforms']
        
        formatted = {}
        
        for platform in platforms:
            if platform == 'instagram':
                formatted[platform] = self._format_for_instagram(content)
            elif platform == 'tiktok':
                formatted[platform] = self._format_for_tiktok(content)
            elif platform == 'wix':
                formatted[platform] = self._format_for_wix(content)
        
        return {
            "status": "success",
            "formatted_content": formatted
        }
    
    async def _post_social_media_content(self, task):
        """Post content to social media platforms"""
        content = task['data']['content']
        platforms = task['data']['platforms']
        
        post_ids = {}
        
        for platform in platforms:
            if platform in content:
                # Post to respective platform
                platform_content = content[platform]
                post_id = f"{platform}_{datetime.now().strftime('%Y%m%d%H%M%S')}"
                post_ids[platform] = post_id
                logger.info(f"Posted to {platform}: {post_id}")
        
        return {
            "status": "success",
            "post_ids": post_ids
        }
    
    async def _create_social_media_schedule(self, task):
        """Create a posting schedule for social media content"""
        content = task['data']['content']
        start_date = task['data'].get('start_date', datetime.now().isoformat())
        frequency = task['data'].get('frequency', 'weekly')
        
        schedule = {}
        
        # Create scheduling timestamps
        platforms = content.keys()
        for platform in platforms:
            if frequency == 'daily':
                timestamps = [
                    (datetime.fromisoformat(start_date) + 
                     timedelta(days=i)).isoformat()
                    for i in range(len(content[platform]))
                ]
            elif frequency == 'weekly':
                timestamps = [
                    (datetime.fromisoformat(start_date) + 
                     timedelta(weeks=i)).isoformat()
                    for i in range(len(content[platform]))
                ]
            else:
                timestamps = [
                    (datetime.fromisoformat(start_date) + 
                     timedelta(days=i*3)).isoformat()
                    for i in range(len(content[platform]))
                ]
                
            schedule[platform] = {
                "content": content[platform],
                "timestamps": timestamps
            }
            
        return {
            "status": "success",
            "schedule": schedule,
            "schedule_ids": [f"schedule_{platform}" for platform in platforms]
        }
    
    async def _schedule_social_media_content(self, task):
        """Schedule social media content for posting"""
        content = task['data']['content']
        schedule = task['data']['schedule']
        
        scheduled_ids = {}
        
        for platform, schedule_data in schedule.items():
            if platform in content:
                # Schedule content for the platform
                scheduled_ids[platform] = [
                    f"{platform}_scheduled_{i}"
                    for i in range(len(schedule_data['timestamps']))
                ]
                logger.info(f"Scheduled {len(scheduled_ids[platform])} posts for {platform}")
        
        return {
            "status": "success",
            "scheduled_ids": scheduled_ids
        }
    
    def _format_for_instagram(self, content):
        """Format content for Instagram"""
        if isinstance(content, dict) and 'text' in content:
            # Extract hashtags
            hashtags = " ".join([f"#{tag.strip()}" for tag in content.get('tags', [])])
            
            # Format caption
            caption = content['text']
            if len(caption) > 2200:
                caption = caption[:2197] + "..."
                
            # Add hashtags
            caption = f"{caption}\n\n{hashtags}"
            
            return {
                "caption": caption,
                "images": content.get('images', []),
                "carousel": content.get('carousel', False)
            }
        return content
    
    def _format_for_tiktok(self, content):
        """Format content for TikTok"""
        if isinstance(content, dict) and 'text' in content:
            # Extract hashtags
            hashtags = " ".join([f"#{tag.strip()}" for tag in content.get('tags', [])])
            
            # Format caption
            caption = content['text']
            if len(caption) > 150:
                caption = caption[:147] + "..."
                
            # Add hashtags
            caption = f"{caption}\n{hashtags}"
            
            return {
                "caption": caption,
                "video": content.get('video', None),
                "sound": content.get('sound', None)
            }
        return content
    
    def _format_for_wix(self, content):
        """Format content for Wix website"""
        if isinstance(content, dict) and 'text' in content:
            # Format full article
            article = content['text']
            
            # Process metadata
            meta = {
                "title": content.get('title', ''),
                "description": content.get('description', ''),
                "keywords": content.get('tags', []),
                "featured_image": content.get('featured_image', '')
            }
            
            return {
                "article": article,
                "metadata": meta,
                "images": content.get('images', [])
            }
        return content
    
    def _map_role_to_domain(self, role):
        """Map agent role enum to domain string"""
        # Extract the value from enum if needed
        if hasattr(role, 'value'):
            role_value = role.value
        else:
            role_value = str(role)
            
        # Convert to lowercase for consistency
        role_value = role_value.lower()
        
        # Map roles to domains
        role_domain_map = {
            'insurance': 'insurance',
            'content': 'content',
            'trading': 'trading',
            'learning': 'learning',
            'communication': 'email',
            'social_media': 'social_media',
            'mcp': 'mcp'  # Add MCP role
        }
        
        return role_domain_map.get(role_value, role_value)

    async def process_task(self, task: Dict) -> Dict:
        """Process a task using appropriate agents"""
        task_id = task.get('id', str(datetime.now().timestamp()))
        domain = self._determine_domain(task)
        
        logger.info(f"Processing task {task_id} for domain {domain}")
        
        try:
            # Add task to queue
            await self.task_queue.put((task_id, domain, task))
            
            # Special handling for MCP tasks
            if domain == 'mcp':
                action = task.get('action', 'process')
                data = task.get('data', {})
                return await self._handle_mcp_action(action, data)
            
            # Get main agent and sub-agents
            main_agent = self.main_agents[domain]['agent']
            
            # Query relevant knowledge
            knowledge = await self._query_relevant_knowledge(domain, task)
            
            # Process task with context
            result = await self._process_with_context(main_agent, task, knowledge)
            
            # Learn from interaction
            await self._learn_from_interaction(domain, task, result)
            
            return result
            
        except Exception as e:
            logger.error(f"Error processing task {task_id}: {str(e)}")
            return {"status": "error", "message": str(e)}

    async def _process_with_context(self, agent: Any, task: Dict, knowledge: List[Dict]) -> Dict:
        """Process task with relevant context"""
        # Enhance task with knowledge context
        enhanced_task = {
            **task,
            "context": knowledge,
            "timestamp": datetime.now().isoformat()
        }
        
        # Process through main agent
        result = await self.executor.submit(agent.process_task, enhanced_task)
        
        return result

    async def _query_relevant_knowledge(self, domain: str, task: Dict) -> List[Dict]:
        """Query relevant knowledge for task"""
        kb = self.knowledge_bases[domain]
        query = self._construct_knowledge_query(task)
        
        return await self.executor.submit(kb.query_specialized, query, task.get('specialization', 'general'))

    async def _learn_from_interaction(self, domain: str, task: Dict, result: Dict):
        """Learn from task interaction"""
        # Record interaction
        interaction = {
            'timestamp': datetime.now().isoformat(),
            'domain': domain,
            'task': task,
            'result': result
        }
        self.interaction_history.append(interaction)
        
        # Update knowledge base
        await self.executor.submit(
            self.knowledge_manager.learn_from_interaction,
            interaction
        )
        
        # Update domain-specific learners
        if domain in self.learning_modules:
            learners = self.learning_modules[domain]
            for learner_type, learner in learners.items():
                if learner_type in task.get('sub_types', []):
                    await self.executor.submit(learner.learn_from_interaction, interaction)

    def _determine_domain(self, task: Dict) -> str:
        """Determine which domain a task belongs to"""
        task_type = task.get('type', '').lower()
        
        # Check for MCP-specific tasks
        if task_type.startswith('mcp') or 'mcp' in task_type:
            return 'mcp'
        
        domain_keywords = {
            'insurance': ['quote', 'policy', 'claim', 'coverage'],
            'content': ['article', 'post', 'video', 'media'],
            'email': ['mail', 'message', 'communication'],
            'social_media': ['social', 'instagram', 'tiktok', 'wix', 'post', 'schedule'],
            'mcp': ['mcp', 'model', 'protocol', 'server', 'ai']
        }
        
        for domain, keywords in domain_keywords.items():
            if any(keyword in task_type for keyword in keywords):
                return domain
                
        return 'general'

    def _construct_knowledge_query(self, task: Dict) -> str:
        """Construct knowledge query from task"""
        # Extract key terms from task
        task_type = task.get('type', '').lower()
        action = task.get('action', '').lower()
        
        # Combine with any tags, subjects or data-specific terms
        data = task.get('data', {})
        terms = []
        
        if 'tags' in data:
            terms.extend(data['tags'])
            
        if 'subject' in data:
            terms.append(data['subject'])
            
        if 'topic' in data:
            terms.append(data['topic'])
            
        # Construct query string
        query_parts = [task_type, action] + terms
        query = " ".join(term for term in query_parts if term)
        
        return query

    async def monitor_agent_performance(self):
        """Monitor and track agent performance metrics"""
        while True:
            for domain, agent_info in self.main_agents.items():
                metrics = {
                    'tasks_processed': len(agent_info['active_tasks']),
                    'success_rate': self._calculate_success_rate(domain),
                    'average_response_time': self._calculate_response_time(domain),
                    'learning_progress': self._evaluate_learning_progress(domain)
                }
                agent_info['performance_metrics'] = metrics
                
                logger.info(f"Performance metrics for {domain}: {metrics}")
                
            await asyncio.sleep(300)  # Update every 5 minutes

    def _calculate_success_rate(self, domain: str) -> float:
        """Calculate success rate for domain"""
        recent_interactions = [
            i for i in self.interaction_history[-100:]
            if i['domain'] == domain
        ]
        
        if not recent_interactions:
            return 0.0
            
        successful = sum(1 for i in recent_interactions
                        if i['result'].get('status') == 'success')
        return successful / len(recent_interactions)

    def _calculate_response_time(self, domain: str) -> float:
        """Calculate average response time for domain"""
        recent_interactions = [
            i for i in self.interaction_history[-100:]
            if i['domain'] == domain
        ]
        
        if not recent_interactions:
            return 0.0
            
        total_time = sum(
            (datetime.fromisoformat(i['result'].get('timestamp', i['timestamp'])) -
             datetime.fromisoformat(i['timestamp'])).total_seconds()
            for i in recent_interactions
        )
        return total_time / len(recent_interactions)

    def _evaluate_learning_progress(self, domain: str) -> Dict:
        """Evaluate learning progress for domain"""
        if domain not in self.learning_modules:
            return {}
        
        learners = self.learning_modules[domain]
        progress = {}
        
        for learner_type, learner in learners.items():
            if hasattr(learner, 'get_learning_progress'):
                progress[learner_type] = learner.get_learning_progress()
            
        return progress

    async def test_mcp_integration(self):
        """Test MCP integration functionality"""
        try:
            # Initialize MCP if needed
            if not self.mcp_initialized:
                await self.initialize_mcp()
                
            # Send test email through MCP
            email_result = await mcp_coordinator.send_email_to_paul(
                "MCP Integration Test",
                "This is a test email sent from the agent coordinator via MCP integration.\n\n"
                "If you received this email, the MCP integration is working properly."
            )
            
            logger.info(f"MCP integration test email sent: {email_result}")
            return email_result
            
        except Exception as e:
            logger.error(f"Error in MCP integration test: {str(e)}")
            return False

# Example usage
if __name__ == "__main__":
    coordinator = AgentCoordinator()
    
    async def main():
        # Initialize system
        await coordinator.initialize_system()
        
        # Test MCP integration
        mcp_test_result = await coordinator.test_mcp_integration()
        print(f"MCP integration test result: {mcp_test_result}")
        
        # Example task
        task = {
            "type": "insurance_quote",
            "sub_types": ["quote", "risk_assessment"],
            "data": {
                "customer": "John Doe",
                "insurance_type": "auto",
                "vehicle_info": {
                    "make": "Toyota",
                    "model": "Camry",
                    "year": 2020
                }
            }
        }
        
        # Process task
        result = await coordinator.process_task(task)
        print(f"Task result: {json.dumps(result, indent=2)}")
        
        # Start performance monitoring
        monitor_task = asyncio.create_task(coordinator.monitor_agent_performance())
        
        # Keep the program running
        await asyncio.gather(monitor_task)

    asyncio.run(main())