import asyncio
import logging
from datetime import datetime
import json
import os
from typing import Dict, List, Any

# Import all system components
from secure_credentials import SecureCredentialsManager
from insurance_carriers import CarrierManager
from client_template import ClientManager
from agent_coordinator import AgentCoordinator
from knowledge_management import KnowledgeManager

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('system_check.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class SystemChecker:
    """Utility to verify all system components are working properly"""
    
    def __init__(self):
        self.results = {
            "timestamp": datetime.now().isoformat(),
            "checks": {},
            "overall_status": "pending"
        }
        
    async def run_all_checks(self):
        """Run all system checks"""
        try:
            logger.info("Starting system checks...")
            
            # Check components initialization
            await self.check_components()
            
            # Check credentials
            await self.check_credentials()
            
            # Check carrier connections
            await self.check_carriers()
            
            # Check knowledge base
            await self.check_knowledge_base()
            
            # Check agent coordination
            await self.check_agent_coordination()
            
            # Determine overall status
            self.results["overall_status"] = "success" if all(
                check.get("status") == "success" 
                for check in self.results["checks"].values()
            ) else "failed"
            
            return self.results
            
        except Exception as e:
            logger.error(f"System check failed: {str(e)}")
            self.results["overall_status"] = "error"
            return self.results
            
    async def check_components(self):
        """Verify all components can be initialized"""
        try:
            components = {
                "client_manager": ClientManager(),
                "credentials_manager": SecureCredentialsManager(),
                "carrier_manager": CarrierManager(),
                "agent_coordinator": AgentCoordinator(),
                "knowledge_manager": KnowledgeManager()
            }
            
            self.results["checks"]["components"] = {
                "status": "success",
                "details": {
                    name: "initialized" for name in components.keys()
                }
            }
            logger.info("Component initialization check: PASSED")
            
        except Exception as e:
            logger.error(f"Component initialization failed: {str(e)}")
            self.results["checks"]["components"] = {
                "status": "failed",
                "error": str(e)
            }
            
    async def check_credentials(self):
        """Verify credential management"""
        try:
            creds_manager = SecureCredentialsManager()
            
            # Test agent credentials retrieval
            agent_ids = ["justine", "sandra"]
            carrier_checks = []
            
            for agent_id in agent_ids:
                carriers = [
                    "uhc", "mutual_of_omaha", "americo", 
                    "aetna", "cigna", "healthsherpa"
                ]
                
                for carrier in carriers:
                    try:
                        creds = creds_manager.get_carrier_credentials(agent_id, carrier)
                        carrier_checks.append({
                            "agent": agent_id,
                            "carrier": carrier,
                            "status": "available"
                        })
                    except Exception as e:
                        carrier_checks.append({
                            "agent": agent_id,
                            "carrier": carrier,
                            "status": "unavailable",
                            "error": str(e)
                        })
            
            self.results["checks"]["credentials"] = {
                "status": "success",
                "details": carrier_checks
            }
            logger.info("Credentials check: PASSED")
            
        except Exception as e:
            logger.error(f"Credentials check failed: {str(e)}")
            self.results["checks"]["credentials"] = {
                "status": "failed",
                "error": str(e)
            }
            
    async def check_carriers(self):
        """Verify carrier connections"""
        try:
            carrier_manager = CarrierManager()
            carrier_statuses = {}
            
            # Test carrier portal access
            carriers = [
                ("uhc", "https://jarvys.com"),
                ("mutual_of_omaha", "https://www.mutualofomaha.com"),
                ("americo", "https://www.americo.com"),
                ("aetna", "https://producerworld.com"),
                ("cigna", "https://cignaforbrokers.com")
            ]
            
            for carrier, url in carriers:
                try:
                    # Test connection to carrier URL
                    response = await self._test_connection(url)
                    carrier_statuses[carrier] = {
                        "url": url,
                        "status": "accessible" if response else "inaccessible"
                    }
                except Exception as e:
                    carrier_statuses[carrier] = {
                        "url": url,
                        "status": "error",
                        "error": str(e)
                    }
            
            self.results["checks"]["carriers"] = {
                "status": "success" if any(
                    status["status"] == "accessible" 
                    for status in carrier_statuses.values()
                ) else "failed",
                "details": carrier_statuses
            }
            logger.info("Carrier connections check: PASSED")
            
        except Exception as e:
            logger.error(f"Carrier check failed: {str(e)}")
            self.results["checks"]["carriers"] = {
                "status": "failed",
                "error": str(e)
            }
            
    async def check_knowledge_base(self):
        """Verify knowledge base functionality"""
        try:
            knowledge_manager = KnowledgeManager()
            
            # Test knowledge base operations
            test_knowledge = {
                "content": "Test insurance knowledge entry",
                "domain": "insurance",
                "metadata": {
                    "type": "test",
                    "timestamp": datetime.now().isoformat()
                }
            }
            
            # Add test knowledge
            knowledge_manager.add_knowledge(
                domain="insurance",
                content=test_knowledge["content"],
                metadata=test_knowledge["metadata"],
                agent_id="system_check"
            )
            
            # Query test knowledge
            results = knowledge_manager.query_knowledge(
                domain="insurance",
                query="test insurance"
            )
            
            self.results["checks"]["knowledge_base"] = {
                "status": "success" if results else "failed",
                "details": {
                    "storage": "working",
                    "retrieval": "working" if results else "failed"
                }
            }
            logger.info("Knowledge base check: PASSED")
            
        except Exception as e:
            logger.error(f"Knowledge base check failed: {str(e)}")
            self.results["checks"]["knowledge_base"] = {
                "status": "failed",
                "error": str(e)
            }
            
    async def check_agent_coordination(self):
        """Verify agent coordination system"""
        try:
            coordinator = AgentCoordinator()
            
            # Test basic task processing
            test_task = {
                "type": "system_check",
                "action": "test",
                "data": {
                    "timestamp": datetime.now().isoformat()
                }
            }
            
            result = await coordinator.process_task(test_task)
            
            self.results["checks"]["agent_coordination"] = {
                "status": "success" if result else "failed",
                "details": {
                    "task_processing": "working" if result else "failed"
                }
            }
            logger.info("Agent coordination check: PASSED")
            
        except Exception as e:
            logger.error(f"Agent coordination check failed: {str(e)}")
            self.results["checks"]["agent_coordination"] = {
                "status": "failed",
                "error": str(e)
            }
            
    async def _test_connection(self, url: str) -> bool:
        """Test connection to a URL"""
        try:
            # Add proper headers and timeout
            headers = {
                "User-Agent": "InsuranceSystemCheck/1.0"
            }
            async with aiohttp.ClientSession() as session:
                async with session.get(url, headers=headers, timeout=10) as response:
                    return response.status == 200
        except Exception:
            return False

async def main():
    """Run system checks and display results"""
    checker = SystemChecker()
    results = await checker.run_all_checks()
    
    # Save results
    with open("system_check_results.json", "w") as f:
        json.dump(results, f, indent=2)
        
    # Display summary
    print("\nSystem Check Summary:")
    print("=" * 50)
    print(f"Overall Status: {results['overall_status'].upper()}")
    print("\nComponent Checks:")
    for component, check in results["checks"].items():
        status = check["status"]
        print(f"{component:20}: {status.upper()}")
    print("=" * 50)
    print("\nDetailed results saved to system_check_results.json")

if __name__ == "__main__":
    asyncio.run(main())