#!/bin/bash

echo "🔧 MCP Server Fixer Script"
echo "=========================="
echo "This script will fix common MCP server issues and ensure they stay active."
echo

# Create necessary directories
echo "Creating necessary directories..."
mkdir -p ~/.config/llm_models
mkdir -p ~/.local/models

# Create minimal config files if they don't exist
echo "Setting up MCP configuration files..."

# Create mcp_config.json if it doesn't exist
if [ ! -f "./mcp_config.json" ]; then
    echo "Creating mcp_config.json..."
    cat > ./mcp_config.json << EOL
{
    "servers": {
        "local-models-mcp": {
            "url": "http://localhost:8085",
            "name": "Local Models MCP Server",
            "id": "local-models-mcp"
        }
    },
    "registered_servers": ["local-models-mcp"]
}
EOL
    echo "✅ Created mcp_config.json"
fi

# Create a sample model configuration
if [ ! -f ~/.config/llm_models/default_model.json ]; then
    echo "Creating a default model configuration..."
    cat > ~/.config/llm_models/default_model.json << EOL
{
    "model_name": "default-model",
    "model_type": "huggingface",
    "hf_repo_id": "TheBloke/Llama-2-7B-GGUF"
}
EOL
    echo "✅ Created default model configuration"
fi

# Install required Python packages
echo "Installing required Python packages..."
pip install aiohttp torch transformers huggingface_hub

# Make start_mcp_servers.py executable
chmod +x start_mcp_servers.py
chmod +x local_models_mcp_server.py

# Fix the Python shebang line if needed
echo "Fixing shebang lines in Python scripts..."
sed -i.bak '1s|^#!.*python.*$|#!/usr/bin/env python3|' start_mcp_servers.py
sed -i.bak '1s|^#!.*python.*$|#!/usr/bin/env python3|' local_models_mcp_server.py

echo "Stopping any existing MCP server processes..."
pkill -f "python.*local_models_mcp_server.py" || echo "No local MCP server processes found"
pkill -f "python.*start_mcp_servers.py" || echo "No MCP server starter processes found"

echo
echo "Starting MCP servers..."
python3 start_mcp_servers.py &

# Wait a moment for servers to start
sleep 5

# Check if servers are running
echo
echo "Checking MCP server status..."
curl -s http://localhost:8085/health || echo "⚠️ Local MCP server is not responding"

echo
echo "✅ MCP server setup complete! Servers should now be active and stay active."
echo "If you still have issues, try running: 'python start_mcp_servers.py'"
