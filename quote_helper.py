import logging
import webbrowser
from typing import Dict, Optional
from carrier_urls import CARRIER_URLS

logger = logging.getLogger(__name__)

class QuoteHelper:
    """Helper class for accessing carrier quote platforms directly"""
    
    @staticmethod
    def open_quote_platform(carrier: str) -> bool:
        """Open carrier's quote platform in default browser"""
        try:
            carrier = carrier.lower().replace(' ', '_')
            urls = CARRIER_URLS.get(carrier, {})
            quote_url = urls.get('quote_url')
            
            if quote_url:
                logger.info(f"Opening quote platform for {carrier}: {quote_url}")
                webbrowser.open(quote_url)
                return True
            else:
                logger.error(f"No quote URL found for carrier: {carrier}")
                return False
                
        except Exception as e:
            logger.error(f"Error opening quote platform: {str(e)}")
            return False

    @staticmethod
    def get_quote_urls() -> Dict[str, str]:
        """Get all available quote URLs"""
        return {
            carrier: urls.get('quote_url')
            for carrier, urls in CARRIER_URLS.items()
            if urls.get('quote_url')
        }

    @staticmethod
    def format_client_data(client_data: Dict, carrier: str) -> Dict:
        """Format client data for carrier's quote platform"""
        base_data = {
            "name": client_data.get("name", ""),
            "dob": client_data.get("dob", ""),
            "state": client_data.get("address", "").split(",")[-1].strip().split()[0] if client_data.get("address") else "",
            "tobacco": "Yes" if client_data.get("tobacco_use") else "No"
        }
        
        if carrier == "ffl_trident":
            return {
                **base_data,
                "coverage": client_data.get("desired_coverage", "250000"),
                "product": "Term",
                "term": "20",  # Default term length
                "health": "Preferred" if not client_data.get("tobacco_use") else "Standard"
            }
            
        elif carrier == "mutual_of_omaha":
            return {
                **base_data,
                "product": "Medicare Supplement",
                "height": client_data.get("height", ""),
                "weight": client_data.get("weight", ""),
                "medications": [
                    med.get("drug_name") 
                    for med in client_data.get("medications", [])
                ]
            }
            
        return base_data

def generate_iul_quote(client_data):
    """Generate an IUL quote for a client
    
    Args:
        client_data: Dictionary containing client information
        
    Returns:
        Dictionary containing quote details
    """
    import uuid
    from insurance_products import find_suitable_iul_products
    
    # Extract client data
    age = client_data.get("age", 32)
    gender = client_data.get("gender", "Male")
    health_class = "Preferred" if not client_data.get("medications") and not client_data.get("smoker") else "Standard"
    income = client_data.get("income", 85000)
    budget = client_data.get("budget", 200)
    
    # Find suitable IUL products
    suitable_products = find_suitable_iul_products(age, budget, health_class)
    
    if not suitable_products:
        return {
            "error": "No suitable IUL products found",
            "quote_id": None
        }
    
    # For IUL, we typically want to base the death benefit on income multiple
    # Common rule of thumb: 10-15x annual income
    target_coverage = min(income * 12, 1000000)  # Cap at $1M for simplicity
    
    # Adjust based on budget
    monthly_premium = budget
    
    # Calculate death benefit based on age, gender, and premium
    # This is a simplified calculation - real IUL would use actuarial tables
    base_multiple = 250 if age < 35 else 200
    gender_factor = 1.1 if gender == "Female" else 1.0
    health_factor = 1.2 if health_class == "Preferred" else 1.0
    
    death_benefit = monthly_premium * 12 * base_multiple * gender_factor * health_factor
    
    # Cap at target coverage
    if death_benefit > target_coverage:
        death_benefit = target_coverage
        # Recalculate premium
        monthly_premium = (death_benefit / (base_multiple * gender_factor * health_factor)) / 12
    
    # Calculate cash value growth projections (simplified)
    projected_cash_values = {}
    for year in range(1, 31):  # 30-year projection
        if year <= 5:
            projected_cash_values[year] = monthly_premium * 12 * year * 0.7  # First 5 years lower due to fees
        else:
            # After year 5, assume better growth
            projected_cash_values[year] = projected_cash_values[5] + (monthly_premium * 12 * (year - 5) * 0.9)
    
    # Calculate projected retirement income
    # Assume start at age 65 with 15 years of distributions
    years_to_retirement = max(65 - age, 20)
    retirement_cash_value = projected_cash_values[min(years_to_retirement, 30)]
    annual_retirement_income = retirement_cash_value * 0.05  # 5% annual distribution
    total_retirement_income = annual_retirement_income * 15
    
    # Generate quote ID
    quote_id = str(uuid.uuid4())
    
    # Build the quote summary
    quote_summary = {
        "quote_id": quote_id,
        "client": {
            "name": f"{client_data.get('first_name', '')} {client_data.get('last_name', '')}",
            "age": age,
            "gender": gender,
            "health_class": health_class,
            "income": income,
            "budget": budget
        },
        "product_type": "IUL",
        "recommended_plan": {
            "carrier": suitable_products[0]["carrier"],
            "name": suitable_products[0]["name"],
            "death_benefit": round(death_benefit, 2),
            "monthly_premium": round(monthly_premium, 2),
            "annual_premium": round(monthly_premium * 12, 2),
            "cash_value_projection": {
                "year_10": round(projected_cash_values.get(10, 0), 2),
                "year_20": round(projected_cash_values.get(20, 0), 2),
                "at_retirement": round(retirement_cash_value, 2)
            },
            "retirement_income": {
                "annual_amount": round(annual_retirement_income, 2),
                "years": 15,
                "total_amount": round(total_retirement_income, 2)
            }
        },
        "alternative_plans": [
            {
                "carrier": p["carrier"],
                "name": p["name"],
                "monthly_premium": round(monthly_premium * (1 + (i * 0.05)), 2),
                "death_benefit": round(death_benefit * (1 + (i * 0.03)), 2)
            } for i, p in enumerate(suitable_products[1:4])  # Next 3 products as alternatives
        ] if len(suitable_products) > 1 else [],
        "notes": [
            f"This IUL plan is recommended because it provides strong cash value growth potential with a competitive death benefit.",
            f"The plan is designed to stay within your budget of ${budget}/month while providing a ${round(death_benefit):,} death benefit.",
            f"Projected to provide ${round(annual_retirement_income):,} of annual tax-free retirement income for 15 years, totaling ${round(total_retirement_income):,}."
        ]
    }
    
    return quote_summary

def format_iul_proposal(quote_summary, client_data=None):
    """Format an IUL proposal for presentation
    
    Args:
        quote_summary: Dictionary containing quote details
        client_data: Optional additional client information
        
    Returns:
        String containing formatted proposal
    """
    if "error" in quote_summary:
        return f"Error generating IUL quote: {quote_summary['error']}"
    
    client = quote_summary.get("client", {})
    if client_data:
        client.update({
            "name": f"{client_data.get('first_name', '')} {client_data.get('last_name', '')}",
            "email": client_data.get("email", ""),
            "phone": client_data.get("phone", ""),
            "address": client_data.get("address", ""),
            "city": client_data.get("city", ""),
            "state": client_data.get("state", ""),
            "zip": client_data.get("zip", "")
        })
    
    rec_plan = quote_summary.get("recommended_plan", {})
    
    proposal = f"""
# Indexed Universal Life Insurance Proposal

## Client Information
- **Name:** {client.get("name", "N/A")}
- **Age:** {client.get("age", "N/A")}
- **Health Class:** {client.get("health_class", "Preferred")}

## Recommended IUL Plan
- **Carrier:** {rec_plan.get("carrier", "N/A")}
- **Product:** {rec_plan.get("name", "N/A")}
- **Death Benefit:** ${rec_plan.get("death_benefit", 0):,.2f}
- **Monthly Premium:** ${rec_plan.get("monthly_premium", 0):,.2f}
- **Annual Premium:** ${rec_plan.get("annual_premium", 0):,.2f}

## Projected Cash Value
- **Year 10:** ${rec_plan.get("cash_value_projection", {}).get("year_10", 0):,.2f}
- **Year 20:** ${rec_plan.get("cash_value_projection", {}).get("year_20", 0):,.2f}
- **At Retirement:** ${rec_plan.get("cash_value_projection", {}).get("at_retirement", 0):,.2f}

## Projected Retirement Income
- **Annual Tax-Free Income:** ${rec_plan.get("retirement_income", {}).get("annual_amount", 0):,.2f}
- **Income Duration:** {rec_plan.get("retirement_income", {}).get("years", 15)} years
- **Total Tax-Free Retirement Income:** ${rec_plan.get("retirement_income", {}).get("total_amount", 0):,.2f}

## Key Benefits
1. Death benefit protection for your loved ones
2. Tax-advantaged cash value accumulation
3. Tax-free retirement income potential through policy loans
4. Flexible premium payments
5. Protection against market downturns with guaranteed minimum interest rates

## Notes
"""
    
    for note in quote_summary.get("notes", []):
        proposal += f"- {note}\n"
    
    proposal += """
## Alternative Plans
"""
    
    for i, alt in enumerate(quote_summary.get("alternative_plans", []), 1):
        proposal += f"""
### Alternative {i}
- **Carrier:** {alt.get("carrier", "N/A")}
- **Product:** {alt.get("name", "N/A")}
- **Monthly Premium:** ${alt.get("monthly_premium", 0):,.2f}
- **Death Benefit:** ${alt.get("death_benefit", 0):,.2f}
"""
    
    proposal += """
## Next Steps
1. Review this proposal and note any questions
2. Schedule a follow-up call to discuss your options
3. Complete application if you wish to proceed
4. Medical underwriting (if required)
5. Policy delivery and implementation

Thank you for the opportunity to present this customized IUL solution.
"""
    
    return proposal

def main():
    """Test quote platform access"""
    logging.basicConfig(level=logging.INFO)
    
    # Test opening quote platforms
    carriers = ["mutual_of_omaha", "ffl_trident"]
    
    for carrier in carriers:
        logger.info(f"\nTesting {carrier} quote platform...")
        success = QuoteHelper.open_quote_platform(carrier)
        
        if success:
            logger.info(f"Successfully opened {carrier} quote platform")
        else:
            logger.error(f"Failed to open {carrier} quote platform")
            
    # Show all available quote URLs
    logger.info("\nAvailable Quote URLs:")
    urls = QuoteHelper.get_quote_urls()
    for carrier, url in urls.items():
        logger.info(f"{carrier}: {url}")

if __name__ == "__main__":
    main()