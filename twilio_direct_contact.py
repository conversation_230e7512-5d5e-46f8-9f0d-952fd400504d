"""
Direct Contact with <PERSON> using <PERSON>wilio

This script uses <PERSON><PERSON><PERSON> to directly contact <PERSON> through:
1. SMS
2. Phone calls
3. Voicemails

It uses environment variables for secure credential management.
"""

import os
import requests
import base64
import json
import time
from dotenv import load_dotenv
from twilio_config import (
    validate_twilio_credentials, 
    get_account_info,
    format_error_message,
    is_trial_account,
    get_verified_numbers,
    get_from_number
)

# Load environment variables
load_dotenv()

# Twilio Configuration
TWILIO_ACCOUNT_SID = os.getenv("TWILIO_ACCOUNT_SID")
TWILIO_AUTH_TOKEN = os.getenv("TWILIO_AUTH_TOKEN")
TWILIO_PHONE_NUMBER = os.getenv("TWILIO_PHONE_NUMBER")

# <PERSON> contact information
PAUL_EDWARDS = {
    "first_name": "<PERSON>",
    "last_name": "<PERSON>",
    "email": "<EMAIL>",
    "primary_phone": "+***********",
    "secondary_phone": "+***********"  # This might also be a verified Twilio number
}

# Agent information
AGENT_INFO = {
    "name": "<PERSON>",
    "agency": "Flo Faction Insurance",
    "email": "<EMAIL>",
    "website": "https://www.flofaction.com/insurance",
}

def verify_setup():
    """Verify Twilio credentials and setup before proceeding"""
    print("=" * 80)
    print("VERIFYING TWILIO SETUP")
    print("=" * 80)
    
    # Validate credentials
    is_valid, error_message = validate_twilio_credentials()
    
    if not is_valid:
        print(f"❌ Twilio setup validation failed: {error_message}")
        print("\nPlease check your .env file and ensure it contains:")
        print("TWILIO_ACCOUNT_SID=your_account_sid")
        print("TWILIO_AUTH_TOKEN=your_auth_token")
        print("TWILIO_PHONE_NUMBER=your_phone_number")
        return False
    
    # Get account information
    account_info = get_account_info()
    
    print("✓ Twilio setup validation successful!")
    print(f"Account Name: {account_info.get('friendly_name')}")
    print(f"Account Type: {account_info.get('type')}")
    print(f"Account Status: {account_info.get('status')}")
    
    # Check if we're using a trial account
    if is_trial_account():
        print("\n⚠️ This is a TRIAL account")
        print("Trial accounts have restrictions:")
        print("- Can only send messages to verified numbers")
        print("- Limited phone call functionality")
        
        # Check verified numbers
        verified_numbers = get_verified_numbers()
        print(f"\nVerified Numbers: {len(verified_numbers)}")
        
        for number in verified_numbers:
            print(f"- {number}")
            
        # Check if Paul's numbers are verified
        if PAUL_EDWARDS["primary_phone"] in verified_numbers:
            print(f"\n✓ {PAUL_EDWARDS['first_name']}'s primary number is verified")
        else:
            print(f"\n⚠️ {PAUL_EDWARDS['first_name']}'s primary number is NOT verified")
            print("You may need to verify this number in your Twilio console:")
            print("https://console.twilio.com/phone-numbers/verified")
            
        if PAUL_EDWARDS["secondary_phone"] in verified_numbers:
            print(f"✓ {PAUL_EDWARDS['first_name']}'s secondary number is verified")
        else:
            print(f"⚠️ {PAUL_EDWARDS['first_name']}'s secondary number is NOT verified")
    
    return True

def send_sms():
    """Send an SMS to Paul Edwards using Twilio"""
    print("=" * 80)
    print("SENDING SMS TO PAUL EDWARDS USING TWILIO")
    print("=" * 80)
    
    # Create message content
    message = f"""
Hi {PAUL_EDWARDS['first_name']}, this is {AGENT_INFO['name']} with {AGENT_INFO['agency']}. I help people create tax-free retirement income without stock market risk. I've prepared a personalized analysis for you. Would you be interested in seeing this analysis? Visit {AGENT_INFO['website']} for more information. Thanks!
    """.strip()
    
    # Twilio API endpoint for sending messages
    url = f"https://api.twilio.com/2010-04-01/Accounts/{TWILIO_ACCOUNT_SID}/Messages.json"
    
    # Determine which number to use
    from_number = get_from_number(PAUL_EDWARDS["primary_phone"])
    
    # Prepare the request data
    data = {
        "To": PAUL_EDWARDS["primary_phone"],
        "From": from_number,
        "Body": message
    }
    
    print(f"Sending SMS from {from_number} to {PAUL_EDWARDS['primary_phone']}")
    
    try:
        # Make the request
        response = requests.post(url, auth=(TWILIO_ACCOUNT_SID, TWILIO_AUTH_TOKEN), data=data)
        
        # Check if the request was successful
        if response.status_code == 201:
            response_data = response.json()
            print(f"\n✓ SMS sent successfully!")
            print(f"Message SID: {response_data.get('sid')}")
            print(f"Status: {response_data.get('status')}")
            print(f"View in console: https://console.twilio.com/us1/develop/sms/logs/SM{response_data.get('sid')[2:]}")
            return True
        else:
            print(f"\n❌ Error sending SMS: HTTP {response.status_code}")
            print(format_error_message(response))
            
            # Special handling for common errors
            if response.status_code == 400:
                try:
                    error_data = response.json()
                    error_code = error_data.get('code')
                    
                    if error_code == 21211:
                        print("\nThis error often means the destination number is not verified in your trial account.")
                        print("Please verify the number in your Twilio console:")
                        print("https://console.twilio.com/phone-numbers/verified")
                    elif error_code == 21606:
                        print("\nThis error means the 'From' number is not valid or not owned by your account.")
                        print("Please check your phone number in the Twilio console.")
                except:
                    pass
            
            return False
    except Exception as e:
        print(f"\n❌ Exception sending SMS: {str(e)}")
        return False

def make_call():
    """Make a phone call to Paul Edwards using Twilio"""
    print("=" * 80)
    print("MAKING PHONE CALL TO PAUL EDWARDS USING TWILIO")
    print("=" * 80)
    
    # Create TwiML for the call
    twiml = f"""
    <Response>
        <Say voice="woman">
            Hello {PAUL_EDWARDS['first_name']}, this is {AGENT_INFO['name']} with {AGENT_INFO['agency']}. How are you doing today?
            
            I'm reaching out because I help people like you create tax-free retirement income without the risk of the stock market. Based on your profile, I've found that you might qualify for a strategy that could provide around $5,000 per month in tax-free retirement income.
            
            I'd love to share a personalized analysis I've prepared for you. Please give me a call back when you have a moment, or visit our website at {AGENT_INFO['website']} for more information.
            
            Thank you and have a great day!
        </Say>
    </Response>
    """
    
    # Twilio API endpoint for making calls
    url = f"https://api.twilio.com/2010-04-01/Accounts/{TWILIO_ACCOUNT_SID}/Calls.json"
    
    # Determine which number to use
    from_number = get_from_number(PAUL_EDWARDS["primary_phone"])
    
    # Prepare the request data
    data = {
        "To": PAUL_EDWARDS["primary_phone"],
        "From": from_number,
        "Twiml": twiml
    }
    
    print(f"Calling from {from_number} to {PAUL_EDWARDS['primary_phone']}")
    
    try:
        # Make the request
        response = requests.post(url, auth=(TWILIO_ACCOUNT_SID, TWILIO_AUTH_TOKEN), data=data)
        
        # Check if the request was successful
        if response.status_code >= 200 and response.status_code < 300:
            response_data = response.json()
            print(f"\n✓ Call initiated successfully!")
            print(f"Call SID: {response_data.get('sid')}")
            print(f"Status: {response_data.get('status')}")
            print(f"View in console: https://console.twilio.com/us1/develop/voice/logs/CA{response_data.get('sid')[2:]}")
            return True
        else:
            print(f"\n❌ Error initiating call: HTTP {response.status_code}")
            print(format_error_message(response))
            return False
    except Exception as e:
        print(f"\n❌ Exception making call: {str(e)}")
        return False

def leave_voicemail():
    """Leave a voicemail for Paul Edwards using Twilio"""
    print("=" * 80)
    print("LEAVING VOICEMAIL FOR PAUL EDWARDS USING TWILIO")
    print("=" * 80)
    
    # Create TwiML for the voicemail
    twiml = f"""
    <Response>
        <Pause length="2"/>
        <Say voice="woman">
            Hi {PAUL_EDWARDS['first_name']}, this is {AGENT_INFO['name']} with {AGENT_INFO['agency']}. I'm calling because I help people like you create tax-free retirement income without the risk of the stock market.
            
            Based on your profile, I've found that you might qualify for a strategy that could provide significant tax-free retirement income while also protecting your family with a death benefit.
            
            I'd love to share a personalized analysis I've prepared for you. Please give me a call back when you have a moment, or visit our website at {AGENT_INFO['website']} for more information.
            
            Thank you and have a great day!
        </Say>
        <Hangup/>
    </Response>
    """
    
    # Twilio API endpoint for making calls
    url = f"https://api.twilio.com/2010-04-01/Accounts/{TWILIO_ACCOUNT_SID}/Calls.json"
    
    # Determine which number to use
    from_number = get_from_number(PAUL_EDWARDS["primary_phone"])
    
    # Prepare the request data
    data = {
        "To": PAUL_EDWARDS["primary_phone"],
        "From": from_number,
        "Twiml": twiml,
        "SendDigits": "1"  # This might help to skip to voicemail on some systems
    }
    
    print(f"Calling for voicemail from {from_number} to {PAUL_EDWARDS['primary_phone']}")
    
    try:
        # Make the request
        response = requests.post(url, auth=(TWILIO_ACCOUNT_SID, TWILIO_AUTH_TOKEN), data=data)
        
        # Check if the request was successful
        if response.status_code >= 200 and response.status_code < 300:
            response_data = response.json()
            print(f"\n✓ Voicemail call initiated successfully!")
            print(f"Call SID: {response_data.get('sid')}")
            print(f"Status: {response_data.get('status')}")
            print(f"View in console: https://console.twilio.com/us1/develop/voice/logs/CA{response_data.get('sid')[2:]}")
            return True
        else:
            print(f"\n❌ Error initiating voicemail call: HTTP {response.status_code}")
            print(format_error_message(response))
            return False
    except Exception as e:
        print(f"\n❌ Exception leaving voicemail: {str(e)}")
        return False

def main():
    """Main function to contact Paul Edwards"""
    print("=" * 80)
    print("DIRECT CONTACT WITH PAUL EDWARDS USING TWILIO")
    print("=" * 80)
    
    print("\nThis script uses Twilio to directly contact Paul Edwards through:")
    print("1. SMS")
    print("2. Phone calls")
    print("3. Voicemails")
    
    # Verify setup before proceeding
    if not verify_setup():
        print("\n❌ Setup verification failed. Please fix the issues and try again.")
        return
    
    print(f"\nPaul Edwards contact information:")
    print(f"Name: {PAUL_EDWARDS['first_name']} {PAUL_EDWARDS['last_name']}")
    print(f"Email: {PAUL_EDWARDS['email']}")
    print(f"Primary Phone: {PAUL_EDWARDS['primary_phone']}")
    
    print("\nWhat would you like to do?")
    print("1. Send SMS")
    print("2. Make phone call")
    print("3. Leave voicemail")
    print("4. All of the above")
    print("5. Interactive test mode")
    
    choice = input("\nEnter your choice (1-5): ")
    
    if choice == "1":
        send_sms()
    elif choice == "2":
        make_call()
    elif choice == "3":
        leave_voicemail()
    elif choice == "4":
        print("\nExecuting all contact methods...")
        
        # Send SMS
        sms_success = send_sms()
        
        # Wait a bit between communications
        if sms_success:
            print("\nWaiting 10 seconds before next communication...")
            time.sleep(10)
        
        # Make phone call
        call_success = make_call()
        
        # Wait a bit between communications
        if call_success:
            print("\nWaiting 30 seconds before next communication...")
            time.sleep(30)
        
        # Leave voicemail
        voicemail_success = leave_voicemail()
        
        # Print summary
        print("\n" + "=" * 80)
        print("CONTACT SUMMARY")
        print("=" * 80)
        
        print(f"SMS: {'✓ Success' if sms_success else '❌ Failed'}")
        print(f"Phone Call: {'✓ Success' if call_success else '❌ Failed'}")
        print(f"Voicemail: {'✓ Success' if voicemail_success else '❌ Failed'}")
    elif choice == "5":
        # Interactive test mode
        print("\n" + "=" * 80)
        print("INTERACTIVE TEST MODE")
        print("=" * 80)
        
        print("This mode allows you to test your Twilio credentials interactively.")
        use_interactive = input("Would you like to enter your Twilio credentials now? (y/n): ")
        
        if use_interactive.lower() == 'y':
            account_sid = input("Enter your Twilio Account SID: ")
            auth_token = input("Enter your Twilio Auth Token: ")
            phone_number = input("Enter your Twilio Phone Number: ")
            
            # Temporarily override the global variables
            global TWILIO_ACCOUNT_SID, TWILIO_AUTH_TOKEN, TWILIO_PHONE_NUMBER
            TWILIO_ACCOUNT_SID = account_sid
            TWILIO_AUTH_TOKEN = auth_token
            TWILIO_PHONE_NUMBER = phone_number
            
            # Write to .env file
            with open('.env', 'w') as f:
                f.write(f"TWILIO_ACCOUNT_SID={account_sid}\n")
                f.write(f"TWILIO_AUTH_TOKEN={auth_token}\n")
                f.write(f"TWILIO_PHONE_NUMBER={phone_number}\n")
            
            print("\n✓ Credentials saved to .env file!")
            
            # Re-verify setup
            verify_setup()
            
            # Ask what to test
            test_option = input("\nWhat would you like to test? (1=SMS, 2=Call, 3=Voicemail): ")
            
            if test_option == '1':
                send_sms()
            elif test_option == '2':
                make_call()
            elif test_option == '3':
                leave_voicemail()
            else:
                print("Invalid choice. Exiting test mode.")
        else:
            print("Exiting test mode.")
    else:
        print("Invalid choice. Please run the script again.")
    
    print("\n" + "=" * 80)
    print("CONTACT OPERATIONS COMPLETED")
    print("=" * 80)

if __name__ == "__main__":
    main()
