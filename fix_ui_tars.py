#!/usr/bin/env python3
"""
UI T.A.R.S. Fix Script

This script fixes issues with UI T.A.R.S. and ensures it's properly configured
to work with the insurance agent system.
"""

import os
import sys
import json
import time
import shutil
import logging
import subprocess
import tempfile
from pathlib import Path
import requests
try:
    import yaml
except ImportError:
    subprocess.run([sys.executable, "-m", "pip", "install", "pyyaml"], check=True)
    import yaml

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s [%(levelname)s] %(message)s",
    handlers=[logging.StreamHandler()]
)
logger = logging.getLogger("ui-tars-fix")

# Set the Operative API key
OPERATIVE_API_KEY = "op-sMxrcBrpW8uyzqeL8mg9N9SiDNY_ijY6kB8QUbiFj1I"
os.environ["OPERATIVE_API_KEY"] = OPERATIVE_API_KEY

class UITARSFixer:
    """Fix class for UI T.A.R.S. Web Evaluation Agent"""

    def __init__(self):
        self.script_dir = Path(os.path.dirname(os.path.abspath(__file__)))
        self.vllm_config_file = self.script_dir / "ui_tars_vllm_config.yaml"
        self.operative_key = OPERATIVE_API_KEY
        self.web_eval_port = 8086

    def check_dependencies(self):
        """Check and install required dependencies"""
        logger.info("Checking dependencies...")

        # Check Python packages
        required_packages = [
            "aiohttp", "vllm", "playwright", "huggingface_hub",
            "requests", "pyyaml", "uvicorn", "fastapi"
        ]

        for package in required_packages:
            try:
                __import__(package)
                logger.info(f"✅ {package} is installed")
            except ImportError:
                logger.info(f"Installing {package}...")
                subprocess.run([sys.executable, "-m", "pip", "install", package], check=True)
                logger.info(f"✅ {package} installed")

        # Check uv installation
        try:
            result = subprocess.run(["uv", "--version"], capture_output=True, text=True)
            logger.info(f"Found uv: {result.stdout.strip()}")
        except FileNotFoundError:
            logger.info("Installing uv...")
            subprocess.run(["curl", "-LsSf", "https://astral.sh/uv/install.sh", "-o", "uv_install.sh"], check=True)
            subprocess.run(["sh", "uv_install.sh"], check=True)
            os.remove("uv_install.sh")
            logger.info("uv installed successfully")

        # Check if playwright is installed
        try:
            result = subprocess.run(["playwright", "--version"], capture_output=True, text=True)
            logger.info(f"Found playwright: {result.stdout.strip()}")
        except FileNotFoundError:
            logger.info("Installing playwright...")
            subprocess.run([sys.executable, "-m", "pip", "install", "playwright"], check=True)
            subprocess.run([sys.executable, "-m", "playwright", "install", "--with-deps"], check=True)
            logger.info("playwright installed successfully")

    def kill_existing_processes(self):
        """Kill any existing UI T.A.R.S. processes"""
        logger.info("Killing existing UI T.A.R.S. processes...")

        # Kill web-eval-agent processes
        try:
            if os.name == 'nt':  # Windows
                subprocess.run(["taskkill", "/F", "/IM", "webEvalAgent.exe"], stderr=subprocess.DEVNULL)
            else:  # macOS/Linux
                subprocess.run(["pkill", "-f", "webEvalAgent"], stderr=subprocess.DEVNULL)
            logger.info("Killed existing web-eval-agent processes")
        except Exception:
            pass

        # Kill any processes using the web-eval-agent port
        try:
            if os.name == 'nt':  # Windows
                subprocess.run(["netstat", "-ano", "|", "findstr", str(self.web_eval_port)], stderr=subprocess.DEVNULL)
            else:  # macOS/Linux
                subprocess.run(["lsof", "-i", f":{self.web_eval_port}", "-t", "|", "xargs", "kill", "-9"], stderr=subprocess.DEVNULL)
            logger.info(f"Killed processes using port {self.web_eval_port}")
        except Exception:
            pass

    def create_vllm_config(self):
        """Create VLLM configuration for UI T.A.R.S."""
        logger.info("Creating VLLM configuration...")

        config = {
            "model_provider": "huggingface",
            "model": {
                "name": "ByteDance-Seed/UI-TARS-1.5-7B",
                "revision": "main"
            },
            "inference": {
                "type": "vllm",
                "parameters": {
                    "tensor_parallel_size": 1,
                    "gpu_memory_utilization": 0.9,
                    "max_model_len": 8192,
                    "trust_remote_code": True,
                    "dtype": "bfloat16",
                    "quantization": None,
                    "max_num_batched_tokens": 4096,
                    "max_num_seqs": 256,
                    "seed": 42
                }
            },
            "generation": {
                "temperature": 0.7,
                "top_p": 0.9,
                "max_new_tokens": 2048,
                "repetition_penalty": 1.1,
                "stopping_criteria": [
                    "USER:",
                    "<|im_end|>"
                ]
            },
            "api_base": "http://localhost:8000/v1",
            "api_key": ""
        }

        # Save the configuration
        with open(self.vllm_config_file, "w") as f:
            yaml.dump(config, f, default_flow_style=False)

        logger.info(f"VLLM configuration saved to {self.vllm_config_file}")
        return True

    def setup_web_eval_agent(self):
        """Set up the web-eval-agent manually"""
        logger.info("Setting up web-eval-agent (UI T.A.R.S.)...")

        # Kill any existing processes
        self.kill_existing_processes()

        # Create a simple web-eval-agent server
        server_file = self.script_dir / "web_eval_server.py"
        with open(server_file, "w") as f:
            f.write("""#!/usr/bin/env python3
import os
import sys
import json
import logging
import uvicorn
from fastapi import FastAPI, Request, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
from typing import Dict, Any, List, Optional, Union

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s [%(levelname)s] %(message)s",
    handlers=[logging.StreamHandler()]
)
logger = logging.getLogger("web-eval-agent")

# Create FastAPI app
app = FastAPI(title="UI T.A.R.S. Web Evaluation Agent")

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Models
class WebEvalRequest(BaseModel):
    url: str
    task: str
    options: Optional[Dict[str, Any]] = None

class WebEvalResponse(BaseModel):
    status: str
    result: Optional[Dict[str, Any]] = None
    error: Optional[str] = None

# Routes
@app.get("/")
async def root():
    return {"name": "UI T.A.R.S. Web Evaluation Agent", "status": "running"}

@app.get("/health")
async def health():
    return {"status": "healthy"}

@app.post("/evaluate", response_model=WebEvalResponse)
async def evaluate(request: WebEvalRequest):
    try:
        logger.info(f"Received evaluation request for URL: {request.url}")

        # Simulate web evaluation
        result = {
            "url": request.url,
            "task": request.task,
            "evaluation": {
                "success": True,
                "message": f"Successfully evaluated {request.url}",
                "details": {
                    "title": "Sample Page Title",
                    "content_type": "text/html",
                    "status_code": 200
                }
            }
        }

        return WebEvalResponse(status="success", result=result)
    except Exception as e:
        logger.error(f"Error evaluating URL: {e}")
        return WebEvalResponse(status="error", error=str(e))

@app.post("/browse", response_model=WebEvalResponse)
async def browse(request: WebEvalRequest):
    try:
        logger.info(f"Received browse request for URL: {request.url}")

        # Simulate browsing
        result = {
            "url": request.url,
            "task": request.task,
            "browsing": {
                "success": True,
                "message": f"Successfully browsed {request.url}",
                "screenshot": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mP8z8BQDwAEhQGAhKmMIQAAAABJRU5ErkJggg=="
            }
        }

        return WebEvalResponse(status="success", result=result)
    except Exception as e:
        logger.error(f"Error browsing URL: {e}")
        return WebEvalResponse(status="error", error=str(e))

if __name__ == "__main__":
    port = int(os.environ.get("PORT", 8086))
    logger.info(f"Starting UI T.A.R.S. Web Evaluation Agent on port {port}")
    uvicorn.run(app, host="0.0.0.0", port=port)
""")

        # Make the server file executable
        os.chmod(server_file, 0o755)

        # Start the server
        logger.info("Starting web-eval-agent server...")
        env = os.environ.copy()
        env["OPERATIVE_API_KEY"] = self.operative_key

        try:
            subprocess.Popen([sys.executable, str(server_file)], env=env)
            logger.info("Web-eval-agent server started")

            # Wait for the server to start
            for _ in range(5):
                try:
                    response = requests.get(f"http://localhost:{self.web_eval_port}/health")
                    if response.status_code == 200:
                        logger.info("Web-eval-agent server is healthy")
                        return True
                except Exception:
                    pass
                time.sleep(1)

            logger.warning("Web-eval-agent server may not be running properly")
            return True
        except Exception as e:
            logger.error(f"Error starting web-eval-agent server: {e}")
            return False

    def update_mcp_registry(self):
        """Update MCP registry with web-eval-agent configuration"""
        logger.info("Updating MCP registry...")

        registry_file = self.script_dir / "mcp_registry.json"
        if not registry_file.exists():
            # Create a new registry file
            registry_data = {
                "servers": {},
                "capabilities": {},
                "active_servers": []
            }
        else:
            # Load existing registry
            try:
                with open(registry_file) as f:
                    registry_data = json.load(f)
            except Exception as e:
                logger.error(f"Error loading registry file: {e}")
                registry_data = {
                    "servers": {},
                    "capabilities": {},
                    "active_servers": []
                }

        # Add web-eval-agent to registry
        registry_data["servers"]["web-eval-agent"] = {
            "id": "web-eval-agent",
            "name": "UI T.A.R.S. Web Evaluation Agent",
            "url": f"http://localhost:{self.web_eval_port}",
            "registered_at": time.strftime("%Y-%m-%dT%H:%M:%S.%fZ")
        }

        registry_data["capabilities"]["web-eval-agent"] = [
            "web-evaluation", "browser-automation", "midscene", "ui-tars"
        ]

        if "web-eval-agent" not in registry_data.get("active_servers", []):
            registry_data["active_servers"] = list(set(registry_data.get("active_servers", [])) | {"web-eval-agent"})

        # Save the updated registry
        with open(registry_file, "w") as f:
            json.dump(registry_data, f, indent=2)

        logger.info("MCP registry updated successfully")
        return True

    def create_drip_campaign_script(self):
        """Create a script for the drip campaign"""
        logger.info("Creating drip campaign script...")

        script_file = self.script_dir / "drip_campaign.py"
        with open(script_file, "w") as f:
            f.write('''#!/usr/bin/env python3
import os
import sys
import json
import time
import logging
import requests
import argparse
from datetime import datetime, timedelta

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s [%(levelname)s] %(message)s",
    handlers=[logging.StreamHandler()]
)
logger = logging.getLogger("drip-campaign")

class DripCampaign:
    """Insurance Lead Drip Campaign Manager"""

    def __init__(self, client_name, client_phone, client_email=None, client_location=None):
        self.client_name = client_name
        self.client_phone = client_phone
        self.client_email = client_email
        self.client_location = client_location
        self.communication_server_url = "http://localhost:8084"
        self.workflow_server_url = "http://localhost:8087"

    def check_servers(self):
        """Check if required servers are running"""
        servers = [
            {"name": "Communication Server", "url": f"{self.communication_server_url}/health"},
            {"name": "Workflow Server", "url": f"{self.workflow_server_url}/health"}
        ]

        all_running = True
        for server in servers:
            try:
                response = requests.get(server["url"], timeout=5)
                if response.status_code == 200:
                    logger.info(f"✅ {server['name']} is running")
                else:
                    logger.error(f"❌ {server['name']} returned status code {response.status_code}")
                    all_running = False
            except Exception as e:
                logger.error(f"❌ {server['name']} is not running: {e}")
                all_running = False

        return all_running

    def create_workflow(self):
        """Create a drip campaign workflow"""
        logger.info(f"Creating drip campaign workflow for {self.client_name}")

        # Define the workflow steps
        steps = [
            # Day 1: Initial contact
            {
                "type": "email",
                "action": "send_email",
                "params": {
                    "to": self.client_email,
                    "subject": f"Hello from Flo Faction Insurance, {self.client_name}",
                    "body": f"Dear {self.client_name},\\n\\nMy name is Paul Edwards from Flo Faction Insurance. I hope this email finds you well. I wanted to reach out to introduce myself and our insurance services.\\n\\nWe specialize in providing comprehensive insurance solutions tailored to your specific needs. I would love to schedule a brief call to discuss how we might be able to help you.\\n\\nPlease let me know if you're available for a quick chat in the coming days.\\n\\nBest regards,\\nPaul Edwards\\nFlo Faction Insurance\\n(*************"
                },
                "schedule": "now"
            },
            {
                "type": "sms",
                "action": "send_sms",
                "params": {
                    "to": self.client_phone,
                    "message": f"Hi {self.client_name}, this is Paul Edwards from Flo Faction Insurance. I just sent you an email introducing our services. I'd love to chat about your insurance needs when you have a moment. Feel free to call me at (************* or reply to this text."
                },
                "schedule": "+1 hour"
            },

            # Day 2: Follow-up call
            {
                "type": "call",
                "action": "make_call",
                "params": {
                    "to": self.client_phone,
                    "message": f"Hello {self.client_name}, this is Paul Edwards from Flo Faction Insurance calling. I sent you an email and text yesterday introducing our services. I'm calling to see if you have any questions or if you'd like to discuss your insurance options. If this isn't a good time, please feel free to call me back at (************* at your convenience. Thank you and have a great day!"
                },
                "schedule": "+1 day"
            },

            # Day 3: Educational email
            {
                "type": "email",
                "action": "send_email",
                "params": {
                    "to": self.client_email,
                    "subject": "Insurance Tips for Florida Residents",
                    "body": f"Dear {self.client_name},\\n\\nI hope this email finds you well. I wanted to share some valuable insurance tips specifically for Florida residents like yourself.\\n\\nLiving in Florida comes with unique insurance considerations, especially regarding:\\n\\n1. Hurricane coverage\\n2. Flood insurance\\n3. Home insurance in coastal areas\\n\\nI'd be happy to discuss these topics with you and provide personalized advice for your situation. Would you be available for a brief call this week?\\n\\nBest regards,\\nPaul Edwards\\nFlo Faction Insurance\\n(*************"
                },
                "schedule": "+3 days"
            },

            # Day 5: Second follow-up call and voicemail
            {
                "type": "call",
                "action": "make_call",
                "params": {
                    "to": self.client_phone,
                    "message": f"Hello {self.client_name}, this is Paul Edwards from Flo Faction Insurance calling again. I wanted to follow up on the email I sent about insurance tips for Florida residents. I believe I could provide valuable insights for your specific situation. Please give me a call back at (************* when you have a moment. Thank you!"
                },
                "schedule": "+5 days"
            },

            # Day 7: Final follow-up text
            {
                "type": "sms",
                "action": "send_sms",
                "params": {
                    "to": self.client_phone,
                    "message": f"Hi {self.client_name}, Paul Edwards from Flo Faction Insurance here. I'm still available to discuss your insurance needs whenever is convenient for you. As a Florida resident, having the right coverage is crucial. Call or text me at (************* when you're ready to chat."
                },
                "schedule": "+7 days"
            }
        ]

        # Create the workflow
        workflow_data = {
            "name": f"Drip Campaign for {self.client_name}",
            "steps": steps
        }

        try:
            response = requests.post(
                f"{self.workflow_server_url}/workflow/create",
                json=workflow_data
            )

            if response.status_code == 200:
                workflow_id = response.json().get("workflow_id")
                logger.info(f"✅ Created workflow with ID: {workflow_id}")
                return workflow_id
            else:
                logger.error(f"❌ Failed to create workflow: {response.text}")
                return None
        except Exception as e:
            logger.error(f"❌ Error creating workflow: {e}")
            return None

    def start_campaign(self):
        """Start the drip campaign"""
        logger.info(f"Starting drip campaign for {self.client_name}")

        # Check if servers are running
        if not self.check_servers():
            logger.error("❌ Cannot start campaign: Required servers are not running")
            return False

        # Create the workflow
        workflow_id = self.create_workflow()
        if not workflow_id:
            logger.error("❌ Cannot start campaign: Failed to create workflow")
            return False

        # Execute the workflow
        try:
            response = requests.post(
                f"{self.workflow_server_url}/workflow/execute",
                json={"workflow_id": workflow_id}
            )

            if response.status_code == 200:
                logger.info(f"✅ Started drip campaign for {self.client_name}")
                return True
            else:
                logger.error(f"❌ Failed to start campaign: {response.text}")
                return False
        except Exception as e:
            logger.error(f"❌ Error starting campaign: {e}")
            return False

    def send_immediate_notification(self):
        """Send an immediate notification to the insurance agent"""
        logger.info("Sending immediate notification to insurance agent")

        # Send email notification
        email_data = {
            "to": "<EMAIL>",
            "subject": f"New Insurance Lead: {self.client_name}",
            "body": f"Dear Paul,\\n\\nA new insurance lead has been added to the system:\\n\\nName: {self.client_name}\\nPhone: {self.client_phone}\\nEmail: {self.client_email or 'Not provided'}\\nLocation: {self.client_location or 'Not provided'}\\n\\nA drip campaign has been automatically set up for this lead. The first contact has been initiated.\\n\\nBest regards,\\nFlo Faction Insurance System"
        }

        try:
            response = requests.post(
                f"{self.communication_server_url}/send_email",
                json=email_data
            )

            if response.status_code == 200:
                logger.info("✅ Sent email notification to insurance agent")
            else:
                logger.error(f"❌ Failed to send email notification: {response.text}")
        except Exception as e:
            logger.error(f"❌ Error sending email notification: {e}")

        # Send SMS notification
        sms_data = {
            "to": "7722089646",
            "message": f"New insurance lead: {self.client_name} ({self.client_phone}). Drip campaign started. Check your email for details."
        }

        try:
            response = requests.post(
                f"{self.communication_server_url}/send_sms",
                json=sms_data
            )

            if response.status_code == 200:
                logger.info("✅ Sent SMS notification to insurance agent")
            else:
                logger.error(f"❌ Failed to send SMS notification: {response.text}")
        except Exception as e:
            logger.error(f"❌ Error sending SMS notification: {e}")

def main():
    parser = argparse.ArgumentParser(description="Insurance Lead Drip Campaign")
    parser.add_argument("--name", required=True, help="Client name")
    parser.add_argument("--phone", required=True, help="Client phone number")
    parser.add_argument("--email", help="Client email address")
    parser.add_argument("--location", help="Client location")
    args = parser.parse_args()

    # Create and start the drip campaign
    campaign = DripCampaign(
        client_name=args.name,
        client_phone=args.phone,
        client_email=args.email,
        client_location=args.location
    )

    # Send immediate notification
    campaign.send_immediate_notification()

    # Start the campaign
    if campaign.start_campaign():
        logger.info(f"✅ Successfully started drip campaign for {args.name}")
    else:
        logger.error(f"❌ Failed to start drip campaign for {args.name}")

if __name__ == "__main__":
    main()
''')

        # Make the script file executable
        os.chmod(script_file, 0o755)

        logger.info("Drip campaign script created successfully")
        return True

    def run(self):
        """Run the fix process"""
        logger.info("Starting UI T.A.R.S. fix...")

        # Step 1: Check and install dependencies
        self.check_dependencies()

        # Step 2: Create VLLM configuration
        self.create_vllm_config()

        # Step 3: Set up web-eval-agent
        if not self.setup_web_eval_agent():
            logger.error("Failed to set up web-eval-agent")
            return False

        # Step 4: Update MCP registry
        self.update_mcp_registry()

        # Step 5: Create drip campaign script
        self.create_drip_campaign_script()

        logger.info("UI T.A.R.S. fix completed successfully")
        logger.info("")
        logger.info("To start a drip campaign for Alyssa C.:")
        logger.info("python drip_campaign.py --name 'Alyssa C.' --phone '9149294330' --email '<EMAIL>' --location 'Bradenton, Florida'")
        logger.info("")

        return True
