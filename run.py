#!/usr/bin/env python3
"""
Convenience runner script for agent system operations
"""

import asyncio
import sys
import argparse
import subprocess
from pathlib import Path
from typing import List, Optional
import psutil
import yaml
import logging
from setup_system import SystemSetup

class SystemRunner:
    """Manages agent system operations"""
    
    def __init__(self):
        self.logger = self._setup_logging()
        self.config_path = Path("config/config.yml")
        self.pid_file = Path("data/system.pid")
        
    def _setup_logging(self) -> logging.Logger:
        """Set up logging"""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s [%(levelname)s] %(message)s'
        )
        return logging.getLogger("system_runner")
        
    def setup(self, force: bool = False):
        """Run system setup"""
        if self.config_path.exists() and not force:
            self.logger.error(
                "System already set up. Use --force to override."
            )
            return 1
            
        try:
            setup = SystemSetup(self.config_path)
            setup.create_directory_structure()
            config = setup.generate_config()
            setup.save_config(config)
            setup.setup_database(config["database"])
            setup.setup_redis(config["redis"])
            setup.create_admin_user(config)
            
            self.logger.info("System setup completed successfully")
            return 0
            
        except Exception as e:
            self.logger.error(f"Setup failed: {str(e)}")
            return 1
            
    def start(self, config: Optional[str] = None, plugins: Optional[str] = None):
        """Start the system"""
        if self.is_running():
            self.logger.error("System is already running")
            return 1
            
        try:
            cmd = [sys.executable, "main.py"]
            if config:
                cmd.extend(["-c", config])
            if plugins:
                cmd.extend(["-p", plugins])
                
            # Start process
            process = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE
            )
            
            # Write PID file
            with open(self.pid_file, 'w') as f:
                f.write(str(process.pid))
                
            self.logger.info(f"System started (PID: {process.pid})")
            return 0
            
        except Exception as e:
            self.logger.error(f"Start failed: {str(e)}")
            return 1
            
    def stop(self):
        """Stop the system"""
        if not self.is_running():
            self.logger.error("System is not running")
            return 1
            
        try:
            # Read PID
            with open(self.pid_file) as f:
                pid = int(f.read().strip())
                
            # Kill process
            process = psutil.Process(pid)
            process.terminate()
            
            # Wait for process to end
            try:
                process.wait(timeout=10)
            except psutil.TimeoutExpired:
                process.kill()
                
            # Remove PID file
            self.pid_file.unlink()
            
            self.logger.info("System stopped")
            return 0
            
        except Exception as e:
            self.logger.error(f"Stop failed: {str(e)}")
            return 1
            
    def restart(self):
        """Restart the system"""
        if self.stop() == 0:
            return self.start()
        return 1
        
    def status(self):
        """Check system status"""
        try:
            if not self.is_running():
                self.logger.info("System is not running")
                return 0
                
            # Read PID
            with open(self.pid_file) as f:
                pid = int(f.read().strip())
                
            # Get process info
            process = psutil.Process(pid)
            
            # Get system config
            with open(self.config_path) as f:
                config = yaml.safe_load(f)
                
            # Print status
            print("\nSystem Status")
            print("-------------")
            print(f"Status: Running")
            print(f"PID: {pid}")
            print(f"Environment: {config.get('environment', 'unknown')}")
            print(f"CPU Usage: {process.cpu_percent()}%")
            memory = process.memory_info()
            print(f"Memory Usage: {memory.rss / 1024 / 1024:.1f} MB")
            print(f"Running Time: {self._format_time(process.create_time())}")
            
            return 0
            
        except Exception as e:
            self.logger.error(f"Status check failed: {str(e)}")
            return 1
            
    def logs(self, n: int = 100):
        """Show system logs"""
        try:
            log_file = Path("logs/agent_system/agent_system.log")
            if not log_file.exists():
                self.logger.error("Log file not found")
                return 1
                
            # Read last N lines
            with open(log_file) as f:
                lines = f.readlines()
                for line in lines[-n:]:
                    print(line.strip())
                    
            return 0
            
        except Exception as e:
            self.logger.error(f"Error reading logs: {str(e)}")
            return 1
            
    def cleanup(self, force: bool = False):
        """Clean up system data"""
        if self.is_running() and not force:
            self.logger.error(
                "System is running. Stop it first or use --force"
            )
            return 1
            
        try:
            # Remove data directories
            dirs = [
                Path("data"),
                Path("logs")
            ]
            
            for directory in dirs:
                if directory.exists():
                    for item in directory.glob("**/*"):
                        if item.is_file():
                            item.unlink()
                        elif item.is_dir():
                            item.rmdir()
                    directory.rmdir()
                    
            self.logger.info("Cleanup completed")
            return 0
            
        except Exception as e:
            self.logger.error(f"Cleanup failed: {str(e)}")
            return 1
            
    def is_running(self) -> bool:
        """Check if system is running"""
        if not self.pid_file.exists():
            return False
            
        try:
            with open(self.pid_file) as f:
                pid = int(f.read().strip())
            return psutil.pid_exists(pid)
        except:
            return False
            
    def _format_time(self, timestamp: float) -> str:
        """Format timestamp into readable duration"""
        import time
        from datetime import datetime, timedelta
        
        duration = time.time() - timestamp
        return str(timedelta(seconds=int(duration)))

def main():
    """Main entry point"""
    parser = argparse.ArgumentParser(
        description="Agent System Runner"
    )
    
    subparsers = parser.add_subparsers(
        dest="command",
        help="Command to run"
    )
    
    # Setup command
    setup_parser = subparsers.add_parser(
        "setup",
        help="Set up the system"
    )
    setup_parser.add_argument(
        "--force",
        action="store_true",
        help="Force setup even if already configured"
    )
    
    # Start command
    start_parser = subparsers.add_parser(
        "start",
        help="Start the system"
    )
    start_parser.add_argument(
        "-c",
        "--config",
        help="Path to config file"
    )
    start_parser.add_argument(
        "-p",
        "--plugins",
        help="Path to plugins directory"
    )
    
    # Stop command
    subparsers.add_parser(
        "stop",
        help="Stop the system"
    )
    
    # Restart command
    subparsers.add_parser(
        "restart",
        help="Restart the system"
    )
    
    # Status command
    subparsers.add_parser(
        "status",
        help="Show system status"
    )
    
    # Logs command
    logs_parser = subparsers.add_parser(
        "logs",
        help="Show system logs"
    )
    logs_parser.add_argument(
        "-n",
        type=int,
        default=100,
        help="Number of lines to show"
    )
    
    # Cleanup command
    cleanup_parser = subparsers.add_parser(
        "cleanup",
        help="Clean up system data"
    )
    cleanup_parser.add_argument(
        "--force",
        action="store_true",
        help="Force cleanup even if system is running"
    )
    
    args = parser.parse_args()
    
    if not args.command:
        parser.print_help()
        return 1
        
    runner = SystemRunner()
    
    if args.command == "setup":
        return runner.setup(args.force)
    elif args.command == "start":
        return runner.start(args.config, args.plugins)
    elif args.command == "stop":
        return runner.stop()
    elif args.command == "restart":
        return runner.restart()
    elif args.command == "status":
        return runner.status()
    elif args.command == "logs":
        return runner.logs(args.n)
    elif args.command == "cleanup":
        return runner.cleanup(args.force)
        
    return 1

if __name__ == "__main__":
    sys.exit(main())