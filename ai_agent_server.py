#!/usr/bin/env python3
"""
AI Agent MCP Server (Port 8081)

This module provides MCP server functionality for AI agent tools and code execution.
"""

import os
import sys
import json
import logging
import asyncio
import argparse
import subprocess
from pathlib import Path
from typing import Dict, List, Optional, Union, Any
from aiohttp import web

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s [%(levelname)s] %(name)s: %(message)s",
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler("ai_agent_server.log")
    ]
)
logger = logging.getLogger("ai-agent-server")

# Add debug logging to capture startup issues
logger.setLevel(logging.DEBUG)
logger.debug("Debug mode enabled for AI Agent MCP Server")

class AIAgentServer:
    """MCP Server implementation for AI agent tools"""
    
    def __init__(self, host: str = "localhost", port: int = 8081):
        self.host = host
        self.port = port
        self.app = web.Application()
        self.register_routes()
        
    def register_routes(self):
        """Register API routes"""
        self.app.router.add_get("/", self.handle_root)
        self.app.router.add_get("/health", self.handle_health)
        self.app.router.add_get("/capabilities", self.handle_capabilities)
        self.app.router.add_post("/execute", self.handle_execute)
        self.app.router.add_post("/agent_task", self.handle_agent_task)

    async def handle_root(self, request):
        """Handle root endpoint"""
        return web.json_response({
            "name": "AI Agent MCP Server",
            "version": "1.0.0",
            "status": "running",
            "port": self.port
        })
        
    async def handle_health(self, request):
        """Handle health check endpoint"""
        return web.json_response({
            "status": "healthy",
            "timestamp": asyncio.get_event_loop().time(),
            "capabilities": ["agent-tools", "code-execution"]
        })
        
    async def handle_capabilities(self, request):
        """Handle capabilities endpoint"""
        return web.json_response({
            "capabilities": [
                "agent-tools",
                "code-execution",
                "task-automation",
                "system-integration"
            ]
        })
        
    async def handle_execute(self, request):
        """Handle code execution endpoint"""
        try:
            data = await request.json()
            code = data.get("code", "")
            language = data.get("language", "python")
            
            # Execute code safely (placeholder implementation)
            result = await self._execute_code(code, language)
            
            return web.json_response({
                "status": "success",
                "result": result,
                "code": code,
                "language": language
            })
            
        except Exception as e:
            logger.error(f"Error executing code: {e}")
            return web.json_response({
                "status": "error",
                "message": str(e)
            }, status=500)
            
    async def handle_agent_task(self, request):
        """Handle agent task endpoint"""
        try:
            data = await request.json()
            task_type = data.get("task_type", "")
            task_data = data.get("data", {})
            
            # Process agent task
            result = await self._process_agent_task(task_type, task_data)
            
            return web.json_response({
                "status": "success",
                "result": result,
                "task_type": task_type
            })
            
        except Exception as e:
            logger.error(f"Error processing agent task: {e}")
            return web.json_response({
                "status": "error",
                "message": str(e)
            }, status=500)
    
    async def _execute_code(self, code: str, language: str) -> str:
        """Execute code safely (placeholder implementation)"""
        if language == "python":
            # For safety, only allow simple print statements
            if "print(" in code and not any(dangerous in code for dangerous in ["import", "exec", "eval", "open", "file"]):
                try:
                    # Create a simple namespace for execution
                    namespace = {"__builtins__": {"print": print}}
                    exec(code, namespace)
                    return "Code executed successfully"
                except Exception as e:
                    return f"Execution error: {str(e)}"
            else:
                return "Code execution restricted for security"
        else:
            return f"Language {language} not supported for execution"
    
    async def _process_agent_task(self, task_type: str, task_data: Dict) -> Dict:
        """Process agent task (placeholder implementation)"""
        task_handlers = {
            "file_operation": self._handle_file_operation,
            "system_command": self._handle_system_command,
            "data_processing": self._handle_data_processing,
            "api_call": self._handle_api_call
        }
        
        handler = task_handlers.get(task_type, self._handle_unknown_task)
        return await handler(task_data)
    
    async def _handle_file_operation(self, data: Dict) -> Dict:
        """Handle file operations"""
        operation = data.get("operation", "")
        file_path = data.get("file_path", "")
        
        if operation == "read" and file_path:
            try:
                # Only allow reading from safe directories
                if not file_path.startswith("/") and ".." not in file_path:
                    with open(file_path, 'r') as f:
                        content = f.read()
                    return {"status": "success", "content": content}
                else:
                    return {"status": "error", "message": "File path not allowed"}
            except Exception as e:
                return {"status": "error", "message": str(e)}
        
        return {"status": "error", "message": "Unsupported file operation"}
    
    async def _handle_system_command(self, data: Dict) -> Dict:
        """Handle system commands (restricted)"""
        command = data.get("command", "")
        
        # Only allow safe commands
        safe_commands = ["ls", "pwd", "date", "whoami"]
        if command.split()[0] in safe_commands:
            try:
                result = subprocess.run(command, shell=True, capture_output=True, text=True, timeout=10)
                return {
                    "status": "success",
                    "stdout": result.stdout,
                    "stderr": result.stderr,
                    "returncode": result.returncode
                }
            except Exception as e:
                return {"status": "error", "message": str(e)}
        
        return {"status": "error", "message": "Command not allowed"}
    
    async def _handle_data_processing(self, data: Dict) -> Dict:
        """Handle data processing tasks"""
        return {
            "status": "success",
            "message": "Data processing task completed",
            "processed_items": len(data.get("items", []))
        }
    
    async def _handle_api_call(self, data: Dict) -> Dict:
        """Handle API calls"""
        return {
            "status": "success",
            "message": "API call simulated",
            "endpoint": data.get("endpoint", "unknown")
        }
    
    async def _handle_unknown_task(self, data: Dict) -> Dict:
        """Handle unknown task types"""
        return {
            "status": "error",
            "message": "Unknown task type"
        }
            
    async def start(self):
        """Start the server"""
        runner = web.AppRunner(self.app)
        await runner.setup()
        site = web.TCPSite(runner, self.host, self.port)
        
        logger.info(f"Starting AI Agent MCP Server on {self.host}:{self.port}")
        await site.start()
        
        return site

def main():
    """Main entry point"""
    parser = argparse.ArgumentParser(description="AI Agent MCP Server")
    parser.add_argument("--host", default="localhost", help="Host to bind to")
    parser.add_argument("--port", type=int, default=8081, help="Port to bind to")
    args = parser.parse_args()
    
    server = AIAgentServer(host=args.host, port=args.port)
    
    loop = asyncio.get_event_loop()
    try:
        loop.run_until_complete(server.start())
        loop.run_forever()
    except KeyboardInterrupt:
        logger.info("Server stopped by keyboard interrupt")
    finally:
        loop.close()

if __name__ == "__main__":
    main()
