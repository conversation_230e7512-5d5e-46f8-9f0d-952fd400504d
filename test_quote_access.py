import asyncio
import logging
from datetime import datetime
from quote_helper import QuoteHelper
from client_template import ClientManager

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def test_quote_access():
    """Test direct access to quote platforms"""
    
    # Initialize client data
    test_client = {
        "name": "John Medicare Test",
        "dob": "1955-06-15",
        "height": "5'10\"",
        "weight": "175",
        "gender": "M",
        "phone": "************",
        "email": "<EMAIL>",
        "address": "123 Senior Way, Port St. Lucie, FL 34952",
        "ssn": "***********",
        "drivers_license": "FL123456789",
        
        "medications": [
            {
                "drug_name": "Metformin",
                "dosage": "500mg",
                "frequency": "twice daily"
            }
        ],
        
        "tobacco_use": False,
        "marijuana_use": False,
        
        "desired_coverage": "250000",
        "product_type": "Medicare Supplement"
    }
    
    # Test MOO quote platform
    logger.info("\nTesting Mutual of Omaha quote access...")
    moo_data = QuoteHelper.format_client_data(test_client, "mutual_of_omaha")
    logger.info("Formatted data for MOO:")
    logger.info(f"{moo_data}")
    
    success = QuoteHelper.open_quote_platform("mutual_of_omaha")
    if success:
        logger.info("Successfully opened MOO quote platform")
        
    # Update data for FFL
    test_client["product_type"] = "Term Life"
    test_client["desired_coverage"] = "250000"
    
    # Test FFL quote platform
    logger.info("\nTesting FFL Trident quote access...")
    ffl_data = QuoteHelper.format_client_data(test_client, "ffl_trident")
    logger.info("Formatted data for FFL:")
    logger.info(f"{ffl_data}")
    
    success = QuoteHelper.open_quote_platform("ffl_trident")
    if success:
        logger.info("Successfully opened FFL quote platform")
    
    # Show all available quote URLs
    logger.info("\nAvailable Quote Platforms:")
    urls = QuoteHelper.get_quote_urls()
    for carrier, url in urls.items():
        logger.info(f"{carrier.upper()}: {url}")

async def main():
    """Run quote platform tests"""
    logger.info("=== Testing Quote Platform Access ===")
    await test_quote_access()

if __name__ == "__main__":
    asyncio.run(main())