#!/bin/bash
# IRIS Quick Start Script

echo "🚀 IRIS System Quick Start"
echo "=========================="

# Check Python version
python_version=$(python3 --version 2>&1)
echo "Python: $python_version"

# Check if virtual environment is active
if [[ "$VIRTUAL_ENV" != "" ]]; then
    echo "✅ Virtual environment: $VIRTUAL_ENV"
else
    echo "⚠️ No virtual environment detected"
    echo "Consider activating a virtual environment first"
fi

# Install dependencies if needed
echo "📦 Checking dependencies..."
pip install -q -r requirements.txt 2>/dev/null || echo "⚠️ Some dependencies may be missing"

# Start IRIS
echo "🚀 Starting IRIS..."
python3 iris_launcher.py
