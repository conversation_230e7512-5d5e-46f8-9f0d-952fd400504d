<!DOCTYPE html><html><head><title>Agentic Dashboard</title></head><body><h1>Agentic Workflow Dashboard</h1><form id="workflowForm"><label>Workflow Type: <input name="workflow_type" required></label><br><label>Data (JSON): <textarea name="data" required>{}</textarea></label><br><button type="submit">Submit Workflow</button></form><div id="result"></div><script>document.getElementById(workflowForm).onsubmit = async function(e){e.preventDefault();const type=this.workflow_type.value;const data=JSON.parse(this.data.value);const res=await fetch("/submit_workflow",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({workflow_type:type,data})});const json=await res.json();document.getElementById(result).innerText=JSON.stringify(json,null,2);};</script></body></html>
