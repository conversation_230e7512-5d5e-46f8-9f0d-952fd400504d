#!/usr/bin/env python3
"""
UI T.A.R.S. Setup Script

This script sets up the UI T.A.R.S. web evaluation agent with the proper
VLLM configuration and dependencies to work with MidScene for Chrome automation.
"""

import os
import sys
import subprocess
import json
import time
import shutil
import requests
import socket
from pathlib import Path
import logging
from cryptography.fernet import Fernet

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s [%(levelname)s] %(message)s",
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler("ui_tars_setup.log")
    ]
)
logger = logging.getLogger("ui-tars-setup")

# Set the Operative API key
OPERATIVE_API_KEY = "op-sMxrcBrpW8uyzqeL8mg9N9SiDNY_ijY6kB8QUbiFj1I"
os.environ["OPERATIVE_API_KEY"] = OPERATIVE_API_KEY

class UITARSSetup:
    """Setup class for UI T.A.R.S. Web Evaluation Agent"""

    def __init__(self):
        self.script_dir = Path(os.path.dirname(os.path.abspath(__file__)))
        self.agent_dir = self.script_dir / "web-eval-agent"
        self.vllm_config_file = self.script_dir / "vllm_config.json"
        self.credential_file = self.script_dir / "credential_store.json"
        self.fernet_key_file = self.script_dir / "fernet_config.txt"
        self.operative_key = OPERATIVE_API_KEY

    def check_dependencies(self):
        """Check and install required dependencies"""
        logger.info("Checking dependencies...")

        # Check uv installation
        try:
            result = subprocess.run(["uv", "--version"], capture_output=True, text=True)
            logger.info(f"Found uv: {result.stdout.strip()}")
        except FileNotFoundError:
            logger.info("Installing uv...")
            subprocess.run(["curl", "-LsSf", "https://astral.sh/uv/install.sh", "-o", "uv_install.sh"], check=True)
            subprocess.run(["sh", "uv_install.sh"], check=True)
            os.remove("uv_install.sh")
            logger.info("uv installed successfully")

        # Check if playwright is installed
        try:
            result = subprocess.run(["playwright", "--version"], capture_output=True, text=True)
            logger.info(f"Found playwright: {result.stdout.strip()}")
        except FileNotFoundError:
            logger.info("Installing playwright...")
            subprocess.run(["npm", "install", "-g", "chromium", "playwright"], check=True)
            subprocess.run(["uvx", "--with", "playwright", "playwright", "install", "--with-deps"], check=True)
            logger.info("playwright installed successfully")

    def setup_web_eval_agent(self):
        """Install or update web-eval-agent"""
        logger.info("Setting up web-eval-agent (UI T.A.R.S.)...")

        # First, check if the web-eval-agent is already running
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            result = sock.connect_ex(('localhost', 8086))
            sock.close()

            if result == 0:
                logger.info("Web-eval-agent is already running on port 8086")
                return True
        except Exception as e:
            logger.warning(f"Error checking if web-eval-agent is running: {e}")

        # Kill any existing web-eval-agent processes
        try:
            if os.name == 'nt':  # Windows
                subprocess.run(["taskkill", "/f", "/im", "webEvalAgent.exe"],
                              capture_output=True, text=True)
            else:  # macOS/Linux
                subprocess.run(["pkill", "-f", "webEvalAgent"],
                              capture_output=True, text=True)
            logger.info("Killed existing web-eval-agent processes")
        except Exception as e:
            logger.warning(f"Error killing existing web-eval-agent processes: {e}")

        # Install or update web-eval-agent
        cmd = [
            "uvx",
            "--refresh-package",
            "webEvalAgent",
            "--from",
            "git+https://github.com/Operative-Sh/web-eval-agent.git",
            "webEvalAgent"
        ]

        env = os.environ.copy()
        env["OPERATIVE_API_KEY"] = self.operative_key

        try:
            logger.info("Running command: " + " ".join(cmd))
            result = subprocess.run(cmd, env=env, capture_output=True, text=True)
            if result.returncode == 0:
                logger.info("UI T.A.R.S. web-eval-agent installed successfully")

                # Start the web-eval-agent
                start_cmd = ["uvx", "webEvalAgent"]
                logger.info("Starting web-eval-agent with command: " + " ".join(start_cmd))

                # Start in background
                if os.name == 'nt':  # Windows
                    subprocess.Popen(start_cmd, env=env,
                                    creationflags=subprocess.CREATE_NEW_CONSOLE)
                else:  # macOS/Linux
                    subprocess.Popen(start_cmd, env=env,
                                    stdout=subprocess.PIPE,
                                    stderr=subprocess.PIPE)

                # Wait for the server to start
                logger.info("Waiting for web-eval-agent to start...")
                for i in range(10):
                    time.sleep(2)
                    try:
                        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
                        result = sock.connect_ex(('localhost', 8086))
                        sock.close()

                        if result == 0:
                            logger.info("Web-eval-agent started successfully on port 8086")
                            return True
                    except Exception:
                        pass

                logger.warning("Web-eval-agent did not start within the expected time")
                return False
            else:
                logger.error(f"Error installing UI T.A.R.S. web-eval-agent: {result.stderr}")
                return False
        except Exception as e:
            logger.error(f"Error setting up UI T.A.R.S. web-eval-agent: {e}")
            return False

    def configure_vllm(self):
        """Configure VLLM settings for optimal MidScene integration"""
        logger.info("Configuring VLLM settings...")

        vllm_config = {
            "model_settings": {
                "default": {
                    "model_path": "local",
                    "tensor_parallel_size": 1,
                    "max_model_len": 8192,
                    "trust_remote_code": True,
                    "gpu_memory_utilization": 0.9,
                    "quantization": "awq"
                }
            },
            "midscene_settings": {
                "enabled": True,
                "chrome_path": "/Applications/Google Chrome.app/Contents/MacOS/Google Chrome",
                "headless": False,
                "timeout_ms": 60000,
                "viewport": {
                    "width": 1280,
                    "height": 800
                },
                "connection_retries": 3,
                "connection_retry_delay": 2000
            },
            "server": {
                "host": "localhost",
                "port": 8086,
                "cors_origin": "*"
            },
            "logging": {
                "level": "debug"
            }
        }

        # Check if Chrome exists in the default location for macOS
        if not os.path.exists(vllm_config["midscene_settings"]["chrome_path"]):
            # Try to find Chrome in common locations
            possible_paths = [
                "/Applications/Google Chrome.app/Contents/MacOS/Google Chrome",
                "/Applications/Chrome.app/Contents/MacOS/Chrome",
                "/usr/bin/google-chrome",
                "/usr/bin/chromium-browser"
            ]

            chrome_found = False
            for path in possible_paths:
                if os.path.exists(path):
                    vllm_config["midscene_settings"]["chrome_path"] = path
                    logger.info(f"Found Chrome at {path}")
                    chrome_found = True
                    break

            if not chrome_found:
                logger.warning("Could not find Chrome. Please install Google Chrome.")
                chrome_path = input("Enter path to Chrome executable (or press Enter to search later): ").strip()
                if chrome_path:
                    vllm_config["midscene_settings"]["chrome_path"] = chrome_path

        # Save the configuration
        with open(self.vllm_config_file, "w") as f:
            json.dump(vllm_config, f, indent=4)

        logger.info(f"VLLM configuration saved to {self.vllm_config_file}")
        return True

    def update_mcp_registry(self):
        """Update MCP registry with web-eval-agent configuration"""
        logger.info("Updating MCP registry...")

        registry_file = self.script_dir / "mcp_registry.json"
        if not registry_file.exists():
            # Create a new registry file
            registry_data = {
                "servers": {},
                "capabilities": {},
                "active_servers": []
            }
        else:
            # Load existing registry
            try:
                with open(registry_file) as f:
                    registry_data = json.load(f)
            except Exception as e:
                logger.error(f"Error loading registry file: {e}")
                registry_data = {
                    "servers": {},
                    "capabilities": {},
                    "active_servers": []
                }

        # Add web-eval-agent to registry
        registry_data["servers"]["web-eval-agent"] = {
            "id": "web-eval-agent",
            "name": "UI T.A.R.S. Web Evaluation Agent",
            "url": "http://localhost:8086",
            "registered_at": time.strftime("%Y-%m-%dT%H:%M:%S.%fZ")
        }

        registry_data["capabilities"]["web-eval-agent"] = [
            "web-evaluation", "browser-automation", "midscene", "ui-tars"
        ]

        if "web-eval-agent" not in registry_data.get("active_servers", []):
            registry_data["active_servers"] = list(set(registry_data.get("active_servers", [])) | {"web-eval-agent"})

        # Save the updated registry
        with open(registry_file, "w") as f:
            json.dump(registry_data, f, indent=2)

        logger.info("MCP registry updated successfully")
        return True

    def store_credentials(self):
        """Store the API key and other credentials securely"""
        logger.info("Storing credentials securely...")

        # Load or generate Fernet key
        fernet_key = None
        if self.fernet_key_file.exists():
            with open(self.fernet_key_file, 'rb') as f:
                fernet_key = f.read()
        else:
            fernet_key = Fernet.generate_key()
            with open(self.fernet_key_file, 'wb') as f:
                f.write(fernet_key)

        cipher = Fernet(fernet_key)

        # Load existing credentials if available
        credentials = {}
        if self.credential_file.exists():
            try:
                with open(self.credential_file, 'r') as f:
                    credentials = json.load(f)
            except Exception as e:
                logger.error(f"Error loading credentials: {e}")

        # Update the Operative API key
        api_credentials = credentials.get("api_keys", {})
        api_credentials["operative"] = self.operative_key
        credentials["api_keys"] = api_credentials

        # Save credentials
        with open(self.credential_file, 'w') as f:
            json.dump(credentials, f, indent=4)

        logger.info("Credentials stored securely")
        return True

    def update_mcp_config(self):
        """Update MCP config file with correct UI T.A.R.S. naming"""
        logger.info("Updating MCP configuration...")

        mcp_config_file = self.script_dir / "mcp_config.json"
        if not mcp_config_file.exists():
            logger.warning("MCP config file not found")
            return False

        try:
            with open(mcp_config_file, 'r') as f:
                mcp_config = json.load(f)

            # Update web-eval-agent server name
            for server in mcp_config.get("servers", []):
                if server.get("server_id") == "web-eval-agent":
                    server["name"] = "UI T.A.R.S. Web Evaluation Agent"
                    break

            with open(mcp_config_file, 'w') as f:
                json.dump(mcp_config, f, indent=4)

            logger.info("MCP config updated successfully")
            return True

        except Exception as e:
            logger.error(f"Error updating MCP config: {e}")
            return False

    def run(self):
        """Run the setup process"""
        logger.info("Starting UI T.A.R.S. setup...")

        # Step 1: Check and install dependencies
        self.check_dependencies()

        # Step 2: Configure VLLM
        self.configure_vllm()

        # Step 3: Set up web-eval-agent
        if not self.setup_web_eval_agent():
            logger.error("Failed to set up web-eval-agent")
            return False

        # Step 4: Update MCP registry
        self.update_mcp_registry()

        # Step 5: Store credentials securely
        self.store_credentials()

        # Step 6: Update MCP config file
        self.update_mcp_config()

        logger.info("UI T.A.R.S. setup completed successfully")
        logger.info("")
        logger.info("To use UI T.A.R.S.:")
        logger.info("1. Run the MCP server: python start_mcp_servers.py")
        logger.info("2. Your UI T.A.R.S. should now connect properly with MidScene")
        logger.info("")

        return True

if __name__ == "__main__":
    setup = UITARSSetup()
    if not setup.run():
        sys.exit(1)