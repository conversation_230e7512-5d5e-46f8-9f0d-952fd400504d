#!/usr/bin/env python3
"""
Cursor Integration for Agent System

This module provides integration between Cursor AI coding assistant and the agent system.
It allows agents to leverage Cursor's capabilities for code generation and analysis.
"""

import os
import json
import subprocess
import tempfile
import logging
from typing import Dict, List, Any, Optional, Union
import asyncio
import httpx

logger = logging.getLogger(__name__)

class CursorIntegration:
    """Integration with Cursor AI coding assistant"""
    
    def __init__(self):
        self.cursor_path = self._find_cursor_path()
        self.temp_dir = tempfile.mkdtemp(prefix="cursor_integration_")
        logger.info(f"Initialized Cursor integration with temp dir: {self.temp_dir}")
        
    def _find_cursor_path(self) -> str:
        """Find the Cursor application path"""
        # Common paths for Cursor on macOS
        mac_paths = [
            "/Applications/Cursor.app/Contents/MacOS/Cursor",
            os.path.expanduser("~/Applications/Cursor.app/Contents/MacOS/Cursor")
        ]
        
        for path in mac_paths:
            if os.path.exists(path):
                return path
                
        # If not found, return empty string
        return ""
        
    def is_cursor_installed(self) -> bool:
        """Check if Cursor is installed"""
        return bool(self.cursor_path)
        
    def install_cursor(self) -> bool:
        """Provide instructions to install Cursor"""
        print("Cursor is not installed. Please follow these steps to install it:")
        print("1. Visit https://cursor.sh/")
        print("2. Download and install Cursor for your operating system")
        print("3. Run this script again after installation")
        return False
        
    def open_project_in_cursor(self, project_path: str) -> bool:
        """Open a project in Cursor"""
        if not self.is_cursor_installed():
            return self.install_cursor()
            
        try:
            subprocess.Popen([self.cursor_path, project_path])
            logger.info(f"Opened project in Cursor: {project_path}")
            return True
        except Exception as e:
            logger.error(f"Error opening project in Cursor: {e}")
            return False
            
    def create_cursor_workspace_config(self, project_path: str, config: Dict[str, Any]) -> bool:
        """Create a Cursor workspace configuration file"""
        config_path = os.path.join(project_path, ".cursor.json")
        
        try:
            with open(config_path, "w") as f:
                json.dump(config, f, indent=2)
            logger.info(f"Created Cursor workspace config at: {config_path}")
            return True
        except Exception as e:
            logger.error(f"Error creating Cursor workspace config: {e}")
            return False
            
    def setup_cursor_for_agent_system(self, project_path: str) -> bool:
        """Set up Cursor for use with the agent system"""
        # Create a Cursor workspace configuration
        config = {
            "name": "Agent System",
            "settings": {
                "ai": {
                    "provider": "anthropic",  # Use Anthropic/Claude
                    "model": "claude-3-7-sonnet-20240620"  # Use Claude 3.7 Sonnet
                },
                "editor": {
                    "formatOnSave": True,
                    "tabSize": 4,
                    "insertSpaces": True
                }
            }
        }
        
        # Create the config file
        if not self.create_cursor_workspace_config(project_path, config):
            return False
            
        # Open the project in Cursor
        return self.open_project_in_cursor(project_path)
        
def main():
    """Main function to set up Cursor integration"""
    logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    
    print("Cursor Integration Setup")
    print("=======================")
    print("This script will set up Cursor AI coding assistant for use with your agent system.")
    print()
    
    # Get the project path
    project_path = os.path.dirname(os.path.abspath(__file__))
    
    # Initialize Cursor integration
    cursor = CursorIntegration()
    
    if not cursor.is_cursor_installed():
        cursor.install_cursor()
        return
        
    # Set up Cursor for the agent system
    if cursor.setup_cursor_for_agent_system(project_path):
        print()
        print("✅ Cursor has been set up for your agent system.")
        print("You can now use Cursor to work with your code and leverage Claude 3.7 Sonnet.")
        print()
        print("To use Cursor with your agent system:")
        print("1. Open your project in Cursor")
        print("2. Use Cursor's AI features to generate and modify code")
        print("3. Your agents can now leverage Cursor's capabilities")
    else:
        print()
        print("❌ Failed to set up Cursor for your agent system.")
        print("Please check the logs for more information.")
        
if __name__ == "__main__":
    main()
