"""
IRIS Visual Task Executor
=========================

Executes complex visual tasks by combining vision understanding with action execution.
Handles insurance-related visual tasks, form filling, and document processing.
"""

import asyncio
import logging
import os
import json
import time
from typing import Dict, List, Any, Optional, Union
from dataclasses import dataclass
from enum import Enum
from pathlib import Path

logger = logging.getLogger(__name__)

class TaskType(Enum):
    INSURANCE_FORM = "insurance_form"
    DOCUMENT_ANALYSIS = "document_analysis"
    WEB_NAVIGATION = "web_navigation"
    DATA_EXTRACTION = "data_extraction"
    VISUAL_VERIFICATION = "visual_verification"
    AUTOMATED_WORKFLOW = "automated_workflow"

@dataclass
class VisualTask:
    task_id: str
    task_type: TaskType
    description: str
    target_url: Optional[str] = None
    image_data: Optional[bytes] = None
    form_data: Optional[Dict[str, Any]] = None
    expected_outcome: Optional[str] = None
    priority: int = 1

@dataclass
class TaskResult:
    task_id: str
    success: bool
    result_data: Dict[str, Any]
    screenshots: List[bytes]
    execution_time: float
    error_message: Optional[str] = None

class VisualTaskExecutor:
    """Executes complex visual tasks using IRIS vision capabilities"""
    
    def __init__(self):
        self.initialized = False
        self.vision_agent = None
        self.web_agent = None
        self.active_tasks = {}
        self.task_history = []
        
    async def initialize(self):
        """Initialize visual task executor"""
        logger.info("Initializing IRIS Visual Task Executor...")
        
        try:
            # Initialize vision components
            from .mimo_vl_agent import MimoVLAgent
            from .web_vision_agent import WebVisionAgent
            
            self.vision_agent = MimoVLAgent()
            await self.vision_agent.initialize()
            
            self.web_agent = WebVisionAgent()
            await self.web_agent.initialize()
            
            self.initialized = True
            logger.info("Visual Task Executor initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize Visual Task Executor: {e}")
            raise
    
    async def execute_insurance_form_task(self, task: VisualTask) -> TaskResult:
        """Execute insurance form filling task"""
        start_time = time.time()
        screenshots = []
        
        try:
            logger.info(f"Executing insurance form task: {task.description}")
            
            # Navigate to the form URL
            if task.target_url:
                state = await self.web_agent.navigate_to_url(task.target_url)
                screenshots.append(state.screenshot)
            
            # Analyze the form visually
            form_analysis = await self.web_agent.analyze_page_visually(
                "Analyze this insurance form and identify all input fields, buttons, and required information"
            )
            
            # Extract form fields
            form_fields = await self._identify_form_fields(form_analysis)
            
            # Fill out the form using provided data
            if task.form_data:
                fill_results = await self._fill_insurance_form(form_fields, task.form_data)
            else:
                fill_results = {"message": "No form data provided"}
            
            # Take final screenshot
            final_screenshot = await self.web_agent.take_screenshot()
            screenshots.append(final_screenshot)
            
            execution_time = time.time() - start_time
            
            return TaskResult(
                task_id=task.task_id,
                success=True,
                result_data={
                    'form_analysis': form_analysis,
                    'form_fields': form_fields,
                    'fill_results': fill_results,
                    'final_url': self.web_agent.current_state.url if self.web_agent.current_state else None
                },
                screenshots=screenshots,
                execution_time=execution_time
            )
            
        except Exception as e:
            logger.error(f"Insurance form task failed: {e}")
            execution_time = time.time() - start_time
            
            return TaskResult(
                task_id=task.task_id,
                success=False,
                result_data={},
                screenshots=screenshots,
                execution_time=execution_time,
                error_message=str(e)
            )
    
    async def execute_document_analysis_task(self, task: VisualTask) -> TaskResult:
        """Execute document analysis task"""
        start_time = time.time()
        
        try:
            logger.info(f"Executing document analysis task: {task.description}")
            
            if not task.image_data:
                raise ValueError("No image data provided for document analysis")
            
            # Analyze document using vision agent
            analysis_result = await self.vision_agent.process_vision_query(
                query=f"Analyze this insurance document: {task.description}",
                image_data=task.image_data
            )
            
            # Extract specific information based on task type
            extracted_data = await self._extract_document_data(task.image_data, task.description)
            
            # Verify extracted information
            verification_result = await self._verify_extracted_data(extracted_data, task.expected_outcome)
            
            execution_time = time.time() - start_time
            
            return TaskResult(
                task_id=task.task_id,
                success=True,
                result_data={
                    'analysis': analysis_result,
                    'extracted_data': extracted_data,
                    'verification': verification_result
                },
                screenshots=[task.image_data],
                execution_time=execution_time
            )
            
        except Exception as e:
            logger.error(f"Document analysis task failed: {e}")
            execution_time = time.time() - start_time
            
            return TaskResult(
                task_id=task.task_id,
                success=False,
                result_data={},
                screenshots=[task.image_data] if task.image_data else [],
                execution_time=execution_time,
                error_message=str(e)
            )
    
    async def execute_web_navigation_task(self, task: VisualTask) -> TaskResult:
        """Execute web navigation task"""
        start_time = time.time()
        screenshots = []
        
        try:
            logger.info(f"Executing web navigation task: {task.description}")
            
            # Execute the web task
            web_result = await self.web_agent.execute_web_task(task.description)
            
            # Collect screenshots from the process
            if self.web_agent.current_state:
                screenshots.append(self.web_agent.current_state.screenshot)
            
            execution_time = time.time() - start_time
            
            return TaskResult(
                task_id=task.task_id,
                success=web_result.get('success', False),
                result_data=web_result,
                screenshots=screenshots,
                execution_time=execution_time
            )
            
        except Exception as e:
            logger.error(f"Web navigation task failed: {e}")
            execution_time = time.time() - start_time
            
            return TaskResult(
                task_id=task.task_id,
                success=False,
                result_data={},
                screenshots=screenshots,
                execution_time=execution_time,
                error_message=str(e)
            )
    
    async def execute_automated_workflow(self, task: VisualTask) -> TaskResult:
        """Execute complex automated workflow"""
        start_time = time.time()
        screenshots = []
        workflow_steps = []
        
        try:
            logger.info(f"Executing automated workflow: {task.description}")
            
            # Parse workflow from description
            steps = await self._parse_workflow_steps(task.description)
            
            for i, step in enumerate(steps, 1):
                logger.info(f"Executing workflow step {i}: {step['action']}")
                
                step_result = await self._execute_workflow_step(step)
                workflow_steps.append(step_result)
                
                # Capture screenshot after each step
                if self.web_agent.current_state:
                    screenshots.append(self.web_agent.current_state.screenshot)
                
                # Wait between steps
                await asyncio.sleep(1)
            
            execution_time = time.time() - start_time
            
            # Determine overall success
            success = all(step.get('success', False) for step in workflow_steps)
            
            return TaskResult(
                task_id=task.task_id,
                success=success,
                result_data={
                    'workflow_steps': workflow_steps,
                    'total_steps': len(steps),
                    'successful_steps': sum(1 for step in workflow_steps if step.get('success', False))
                },
                screenshots=screenshots,
                execution_time=execution_time
            )
            
        except Exception as e:
            logger.error(f"Automated workflow failed: {e}")
            execution_time = time.time() - start_time
            
            return TaskResult(
                task_id=task.task_id,
                success=False,
                result_data={'workflow_steps': workflow_steps},
                screenshots=screenshots,
                execution_time=execution_time,
                error_message=str(e)
            )
    
    async def execute_task(self, task: VisualTask) -> TaskResult:
        """Execute any visual task based on its type"""
        if not self.initialized:
            await self.initialize()
        
        # Add to active tasks
        self.active_tasks[task.task_id] = task
        
        try:
            # Route to appropriate executor
            if task.task_type == TaskType.INSURANCE_FORM:
                result = await self.execute_insurance_form_task(task)
            elif task.task_type == TaskType.DOCUMENT_ANALYSIS:
                result = await self.execute_document_analysis_task(task)
            elif task.task_type == TaskType.WEB_NAVIGATION:
                result = await self.execute_web_navigation_task(task)
            elif task.task_type == TaskType.AUTOMATED_WORKFLOW:
                result = await self.execute_automated_workflow(task)
            else:
                # Generic visual task
                result = await self._execute_generic_visual_task(task)
            
            # Add to history
            self.task_history.append(result)
            
            # Remove from active tasks
            if task.task_id in self.active_tasks:
                del self.active_tasks[task.task_id]
            
            return result
            
        except Exception as e:
            logger.error(f"Task execution failed: {e}")
            
            # Remove from active tasks
            if task.task_id in self.active_tasks:
                del self.active_tasks[task.task_id]
            
            return TaskResult(
                task_id=task.task_id,
                success=False,
                result_data={},
                screenshots=[],
                execution_time=0.0,
                error_message=str(e)
            )
    
    async def _identify_form_fields(self, form_analysis: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Identify form fields from visual analysis"""
        fields = []
        
        try:
            # Extract elements from analysis
            elements = form_analysis.get('elements', [])
            
            for element in elements:
                if element.is_input:
                    field_info = {
                        'type': element.tag,
                        'label': element.text or element.attributes.get('placeholder', ''),
                        'name': element.attributes.get('name', ''),
                        'id': element.attributes.get('id', ''),
                        'required': 'required' in element.attributes,
                        'coordinates': element.coordinates
                    }
                    fields.append(field_info)
            
        except Exception as e:
            logger.error(f"Form field identification failed: {e}")
        
        return fields
    
    async def _fill_insurance_form(self, form_fields: List[Dict], form_data: Dict[str, Any]) -> Dict[str, Any]:
        """Fill insurance form with provided data"""
        results = []
        
        try:
            for field in form_fields:
                field_name = field.get('name', '').lower()
                field_label = field.get('label', '').lower()
                
                # Map form data to fields
                value = None
                for key, val in form_data.items():
                    if key.lower() in field_name or key.lower() in field_label:
                        value = str(val)
                        break
                
                if value:
                    # Fill the field
                    success = await self.web_agent.type_in_element(
                        field.get('label', field.get('name', 'input field')),
                        value
                    )
                    
                    results.append({
                        'field': field_name,
                        'value': value,
                        'success': success
                    })
            
            return {
                'filled_fields': results,
                'total_fields': len(form_fields),
                'successful_fills': sum(1 for r in results if r['success'])
            }
            
        except Exception as e:
            logger.error(f"Form filling failed: {e}")
            return {'error': str(e)}
    
    async def _extract_document_data(self, image_data: bytes, description: str) -> Dict[str, Any]:
        """Extract specific data from document image"""
        try:
            # Use OCR to extract text
            text_result = await self.vision_agent.extract_text(image_data)
            
            # Analyze for specific insurance information
            analysis_result = await self.vision_agent.process_vision_query(
                query=f"Extract insurance information from this document: {description}",
                image_data=image_data
            )
            
            return {
                'extracted_text': text_result.get('response', ''),
                'analysis': analysis_result.get('response', ''),
                'confidence': analysis_result.get('confidence', 0.0)
            }
            
        except Exception as e:
            logger.error(f"Document data extraction failed: {e}")
            return {'error': str(e)}
    
    async def _verify_extracted_data(self, extracted_data: Dict, expected_outcome: Optional[str]) -> Dict[str, Any]:
        """Verify extracted data against expected outcome"""
        if not expected_outcome:
            return {'verified': True, 'note': 'No expected outcome provided'}
        
        try:
            # Simple verification - can be enhanced with ML
            extracted_text = extracted_data.get('extracted_text', '').lower()
            expected_lower = expected_outcome.lower()
            
            # Check if expected keywords are present
            keywords = expected_lower.split()
            found_keywords = sum(1 for keyword in keywords if keyword in extracted_text)
            
            verification_score = found_keywords / len(keywords) if keywords else 0
            
            return {
                'verified': verification_score > 0.5,
                'score': verification_score,
                'found_keywords': found_keywords,
                'total_keywords': len(keywords)
            }
            
        except Exception as e:
            logger.error(f"Data verification failed: {e}")
            return {'verified': False, 'error': str(e)}
    
    async def _parse_workflow_steps(self, description: str) -> List[Dict[str, Any]]:
        """Parse workflow description into actionable steps"""
        # Simplified parsing - can be enhanced with NLP
        steps = []
        
        # Split by common step indicators
        step_indicators = ['first', 'then', 'next', 'after', 'finally', 'step']
        
        # Basic step extraction
        if 'navigate to' in description.lower():
            steps.append({'action': 'navigate', 'target': 'specified URL'})
        
        if 'fill' in description.lower() and 'form' in description.lower():
            steps.append({'action': 'fill_form', 'target': 'form fields'})
        
        if 'click' in description.lower():
            steps.append({'action': 'click', 'target': 'button or link'})
        
        if 'submit' in description.lower():
            steps.append({'action': 'submit', 'target': 'form'})
        
        # Default step if none identified
        if not steps:
            steps.append({'action': 'analyze', 'target': 'current page'})
        
        return steps
    
    async def _execute_workflow_step(self, step: Dict[str, Any]) -> Dict[str, Any]:
        """Execute a single workflow step"""
        try:
            action = step['action']
            target = step['target']
            
            if action == 'navigate':
                # Navigate to URL (would need actual URL)
                result = {'success': True, 'message': f'Navigation step: {target}'}
            
            elif action == 'fill_form':
                # Fill form fields
                result = {'success': True, 'message': f'Form filling step: {target}'}
            
            elif action == 'click':
                # Click element
                success = await self.web_agent.click_element_by_description(target)
                result = {'success': success, 'message': f'Click step: {target}'}
            
            elif action == 'submit':
                # Submit form
                success = await self.web_agent.click_element_by_description('submit button')
                result = {'success': success, 'message': f'Submit step: {target}'}
            
            elif action == 'analyze':
                # Analyze current state
                analysis = await self.web_agent.analyze_page_visually(f"Analyze {target}")
                result = {'success': True, 'message': f'Analysis step: {target}', 'analysis': analysis}
            
            else:
                result = {'success': False, 'message': f'Unknown action: {action}'}
            
            return result
            
        except Exception as e:
            logger.error(f"Workflow step execution failed: {e}")
            return {'success': False, 'message': str(e)}
    
    async def _execute_generic_visual_task(self, task: VisualTask) -> TaskResult:
        """Execute generic visual task"""
        start_time = time.time()
        
        try:
            if task.image_data:
                # Image-based task
                result = await self.vision_agent.process_vision_query(
                    query=task.description,
                    image_data=task.image_data
                )
                screenshots = [task.image_data]
            
            elif task.target_url:
                # Web-based task
                await self.web_agent.navigate_to_url(task.target_url)
                result = await self.web_agent.analyze_page_visually(task.description)
                screenshots = [self.web_agent.current_state.screenshot] if self.web_agent.current_state else []
            
            else:
                # Text-based task
                result = {'response': f'Generic task processed: {task.description}', 'confidence': 0.7}
                screenshots = []
            
            execution_time = time.time() - start_time
            
            return TaskResult(
                task_id=task.task_id,
                success=True,
                result_data=result,
                screenshots=screenshots,
                execution_time=execution_time
            )
            
        except Exception as e:
            logger.error(f"Generic visual task failed: {e}")
            execution_time = time.time() - start_time
            
            return TaskResult(
                task_id=task.task_id,
                success=False,
                result_data={},
                screenshots=[],
                execution_time=execution_time,
                error_message=str(e)
            )
    
    def get_task_status(self, task_id: str) -> Optional[Dict[str, Any]]:
        """Get status of a specific task"""
        if task_id in self.active_tasks:
            return {
                'status': 'active',
                'task': self.active_tasks[task_id]
            }
        
        # Check history
        for result in self.task_history:
            if result.task_id == task_id:
                return {
                    'status': 'completed',
                    'result': result
                }
        
        return None
    
    def get_capabilities(self) -> List[str]:
        """Get visual task execution capabilities"""
        return [
            "Insurance form automation and filling",
            "Document analysis and data extraction",
            "Web navigation and interaction",
            "Visual verification and validation",
            "Automated workflow execution",
            "OCR and text extraction from images",
            "Visual element recognition and interaction",
            "Multi-step task orchestration"
        ]
    
    async def cleanup(self):
        """Cleanup resources"""
        try:
            if self.vision_agent:
                await self.vision_agent.cleanup()
            
            if self.web_agent:
                await self.web_agent.cleanup()
            
            self.active_tasks.clear()
            self.initialized = False
            
            logger.info("Visual Task Executor cleaned up")
            
        except Exception as e:
            logger.error(f"Cleanup error: {e}")
