"""
Advanced AI Models and Agents Integration
=========================================

This module provides integration for advanced AI models and agents:
- MANUS/OpenManus: Open-source AI agent framework
- MiMo-VL-7B: <PERSON><PERSON>'s vision-language model
- Detail Flow: ByteDance's flow-based AI system
- Giga Agent: Abacus.ai's autonomous agent system
- Honest AI Agent: Google's research agent with Gemini 2.5

All models are integrated with unified interfaces for seamless interaction.
"""

from .model_manager import AdvancedModelManager
from .manus_agent import ManusAgent
from .mimo_vl_agent import MimoVLAgent
from .detail_flow_agent import DetailFlowAgent
from .giga_agent import GigaAgent
from .honest_ai_agent import HonestAIAgent
from .unified_interface import UnifiedModelInterface
from .web_vision_agent import WebVisionAgent
from .visual_task_executor import VisualTaskExecutor, VisualTask, TaskType

__all__ = [
    'AdvancedModelManager',
    'ManusAgent',
    'MimoVLAgent',
    'DetailFlowAgent',
    'GigaAgent',
    'HonestAIAgent',
    'UnifiedModelInterface',
    'WebVisionAgent',
    'VisualTaskExecutor',
    'VisualTask',
    'TaskType'
]

__version__ = "1.0.0"
