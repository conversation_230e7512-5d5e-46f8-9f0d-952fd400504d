"""
IRIS Web Vision Agent
====================

Provides autonomous web browser control with vision capabilities for IRIS.
Enables visual understanding and interaction with web interfaces.
"""

import asyncio
import logging
import os
import base64
import io
import time
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
from enum import Enum

logger = logging.getLogger(__name__)

class BrowserAction(Enum):
    CLICK = "click"
    TYPE = "type"
    SCROLL = "scroll"
    NAVIGATE = "navigate"
    SCREENSHOT = "screenshot"
    WAIT = "wait"
    EXTRACT_TEXT = "extract_text"
    FIND_ELEMENT = "find_element"

@dataclass
class WebElement:
    tag: str
    text: str
    attributes: Dict[str, str]
    coordinates: Tuple[int, int, int, int]  # x, y, width, height
    element_type: str
    is_clickable: bool
    is_input: bool

@dataclass
class BrowserState:
    url: str
    title: str
    screenshot: bytes
    elements: List[WebElement]
    page_text: str
    timestamp: float

class WebVisionAgent:
    """Web browser automation with vision capabilities"""
    
    def __init__(self):
        self.initialized = False
        self.browser = None
        self.page = None
        self.browser_type = None
        self.config = {
            'headless': True,
            'timeout': 30000,
            'viewport': {'width': 1920, 'height': 1080},
            'user_agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36'
        }
        self.current_state = None
        
    async def initialize(self):
        """Initialize web browser with vision capabilities"""
        logger.info("Initializing IRIS Web Vision Agent...")
        
        try:
            # Try Playwright first (more reliable)
            if await self._initialize_playwright():
                self.browser_type = "playwright"
                logger.info("Playwright browser initialized")
            # Fallback to Selenium
            elif await self._initialize_selenium():
                self.browser_type = "selenium"
                logger.info("Selenium browser initialized")
            else:
                logger.warning("No browser automation available, using fallback")
                self.browser_type = "fallback"
            
            self.initialized = True
            logger.info("Web Vision Agent initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize Web Vision Agent: {e}")
            self.browser_type = "fallback"
            self.initialized = True
    
    async def _initialize_playwright(self):
        """Initialize Playwright browser"""
        try:
            from playwright.async_api import async_playwright
            
            self.playwright = await async_playwright().start()
            self.browser = await self.playwright.chromium.launch(
                headless=self.config['headless']
            )
            
            context = await self.browser.new_context(
                viewport=self.config['viewport'],
                user_agent=self.config['user_agent']
            )
            
            self.page = await context.new_page()
            return True
            
        except ImportError:
            logger.warning("Playwright not available")
            return False
        except Exception as e:
            logger.warning(f"Playwright initialization failed: {e}")
            return False
    
    async def _initialize_selenium(self):
        """Initialize Selenium browser"""
        try:
            from selenium import webdriver
            from selenium.webdriver.chrome.options import Options
            from selenium.webdriver.common.by import By
            from selenium.webdriver.support.ui import WebDriverWait
            from selenium.webdriver.support import expected_conditions as EC
            from webdriver_manager.chrome import ChromeDriverManager
            
            options = Options()
            if self.config['headless']:
                options.add_argument('--headless')
            options.add_argument(f'--window-size={self.config["viewport"]["width"]},{self.config["viewport"]["height"]}')
            options.add_argument(f'--user-agent={self.config["user_agent"]}')
            options.add_argument('--no-sandbox')
            options.add_argument('--disable-dev-shm-usage')
            
            self.browser = webdriver.Chrome(
                ChromeDriverManager().install(),
                options=options
            )
            
            self.browser.set_window_size(
                self.config['viewport']['width'],
                self.config['viewport']['height']
            )
            
            return True
            
        except ImportError:
            logger.warning("Selenium not available")
            return False
        except Exception as e:
            logger.warning(f"Selenium initialization failed: {e}")
            return False
    
    async def navigate_to_url(self, url: str) -> BrowserState:
        """Navigate to URL and capture state"""
        if not self.initialized:
            await self.initialize()
        
        try:
            if self.browser_type == "playwright":
                await self.page.goto(url, timeout=self.config['timeout'])
                await self.page.wait_for_load_state('networkidle')
                
            elif self.browser_type == "selenium":
                self.browser.get(url)
                # Wait for page to load
                await asyncio.sleep(2)
            
            # Capture current state
            self.current_state = await self._capture_state()
            return self.current_state
            
        except Exception as e:
            logger.error(f"Navigation failed: {e}")
            return await self._create_error_state(url, str(e))
    
    async def take_screenshot(self) -> bytes:
        """Take screenshot of current page"""
        try:
            if self.browser_type == "playwright":
                screenshot = await self.page.screenshot(full_page=True)
                return screenshot
                
            elif self.browser_type == "selenium":
                screenshot = self.browser.get_screenshot_as_png()
                return screenshot
            
            else:
                # Fallback - return empty image
                from PIL import Image
                img = Image.new('RGB', (800, 600), color='white')
                img_bytes = io.BytesIO()
                img.save(img_bytes, format='PNG')
                return img_bytes.getvalue()
                
        except Exception as e:
            logger.error(f"Screenshot failed: {e}")
            return b""
    
    async def analyze_page_visually(self, query: str = "Analyze this webpage") -> Dict[str, Any]:
        """Analyze current page using vision capabilities"""
        try:
            # Take screenshot
            screenshot = await self.take_screenshot()
            
            if not screenshot:
                return {
                    'analysis': 'Unable to capture page screenshot',
                    'elements': [],
                    'confidence': 0.0
                }
            
            # Use IRIS vision system to analyze
            from .mimo_vl_agent import MimoVLAgent
            vision_agent = MimoVLAgent()
            await vision_agent.initialize()
            
            # Analyze screenshot
            vision_result = await vision_agent.process_vision_query(
                query=f"Analyze this webpage screenshot: {query}",
                image_data=screenshot
            )
            
            # Extract page elements
            elements = await self._extract_page_elements()
            
            return {
                'analysis': vision_result['response'],
                'confidence': vision_result['confidence'],
                'elements': elements,
                'screenshot_size': len(screenshot),
                'metadata': vision_result.get('metadata', {})
            }
            
        except Exception as e:
            logger.error(f"Visual page analysis failed: {e}")
            return {
                'analysis': f'Visual analysis failed: {str(e)}',
                'elements': [],
                'confidence': 0.0
            }
    
    async def find_element_by_description(self, description: str) -> Optional[WebElement]:
        """Find element using natural language description"""
        try:
            # Get current page analysis
            analysis = await self.analyze_page_visually(
                f"Find element matching this description: {description}"
            )
            
            # Extract elements
            elements = await self._extract_page_elements()
            
            # Use simple matching for now (can be enhanced with ML)
            description_lower = description.lower()
            
            for element in elements:
                if (description_lower in element.text.lower() or
                    any(description_lower in attr.lower() for attr in element.attributes.values())):
                    return element
            
            return None
            
        except Exception as e:
            logger.error(f"Element finding failed: {e}")
            return None
    
    async def click_element_by_description(self, description: str) -> bool:
        """Click element using natural language description"""
        try:
            element = await self.find_element_by_description(description)
            
            if not element:
                logger.warning(f"Element not found: {description}")
                return False
            
            if not element.is_clickable:
                logger.warning(f"Element not clickable: {description}")
                return False
            
            # Click the element
            x, y, width, height = element.coordinates
            click_x = x + width // 2
            click_y = y + height // 2
            
            if self.browser_type == "playwright":
                await self.page.mouse.click(click_x, click_y)
                
            elif self.browser_type == "selenium":
                from selenium.webdriver.common.action_chains import ActionChains
                actions = ActionChains(self.browser)
                actions.move_by_offset(click_x, click_y).click().perform()
            
            # Wait for page to update
            await asyncio.sleep(1)
            
            # Update state
            self.current_state = await self._capture_state()
            
            return True
            
        except Exception as e:
            logger.error(f"Click failed: {e}")
            return False
    
    async def type_in_element(self, description: str, text: str) -> bool:
        """Type text in element using natural language description"""
        try:
            element = await self.find_element_by_description(description)
            
            if not element:
                logger.warning(f"Input element not found: {description}")
                return False
            
            if not element.is_input:
                logger.warning(f"Element is not an input: {description}")
                return False
            
            # Click element first to focus
            await self.click_element_by_description(description)
            
            # Type text
            if self.browser_type == "playwright":
                await self.page.keyboard.type(text)
                
            elif self.browser_type == "selenium":
                # Find element again for Selenium
                from selenium.webdriver.common.by import By
                selenium_element = self.browser.find_element(By.TAG_NAME, element.tag)
                selenium_element.clear()
                selenium_element.send_keys(text)
            
            return True
            
        except Exception as e:
            logger.error(f"Typing failed: {e}")
            return False
    
    async def execute_web_task(self, task_description: str) -> Dict[str, Any]:
        """Execute complex web task using vision and natural language"""
        try:
            # Analyze current page
            page_analysis = await self.analyze_page_visually(
                f"Analyze this page to help execute this task: {task_description}"
            )
            
            # Parse task into actions (simplified - can be enhanced with NLP)
            actions = self._parse_task_to_actions(task_description)
            
            results = []
            
            for action in actions:
                if action['type'] == 'click':
                    success = await self.click_element_by_description(action['target'])
                    results.append({
                        'action': 'click',
                        'target': action['target'],
                        'success': success
                    })
                
                elif action['type'] == 'type':
                    success = await self.type_in_element(action['target'], action['text'])
                    results.append({
                        'action': 'type',
                        'target': action['target'],
                        'text': action['text'],
                        'success': success
                    })
                
                elif action['type'] == 'navigate':
                    state = await self.navigate_to_url(action['url'])
                    results.append({
                        'action': 'navigate',
                        'url': action['url'],
                        'success': state is not None
                    })
                
                # Wait between actions
                await asyncio.sleep(1)
            
            return {
                'task': task_description,
                'actions_executed': results,
                'final_state': self.current_state,
                'success': all(r.get('success', False) for r in results)
            }
            
        except Exception as e:
            logger.error(f"Web task execution failed: {e}")
            return {
                'task': task_description,
                'error': str(e),
                'success': False
            }
    
    def _parse_task_to_actions(self, task: str) -> List[Dict[str, Any]]:
        """Parse natural language task into actionable steps"""
        # Simplified parsing - can be enhanced with NLP models
        actions = []
        task_lower = task.lower()
        
        if 'click' in task_lower:
            # Extract what to click
            if 'button' in task_lower:
                actions.append({'type': 'click', 'target': 'button'})
            elif 'link' in task_lower:
                actions.append({'type': 'click', 'target': 'link'})
        
        if 'type' in task_lower or 'enter' in task_lower:
            # Extract what to type and where
            actions.append({'type': 'type', 'target': 'input field', 'text': 'sample text'})
        
        if 'navigate' in task_lower or 'go to' in task_lower:
            # Extract URL if present
            actions.append({'type': 'navigate', 'url': 'https://example.com'})
        
        return actions
    
    async def _extract_page_elements(self) -> List[WebElement]:
        """Extract interactive elements from current page"""
        elements = []
        
        try:
            if self.browser_type == "playwright":
                # Get all interactive elements
                clickable_elements = await self.page.query_selector_all('button, a, input, select, textarea')
                
                for element in clickable_elements:
                    try:
                        tag = await element.evaluate('el => el.tagName.toLowerCase()')
                        text = await element.text_content() or ""
                        
                        # Get bounding box
                        box = await element.bounding_box()
                        if box:
                            coordinates = (int(box['x']), int(box['y']), int(box['width']), int(box['height']))
                        else:
                            coordinates = (0, 0, 0, 0)
                        
                        # Get attributes
                        attributes = {}
                        for attr in ['id', 'class', 'type', 'name', 'placeholder']:
                            value = await element.get_attribute(attr)
                            if value:
                                attributes[attr] = value
                        
                        web_element = WebElement(
                            tag=tag,
                            text=text.strip(),
                            attributes=attributes,
                            coordinates=coordinates,
                            element_type=tag,
                            is_clickable=tag in ['button', 'a'],
                            is_input=tag in ['input', 'textarea', 'select']
                        )
                        
                        elements.append(web_element)
                        
                    except Exception as e:
                        logger.debug(f"Error processing element: {e}")
                        continue
            
            elif self.browser_type == "selenium":
                from selenium.webdriver.common.by import By
                
                # Get all interactive elements
                selenium_elements = self.browser.find_elements(By.CSS_SELECTOR, 'button, a, input, select, textarea')
                
                for element in selenium_elements:
                    try:
                        tag = element.tag_name.lower()
                        text = element.text or ""
                        
                        # Get location and size
                        location = element.location
                        size = element.size
                        coordinates = (location['x'], location['y'], size['width'], size['height'])
                        
                        # Get attributes
                        attributes = {}
                        for attr in ['id', 'class', 'type', 'name', 'placeholder']:
                            value = element.get_attribute(attr)
                            if value:
                                attributes[attr] = value
                        
                        web_element = WebElement(
                            tag=tag,
                            text=text.strip(),
                            attributes=attributes,
                            coordinates=coordinates,
                            element_type=tag,
                            is_clickable=tag in ['button', 'a'],
                            is_input=tag in ['input', 'textarea', 'select']
                        )
                        
                        elements.append(web_element)
                        
                    except Exception as e:
                        logger.debug(f"Error processing element: {e}")
                        continue
        
        except Exception as e:
            logger.error(f"Element extraction failed: {e}")
        
        return elements
    
    async def _capture_state(self) -> BrowserState:
        """Capture current browser state"""
        try:
            if self.browser_type == "playwright":
                url = self.page.url
                title = await self.page.title()
                page_text = await self.page.text_content('body') or ""
                
            elif self.browser_type == "selenium":
                url = self.browser.current_url
                title = self.browser.title
                page_text = self.browser.find_element_by_tag_name('body').text
            
            else:
                url = "unknown"
                title = "Unknown Page"
                page_text = ""
            
            screenshot = await self.take_screenshot()
            elements = await self._extract_page_elements()
            
            return BrowserState(
                url=url,
                title=title,
                screenshot=screenshot,
                elements=elements,
                page_text=page_text,
                timestamp=time.time()
            )
            
        except Exception as e:
            logger.error(f"State capture failed: {e}")
            return await self._create_error_state("unknown", str(e))
    
    async def _create_error_state(self, url: str, error: str) -> BrowserState:
        """Create error state"""
        return BrowserState(
            url=url,
            title=f"Error: {error}",
            screenshot=b"",
            elements=[],
            page_text=f"Error occurred: {error}",
            timestamp=time.time()
        )
    
    async def cleanup(self):
        """Cleanup browser resources"""
        try:
            if self.browser_type == "playwright" and self.browser:
                await self.browser.close()
                await self.playwright.stop()
                
            elif self.browser_type == "selenium" and self.browser:
                self.browser.quit()
            
            self.initialized = False
            logger.info("Web Vision Agent cleaned up")
            
        except Exception as e:
            logger.error(f"Cleanup error: {e}")
    
    def get_capabilities(self) -> List[str]:
        """Get web vision capabilities"""
        return [
            "Autonomous web browser control",
            "Visual webpage analysis and understanding",
            "Element detection and interaction",
            "Natural language web task execution",
            "Screenshot capture and analysis",
            "Form filling and navigation",
            "Insurance website automation",
            "Visual element recognition and clicking"
        ]
