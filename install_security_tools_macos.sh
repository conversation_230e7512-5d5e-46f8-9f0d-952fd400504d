#!/bin/bash

# Ethical Hacking Tools Installation Script for macOS
# This script installs a comprehensive set of open-source security and penetration testing tools
# Created: April 29, 2025

# Text colors
GREEN='\033[0;32m'
BLUE='\033[0;34m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Function to display a banner
display_banner() {
    clear
    echo -e "${BLUE}"
    echo "╔═══════════════════════════════════════════════════════════════╗"
    echo "║                                                               ║"
    echo "║            ETHICAL HACKING TOOLS INSTALLATION                 ║"
    echo "║                        macOS Edition                          ║"
    echo "║                                                               ║"
    echo "╚═══════════════════════════════════════════════════════════════╝"
    echo -e "${NC}"
}

# Function to display section headers
section_header() {
    echo -e "${YELLOW}\n════════════════════════════════════════════════════════════════════"
    echo "  $1"
    echo -e "════════════════════════════════════════════════════════════════════${NC}\n"
}

# Function to check if a command was successful
check_success() {
    if [ $? -eq 0 ]; then
        echo -e "${GREEN}✓ Success: $1${NC}"
    else
        echo -e "${RED}✗ Error: Failed to $1${NC}"
        # Continue instead of exiting - this makes the script more resilient
        echo -e "${YELLOW}→ Continuing with the rest of the installation...${NC}"
    fi
}

# Function to check if Homebrew is installed
check_homebrew() {
    if ! command -v brew &> /dev/null; then
        echo -e "${BLUE}→ Installing Homebrew...${NC}"
        /bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"
        check_success "install Homebrew"
    else
        echo -e "${GREEN}✓ Homebrew is already installed${NC}"
    fi
}

# Function to install a specific tool using Homebrew
install_brew_tool() {
    echo -e "${BLUE}→ Installing $1...${NC}"
    if ! brew list $2 &>/dev/null; then
        brew install $2 2>/dev/null || brew install --cask $2 2>/dev/null
        check_success "install $1"
    else
        echo -e "${GREEN}✓ $1 is already installed${NC}"
    fi
}

# Function to install a specific tool using Python pip
install_pip_tool() {
    echo -e "${BLUE}→ Installing $1...${NC}"
    pip3 install $2 2>/dev/null
    check_success "install $1"
}

# Function to install a specific tool using git clone
install_git_tool() {
    echo -e "${BLUE}→ Installing $1...${NC}"
    mkdir -p $(dirname "$3")
    if [ ! -d "$3" ]; then
        git clone $2 $3 2>/dev/null
        check_success "install $1"
    else
        echo -e "${GREEN}✓ $1 is already installed${NC}"
    fi
}

# Display banner
display_banner

# Check for Homebrew and install if not present
section_header "CHECKING PREREQUISITES"
check_homebrew

# Ensure XCode command line tools are installed
echo -e "${BLUE}→ Checking for XCode Command Line Tools...${NC}"
if ! xcode-select -p &>/dev/null; then
    echo -e "${YELLOW}Note: XCode Command Line Tools need to be installed.${NC}"
    echo -e "${YELLOW}A dialog box may appear requesting installation.${NC}"
    xcode-select --install 2>/dev/null
    echo -e "${YELLOW}Please follow the prompts to install XCode Command Line Tools.${NC}"
    echo -e "${YELLOW}After installation completes, please run this script again.${NC}"
    echo -e "${YELLOW}Press ENTER to continue anyway, or CTRL+C to exit and install XCode tools first.${NC}"
    read -r
else
    echo -e "${GREEN}✓ XCode Command Line Tools are installed${NC}"
fi

# Update Homebrew
section_header "UPDATING PACKAGE MANAGER"
echo "→ Updating Homebrew..."
brew update 2>/dev/null
check_success "update Homebrew"

echo "→ Upgrading existing packages..."
brew upgrade 2>/dev/null
check_success "upgrade packages"

# Install essential dependencies
section_header "INSTALLING DEPENDENCIES"
install_brew_tool "Python 3" "python3"
install_brew_tool "Git" "git"
install_brew_tool "Wget" "wget"
install_brew_tool "OpenSSL" "openssl"

# Reconnaissance Tools
section_header "RECONNAISSANCE TOOLS"
install_brew_tool "Nmap" "nmap"
install_pip_tool "theHarvester" "theHarvester"

# Vulnerability Scanning Tools
section_header "VULNERABILITY SCANNING TOOLS"
install_brew_tool "Nikto" "nikto"
echo -e "${BLUE}→ Checking for Docker...${NC}"
if ! command -v docker &>/dev/null; then
    echo -e "${BLUE}→ Installing Docker for OpenVAS...${NC}"
    brew install --cask docker 2>/dev/null
    check_success "install Docker"
    echo -e "${YELLOW}Note: You need to start Docker manually after installation.${NC}"
    echo -e "${YELLOW}Then run: docker pull greenbone/openvas${NC}"
else
    echo -e "${GREEN}✓ Docker is already installed${NC}"
    echo -e "${BLUE}→ Pulling OpenVAS Docker image...${NC}"
    docker pull greenbone/openvas 2>/dev/null || true
    check_success "pull OpenVAS Docker image"
fi

# Exploitation Tools
section_header "EXPLOITATION TOOLS"
install_brew_tool "Metasploit Framework" "metasploit"

# Password Cracking Tools
section_header "PASSWORD CRACKING TOOLS"
install_brew_tool "John the Ripper" "john-jumbo"
install_brew_tool "Hashcat" "hashcat"

# Wireless Testing Tools
section_header "WIRELESS TESTING TOOLS"
install_brew_tool "Aircrack-ng" "aircrack-ng"
install_brew_tool "Kismet" "kismet"

# Web Application Testing Tools
section_header "WEB APPLICATION TESTING TOOLS"
install_brew_tool "OWASP ZAP" "zaproxy"
install_pip_tool "sqlmap" "sqlmap"

# Social Engineering Tools
section_header "SOCIAL ENGINEERING TOOLS"
install_git_tool "Social Engineer Toolkit (SET)" "https://github.com/trustedsec/social-engineer-toolkit.git" "$HOME/tools/set"
echo -e "${BLUE}→ Setting up Social Engineer Toolkit...${NC}"
if [ -d "$HOME/tools/set" ]; then
    cd "$HOME/tools/set"
    pip3 install -r requirements.txt 2>/dev/null
    python3 setup.py install 2>/dev/null
    check_success "setup Social Engineer Toolkit"
fi

# Forensics Tools
section_header "FORENSICS TOOLS"
install_brew_tool "Sleuth Kit" "sleuthkit"
install_brew_tool "Autopsy" "autopsy" || echo -e "${YELLOW}Note: Autopsy may need manual installation from https://www.autopsy.com/download/${NC}"

# Additional useful tools
section_header "ADDITIONAL TOOLS"
install_brew_tool "Wireshark" "wireshark"
install_brew_tool "Hydra" "hydra"
install_brew_tool "Netcat" "netcat"
install_brew_tool "Tcpdump" "tcpdump"
install_brew_tool "Ettercap" "ettercap"

# Final setup
section_header "FINALIZATION"
echo "→ Cleaning up..."
brew cleanup 2>/dev/null
check_success "clean up"

echo -e "\n${GREEN}═════════════════════════════════════════════════════════════════════"
echo "                   INSTALLATION COMPLETED!                        "
echo -e "═════════════════════════════════════════════════════════════════════${NC}"
echo -e "\nEthical hacking tools have been successfully installed."
echo -e "\n${YELLOW}IMPORTANT:${NC} Remember to use these tools responsibly and ethically."
echo -e "Only use these tools on systems you own or have explicit permission to test."
echo -e "Unauthorized access to computer systems is illegal and unethical.\n"

# Add some help for the user
echo -e "${BLUE}Tools installed:${NC}"
echo "  • Reconnaissance: Nmap, theHarvester"
echo "  • Vulnerability Scanning: Nikto, OpenVAS (Docker)"
echo "  • Exploitation: Metasploit Framework"
echo "  • Password Cracking: John the Ripper, Hashcat"
echo "  • Wireless Testing: Aircrack-ng, Kismet"
echo "  • Web Application Testing: OWASP ZAP, sqlmap"
echo "  • Social Engineering: Social-Engineer Toolkit (SET)"
echo "  • Forensics: Autopsy, Sleuth Kit"
echo "  • Additional: Wireshark, Hydra, Netcat, Tcpdump, Ettercap"
echo -e "\n${BLUE}To open specific tools, use the following commands:${NC}"
echo "  • Nmap: nmap"
echo "  • OWASP ZAP: zaproxy"
echo "  • Wireshark: wireshark"
echo "  • Metasploit: msfconsole"
echo "  • Social Engineer Toolkit: cd ~/tools/set && python3 setoolkit"
echo "  • OpenVAS: docker run -d -p 443:443 greenbone/openvas"
echo -e "\n${GREEN}Thank you for using this installation script!${NC}\n"