"""
Automated Audio Email to <PERSON>

This script automatically:
1. Generates audio using Eleven Labs API with a female voice
2. Sends an email to <PERSON> with the audio file attached
3. Uses environment variables for credentials

No user input required - fully automated process.
"""

import os
import smtplib
import requests
import json
import ssl
import time
from email.mime.text import MIMEText
from email.mime.multipart import MIME<PERSON>ult<PERSON>art
from email.mime.audio import MI<PERSON><PERSON>udi<PERSON>
from dotenv import load_dotenv
import platform
import webbrowser
import urllib.parse
import subprocess

# Load environment variables
load_dotenv()

# Eleven Labs credentials from environment variables
ELEVEN_LABS_API_KEY = os.getenv("ELEVENLABS_API_KEY", "***************************************************")
ELEVEN_LABS_VOICE_ID = os.getenv("ELEVEN_LABS_VOICE_ID", "21m00Tcm4TlvDq8ikWAM")  # <PERSON> voice ID (female voice)

# <PERSON> contact information from environment variables
PAUL_EDWARDS = {
    "first_name": os.getenv("PAUL_FIRST_NAME", "<PERSON>"),
    "last_name": os.getenv("PAUL_LAST_NAME", "Edwards"),
    "email": os.getenv("PAUL_EMAIL", "<EMAIL>"),
    "primary_phone": os.getenv("PAUL_PRIMARY_PHONE", "+17722089646"),
    "secondary_phone": os.getenv("PAUL_SECONDARY_PHONE", "+17725395908")
}

# Agent information from environment variables
AGENT_INFO = {
    "name": os.getenv("AGENT_NAME", "Sandra Smith"),
    "agency": os.getenv("AGENT_AGENCY", "Flo Faction Insurance"),
    "email": os.getenv("AGENT_EMAIL", "<EMAIL>"),
    "website": os.getenv("AGENT_WEBSITE", "https://www.flofaction.com/insurance"),
}

# Email configuration
EMAIL_SENDER = os.getenv("EMAIL_SENDER", AGENT_INFO["email"])
EMAIL_PASSWORD = os.getenv("EMAIL_PASSWORD", "")  # Will try alternative methods if not provided

def generate_voicemail_script():
    """Generate a voicemail script for Paul Edwards"""
    script = f"""
Hi {PAUL_EDWARDS['first_name']}, this is {AGENT_INFO['name']} with {AGENT_INFO['agency']}. I'm calling because I help people like you create tax-free retirement income without the risk of the stock market.

Based on your profile, I've found that you might qualify for a strategy that could provide significant tax-free retirement income while also protecting your family with a death benefit.

I'd love to share a personalized analysis I've prepared for you. Please give me a call back when you have a moment, or visit our website at {AGENT_INFO['website']} for more information.

Thank you and have a great day!
    """
    return script.strip()

def generate_audio(text, output_file="audio/paul_edwards_voicemail.mp3"):
    """Generate audio using Eleven Labs API"""
    print("=" * 80)
    print("GENERATING AUDIO WITH ELEVEN LABS")
    print("=" * 80)
    
    # Create audio directory if it doesn't exist
    os.makedirs(os.path.dirname(output_file), exist_ok=True)
    
    url = f"https://api.elevenlabs.io/v1/text-to-speech/{ELEVEN_LABS_VOICE_ID}"
    
    headers = {
        "Accept": "audio/mpeg",
        "Content-Type": "application/json",
        "xi-api-key": ELEVEN_LABS_API_KEY
    }
    
    data = {
        "text": text,
        "model_id": "eleven_monolingual_v1",
        "voice_settings": {
            "stability": 0.5,
            "similarity_boost": 0.5
        }
    }
    
    try:
        print(f"Sending request to Eleven Labs API...")
        response = requests.post(url, json=data, headers=headers)
        
        if response.status_code == 200:
            # Save the audio file
            with open(output_file, "wb") as f:
                f.write(response.content)
            
            print(f"Audio generated successfully and saved as {output_file}")
            return output_file
        else:
            print(f"Error generating audio: {response.status_code} - {response.text}")
            return None
    except Exception as e:
        print(f"Exception generating audio: {str(e)}")
        return None

def create_email_content():
    """Create email subject and body"""
    subject = f"Personal Message from {AGENT_INFO['name']} - {AGENT_INFO['agency']}"
    body = f"""
Dear {PAUL_EDWARDS['first_name']},

I hope this email finds you well. My name is {AGENT_INFO['name']} with {AGENT_INFO['agency']}, and I specialize in helping people like you create tax-free retirement income without the risk of the stock market.

I've attached a personal audio message for you. Please take a moment to listen to it when you have a chance.

Based on the information I have, I've prepared a personalized analysis that shows how you could potentially:

1. Generate approximately $5,000 per month in tax-free retirement income
2. Build cash value that grows without the risk of market losses
3. Provide a death benefit of around $720,000 to protect your family
4. Create a financial strategy that offers both protection and growth potential

Would you be interested in a brief 30-minute conversation to review this analysis? If so, please call me or reply to this email with a good time to connect.

You can also learn more about our services by visiting our website at {AGENT_INFO['website']}.

Thank you for your time, and I look forward to speaking with you soon.

Best regards,
{AGENT_INFO['name']}
{AGENT_INFO['agency']}
{AGENT_INFO['website']}
    """
    return subject, body

def send_email_with_smtp(audio_file):
    """Send email using SMTP with environment variables"""
    print("=" * 80)
    print("ATTEMPTING TO SEND EMAIL WITH SMTP")
    print("=" * 80)
    
    # Get email credentials from environment variables
    sender_email = EMAIL_SENDER
    password = EMAIL_PASSWORD
    
    if not password:
        print("Email password not found in environment variables.")
        return False
    
    subject, body = create_email_content()
    
    # Create message
    message = MIMEMultipart()
    message["From"] = sender_email
    message["To"] = PAUL_EDWARDS["email"]
    message["Subject"] = subject
    
    # Attach body
    message.attach(MIMEText(body, "plain"))
    
    # Attach audio file
    try:
        with open(audio_file, "rb") as f:
            audio_attachment = MIMEAudio(f.read(), _subtype="mp3")
        
        audio_attachment.add_header(
            "Content-Disposition",
            f"attachment; filename={os.path.basename(audio_file)}"
        )
        
        message.attach(audio_attachment)
    except Exception as e:
        print(f"Error attaching audio file: {str(e)}")
        return False
    
    try:
        # Create secure connection and send email
        context = ssl.create_default_context()
        
        # Determine SMTP server based on email domain
        if "@gmail.com" in sender_email.lower():
            smtp_server = "smtp.gmail.com"
            smtp_port = 587
        elif "@outlook.com" in sender_email.lower() or "@hotmail.com" in sender_email.lower():
            smtp_server = "smtp.office365.com"
            smtp_port = 587
        elif "@yahoo.com" in sender_email.lower():
            smtp_server = "smtp.mail.yahoo.com"
            smtp_port = 587
        else:
            smtp_server = os.getenv("SMTP_SERVER", "smtp.gmail.com")
            smtp_port = int(os.getenv("SMTP_PORT", "587"))
        
        with smtplib.SMTP(smtp_server, smtp_port) as server:
            server.starttls(context=context)
            server.login(sender_email, password)
            server.sendmail(sender_email, PAUL_EDWARDS["email"], message.as_string())
        
        print(f"Email with audio attachment sent successfully to {PAUL_EDWARDS['email']}")
        return True
    except Exception as e:
        print(f"Error sending email with SMTP: {str(e)}")
        return False

def send_email_with_default_client(audio_file):
    """Send email using the default email client"""
    print("=" * 80)
    print("ATTEMPTING TO SEND EMAIL WITH DEFAULT CLIENT")
    print("=" * 80)
    
    subject, body = create_email_content()
    
    # Add note about attachment
    body += f"\n\nIMPORTANT: Please remember to attach the audio file '{os.path.basename(audio_file)}' to this email before sending."
    
    # URL encode the subject and body
    subject_encoded = urllib.parse.quote(subject)
    body_encoded = urllib.parse.quote(body)
    
    # Create mailto URL
    mailto_url = f"mailto:{PAUL_EDWARDS['email']}?subject={subject_encoded}&body={body_encoded}"
    
    try:
        # Open the default email client
        webbrowser.open(mailto_url)
        print(f"Default email client opened with message to {PAUL_EDWARDS['email']}")
        print("Please attach the audio file and complete the email sending process in your email client.")
        
        # Try to open the folder containing the audio file
        if os.path.exists(audio_file):
            if platform.system() == "Darwin":  # macOS
                subprocess.run(["open", "-R", audio_file])
                print(f"Opened Finder with '{audio_file}' selected.")
            elif platform.system() == "Windows":
                subprocess.run(["explorer", "/select,", os.path.abspath(audio_file)])
                print(f"Opened Explorer with '{audio_file}' selected.")
            elif platform.system() == "Linux":
                subprocess.run(["xdg-open", os.path.dirname(os.path.abspath(audio_file))])
                print(f"Opened file manager to the folder containing '{audio_file}'.")
        
        # Wait for user to confirm
        input("\nPress Enter after you've sent the email in your email client...")
        return True
    except Exception as e:
        print(f"Error opening default email client: {str(e)}")
        return False

def send_email_with_command_line(audio_file):
    """Send email using command line tools"""
    print("=" * 80)
    print("ATTEMPTING TO SEND EMAIL WITH COMMAND LINE")
    print("=" * 80)
    
    subject, body = create_email_content()
    
    try:
        if platform.system() == "Darwin":  # macOS
            # Create a temporary file with the email body
            with open("temp_email.txt", "w") as f:
                f.write(body)
            
            # Use the mail command with attachment
            cmd = f"cat temp_email.txt | mail -s '{subject}' -a '{audio_file}' {PAUL_EDWARDS['email']}"
            result = subprocess.run(cmd, shell=True, capture_output=True, text=True)
            
            # Remove the temporary file
            os.remove("temp_email.txt")
            
            if result.returncode == 0:
                print(f"Email sent to {PAUL_EDWARDS['email']} using mail command")
                return True
            else:
                print(f"Error sending email with mail command: {result.stderr}")
                return False
        else:
            print(f"Command line email sending not implemented for {platform.system()}")
            return False
    except Exception as e:
        print(f"Error sending email with command line: {str(e)}")
        return False

def main():
    """Main function to generate audio and send email"""
    print("=" * 80)
    print("AUTOMATED AUDIO EMAIL TO PAUL EDWARDS")
    print("=" * 80)
    
    # 1. Generate the voicemail script
    script = generate_voicemail_script()
    print("\nVoicemail Script:")
    print("-" * 80)
    print(script)
    print("-" * 80)
    
    # 2. Generate the audio file
    audio_file = generate_audio(script)
    
    if not audio_file or not os.path.exists(audio_file):
        print("Failed to generate audio file. Exiting.")
        return False
    
    # 3. Try multiple methods to send the email
    print("\nAttempting to send email with audio attachment...")
    
    # Try SMTP first if password is available
    if EMAIL_PASSWORD:
        if send_email_with_smtp(audio_file):
            return True
    else:
        print("No email password found in environment variables. Skipping SMTP method.")
    
    # Try command line next
    if send_email_with_command_line(audio_file):
        return True
    
    # Try default email client as last resort
    if send_email_with_default_client(audio_file):
        return True
    
    print("\nAll email sending methods failed.")
    return False

if __name__ == "__main__":
    main()
