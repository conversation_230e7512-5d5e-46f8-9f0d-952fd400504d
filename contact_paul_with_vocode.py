"""
Contact <PERSON> with Vocode

This script uses Vocode to make an outbound call to <PERSON>.
"""

import os
import asyncio
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()

# Set environment variables if not already set
if not os.environ.get("OPENAI_API_KEY"):
    os.environ["OPENAI_API_KEY"] = "***************************************************"
if not os.environ.get("TWILIO_ACCOUNT_SID"):
    os.environ["TWILIO_ACCOUNT_SID"] = "AC187c871afa232bbbc978caf33f3e25d9"
if not os.environ.get("TWILIO_AUTH_TOKEN"):
    os.environ["TWILIO_AUTH_TOKEN"] = "CqpVewwter1BEMdFIFHrN2XmUyt22wBP"
if not os.environ.get("ELEVEN_LABS_API_KEY"):
    os.environ["ELEVEN_LABS_API_KEY"] = "***************************************************"

try:
    from vocode.streaming.telephony.conversation.outbound_call import OutboundCall
    from vocode.streaming.models.agent import ChatGPTAgentConfig
    from vocode.streaming.models.message import BaseMessage
    from vocode.streaming.models.synthesizer import ElevenLabsSynthesizerConfig
    VOCODE_INSTALLED = True
except ImportError:
    VOCODE_INSTALLED = False
    print("Vocode is not installed. Please install it with 'pip install vocode'.")

# Paul Edwards contact information
PAUL_EDWARDS = {
    "first_name": "Paul",
    "last_name": "Edwards",
    "email": "<EMAIL>",
    "primary_phone": "+***********",  # Primary number
    "secondary_phone": "+***********"  # Secondary number (verified in Twilio)
}

# Agent information
AGENT_INFO = {
    "name": "Sandra Smith",
    "agency": "Flo Faction Insurance",
    "phone": "Flo Faction AI Assistant",
    "email": "<EMAIL>",
    "website": "https://www.flofaction.com",
}

async def make_call_to_paul():
    """Make an outbound call to Paul Edwards using Vocode"""
    if not VOCODE_INSTALLED:
        print("Cannot make call: Vocode is not installed.")
        return
    
    print("=" * 80)
    print("MAKING OUTBOUND CALL TO PAUL EDWARDS")
    print("=" * 80)
    
    # Create the agent prompt
    prompt_preamble = f"""
    You are {AGENT_INFO['name']}, an insurance agent from {AGENT_INFO['agency']}. 
    You're calling {PAUL_EDWARDS['first_name']} {PAUL_EDWARDS['last_name']} to discuss tax-free retirement income options through Indexed Universal Life policies.
    
    Based on {PAUL_EDWARDS['first_name']}'s profile, you've found that he might qualify for a strategy that could provide around $5,000 per month in tax-free retirement income.
    
    Be friendly, professional, and focus on how you can help {PAUL_EDWARDS['first_name']} achieve his retirement goals.
    
    Key talking points:
    - Tax-free retirement income potential
    - Protection from market downturns
    - Death benefit protection for family
    - Cash value growth potential
    - Flexibility of the policy
    
    If {PAUL_EDWARDS['first_name']} is interested, try to schedule a 30-minute call to review a personalized analysis.
    
    If {PAUL_EDWARDS['first_name']} has objections, address them professionally:
    - "I need to think about it" → "I understand completely. This is an important decision. The meeting is just to provide information so you can make an informed decision. There's no obligation."
    - "I already have retirement plans" → "That's great! Many of my clients use this strategy to complement their existing retirement plans, especially to create a tax-free income stream."
    - "Is this whole life insurance?" → "No, this is Indexed Universal Life, which is quite different. It provides both death benefit protection and cash value growth potential linked to market indexes, but without the risk of market losses."
    
    At the end of the call, thank {PAUL_EDWARDS['first_name']} for his time and mention that you'll send a follow-up email with more information.
    """
    
    # Configure the agent
    agent_config = ChatGPTAgentConfig(
        initial_message=BaseMessage(text=f"Hello, this is {AGENT_INFO['name']} from {AGENT_INFO['agency']}. Am I speaking with {PAUL_EDWARDS['first_name']} {PAUL_EDWARDS['last_name']}?"),
        prompt_preamble=prompt_preamble,
        openai_api_key=os.environ.get("OPENAI_API_KEY"),
    )
    
    # Configure the synthesizer (voice)
    synthesizer_config = ElevenLabsSynthesizerConfig(
        api_key=os.environ.get("ELEVEN_LABS_API_KEY"),
        voice_id="21m00Tcm4TlvDq8ikWAM",  # Rachel voice ID
    )
    
    try:
        # Make the outbound call
        outbound_call = OutboundCall(
            recipient_phone_number=PAUL_EDWARDS["primary_phone"],
            caller_phone_number=PAUL_EDWARDS["secondary_phone"],  # Using the verified number as caller
            agent_config=agent_config,
            synthesizer_config=synthesizer_config,
            twilio_account_sid=os.environ.get("TWILIO_ACCOUNT_SID"),
            twilio_auth_token=os.environ.get("TWILIO_AUTH_TOKEN"),
        )
        
        # Start the call
        await outbound_call.start()
        
        print(f"Call initiated to {PAUL_EDWARDS['primary_phone']}")
        print("Waiting for call to complete...")
        
        # Wait for the call to complete
        while outbound_call.is_active():
            await asyncio.sleep(1)
        
        print("Call completed")
        return True
    except Exception as e:
        print(f"Error making call: {str(e)}")
        return False

async def send_text_to_paul():
    """Send a text message to Paul Edwards using Vocode"""
    if not VOCODE_INSTALLED:
        print("Cannot send text: Vocode is not installed.")
        return
    
    print("=" * 80)
    print("SENDING TEXT MESSAGE TO PAUL EDWARDS")
    print("=" * 80)
    
    # This is a placeholder - Vocode doesn't have a direct SMS function
    # You would need to use Twilio's SMS API directly or another method
    print("Text messaging not implemented in this script.")
    print("Please use the Twilio SMS API directly or another method.")
    
    return False

async def main():
    """Main function to contact Paul Edwards"""
    print("This script will contact Paul Edwards using Vocode.")
    
    if not VOCODE_INSTALLED:
        print("Vocode is not installed. Please install it with 'pip install vocode'.")
        print("This script will not be able to make calls or send texts.")
        return
    
    action = input("What would you like to do? (call/text/both/exit): ").lower()
    
    if action == "call" or action == "both":
        await make_call_to_paul()
    
    if action == "text" or action == "both":
        await send_text_to_paul()
    
    if action == "exit":
        print("Exiting...")
    
    print("=" * 80)
    print("CONTACT OPERATIONS COMPLETED")
    print("=" * 80)

if __name__ == "__main__":
    asyncio.run(main())
