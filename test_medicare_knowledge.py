import logging
from datetime import date
from medicare_plans import (
    get_plan_details,
    compare_plans,
    recommend_plan,
    calculate_eligibility,
    MEDICARE_SUPPLEMENT_PLANS
)

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def test_plan_knowledge():
    """Test detailed knowledge of Medicare Supplement plans"""
    
    logger.info("\n=== Testing Medicare Supplement Plan Knowledge ===")
    
    # Test Plan G details
    logger.info("\nTesting Plan G Details:")
    plan_g = get_plan_details("Plan G")
    if plan_g:
        logger.info(f"Description: {plan_g.description}")
        logger.info("Key Coverage:")
        for benefit, coverage in plan_g.coverage.items():
            logger.info(f"- {benefit.replace('_', ' ').title()}: {coverage}")
            
    # Test Plan Comparison
    logger.info("\nComparing Plan G and Plan N:")
    comparison = compare_plans(["Plan G", "Plan N"])
    for plan, details in comparison.items():
        logger.info(f"\n{plan} Coverage Highlights:")
        key_benefits = [
            "part_b_coinsurance",
            "part_b_excess_charges",
            "copays"
        ]
        for benefit in key_benefits:
            if benefit in details['coverage']:
                logger.info(f"- {benefit.replace('_', ' ').title()}: {details['coverage'][benefit]}")

def test_plan_recommendations():
    """Test plan recommendation logic"""
    
    logger.info("\n=== Testing Plan Recommendations ===")
    
    # Test cases
    test_scenarios = [
        {
            "age": 67,
            "budget": "high",
            "health": "frequent_visits",
            "description": "Frequent healthcare user with high budget"
        },
        {
            "age": 65,
            "budget": "medium",
            "health": "healthy",
            "description": "Healthy individual with medium budget"
        },
        {
            "age": 70,
            "budget": "low",
            "health": "healthy",
            "description": "Healthy individual with low budget"
        }
    ]
    
    for scenario in test_scenarios:
        logger.info(f"\nScenario: {scenario['description']}")
        recommendations = recommend_plan(
            scenario["age"],
            scenario["budget"],
            scenario["health"]
        )
        logger.info(f"Recommended plans: {', '.join(recommendations)}")
        
        # Show key features of recommended plans
        for plan in recommendations:
            plan_details = get_plan_details(plan)
            if plan_details:
                logger.info(f"\n{plan} Key Features:")
                for note in plan_details.notes[:2]:  # Show top 2 notes
                    logger.info(f"- {note}")

def test_eligibility_calculations():
    """Test Medicare Supplement eligibility calculations"""
    
    logger.info("\n=== Testing Eligibility Calculations ===")
    
    # Test scenarios
    test_dates = [
        {
            "birth_date": date(1957, 6, 15),
            "part_b_date": date(2024, 1, 1),
            "description": "Recent Medicare enrollee"
        },
        {
            "birth_date": date(1950, 3, 1),
            "part_b_date": date(2015, 3, 1),
            "description": "Long-time Medicare beneficiary"
        },
        {
            "birth_date": date(1960, 8, 1),
            "part_b_date": date(2025, 8, 1),
            "description": "Future Medicare beneficiary"
        }
    ]
    
    for scenario in test_dates:
        logger.info(f"\nScenario: {scenario['description']}")
        eligibility = calculate_eligibility(
            scenario["birth_date"],
            scenario["part_b_date"]
        )
        logger.info("Eligibility Status:")
        for status, eligible in eligibility.items():
            logger.info(f"- {status.replace('_', ' ').title()}: {'Yes' if eligible else 'No'}")

def main():
    """Run all Medicare knowledge tests"""
    try:
        # Test plan knowledge
        test_plan_knowledge()
        
        # Test plan recommendations
        test_plan_recommendations()
        
        # Test eligibility calculations
        test_eligibility_calculations()
        
        logger.info("\nAll Medicare knowledge tests completed successfully!")
        
    except Exception as e:
        logger.error(f"Error during testing: {str(e)}")
        raise

if __name__ == "__main__":
    main()