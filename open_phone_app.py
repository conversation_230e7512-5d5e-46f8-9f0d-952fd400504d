"""
Open Phone App to Call <PERSON>

This script opens the default phone app to call <PERSON>.
"""

import webbrowser
import platform
import os

# <PERSON> contact information
PAUL_PHONE = "+17722089646"
PAUL_NAME = "<PERSON>"

def open_phone_app():
    """Open the default phone app to call <PERSON>"""
    print("=" * 80)
    print("OPENING PHONE APP TO CALL PAUL EDWARDS")
    print("=" * 80)
    
    # Create phone URL based on platform
    if platform.system() == "Darwin":  # macOS
        # For macOS, use the tel: protocol
        phone_url = f"tel:{PAUL_PHONE}"
    elif platform.system() == "Windows":
        # For Windows, use the tel: protocol
        phone_url = f"tel:{PAUL_PHONE}"
    elif platform.system() == "Linux":
        # For Linux, try the tel: protocol
        phone_url = f"tel:{PAUL_PHONE}"
    else:
        print(f"Unsupported platform: {platform.system()}")
        return False
    
    try:
        # Open the default phone app
        webbrowser.open(phone_url)
        print(f"Default phone app opened to call {PAUL_PHONE}")
        print("Please complete the call in your phone app.")
        return True
    except Exception as e:
        print(f"Error opening default phone app: {str(e)}")
        return False

def print_call_script():
    """Print the call script for <PERSON>"""
    script = f"""
CALL SCRIPT FOR PAUL EDWARDS

Introduction:
"Hi {PAUL_NAME.split()[0]}, this is Sandra Smith with Flo Faction Insurance. How are you doing today? [PAUSE FOR RESPONSE]

I'm reaching out because I help people like you create tax-free retirement income without the risk of the stock market. Based on your profile, I've found that you might qualify for a strategy that could provide around $5,000 per month in tax-free retirement income. Would that be something you'd be interested in learning more about? [PAUSE FOR RESPONSE]

Great! I've prepared a personalized analysis for you that shows how an Indexed Universal Life policy could help you build tax-free retirement income while also providing a death benefit of around $720,000 to protect your family.

I'd like to schedule about 30 minutes to walk you through this analysis and answer any questions you might have. Would Tuesday at 2:00 PM or Thursday at 4:00 PM work better for your schedule? [PAUSE FOR RESPONSE]

Excellent! I'll send you a calendar invitation with all the details. Before we wrap up, do you have any initial questions about how this strategy works? [PAUSE FOR RESPONSE]

[ADDRESS ANY QUESTIONS]

I'm looking forward to our conversation on [CONFIRMED DATE/TIME]. In the meantime, I'll send you a brief overview via email so you can get familiar with the concept. Thank you for your time today, {PAUL_NAME.split()[0]}, and have a great day!"

Key Talking Points:
- Tax-free retirement income potential
- Protection from market downturns
- Death benefit protection for family
- Cash value growth potential
- Flexibility of the policy

Objection Handling:
- "I need to think about it" → "I understand completely. This is an important decision. The meeting is just to provide information so you can make an informed decision. There's no obligation, and I won't be asking you to make any decisions during our call."
- "I already have retirement plans" → "That's great! Many of my clients use this strategy to complement their existing retirement plans, especially to create a tax-free income stream. Would it be worth 30 minutes to see if this could enhance what you're already doing?"
- "Is this whole life insurance?" → "No, this is Indexed Universal Life, which is quite different. It provides both death benefit protection and cash value growth potential linked to market indexes, but without the risk of market losses. I'd be happy to explain the differences in our meeting."
    """
    print("\n" + "=" * 80)
    print("CALL SCRIPT")
    print("=" * 80)
    print(script)

if __name__ == "__main__":
    print("This script will open your default phone app to call Paul Edwards.")
    print(f"Recipient: {PAUL_PHONE}")
    
    # Print the call script
    print_call_script()
    
    proceed = input("\nDo you want to proceed with the call? (yes/no): ")
    
    if proceed.lower() == "yes":
        open_phone_app()
    else:
        print("Operation cancelled.")
