"""
Twilio API Key Test

This script tests Twilio authentication using API Key and Secret instead of Account SID and Auth Token.
"""

import os
from twilio.rest import Client

# Twilio credentials
TWILIO_ACCOUNT_SID = "AC187c871afa232bbbc978caf33f3e25d9"
TWILIO_API_KEY = "**********************************"
TWILIO_API_SECRET = "CqpVewwter1BEMdFIFHrN2XmUyt22wBP"

def test_twilio_api_key():
    """Test Twilio authentication using API Key and Secret"""
    print("=" * 80)
    print("TESTING TWILIO API KEY AUTHENTICATION")
    print("=" * 80)
    
    try:
        # Initialize Twilio client with API Key and Secret
        client = Client(TWILIO_API_KEY, TWILIO_API_SECRET, TWILIO_ACCOUNT_SID)
        
        # Try to fetch account information
        account = client.api.accounts(TWILIO_ACCOUNT_SID).fetch()
        
        print(f"Authentication successful!")
        print(f"Account SID: {account.sid}")
        print(f"Account Name: {account.friendly_name}")
        print(f"Account Status: {account.status}")
        
        return True
    except Exception as e:
        print(f"Authentication failed: {str(e)}")
        return False

if __name__ == "__main__":
    test_twilio_api_key()
