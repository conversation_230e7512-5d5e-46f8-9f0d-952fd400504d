#!/usr/bin/env python3
"""
Video Comment Lead Monitor

This script starts the continuous monitoring for the VideoCommentLeadFinder and
displays statistics about leads found from comments on social media platforms.
"""

import time
import json
from video_comment_lead_finder import VideoCommentLeadFinder
import logging

# Configure logging
logging.basicConfig(level=logging.INFO,
                    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def main():
    """Start continuous monitoring for video comment leads"""
    logger.info("Starting Video Comment Lead Finder system...")
    
    # Initialize the lead finder
    lead_finder = VideoCommentLeadFinder()
    
    # First run a one-time scan to find initial leads
    logger.info("Running initial scan for existing leads...")
    new_leads_count = lead_finder.find_leads_on_all_platforms()
    logger.info(f"Found {new_leads_count} new leads from existing video comments")
    
    if new_leads_count > 0:
        # Export leads to follow-up system
        exported_count = lead_finder.export_leads_to_crm()
        logger.info(f"Exported {exported_count} leads to follow-up system")
        
        # Display some information about the leads
        display_lead_info(lead_finder)
    
    # Ask if user wants to start continuous monitoring
    user_input = input("\nStart continuous monitoring for new comment leads? (y/n): ")
    if user_input.lower() == 'y':
        logger.info("Starting continuous monitoring...")
        try:
            # Set a shorter interval for the demo (5 minutes instead of default)
            lead_finder.start_monitoring(check_interval_minutes=5)
        except KeyboardInterrupt:
            logger.info("Monitoring stopped by user (Ctrl+C)")
        finally:
            # Display final statistics
            display_lead_info(lead_finder)
    else:
        logger.info("Continuous monitoring not started")

def display_lead_info(lead_finder):
    """Display information about the leads found"""
    stats = lead_finder.get_stats()
    
    print("\n=== Video Comment Lead Statistics ===")
    print(f"Total leads found: {stats['total_leads']}")
    
    print("\nLeads by platform:")
    for platform, count in stats['by_platform'].items():
        print(f"  - {platform.capitalize()}: {count}")
    
    print("\nLeads by product interest:")
    for product, count in stats['by_product'].items():
        product_name = product.replace("_", " ").title() if product else "General"
        print(f"  - {product_name}: {count}")
    
    print("\nLead status breakdown:")
    for status, count in stats['by_status'].items():
        print(f"  - {status.capitalize()}: {count}")
    
    # Display a few sample leads if any exist
    new_leads = lead_finder.get_leads_by_status("new")
    if new_leads:
        print("\nSample new leads:")
        for i, lead in enumerate(new_leads[:3]):  # Show up to 3 sample leads
            print(f"\nLead #{i+1}:")
            print(f"  Platform: {lead.get('platform', 'unknown').capitalize()}")
            print(f"  Author: {lead.get('author_name', 'unknown')}")
            print(f"  Comment: {lead.get('comment', '')[:80]}...")  # First 80 chars
            product = lead.get('detected_interest', {}).get('specific_product')
            if product:
                print(f"  Product Interest: {product.replace('_', ' ').title()}")
            print(f"  Confidence Score: {lead.get('detected_interest', {}).get('confidence_score', 0):.2f}")
    
    print("\n=====================================")

if __name__ == "__main__":
    main()