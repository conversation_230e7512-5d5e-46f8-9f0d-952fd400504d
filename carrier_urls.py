"""Configuration for carrier quote portals"""

CARRIER_URLS = {
    'mutual_of_omaha': {
        'base_url': 'https://www.mutualofomaha.com',
        'quote_url': 'https://www3.mutualofomaha.com/mobile-quotes/#/home',
        'login_url': 'https://www.mutualofomaha.com/accounts/login',
        'support_phone': '(*************'
    },
    
    'ffl_trident': {
        'base_url': 'https://ww3.familyfirstlife.com',
        'quote_url': 'https://ffltridentlife.com/quotes/#',
        'login_url': 'https://ww3.familyfirstlife.com/login',
        'support_phone': None
    },
    
    'uhc': {
        'base_url': 'https://jarvys.com',
        'quote_url': 'https://jarvys.com/quotes',
        'login_url': 'https://jarvys.com/login',
        'support_phone': None
    },
    
    'americo': {
        'base_url': 'https://www.americo.com',
        'quote_url': 'https://www.americo.com/agent/quotes',
        'login_url': 'https://www.americo.com/agent/login',
        'support_phone': '(*************'
    },
    
    'aetna': {
        'base_url': 'https://producerworld.com',
        'quote_url': 'https://producerworld.com/quotes',
        'login_url': 'https://producerworld.com/login',
        'support_phone': '(*************'
    },
    
    'cigna': {
        'base_url': 'https://cignaforbrokers.com',
        'quote_url': 'https://cignaforbrokers.com/quotes',
        'login_url': 'https://cignaforbrokers.com/login',
        'support_phone': None
    }
}

def get_carrier_urls(carrier: str) -> dict:
    """Get URLs for specific carrier"""
    carrier = carrier.lower().replace(' ', '_')
    return CARRIER_URLS.get(carrier, {})

def get_quote_url(carrier: str) -> str:
    """Get quote URL for specific carrier"""
    urls = get_carrier_urls(carrier)
    return urls.get('quote_url')

def get_login_url(carrier: str) -> str:
    """Get login URL for specific carrier"""
    urls = get_carrier_urls(carrier)
    return urls.get('login_url')

def get_support_phone(carrier: str) -> str:
    """Get support phone number for specific carrier"""
    urls = get_carrier_urls(carrier)
    return urls.get('support_phone')