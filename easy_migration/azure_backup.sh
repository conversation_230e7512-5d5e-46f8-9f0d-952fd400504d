#!/bin/bash
# ==============================================================
# PaulEdwardsAI Azure Cloud Migration Script
# Created: May 5, 2025
# Language-independent with visual indicators for Chinese VS Code
# Enhanced with Azure best practices
# ==============================================================

# Exit on error
set -e 

# Script directory for relative paths
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" && pwd )"

# Check for Azure CLI
if ! command -v az &> /dev/null; then
    echo "❌ Azure CLI 未安装 / Not installed"
    echo "🌐 https://docs.microsoft.com/en-us/cli/azure/install-azure-cli"
    exit 1
fi

# Check for Python 3
if ! command -v python3 &> /dev/null; then
    echo "❌ Python 3 未安装 / Not installed"
    echo "🌐 https://www.python.org/downloads/"
    exit 1
fi

# Create colorful header for better visibility regardless of language
echo -e "\033[1;34m======================================================\033[0m"
echo -e "\033[1;32m     PaulEdwardsAI Azure Cloud Migration Tool         \033[0m"
echo -e "\033[1;34m======================================================\033[0m"
echo ""

# Check Azure CLI login status
echo "🔍 检查 Azure 登录状态 / Checking Azure login status..."
az account show --query name -o tsv &> /dev/null
if [ $? -ne 0 ]; then
    echo "🔑 需要登录 Azure / Need to log in to Azure"
    az login
fi

# Show current subscription
CURRENT_SUB=$(az account show --query name -o tsv)
echo "📋 当前订阅 / Current subscription: $CURRENT_SUB"
echo ""

# Create a unique backup directory with timestamp
TIMESTAMP=$(date +%Y%m%d_%H%M%S)
BACKUP_DIR="PaulEdwardsAI_Cloud_${TIMESTAMP}"
mkdir -p "$BACKUP_DIR"
echo "📁 已创建备份目录 / Created backup directory: $BACKUP_DIR"
echo ""

# Function to show progress with visual indicators
show_progress() {
  local msg="$1"
  local progress="$2"
  local total_width=50
  local filled_width=$((progress * total_width / 100))
  local empty_width=$((total_width - filled_width))
  
  printf "\r[%s%s] %3d%% %s" \
         "$(printf '#%.0s' $(seq 1 $filled_width))" \
         "$(printf ' %.0s' $(seq 1 $empty_width))" \
         "$progress" \
         "$msg"
}

# 1. Copy all project files with visual progress
echo "📦 步骤 1/7: 复制项目文件 / Step 1/7: Copying project files..."
total_files=$(find . -type f -not -path "./${BACKUP_DIR}/*" -not -path "./venv/*" -not -path "./.git/*" | wc -l)
current_file=0

find . -type f -not -path "./${BACKUP_DIR}/*" -not -path "./venv/*" -not -path "./.git/*" -not -path "./node_modules/*" | while read file; do
  target_dir="${BACKUP_DIR}/$(dirname "$file")"
  mkdir -p "$target_dir"
  cp "$file" "$target_dir/" 2>/dev/null || true
  current_file=$((current_file + 1))
  progress=$((current_file * 100 / total_files))
  show_progress "Copying files / 复制文件" $progress
done
echo -e "\n✅ 文件复制成功 / Files copied successfully"

# 2. Create inventory of installed packages
echo ""
echo "📋 步骤 2/7: 创建软件包清单 / Step 2/7: Creating package inventory..."
mkdir -p "${BACKUP_DIR}/environment"

# Python packages
if command -v pip &>/dev/null; then
  echo "🐍 Python packages:" > "${BACKUP_DIR}/environment/python_packages.txt"
  pip freeze >> "${BACKUP_DIR}/environment/python_packages.txt"
  echo "✅ Python packages listed / Python 软件包已列出"
fi

# Node packages
if command -v npm &>/dev/null; then
  echo "📦 Node packages:" > "${BACKUP_DIR}/environment/node_packages.txt"
  (npm list --depth=0 2>/dev/null || echo "No node packages found") >> "${BACKUP_DIR}/environment/node_packages.txt"
  echo "✅ Node packages listed / Node 软件包已列出"
fi

# Docker images
if command -v docker &>/dev/null; then
  echo "🐳 Docker images:" > "${BACKUP_DIR}/environment/docker_images.txt"
  (docker images --format "{{.Repository}}:{{.Tag}}" || echo "No docker images found") >> "${BACKUP_DIR}/environment/docker_images.txt"
  echo "✅ Docker images listed / Docker 镜像已列出"
fi

# 3. Secure credentials with encryption
echo ""
echo "🔐 步骤 3/7: 安全凭证 / Step 3/7: Securing credentials..."
mkdir -p "${BACKUP_DIR}/secure_configs"

# Generate secure encryption key using OpenSSL for better security
openssl rand -base64 32 > "${BACKUP_DIR}/secure_configs/encryption_key.txt"
echo "🔑 已创建加密密钥 / Created encryption key"

# Find and encrypt credential files
echo "正在加密凭据文件 / Encrypting credential files..."
find . -type f \( -name "*credential*" -o -name "*.key" -o -name "*.pem" -o -name "*.env" -o -name "*secret*" -o -name "*token*" \) -not -path "./${BACKUP_DIR}/*" -not -path "./venv/*" -not -path "./.git/*" | while read file; do
  if [ -f "$file" ]; then
    encrypted_file="${BACKUP_DIR}/secure_configs/$(basename "$file").enc"
    # Encrypt with generated key
    openssl enc -aes-256-cbc -salt -in "$file" -out "$encrypted_file" -pass file:"${BACKUP_DIR}/secure_configs/encryption_key.txt" 2>/dev/null
    echo "🔒 Encrypted / 已加密: $file"
  fi
done
echo "✅ 凭据已加密 / Credentials secured"

# 4. System information
echo ""
echo "💻 步骤 4/7: 收集系统信息 / Step 4/7: Collecting system information..."
mkdir -p "${BACKUP_DIR}/system_info"

# OS information
echo "🖥️  Operating System / 操作系统:" > "${BACKUP_DIR}/system_info/system_details.txt"
uname -a >> "${BACKUP_DIR}/system_info/system_details.txt"

# Python information
if command -v python &>/dev/null; then
  echo -e "\n🐍 Python Version / Python 版本:" >> "${BACKUP_DIR}/system_info/system_details.txt"
  python --version 2>&1 >> "${BACKUP_DIR}/system_info/system_details.txt"
fi

# Node.js information
if command -v node &>/dev/null; then
  echo -e "\n📦 Node.js Version / Node.js 版本:" >> "${BACKUP_DIR}/system_info/system_details.txt"
  node --version 2>&1 >> "${BACKUP_DIR}/system_info/system_details.txt"
fi

# Docker information
if command -v docker &>/dev/null; then
  echo -e "\n🐳 Docker Version / Docker 版本:" >> "${BACKUP_DIR}/system_info/system_details.txt"
  docker --version 2>&1 >> "${BACKUP_DIR}/system_info/system_details.txt"
fi

# Azure CLI information
echo -e "\n☁️ Azure CLI Version / Azure CLI 版本:" >> "${BACKUP_DIR}/system_info/system_details.txt"
az --version | head -n 1 >> "${BACKUP_DIR}/system_info/system_details.txt"

# Security tools information
echo -e "\n🔒 Security Tools / 安全工具:" >> "${BACKUP_DIR}/system_info/system_details.txt"
if [ -f "install_security_tools.sh" ]; then
  grep "apt-get install\|brew install" install_security_tools.sh >> "${BACKUP_DIR}/system_info/system_details.txt" 2>/dev/null || echo "  No security tools found" >> "${BACKUP_DIR}/system_info/system_details.txt"
elif [ -f "install_security_tools_macos.sh" ]; then
  grep "brew install" install_security_tools_macos.sh >> "${BACKUP_DIR}/system_info/system_details.txt" 2>/dev/null || echo "  No security tools found" >> "${BACKUP_DIR}/system_info/system_details.txt"
fi

echo "✅ 系统信息已收集 / System information collected"

# 5. Create enhanced bilingual HTML migration guide
echo ""
echo "📝 步骤 5/7: 创建迁移指南 / Step 5/7: Creating migration guide..."

# Create bilingual HTML guide
cat > "${BACKUP_DIR}/MIGRATION_GUIDE.html" << 'EOL'
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>PaulEdwardsAI Migration Guide / 迁移指南</title>
    <style>
        body { font-family: Arial, sans-serif; max-width: 800px; margin: 0 auto; padding: 20px; }
        h1 { color: #2c3e50; text-align: center; }
        h2 { color: #3498db; border-bottom: 1px solid #3498db; padding-bottom: 5px; }
        .step { background-color: #f8f9fa; padding: 15px; margin: 15px 0; border-radius: 5px; }
        .icon { font-size: 24px; margin-right: 10px; }
        code { background-color: #f1f1f1; padding: 2px 5px; border-radius: 3px; font-family: monospace; }
        .warning { background-color: #fff3cd; padding: 10px; border-left: 5px solid #ffc107; }
        .success { background-color: #d4edda; padding: 10px; border-left: 5px solid #28a745; }
        .en { color: #000; }
        .zh { color: #0056b3; }
        .azure-section { background-color: #e6f3ff; padding: 15px; margin: 15px 0; border-radius: 5px; border-left: 5px solid #0078d4; }
        .note { font-style: italic; color: #666; }
    </style>
</head>
<body>
    <h1>🚀 PaulEdwardsAI Azure Cloud Migration Guide / Azure 云迁移指南</h1>
    
    <div class="step">
        <h2>📋 Step 1: Download from Azure / 步骤 1: 从 Azure 下载</h2>
        <p class="en">Download your backup from Azure Blob Storage using the Azure Portal or Azure CLI.</p>
        <p class="zh">使用 Azure 门户或 Azure CLI 从 Azure Blob Storage 下载备份。</p>
        <code>
            # Azure CLI<br>
            az storage blob download --account-name [STORAGE_ACCOUNT] --container-name [CONTAINER] --name [BACKUP_NAME] --file [LOCAL_PATH] --auth-mode login
        </code>
        
        <div class="azure-section">
            <p class="en"><strong>Azure Best Practice:</strong> For large files (≥100MB), use the Python helper script for parallel downloads:</p>
            <p class="zh"><strong>Azure 最佳做法:</strong> 对于大文件 (≥100MB), 使用 Python 助手脚本进行并行下载:</p>
            <code>
                python3 azure_backup_helper.py --account [STORAGE_ACCOUNT] --container [CONTAINER] --file [LOCAL_PATH] --blob-name [BACKUP_NAME]
            </code>
        </div>
    </div>
    
    <div class="step">
        <h2>🔧 Step 2: Setup Environment / 步骤 2: 设置环境</h2>
        <p class="en"><strong>Python Setup:</strong></p>
        <p class="zh"><strong>Python 设置:</strong></p>
        <code>
            # Create virtual environment / 创建虚拟环境<br>
            python -m venv venv<br><br>
            
            # Activate virtual environment / 激活虚拟环境<br>
            # On macOS/Linux / 在 macOS/Linux 上:<br>
            source venv/bin/activate<br>
            # On Windows / 在 Windows 上:<br>
            venv\Scripts\activate<br><br>
            
            # Install dependencies / 安装依赖<br>
            pip install -r environment/python_packages.txt
        </code>
        
        <p class="en"><strong>Docker Setup (if needed):</strong></p>
        <p class="zh"><strong>Docker 设置 (如果需要):</strong></p>
        <code>
            # Build container / 构建容器<br>
            docker-compose build<br><br>
            
            # Start services / 启动服务<br>
            docker-compose up -d
        </code>
    </div>
    
    <div class="step">
        <h2>🔐 Step 3: Restore Credentials / 步骤 3: 还原凭证</h2>
        <p class="en">Decrypt your encrypted files:</p>
        <p class="zh">解密您的加密文件:</p>
        <code>
            # Decrypt a file / 解密文件<br>
            openssl enc -aes-256-cbc -d -salt -in secure_configs/filename.enc -out filename -pass file:secure_configs/encryption_key.txt
        </code>
        
        <div class="azure-section">
            <p class="en"><strong>Azure Best Practice:</strong> If your application uses Azure services, update connection strings and configuration to use Managed Identity where possible.</p>
            <p class="zh"><strong>Azure 最佳做法:</strong> 如果您的应用程序使用 Azure 服务，请更新连接字符串和配置以尽可能使用托管标识。</p>
        </div>
    </div>
    
    <div class="step">
        <h2>🔒 Step 4: Install Security Tools / 步骤 4: 安装安全工具</h2>
        <p class="en">Install security tools based on your OS:</p>
        <p class="zh">根据您的操作系统安装安全工具:</p>
        <code>
            # On macOS / 在 macOS 上:<br>
            bash install_security_tools_macos.sh<br><br>
            
            # On Linux / 在 Linux 上:<br>
            bash install_security_tools.sh
        </code>
    </div>
    
    <div class="step">
        <h2>📱 Step 5: Configure Communication / 步骤 5: 配置通信</h2>
        <p class="en"><strong>Email:</strong> Update gmail_integration.py with your new device settings</p>
        <p class="zh"><strong>电子邮件:</strong> 使用新设备设置更新 gmail_integration.py</p>
        <p class="en"><strong>Phone:</strong> Update phone_utils.py with new device paths</p>
        <p class="zh"><strong>电话:</strong> 使用新设备路径更新 phone_utils.py</p>
    </div>
    
    <div class="step">
        <h2>🧪 Step 6: Test / 步骤 6: 测试</h2>
        <p class="en">Test each component:</p>
        <p class="zh">测试每个组件:</p>
        <code>
            # Core functionality / 核心功能<br>
            python main.py<br><br>
            
            # Email integration / 电子邮件集成<br>
            python email_integration_test.py<br><br>
            
            # Security / 安全<br>
            python agent_security_integration.py
        </code>
    </div>
    
    <div class="azure-section">
        <h2>☁️ Azure Integration / Azure 集成</h2>
        <p class="en">If your application uses Azure services:</p>
        <p class="zh">如果您的应用程序使用 Azure 服务：</p>
        <ol>
            <li class="en">Update Azure configuration in your application</li>
            <li class="zh">更新应用程序中的 Azure 配置</li>
            <li class="en">Verify Azure resource access permissions</li>
            <li class="zh">验证 Azure 资源访问权限</li>
            <li class="en">Test Azure service connectivity</li>
            <li class="zh">测试 Azure 服务连接性</li>
        </ol>
        
        <p class="note en">Note: Always use Managed Identity for Azure authentication when possible.</p>
        <p class="note zh">注意：尽可能使用托管标识进行 Azure 身份验证。</p>
    </div>
    
    <div class="success">
        <p class="en">✅ Your PaulEdwardsAI system should now be fully migrated!</p>
        <p class="zh">✅ 您的 PaulEdwardsAI 系统现在应该已完全迁移！</p>
    </div>
</body>
</html>
EOL

# Create compressed archive
echo ""
echo "📦 步骤 6/7: 创建压缩存档 / Step 6/7: Creating compressed archive..."
tar -czf "${BACKUP_DIR}.tar.gz" "$BACKUP_DIR"
echo "✅ 存档已创建 / Archive created: ${BACKUP_DIR}.tar.gz"

# 7. Azure storage setup and upload using the Python helper
echo ""
echo "☁️ 步骤 7/7: 上传到 Azure / Step 7/7: Uploading to Azure..."

# Use GUI-like display for storage account selection
echo "选择存储帐户 / Select Storage Account:"
echo "--------------------------------"
az storage account list --query "[].{Name:name, ResourceGroup:resourceGroup, Location:location}" -o table

# Use env vars if available, otherwise prompt
STORAGE_ACCOUNT=${AZURE_STORAGE_ACCOUNT}
RESOURCE_GROUP=${AZURE_RESOURCE_GROUP}

if [ -z "$STORAGE_ACCOUNT" ]; then
  read -p "输入存储帐户名称 / Enter storage account name: " STORAGE_ACCOUNT
fi

if [ -z "$RESOURCE_GROUP" ]; then
  read -p "输入资源组名称 / Enter resource group name: " RESOURCE_GROUP
fi

# Set container names with emoji prefixes for better visibility
MAIN_CONTAINER="paul-edwards-ai-backup"
SECURE_CONTAINER="paul-edwards-ai-secure"

# Create containers and upload with our Python helper (following Azure best practices)
echo ""
echo "🛠️ 使用 Python 助手上传到 Azure / Using Python helper to upload to Azure..."

# Make sure the Python helper is executable
chmod +x "$SCRIPT_DIR/azure_backup_helper.py"

# Create containers
echo "创建容器 / Creating containers..."
python3 "$SCRIPT_DIR/azure_backup_helper.py" --account "$STORAGE_ACCOUNT" --container "$MAIN_CONTAINER" --file "/dev/null" || true
python3 "$SCRIPT_DIR/azure_backup_helper.py" --account "$STORAGE_ACCOUNT" --container "$SECURE_CONTAINER" --file "/dev/null" || true

# Upload main archive with better handling of large files
echo "上传主备份 / Uploading main backup..."
python3 "$SCRIPT_DIR/azure_backup_helper.py" --account "$STORAGE_ACCOUNT" --container "$MAIN_CONTAINER" --file "${BACKUP_DIR}.tar.gz"

# Upload encryption key to separate secure container
echo "上传加密密钥 / Uploading encryption key..."
python3 "$SCRIPT_DIR/azure_backup_helper.py" --account "$STORAGE_ACCOUNT" --container "$SECURE_CONTAINER" --file "${BACKUP_DIR}/secure_configs/encryption_key.txt" --blob-name "encryption_key.txt"

# Create a bilingual summary report with enhanced Azure information
cat > "azure_backup_summary.txt" << EOL
======================================================
PaulEdwardsAI Azure Backup Summary / Azure 备份摘要
======================================================

Date / 日期: $(date)
Local Backup / 本地备份: $BACKUP_DIR
Archive / 存档: ${BACKUP_DIR}.tar.gz

Azure Storage Details / Azure 存储详细信息:
- Storage Account / 存储帐户: $STORAGE_ACCOUNT
- Resource Group / 资源组: $RESOURCE_GROUP
- Main Container / 主容器: $MAIN_CONTAINER
- Secure Container / 安全容器: $SECURE_CONTAINER

To download your backup / 下载备份:
az storage blob download --account-name $STORAGE_ACCOUNT --container-name $MAIN_CONTAINER --name ${BACKUP_DIR}.tar.gz --file ${BACKUP_DIR}.tar.gz --auth-mode login

To download your encryption key / 下载加密密钥:
az storage blob download --account-name $STORAGE_ACCOUNT --container-name $SECURE_CONTAINER --name encryption_key.txt --file encryption_key.txt --auth-mode login

Azure Best Practices / Azure 最佳做法:
- For large files (≥100MB), use the Python helper script / 对于大文件，请使用 Python 助手脚本
- Use Managed Identity authentication when possible / 尽可能使用托管身份验证
- Ensure proper RBAC permissions on containers / 确保容器上有适当的 RBAC 权限
EOL

# Print completion message with bilingual instructions
echo ""
echo -e "\033[1;32m✅ Azure 云备份已完成! / Azure cloud backup completed!\033[0m"
echo -e "\033[1;34m======================================================\033[0m"
echo "📁 备份目录 / Backup directory: $BACKUP_DIR"
echo "🗜️  压缩存档 / Compressed archive: ${BACKUP_DIR}.tar.gz"
echo "☁️  Azure 存储帐户 / Storage account: $STORAGE_ACCOUNT"
echo -e "\033[1;34m======================================================\033[0m"
echo ""
echo "📱 后续步骤 / Next steps:"
echo "  1️⃣  备份可在 Azure Blob Storage 获取 / Backup is available in Azure Blob Storage"
echo "  2️⃣  在新设备上下载备份 / Download the backup on your new device"
echo "  3️⃣  按照 MIGRATION_GUIDE.html 中的说明进行操作 / Follow instructions in MIGRATION_GUIDE.html"
echo -e "\033[1;34m======================================================\033[0m"