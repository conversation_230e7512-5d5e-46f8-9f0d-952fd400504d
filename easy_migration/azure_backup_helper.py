#!/usr/bin/env python3
"""
PaulEdwardsAI Azure Cloud Backup Helper
Created: May 5, 2025

This script implements Azure best practices for uploading files to Azure Blob Storage
- Parallel uploads for files ≥100MB
- Retry logic with exponential backoff
- Proper error handling and logging
- Support for managed identity when available
"""

import os
import sys
import logging
import time
import argparse
from datetime import datetime
import subprocess
from concurrent.futures import ThreadPoolExecutor
import tempfile
import json

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("azure_backup.log"),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger("azure_backup_helper")

# Constants
MAX_RETRIES = 5
INITIAL_RETRY_DELAY = 1  # seconds
CHUNK_SIZE = 4 * 1024 * 1024  # 4MB chunks for large file uploads
LARGE_FILE_THRESHOLD = 100 * 1024 * 1024  # 100MB threshold for parallel upload
MAX_PARALLEL_UPLOADS = 4

class AzureBackupHelper:
    """Helper class for Azure blob storage backup operations"""
    
    def __init__(self, storage_account, container, resource_group=None):
        """Initialize the backup helper"""
        self.storage_account = storage_account
        self.container = container
        self.resource_group = resource_group
        
        # Check if we're running in Azure environment
        self.in_azure_environment = self._check_in_azure_environment()
        
        # Check if Azure CLI is installed
        self._check_az_cli()
        
        logger.info(f"Azure Backup Helper initialized for storage account: {storage_account}")
        logger.info(f"Container: {container}")
        logger.info(f"Using managed identity: {self.in_azure_environment}")
    
    def _check_in_azure_environment(self):
        """Check if we're running in an Azure environment with managed identity"""
        # Check for Azure environment variables
        azure_env_vars = ['IDENTITY_ENDPOINT', 'IDENTITY_HEADER', 'MSI_ENDPOINT', 'MSI_SECRET']
        return any(env_var in os.environ for env_var in azure_env_vars)
    
    def _check_az_cli(self):
        """Check if Azure CLI is installed"""
        try:
            result = subprocess.run(['az', '--version'], 
                                  capture_output=True, 
                                  text=True, 
                                  check=False)
            if result.returncode != 0:
                logger.error("Azure CLI is not installed or not in PATH")
                print("❌ Azure CLI not found. Please install it first.")
                print("🌐 https://docs.microsoft.com/en-us/cli/azure/install-azure-cli")
                sys.exit(1)
        except FileNotFoundError:
            logger.error("Azure CLI not found")
            print("❌ Azure CLI not found. Please install it first.")
            print("🌐 https://docs.microsoft.com/en-us/cli/azure/install-azure-cli")
            sys.exit(1)
    
    def create_container(self):
        """Create the blob container if it doesn't exist"""
        logger.info(f"Creating container {self.container} if it doesn't exist")
        
        # Determine authentication method
        auth_params = "--auth-mode login"
        if self.in_azure_environment:
            auth_params = "--auth-mode login --identity"
        
        try:
            cmd = f"az storage container create --name {self.container} --account-name {self.storage_account} {auth_params}"
            result = self._run_command_with_retry(cmd)
            
            if '"created": true' in result:
                logger.info(f"Container {self.container} created successfully")
            else:
                logger.info(f"Container {self.container} already exists")
                
            return True
        except Exception as e:
            logger.error(f"Failed to create container: {e}")
            return False
    
    def upload_file(self, local_file, blob_name=None):
        """Upload a file to Azure Blob Storage with retry logic"""
        if not os.path.exists(local_file):
            logger.error(f"File not found: {local_file}")
            return False
        
        if not blob_name:
            blob_name = os.path.basename(local_file)
        
        file_size = os.path.getsize(local_file)
        logger.info(f"Uploading {local_file} ({self._format_size(file_size)}) to blob {blob_name}")
        
        # Determine authentication method
        auth_params = "--auth-mode login"
        if self.in_azure_environment:
            auth_params = "--auth-mode login --identity"
        
        try:
            # For larger files, use azcopy for better performance
            if file_size >= LARGE_FILE_THRESHOLD:
                logger.info(f"Using parallel upload for large file: {local_file}")
                cmd = (f"az storage blob upload-batch --source {os.path.dirname(local_file)} "
                       f"--destination {self.container} "
                       f"--account-name {self.storage_account} {auth_params} "
                       f"--pattern {os.path.basename(local_file)} "
                       f"--max-connections {MAX_PARALLEL_UPLOADS}")
                self._run_command_with_retry(cmd)
            else:
                cmd = (f"az storage blob upload --file {local_file} "
                       f"--name {blob_name} "
                       f"--container-name {self.container} "
                       f"--account-name {self.storage_account} {auth_params}")
                self._run_command_with_retry(cmd)
            
            logger.info(f"Successfully uploaded {local_file} to blob {blob_name}")
            return True
        except Exception as e:
            logger.error(f"Failed to upload file {local_file}: {e}")
            return False
    
    def upload_directory(self, local_dir, prefix=None):
        """Upload an entire directory to Azure Blob Storage"""
        if not os.path.isdir(local_dir):
            logger.error(f"Directory not found: {local_dir}")
            return False
        
        logger.info(f"Uploading directory {local_dir} to container {self.container}")
        
        # Determine authentication method
        auth_params = "--auth-mode login"
        if self.in_azure_environment:
            auth_params = "--auth-mode login --identity"
        
        try:
            cmd = (f"az storage blob upload-batch --source {local_dir} "
                   f"--destination {self.container} "
                   f"--account-name {self.storage_account} {auth_params}")
            
            if prefix:
                cmd += f" --destination-path {prefix}"
            
            self._run_command_with_retry(cmd)
            
            logger.info(f"Successfully uploaded directory {local_dir}")
            return True
        except Exception as e:
            logger.error(f"Failed to upload directory {local_dir}: {e}")
            return False
    
    def _run_command_with_retry(self, cmd):
        """Run a command with retry logic and exponential backoff"""
        retry_count = 0
        last_exception = None
        
        while retry_count < MAX_RETRIES:
            try:
                logger.debug(f"Running command: {cmd}")
                result = subprocess.check_output(cmd, shell=True, text=True)
                return result
            except subprocess.CalledProcessError as e:
                last_exception = e
                retry_count += 1
                
                if retry_count >= MAX_RETRIES:
                    logger.error(f"Command failed after {MAX_RETRIES} retries: {e}")
                    raise
                
                # Calculate exponential backoff with jitter
                delay = min(INITIAL_RETRY_DELAY * (2 ** (retry_count - 1)), 60)
                jitter = delay * 0.2 * (time.time() % 1)  # Add up to 20% random jitter
                delay += jitter
                
                logger.warning(f"Command failed (attempt {retry_count}/{MAX_RETRIES}), retrying in {delay:.2f}s: {e}")
                time.sleep(delay)
        
        if last_exception:
            raise last_exception
    
    @staticmethod
    def _format_size(size_bytes):
        """Format size in human-readable format"""
        for unit in ['B', 'KB', 'MB', 'GB', 'TB']:
            if size_bytes < 1024 or unit == 'TB':
                return f"{size_bytes:.2f} {unit}"
            size_bytes /= 1024

def main():
    """Main function when script is run directly"""
    parser = argparse.ArgumentParser(description='Azure Backup Helper')
    parser.add_argument('--account', required=True, help='Azure storage account name')
    parser.add_argument('--container', required=True, help='Azure container name')
    parser.add_argument('--resource-group', help='Azure resource group name')
    parser.add_argument('--file', help='Local file to upload')
    parser.add_argument('--directory', help='Local directory to upload')
    parser.add_argument('--blob-name', help='Destination blob name (for single file)')
    parser.add_argument('--prefix', help='Prefix for uploaded blobs (for directory)')
    
    args = parser.parse_args()
    
    helper = AzureBackupHelper(
        storage_account=args.account,
        container=args.container,
        resource_group=args.resource_group
    )
    
    # Create container
    helper.create_container()
    
    # Upload file or directory
    if args.file:
        helper.upload_file(args.file, args.blob_name)
    elif args.directory:
        helper.upload_directory(args.directory, args.prefix)
    else:
        parser.error("Either --file or --directory must be specified")

if __name__ == "__main__":
    main()