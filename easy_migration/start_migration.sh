#!/bin/bash
# ==============================================================
# PaulEdwardsAI Migration Launcher
# Created: May 5, 2025
# Language-independent launcher for migration tools
# ==============================================================

# Make colorful header for better visibility
echo -e "\033[1;34m======================================================\033[0m"
echo -e "\033[1;32m      PaulEdwardsAI Migration Tool Launcher           \033[0m"
echo -e "\033[1;34m======================================================\033[0m"
echo ""
echo "🚀 Choose your migration method / 选择迁移方法:"
echo ""
echo "1️⃣  Local Backup / 本地备份"
echo "    - Backup to local files / 备份到本地文件"
echo "    - Password protection option / 密码保护选项"
echo "    - HTML migration guide / HTML 迁移指南"
echo ""
echo "2️⃣  Azure Cloud Backup / Azure 云备份"
echo "    - Backup to Azure Blob Storage / 备份到 Azure Blob 存储"
echo "    - Secure credential encryption / 安全凭证加密" 
echo "    - Bilingual instructions (EN/CN) / 双语指南 (英文/中文)"
echo ""
echo "0️⃣  Exit / 退出"
echo ""

read -p "Enter your choice (1, 2, or 0) / 输入您的选择 (1, 2, 或 0): " choice

case $choice in
    1)
        echo "Starting local backup / 开始本地备份..."
        sleep 1
        ./migrate.sh
        ;;
    2)
        echo "Starting Azure cloud backup / 开始 Azure 云备份..."
        sleep 1
        ./azure_backup.sh
        ;;
    0)
        echo "Exiting / 退出..."
        exit 0
        ;;
    *)
        echo "Invalid choice / 无效选择"
        exit 1
        ;;
esac