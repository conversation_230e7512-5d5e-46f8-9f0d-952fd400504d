#!/bin/bash
# ==============================================================
# PaulEdwardsAI Universal Migration Script
# Created: May 5, 2025
# Works with any language version of VS Code
# ==============================================================

# Exit on error
set -e 

# Check if running with sudo (needed for some operations)
if [ "$EUID" -ne 0 ]; then
  echo "📋 Running without admin privileges. Some operations may fail."
  echo "💡 Consider running with sudo if you encounter permission errors."
fi

# Create colorful header for better visibility regardless of language
echo -e "\033[1;34m======================================================\033[0m"
echo -e "\033[1;32m           PaulEdwardsAI Migration Tool               \033[0m"
echo -e "\033[1;34m======================================================\033[0m"
echo ""
echo "🔄 Migration Start: $(date)"
echo ""

# Create a unique backup directory with timestamp
TIMESTAMP=$(date +%Y%m%d_%H%M%S)
BACKUP_DIR="PaulEdwardsAI_${TIMESTAMP}_Backup"
mkdir -p "$BACKUP_DIR"
echo "📁 Created backup directory: $BACKUP_DIR"

# Function to show progress
show_progress() {
  local msg="$1"
  local progress="$2"
  local total_width=50
  local filled_width=$((progress * total_width / 100))
  local empty_width=$((total_width - filled_width))
  
  printf "\r[%s%s] %3d%% %s" \
         "$(printf '#%.0s' $(seq 1 $filled_width))" \
         "$(printf ' %.0s' $(seq 1 $empty_width))" \
         "$progress" \
         "$msg"
}

# 1. Copy all project files (with visual progress indicators)
echo "📦 Step 1/5: Copying project files..."
total_files=$(find . -type f -not -path "./${BACKUP_DIR}/*" -not -path "./venv/*" -not -path "./.git/*" | wc -l)
current_file=0

find . -type f -not -path "./${BACKUP_DIR}/*" -not -path "./venv/*" -not -path "./.git/*" -not -path "./node_modules/*" | while read file; do
  target_dir="${BACKUP_DIR}/$(dirname "$file")"
  mkdir -p "$target_dir"
  cp "$file" "$target_dir/" 2>/dev/null || true
  current_file=$((current_file + 1))
  progress=$((current_file * 100 / total_files))
  show_progress "Copying files" $progress
done
echo -e "\n✅ Files copied successfully"

# 2. Create inventory of installed packages with icons for better visibility
echo ""
echo "📋 Step 2/5: Creating package inventory..."
mkdir -p "${BACKUP_DIR}/environment"

# Python packages
if command -v pip &>/dev/null; then
  echo "🐍 Python packages:" > "${BACKUP_DIR}/environment/python_packages.txt"
  pip freeze >> "${BACKUP_DIR}/environment/python_packages.txt"
  echo "✅ Python packages inventoried"
fi

# Node packages
if command -v npm &>/dev/null; then
  echo "📦 Node packages:" > "${BACKUP_DIR}/environment/node_packages.txt"
  (npm list --depth=0 2>/dev/null || echo "No node packages found") >> "${BACKUP_DIR}/environment/node_packages.txt"
  echo "✅ Node packages inventoried"
fi

# Docker images
if command -v docker &>/dev/null; then
  echo "🐳 Docker images:" > "${BACKUP_DIR}/environment/docker_images.txt"
  (docker images --format "{{.Repository}}:{{.Tag}}" || echo "No docker images found") >> "${BACKUP_DIR}/environment/docker_images.txt"
  echo "✅ Docker images inventoried"
fi

# 3. Secure credentials with visual password prompt
echo ""
echo "🔐 Step 3/5: Securing credentials and configuration files..."
mkdir -p "${BACKUP_DIR}/secure_configs"

# Generate secure backup password
read -s -p "🔑 Enter password to encrypt sensitive files (or press Enter to skip encryption): " BACKUP_PASSWORD
echo ""

# Find and secure credential files
if [ ! -z "$BACKUP_PASSWORD" ]; then
  # Export password to file to use with openssl
  echo "$BACKUP_PASSWORD" > .temp_pw_file
  
  find . -type f \( -name "*credential*" -o -name "*.key" -o -name "*.pem" -o -name "*.env" -o -name "*secret*" -o -name "*token*" \) -not -path "./${BACKUP_DIR}/*" -not -path "./venv/*" -not -path "./.git/*" | while read file; do
    if [ -f "$file" ]; then
      encrypted_file="${BACKUP_DIR}/secure_configs/$(basename "$file").enc"
      # Encrypt with password
      openssl enc -aes-256-cbc -salt -in "$file" -out "$encrypted_file" -pass file:.temp_pw_file 2>/dev/null
      echo "🔒 Encrypted: $file"
    fi
  done
  
  # Save the password reminder (first character and length) to help user remember
  first_char=$(echo "$BACKUP_PASSWORD" | cut -c1)
  length=${#BACKUP_PASSWORD}
  echo "Password hint: starts with '$first_char' and has $length characters" > "${BACKUP_DIR}/secure_configs/password_hint.txt"
  
  # Delete temporary password file
  rm .temp_pw_file
  
  echo "✅ Credentials secured with encryption"
else
  echo "⚠️  Encryption skipped - credentials will be copied without encryption"
  
  # Copy credential files without encryption
  find . -type f \( -name "*credential*" -o -name "*.key" -o -name "*.pem" -o -name "*.env" -o -name "*secret*" -o -name "*token*" \) -not -path "./${BACKUP_DIR}/*" -not -path "./venv/*" -not -path "./.git/*" | while read file; do
    if [ -f "$file" ]; then
      target_dir="${BACKUP_DIR}/secure_configs/$(dirname "$file")"
      mkdir -p "$target_dir"
      cp "$file" "$target_dir/" 2>/dev/null
      echo "📄 Copied: $file"
    fi
  done
fi

# 4. System information with emoji indicators for clarity
echo ""
echo "💻 Step 4/5: Collecting system information..."
mkdir -p "${BACKUP_DIR}/system_info"

# OS information
echo "🖥️  Operating System:" > "${BACKUP_DIR}/system_info/system_details.txt"
uname -a >> "${BACKUP_DIR}/system_info/system_details.txt"

# Python information
if command -v python &>/dev/null; then
  echo -e "\n🐍 Python Version:" >> "${BACKUP_DIR}/system_info/system_details.txt"
  python --version 2>&1 >> "${BACKUP_DIR}/system_info/system_details.txt"
fi

# Node.js information
if command -v node &>/dev/null; then
  echo -e "\n📦 Node.js Version:" >> "${BACKUP_DIR}/system_info/system_details.txt"
  node --version 2>&1 >> "${BACKUP_DIR}/system_info/system_details.txt"
fi

# Docker information
if command -v docker &>/dev/null; then
  echo -e "\n🐳 Docker Version:" >> "${BACKUP_DIR}/system_info/system_details.txt"
  docker --version 2>&1 >> "${BACKUP_DIR}/system_info/system_details.txt"
fi

# Security tools information
echo -e "\n🔒 Security Tools:" >> "${BACKUP_DIR}/system_info/system_details.txt"
if [ -f "install_security_tools.sh" ]; then
  grep "apt-get install\|brew install" install_security_tools.sh >> "${BACKUP_DIR}/system_info/system_details.txt" 2>/dev/null || echo "  No security tools found" >> "${BACKUP_DIR}/system_info/system_details.txt"
elif [ -f "install_security_tools_macos.sh" ]; then
  grep "brew install" install_security_tools_macos.sh >> "${BACKUP_DIR}/system_info/system_details.txt" 2>/dev/null || echo "  No security tools found" >> "${BACKUP_DIR}/system_info/system_details.txt"
fi

echo "✅ System information collected"

# 5. Create visual migration guide (with language-independent icons)
echo ""
echo "📝 Step 5/5: Creating migration guide..."

# Create visual HTML guide for language independence
cat > "${BACKUP_DIR}/MIGRATION_GUIDE.html" << 'EOL'
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>PaulEdwardsAI Migration Guide</title>
    <style>
        body { font-family: Arial, sans-serif; max-width: 800px; margin: 0 auto; padding: 20px; }
        h1 { color: #2c3e50; text-align: center; }
        h2 { color: #3498db; border-bottom: 1px solid #3498db; padding-bottom: 5px; }
        .step { background-color: #f8f9fa; padding: 15px; margin: 15px 0; border-radius: 5px; }
        .icon { font-size: 24px; margin-right: 10px; }
        code { background-color: #f1f1f1; padding: 2px 5px; border-radius: 3px; font-family: monospace; }
        .warning { background-color: #fff3cd; padding: 10px; border-left: 5px solid #ffc107; }
        .success { background-color: #d4edda; padding: 10px; border-left: 5px solid #28a745; }
    </style>
</head>
<body>
    <h1>🚀 PaulEdwardsAI Migration Guide</h1>
    
    <div class="step">
        <h2>📋 Step 1: Transfer Files</h2>
        <p>Transfer the entire backup folder to your new device.</p>
        <code>scp -r PaulEdwardsAI_*_Backup user@new-device:/destination/path</code>
        <p>Or use any file transfer method (USB drive, cloud storage, etc.)</p>
    </div>
    
    <div class="step">
        <h2>🔧 Step 2: Setup Environment</h2>
        <p><strong>Python Setup:</strong></p>
        <code>
            # Create virtual environment<br>
            python -m venv venv<br><br>
            
            # Activate virtual environment<br>
            # On macOS/Linux:<br>
            source venv/bin/activate<br>
            # On Windows:<br>
            venv\Scripts\activate<br><br>
            
            # Install dependencies<br>
            pip install -r environment/python_packages.txt
        </code>
        
        <p><strong>Docker Setup (if needed):</strong></p>
        <code>
            # Build container<br>
            docker-compose build<br><br>
            
            # Start services<br>
            docker-compose up -d
        </code>
    </div>
    
    <div class="step">
        <h2>🔐 Step 3: Restore Credentials</h2>
        <p>If you encrypted your sensitive files:</p>
        <code>
            # Decrypt a file<br>
            openssl enc -aes-256-cbc -d -salt -in secure_configs/filename.enc -out filename<br><br>
            # You'll be prompted for the password you used during backup
        </code>
        <p class="warning">⚠️ See password hint in secure_configs/password_hint.txt</p>
    </div>
    
    <div class="step">
        <h2>🔒 Step 4: Install Security Tools</h2>
        <p>Install security tools based on your OS:</p>
        <code>
            # On macOS:<br>
            bash install_security_tools_macos.sh<br><br>
            
            # On Linux:<br>
            bash install_security_tools.sh
        </code>
    </div>
    
    <div class="step">
        <h2>📱 Step 5: Configure Communication Channels</h2>
        <p><strong>Email:</strong> Update gmail_integration.py with your new device settings</p>
        <p><strong>Phone:</strong> Update phone_utils.py with new device paths</p>
        <p><strong>Voicemail:</strong> Verify audio files transferred correctly</p>
    </div>
    
    <div class="step">
        <h2>🧪 Step 6: Test Functionality</h2>
        <p>Test each component:</p>
        <code>
            # Core functionality<br>
            python main.py<br><br>
            
            # Email integration<br>
            python email_integration_test.py<br><br>
            
            # Security<br>
            python agent_security_integration.py
        </code>
    </div>
    
    <div class="success">
        <p>✅ Your PaulEdwardsAI system should now be fully migrated!</p>
        <p>If you encounter issues, check system_info/system_details.txt to compare environment details.</p>
    </div>
</body>
</html>
EOL

# Also create a plain text version for broader compatibility
cat > "${BACKUP_DIR}/MIGRATION_GUIDE.txt" << 'EOL'
==============================================================
                PaulEdwardsAI Migration Guide
==============================================================

Step 1: Transfer Files
---------------------
Transfer the entire backup folder to your new device.
- scp -r PaulEdwardsAI_*_Backup user@new-device:/destination/path
- Or use any file transfer method (USB drive, cloud storage, etc.)

Step 2: Setup Environment
------------------------
Python Setup:
# Create virtual environment
python -m venv venv

# Activate virtual environment
# On macOS/Linux:
source venv/bin/activate
# On Windows:
venv\Scripts\activate

# Install dependencies
pip install -r environment/python_packages.txt

Docker Setup (if needed):
# Build container
docker-compose build

# Start services
docker-compose up -d

Step 3: Restore Credentials
--------------------------
If you encrypted your sensitive files:
# Decrypt a file
openssl enc -aes-256-cbc -d -salt -in secure_configs/filename.enc -out filename

You'll be prompted for the password you used during backup.
See password hint in secure_configs/password_hint.txt

Step 4: Install Security Tools
----------------------------
Install security tools based on your OS:
# On macOS:
bash install_security_tools_macos.sh

# On Linux:
bash install_security_tools.sh

Step 5: Configure Communication Channels
--------------------------------------
- Email: Update gmail_integration.py with your new device settings
- Phone: Update phone_utils.py with new device paths
- Voicemail: Verify audio files transferred correctly

Step 6: Test Functionality
------------------------
Test each component:
# Core functionality
python main.py

# Email integration
python email_integration_test.py

# Security
python agent_security_integration.py

Your PaulEdwardsAI system should now be fully migrated!
If you encounter issues, check system_info/system_details.txt to compare environment details.
EOL

echo "✅ Migration guides created"

# Create archive
echo ""
echo "📦 Creating compressed archive..."
tar -czf "${BACKUP_DIR}.tar.gz" "$BACKUP_DIR"

# Print summary with visual elements for clarity
echo ""
echo -e "\033[1;32m✅ Migration backup completed successfully!\033[0m"
echo -e "\033[1;34m======================================================\033[0m"
echo "📁 Backup directory: $BACKUP_DIR"
echo "🗜️  Compressed archive: ${BACKUP_DIR}.tar.gz"
echo ""
echo "📱 Next steps:"
echo "  1️⃣  Transfer the ${BACKUP_DIR}.tar.gz file to your new device"
echo "  2️⃣  Extract: tar -xzf ${BACKUP_DIR}.tar.gz"
echo "  3️⃣  Follow the instructions in MIGRATION_GUIDE.html or MIGRATION_GUIDE.txt"
echo -e "\033[1;34m======================================================\033[0m"