"""
Social Media Manager - Manages interactions with social media platforms for the agency
"""

import os
import logging
import json
import asyncio
from typing import Dict, List, Optional, Union, Any
from datetime import datetime, timedelta
import aiohttp
import re
import base64

from secure_credentials import SecureCredentialsManager

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class PlatformConfig:
    """Configuration settings for social media platforms"""
    
    INSTAGRAM = {
        "api_base": "https://graph.instagram.com/v12.0/",
        "content_types": ["image", "carousel", "video"],
        "max_caption_length": 2200,
        "max_hashtags": 30,
        "max_mentions": 20,
        "supported_formats": [".jpg", ".png", ".mp4"],
    }
    
    TIKTOK = {
        "api_base": "https://open-api.tiktok.com/v2/",
        "content_types": ["video"],
        "max_caption_length": 150,
        "max_hashtags": 20,
        "max_mentions": 10,
        "supported_formats": [".mp4"],
    }
    
    WIX = {
        "api_base": "https://www.wixapis.com/blog/v3/",
        "content_types": ["post", "page"],
        "max_title_length": 100,
        "supported_formats": [".jpg", ".png", ".mp4", ".pdf"],
    }

class SocialMediaManager:
    """Manages social media content creation, posting, and analytics"""
    
    def __init__(self, creds_manager: SecureCredentialsManager = None):
        """Initialize the social media manager"""
        self.creds_manager = creds_manager or SecureCredentialsManager()
        self.platforms = {}
        self.post_history = []
        self.scheduled_posts = []
        self.analytics = {}
        
        # Initialize session
        self.session = None
        
    async def initialize(self):
        """Initialize platform connections"""
        try:
            logger.info("Initializing social media connections")
            
            # Create HTTP session
            self.session = aiohttp.ClientSession()
            
            # Initialize Instagram
            instagram_creds = await self.creds_manager.get_credential("instagram")
            if instagram_creds:
                self.platforms["instagram"] = {
                    "access_token": instagram_creds.get("access_token"),
                    "user_id": instagram_creds.get("user_id"),
                    "connected": True,
                    "config": PlatformConfig.INSTAGRAM
                }
                logger.info("Instagram connection initialized")
            else:
                self.platforms["instagram"] = {
                    "connected": False,
                    "config": PlatformConfig.INSTAGRAM
                }
                logger.warning("Instagram credentials not found")
                
            # Initialize TikTok
            tiktok_creds = await self.creds_manager.get_credential("tiktok")
            if tiktok_creds:
                self.platforms["tiktok"] = {
                    "access_token": tiktok_creds.get("access_token"),
                    "open_id": tiktok_creds.get("open_id"),
                    "connected": True,
                    "config": PlatformConfig.TIKTOK
                }
                logger.info("TikTok connection initialized")
            else:
                self.platforms["tiktok"] = {
                    "connected": False,
                    "config": PlatformConfig.TIKTOK
                }
                logger.warning("TikTok credentials not found")
                
            # Initialize Wix
            wix_creds = await self.creds_manager.get_credential("wix")
            if wix_creds:
                self.platforms["wix"] = {
                    "api_key": wix_creds.get("api_key"),
                    "site_id": wix_creds.get("site_id"),
                    "connected": True,
                    "config": PlatformConfig.WIX
                }
                logger.info("Wix connection initialized")
            else:
                self.platforms["wix"] = {
                    "connected": False,
                    "config": PlatformConfig.WIX
                }
                logger.warning("Wix credentials not found")
                
            # Load post history and scheduled posts
            await self._load_post_history()
            await self._load_scheduled_posts()
            
            return True
            
        except Exception as e:
            logger.error(f"Error initializing social media connections: {str(e)}")
            return False
    
    async def close(self):
        """Close connections"""
        if self.session:
            await self.session.close()
            
    async def _load_post_history(self):
        """Load post history from storage"""
        try:
            # Simple file-based storage for now, could be replaced with a database
            if os.path.exists("data/social_media/post_history.json"):
                with open("data/social_media/post_history.json", "r") as f:
                    self.post_history = json.load(f)
            else:
                self.post_history = []
                
            logger.info(f"Loaded {len(self.post_history)} posts from history")
            
        except Exception as e:
            logger.error(f"Error loading post history: {str(e)}")
            self.post_history = []
            
    async def _load_scheduled_posts(self):
        """Load scheduled posts from storage"""
        try:
            # Simple file-based storage for now
            if os.path.exists("data/social_media/scheduled_posts.json"):
                with open("data/social_media/scheduled_posts.json", "r") as f:
                    self.scheduled_posts = json.load(f)
            else:
                self.scheduled_posts = []
                
            logger.info(f"Loaded {len(self.scheduled_posts)} scheduled posts")
            
        except Exception as e:
            logger.error(f"Error loading scheduled posts: {str(e)}")
            self.scheduled_posts = []
    
    async def _save_post_history(self):
        """Save post history to storage"""
        try:
            os.makedirs("data/social_media", exist_ok=True)
            with open("data/social_media/post_history.json", "w") as f:
                json.dump(self.post_history, f)
                
        except Exception as e:
            logger.error(f"Error saving post history: {str(e)}")
            
    async def _save_scheduled_posts(self):
        """Save scheduled posts to storage"""
        try:
            os.makedirs("data/social_media", exist_ok=True)
            with open("data/social_media/scheduled_posts.json", "w") as f:
                json.dump(self.scheduled_posts, f)
                
        except Exception as e:
            logger.error(f"Error saving scheduled posts: {str(e)}")
    
    async def post_to_instagram(self, content: Dict) -> Dict:
        """Post content to Instagram"""
        try:
            if not self.platforms.get("instagram", {}).get("connected", False):
                return {"status": "error", "message": "Instagram not connected"}
                
            platform = self.platforms["instagram"]
            
            # Prepare request
            url = f"{platform['config']['api_base']}{platform['user_id']}/media"
            
            # Handle different content types
            media_type = "IMAGE"
            if content.get("carousel", False):
                media_type = "CAROUSEL"
            elif "video" in content:
                media_type = "VIDEO"
                
            params = {
                "access_token": platform["access_token"],
                "caption": content.get("caption", ""),
                "media_type": media_type
            }
            
            # Mock the API call for now
            # In production, replace with actual Instagram Graph API calls
            post_id = f"instagram_{datetime.now().strftime('%Y%m%d%H%M%S')}"
            
            # Record the post
            post_record = {
                "id": post_id,
                "platform": "instagram",
                "content": content,
                "timestamp": datetime.now().isoformat(),
                "status": "published"
            }
            self.post_history.append(post_record)
            await self._save_post_history()
            
            logger.info(f"Posted to Instagram: {post_id}")
            
            agent_id: The unique identifier for the insurance agent
            platform: The social media platform to post to
            content: The content to post
            media_paths: Optional list of paths to media files to include
            
        Returns:
            Dictionary containing post information (post_id, url, etc.)
        """
        credential_key = f"social_media_{agent_id}_{platform}"
        
        if not self.credentials_manager.has_credentials(credential_key):
            logger.error(f"No credentials found for agent {agent_id} on {platform}")
            raise ValueError(f"No credentials found for agent {agent_id} on {platform}")
            
        credentials = self.credentials_manager.get_credentials(credential_key)
        
        # Platform-specific posting logic
        try:
            if platform == 'twitter':
                # Placeholder for Twitter API posting
                # twitter = TwitterAPI(credentials)
                # response = twitter.create_tweet(content, media_paths)
                logger.info(f"Posted to Twitter for agent {agent_id}")
                return {"post_id": "mock_id_123", "platform": "twitter", "status": "success"}
                
            elif platform == 'facebook':
                # Placeholder for Facebook API posting
                logger.info(f"Posted to Facebook for agent {agent_id}")
                return {"post_id": "mock_id_456", "platform": "facebook", "status": "success"}
                
            elif platform == 'linkedin':
                # Placeholder for LinkedIn API posting
                logger.info(f"Posted to LinkedIn for agent {agent_id}")
                return {"post_id": "mock_id_789", "platform": "linkedin", "status": "success"}
                
            elif platform == 'instagram':
                # Placeholder for Instagram API posting
                logger.info(f"Posted to Instagram for agent {agent_id}")
                return {"post_id": "mock_id_101", "platform": "instagram", "status": "success"}
                
            else:
                logger.error(f"Unsupported platform: {platform}")
                raise ValueError(f"Unsupported platform: {platform}")
                
        except Exception as e:
            logger.error(f"Error posting to {platform}: {e}")
            raise RuntimeError(f"Error posting to {platform}: {e}")
    
    def schedule_post(self, agent_id: str, platform: str, content: str, 
                     schedule_time: datetime, media_paths: List[str] = None) -> Dict[str, Any]:
        """
        Schedule a post for later publication.
        
        Args:
            agent_id: The unique identifier for the insurance agent
            platform: The social media platform to post to
            content: The content to post
            schedule_time: When to publish the post
            media_paths: Optional list of paths to media files to include
            
        Returns:
            Dictionary containing schedule information (schedule_id, etc.)
        """
        credential_key = f"social_media_{agent_id}_{platform}"
        
        if not self.credentials_manager.has_credentials(credential_key):
            logger.error(f"No credentials found for agent {agent_id} on {platform}")
            raise ValueError(f"No credentials found for agent {agent_id} on {platform}")
            
        # Schedule ID format: platform_agentID_timestamp
        schedule_id = f"{platform}_{agent_id}_{int(datetime.timestamp(schedule_time))}"
        
        # Store the scheduled post in a database or queue system
        # For now, we'll just log it
        logger.info(f"Scheduled post for agent {agent_id} on {platform} at {schedule_time.isoformat()}")
        logger.info(f"Content: {content}")
        
        return {
            "schedule_id": schedule_id,
            "agent_id": agent_id,
            "platform": platform,
            "scheduled_time": schedule_time.isoformat(),
            "status": "scheduled"
        }
    
    def create_campaign(self, agent_id: str, campaign_name: str, platforms: List[str],
                      schedule_times: List[datetime], content_configs: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        Create a multi-post campaign across platforms.
        
        Args:
            agent_id: The unique identifier for the insurance agent
            campaign_name: Name of the campaign
            platforms: List of platforms to post to
            schedule_times: List of times to schedule posts
            content_configs: List of content configurations, each with 'content_type' and 'parameters'
            
        Returns:
            Dictionary containing campaign information
        """
        if len(schedule_times) != len(content_configs):
            raise ValueError("Number of schedule times must match number of content configurations")
            
        # Validate that agent has accounts for all platforms
        for platform in platforms:
            credential_key = f"social_media_{agent_id}_{platform}"
            if not self.credentials_manager.has_credentials(credential_key):
                logger.error(f"No credentials found for agent {agent_id} on {platform}")
                raise ValueError(f"No credentials found for agent {agent_id} on {platform}")
        
        # Create a unique campaign ID
        campaign_id = f"campaign_{agent_id}_{int(datetime.timestamp(datetime.now()))}"
        
        # Schedule all posts
        scheduled_posts = []
        for i, (schedule_time, content_config) in enumerate(zip(schedule_times, content_configs)):
            content_type = content_config.get('content_type')
            parameters = content_config.get('parameters', {})
            
            # Create the content
            try:
                content = self.create_content(content_type, parameters)
            except ValueError as e:
                logger.error(f"Error creating content for campaign {campaign_name}: {e}")
                continue
                
            # Schedule on each platform
            for platform in platforms:
                try:
                    schedule_result = self.schedule_post(agent_id, platform, content, schedule_time)
                    scheduled_posts.append({
                        "platform": platform,
                        "schedule_id": schedule_result['schedule_id'],
                        "scheduled_time": schedule_time.isoformat(),
                        "content_config": content_config,
                        "content": content
                    })
                except Exception as e:
                    logger.error(f"Error scheduling post on {platform}: {e}")
        
        # Store campaign details
        campaign = {
            "campaign_id": campaign_id,
            "agent_id": agent_id,
            "name": campaign_name,
            "platforms": platforms,
            "created_at": datetime.now().isoformat(),
            "scheduled_posts": scheduled_posts,
            "status": "active" if scheduled_posts else "failed"
        }
        
        # Add to active campaigns
        self.active_campaigns[campaign_id] = campaign
        
        logger.info(f"Created campaign '{campaign_name}' for agent {agent_id}")
        logger.info(f"Scheduled {len(scheduled_posts)} posts across {len(platforms)} platforms")
        
        return campaign
    
    def get_analytics(self, agent_id: str, platforms: List[str] = None, 
                    start_date: datetime = None, end_date: datetime = None) -> Dict[str, Any]:
        """
        Retrieve analytics data for an agent's social media accounts.
        
        Args:
            agent_id: The unique identifier for the insurance agent
            platforms: Optional list of platforms to get analytics for (default: all connected platforms)
            start_date: Start date for analytics period (default: 30 days ago)
            end_date: End date for analytics period (default: now)
            
        Returns:
            Dictionary containing analytics data
        """
        # Default date range: last 30 days
        if start_date is None:
            start_date = datetime.now() - timedelta(days=30)
        if end_date is None:
            end_date = datetime.now()
            
        # Determine which platforms to query
        if platforms is None:
            platforms = []
            for platform in self.supported_platforms:
                credential_key = f"social_media_{agent_id}_{platform}"
                if self.credentials_manager.has_credentials(credential_key):
                    platforms.append(platform)
        
        if not platforms:
            logger.warning(f"No social media accounts found for agent {agent_id}")
            return {"error": "No social media accounts found"}
            
        # Collect analytics from each platform
        analytics = {
            "agent_id": agent_id,
            "period": {
                "start": start_date.isoformat(),
                "end": end_date.isoformat()
            },
            "platforms": {},
            "engagement": {
                "total_likes": 0,
                "total_comments": 0,
                "total_shares": 0,
                "total_clicks": 0,
                "total_impressions": 0
            }
        }
        
        for platform in platforms:
            credential_key = f"social_media_{agent_id}_{platform}"
            if not self.credentials_manager.has_credentials(credential_key):
                logger.warning(f"No credentials found for agent {agent_id} on {platform}")
                continue
                
            credentials = self.credentials_manager.get_credentials(credential_key)
            
            # Platform-specific analytics retrieval
            try:
                if platform == 'twitter':
                    # Placeholder for Twitter analytics
                    # In a real implementation, we would call the Twitter API
                    platform_data = {
                        "post_count": 15,
                        "followers": 320,
                        "following": 150,
                        "likes": 230,
                        "retweets": 45,
                        "replies": 68,
                        "impressions": 3200
                    }
                    
                elif platform == 'facebook':
                    # Placeholder for Facebook analytics
                    platform_data = {
                        "post_count": 12,
                        "page_likes": 560,
                        "likes": 350,
                        "comments": 89,
                        "shares": 72,
                        "impressions": 5400
                    }
                    
                elif platform == 'linkedin':
                    # Placeholder for LinkedIn analytics
                    platform_data = {
                        "post_count": 8,
                        "connections": 480,
                        "likes": 190,
                        "comments": 42,
                        "shares": 35,
                        "impressions": 2800
                    }
                    
                elif platform == 'instagram':
                    # Placeholder for Instagram analytics
                    platform_data = {
                        "post_count": 10,
                        "followers": 680,
                        "likes": 420,
                        "comments": 95,
                        "impressions": 4200
                    }
                    
                else:
                    logger.warning(f"Unsupported platform for analytics: {platform}")
                    continue
                
                # Add to analytics data
                analytics["platforms"][platform] = platform_data
                
                # Update totals
                if "likes" in platform_data:
                    analytics["engagement"]["total_likes"] += platform_data["likes"]
                if "comments" in platform_data or "replies" in platform_data:
                    analytics["engagement"]["total_comments"] += platform_data.get("comments", 0) + platform_data.get("replies", 0)
                if "shares" in platform_data or "retweets" in platform_data:
                    analytics["engagement"]["total_shares"] += platform_data.get("shares", 0) + platform_data.get("retweets", 0)
                if "impressions" in platform_data:
                    analytics["engagement"]["total_impressions"] += platform_data["impressions"]
                
            except Exception as e:
                logger.error(f"Error retrieving analytics for {platform}: {e}")
        
        return analytics
    
    def generate_report(self, analytics_data: Dict[str, Any]) -> str:
        """
        Generate a formatted report from analytics data.
        
        Args:
            analytics_data: Analytics data as returned by get_analytics()
            
        Returns:
            Formatted report as a string
        """
        agent_id = analytics_data.get("agent_id", "Unknown")
        period_start = analytics_data.get("period", {}).get("start", "Unknown")
        period_end = analytics_data.get("period", {}).get("end", "Unknown")
        
        report = f"Social Media Analytics Report for Agent: {agent_id}\n"
        report += "=" * 50 + "\n\n"
        
        report += f"Period: {period_start} to {period_end}\n\n"
        
        # Overall engagement
        engagement = analytics_data.get("engagement", {})
        report += "Overall Engagement:\n"
        report += f"Total Likes: {engagement.get('total_likes', 0)}\n"
        report += f"Total Comments: {engagement.get('total_comments', 0)}\n"
        report += f"Total Shares: {engagement.get('total_shares', 0)}\n"
        report += f"Total Impressions: {engagement.get('total_impressions', 0)}\n\n"
        
        # Platform breakdown
        platforms = analytics_data.get("platforms", {})
        report += "Platform Breakdown:\n"
        
        for platform, data in platforms.items():
            report += f"\n{platform.capitalize()}:\n"
            
            for metric, value in data.items():
                # Format metric name for readability
                metric_name = metric.replace("_", " ").title()
                report += f"  {metric_name}: {value}\n"
                
        return report