"""
Simple Email Sender for <PERSON>

This script sends an email to <PERSON> using a simple SMTP connection.
"""

import smtplib
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
import ssl

# <PERSON> contact information
PAUL_EDWARDS = {
    "first_name": "<PERSON>",
    "last_name": "<PERSON>",
    "email": "<EMAIL>",
    "primary_phone": "+17722089646",
    "secondary_phone": "+17725395908"
}

# Agent information
AGENT_INFO = {
    "name": "<PERSON>",
    "agency": "Flo Faction Insurance",
    "phone": "Flo Faction AI Assistant",
    "email": "<EMAIL>",
    "website": "https://www.flofaction.com",
}

def send_email():
    """Send an email to <PERSON>"""
    print("=" * 80)
    print("SENDING EMAIL TO PAUL EDWARDS")
    print("=" * 80)
    
    # Email credentials - you'll need to enter these when prompted
    sender_email = input("Enter your email address: ")
    password = input("Enter your email password or app password: ")
    
    # Create email content
    subject = "Creating Tax-Free Retirement Income Without Market Risk"
    body = f"""
Dear {PAUL_EDWARDS['first_name']},

I hope this email finds you well. My name is {AGENT_INFO['name']} with {AGENT_INFO['agency']}, and I specialize in helping people like you create tax-free retirement income without the risk of the stock market.

Based on the information I have, I've prepared a personalized analysis that shows how you could potentially:

1. Generate approximately $5,000 per month in tax-free retirement income
2. Build cash value that grows without the risk of market losses
3. Provide a death benefit of around $720,000 to protect your family
4. Create a financial strategy that offers both protection and growth potential

Many of my clients are concerned about:
- Market volatility affecting their retirement savings
- Taxes reducing their retirement income
- Having enough money to maintain their lifestyle in retirement
- Protecting their family financially

The strategy I'd like to share with you addresses all these concerns through an Indexed Universal Life policy specifically designed for your situation.

Would you be interested in a brief 30-minute conversation to review this analysis? If so, please call me at {PAUL_EDWARDS['primary_phone']} or reply to this email with a good time to connect.

You can also learn more about our services by visiting our website at {AGENT_INFO['website']}.

Thank you for your time, and I look forward to speaking with you soon.

Best regards,
{AGENT_INFO['name']}
{AGENT_INFO['agency']}
{AGENT_INFO['website']}
    """
    
    # Create message
    message = MIMEMultipart()
    message["From"] = sender_email
    message["To"] = PAUL_EDWARDS["email"]
    message["Subject"] = subject
    
    # Attach body
    message.attach(MIMEText(body, "plain"))
    
    try:
        # Create secure connection and send email
        context = ssl.create_default_context()
        
        # Determine SMTP server based on email domain
        if "@gmail.com" in sender_email.lower():
            smtp_server = "smtp.gmail.com"
            smtp_port = 587
        elif "@outlook.com" in sender_email.lower() or "@hotmail.com" in sender_email.lower():
            smtp_server = "smtp.office365.com"
            smtp_port = 587
        elif "@yahoo.com" in sender_email.lower():
            smtp_server = "smtp.mail.yahoo.com"
            smtp_port = 587
        else:
            smtp_server = input("Enter your SMTP server (e.g., smtp.gmail.com): ")
            smtp_port = int(input("Enter your SMTP port (e.g., 587): "))
        
        with smtplib.SMTP(smtp_server, smtp_port) as server:
            server.starttls(context=context)
            server.login(sender_email, password)
            server.sendmail(sender_email, PAUL_EDWARDS["email"], message.as_string())
        
        print(f"Email sent successfully to {PAUL_EDWARDS['email']}")
        return True
    except Exception as e:
        print(f"Error sending email: {str(e)}")
        return False

if __name__ == "__main__":
    print("This script will send an email to Paul Edwards.")
    print("You will need to provide your email credentials.")
    print("Note: For Gmail, you'll need to use an App Password if you have 2FA enabled.")
    print("You can create an App Password at: https://myaccount.google.com/apppasswords")
    
    proceed = input("Do you want to proceed? (yes/no): ")
    
    if proceed.lower() == "yes":
        send_email()
    else:
        print("Operation cancelled.")
