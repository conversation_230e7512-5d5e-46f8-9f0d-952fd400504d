#!/bin/bash

# Activate virtual environment
source venv/bin/activate

# Function to display usage
show_usage() {
    echo "Usage: ./run.sh [OPTIONS]"
    echo "Options:"
    echo "  --all             Run all tests"
    echo "  --medicare        Test Medicare quote workflow"
    echo "  --life           Test life insurance workflow"
    echo "  --carriers       Test carrier connections"
    echo "  --learning       Test learning modules"
    echo "  --help           Show this help message"
}

# Function to run Medicare tests
run_medicare_tests() {
    echo "Running Medicare insurance workflow tests..."
    python test_live_system.py --mode medicare
}

# Function to run life insurance tests
run_life_tests() {
    echo "Running life insurance workflow tests..."
    python test_live_system.py --mode life
}

# Function to test carrier connections
test_carriers() {
    echo "Testing carrier connections..."
    python test_insurance_workflow.py --mode carriers
}

# Function to test learning modules
test_learning() {
    echo "Testing learning modules..."
    python test_system.py --mode learning
}

# Function to run all tests
run_all_tests() {
    echo "Running all system tests..."
    
    echo "1. Testing carrier connections..."
    test_carriers
    
    echo -e "\n2. Testing Medicare workflow..."
    run_medicare_tests
    
    echo -e "\n3. Testing life insurance workflow..."
    run_life_tests
    
    echo -e "\n4. Testing learning modules..."
    test_learning
    
    echo -e "\nAll tests completed!"
}

# Check for required files
check_requirements() {
    required_files=(
        "secure_credentials.py"
        "insurance_carriers.py"
        "client_template.py"
        "agent_coordinator.py"
        "knowledge_management.py"
        "agent_learning_modules.py"
        ".env"
    )

    for file in "${required_files[@]}"; do
        if [ ! -f "$file" ]; then
            echo "Error: Required file $file not found!"
            echo "Please run setup.sh first."
            exit 1
        fi
    done
}

# Check Python environment
check_python() {
    if ! command -v python3 &> /dev/null; then
        echo "Error: Python 3 is required but not installed."
        exit 1
    fi
}

# Main execution
main() {
    # Perform checks
    check_python
    check_requirements

    # Parse command line arguments
    case "$1" in
        --all)
            run_all_tests
            ;;
        --medicare)
            run_medicare_tests
            ;;
        --life)
            run_life_tests
            ;;
        --carriers)
            test_carriers
            ;;
        --learning)
            test_learning
            ;;
        --help)
            show_usage
            ;;
        *)
            echo "Invalid option. Use --help to see available options."
            exit 1
            ;;
    esac
}

# Create logs directory if it doesn't exist
mkdir -p logs

# Start logging
exec 1> >(tee -a "logs/run_$(date +%Y%m%d_%H%M%S).log") 2>&1

# Print system information
echo "=== System Information ==="
echo "Date: $(date)"
echo "Python version: $(python3 --version)"
echo "Environment: $(grep ENVIRONMENT .env | cut -d '=' -f2)"
echo "======================="

# Run main function with arguments
main "$@"

# Deactivate virtual environment
deactivate