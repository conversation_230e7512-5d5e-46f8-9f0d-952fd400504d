import logging
import os
import datetime
import json
import uuid
from typing import Dict, List, Optional, Any
import pandas as pd
from pydantic import BaseModel, Field, validator

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger("EnhancedLeadSystem")

class ContactInfo(BaseModel):
    """Contact information for a lead"""
    email: Optional[str] = None
    phone: Optional[str] = None
    address: Optional[str] = None
    city: Optional[str] = None
    state: Optional[str] = None
    zip_code: Optional[str] = None
    
    @validator('email')
    def email_must_be_valid(cls, v):
        if v is None:
            return v
        if '@' not in v:
            raise ValueError('Email must contain @')
        return v
    
    @validator('phone')
    def phone_must_be_valid(cls, v):
        if v is None:
            return v
        digits = ''.join(c for c in v if c.isdigit())
        if len(digits) < 10:
            raise ValueError('Phone number must have at least 10 digits')
        return v

class DemographicInfo(BaseModel):
    """Demographic information for a lead"""
    date_of_birth: Optional[str] = None  # ISO format
    age: Optional[int] = None
    gender: Optional[str] = None
    marital_status: Optional[str] = None
    occupation: Optional[str] = None
    
    @validator('date_of_birth')
    def date_of_birth_must_be_valid(cls, v):
        if v is None:
            return v
        try:
            datetime.datetime.fromisoformat(v)
        except ValueError:
            try:
                # Try to parse common date formats
                dt = datetime.datetime.strptime(v, '%m/%d/%Y')
                return dt.isoformat()
            except ValueError:
                raise ValueError('Invalid date format. Use ISO format or MM/DD/YYYY')
        return v
    
    @validator('age')
    def age_must_be_valid(cls, v):
        if v is None:
            return v
        if v < 0 or v > 120:
            raise ValueError('Age must be between 0 and 120')
        return v

class FinancialInfo(BaseModel):
    """Financial information for a lead"""
    income: Optional[float] = None
    budget: Optional[float] = None
    credit_score: Optional[int] = None
    
    @validator('credit_score')
    def credit_score_must_be_valid(cls, v):
        if v is None:
            return v
        if v < 300 or v > 850:
            raise ValueError('Credit score must be between 300 and 850')
        return v

class HealthInfo(BaseModel):
    """Health information for a lead"""
    height: Optional[str] = None  # In feet and inches or cm
    weight: Optional[float] = None  # In pounds or kg
    smoker: Optional[bool] = None
    pre_existing_conditions: List[str] = Field(default_factory=list)
    medications: List[str] = Field(default_factory=list)
    
    @validator('weight')
    def weight_must_be_valid(cls, v):
        if v is None:
            return v
        if v < 0:
            raise ValueError('Weight must be positive')
        return v

class MedicareDetails(BaseModel):
    """Medicare-specific details for a lead"""
    eligible: Optional[bool] = None
    part_a: Optional[bool] = None
    part_b: Optional[bool] = None
    part_c: Optional[bool] = None
    part_d: Optional[bool] = None
    medications: List[str] = Field(default_factory=list)
    preferred_doctors: List[str] = Field(default_factory=list)
    preferred_hospitals: List[str] = Field(default_factory=list)
    enrollment_date: Optional[str] = None  # ISO format
    
    @validator('enrollment_date')
    def enrollment_date_must_be_valid(cls, v):
        if v is None:
            return v
        try:
            datetime.datetime.fromisoformat(v)
        except ValueError:
            try:
                # Try to parse common date formats
                dt = datetime.datetime.strptime(v, '%m/%d/%Y')
                return dt.isoformat()
            except ValueError:
                raise ValueError('Invalid date format. Use ISO format or MM/DD/YYYY')
        return v

class InsurancePreferences(BaseModel):
    """Insurance preferences for a lead"""
    preferred_carriers: List[str] = Field(default_factory=list)
    current_carrier: Optional[str] = None
    current_premium: Optional[float] = None
    current_coverage_end_date: Optional[str] = None  # ISO format
    coverage_needs: List[str] = Field(default_factory=list)
    
    @validator('current_coverage_end_date')
    def current_coverage_end_date_must_be_valid(cls, v):
        if v is None:
            return v
        try:
            datetime.datetime.fromisoformat(v)
        except ValueError:
            try:
                # Try to parse common date formats
                dt = datetime.datetime.strptime(v, '%m/%d/%Y')
                return dt.isoformat()
            except ValueError:
                raise ValueError('Invalid date format. Use ISO format or MM/DD/YYYY')
        return v

class InteractionHistory(BaseModel):
    """Interaction history with a lead"""
    timestamp: str  # ISO format
    type: str  # "email", "call", "text", "meeting", "note"
    summary: str
    details: Optional[str] = None
    success: Optional[bool] = None
    
    @validator('timestamp')
    def timestamp_must_be_valid(cls, v):
        if v is None:
            raise ValueError('Timestamp is required')
        try:
            datetime.datetime.fromisoformat(v)
        except ValueError:
            raise ValueError('Invalid date format. Use ISO format')
        return v

class Lead(BaseModel):
    """Model for a lead"""
    id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    first_name: str
    last_name: str
    created_at: str = Field(default_factory=lambda: datetime.datetime.now().isoformat())
    updated_at: str = Field(default_factory=lambda: datetime.datetime.now().isoformat())
    source: Optional[str] = None
    status: str = "new"  # "new", "contacted", "qualified", "proposal", "won", "lost"
    tags: List[str] = Field(default_factory=list)
    follow_up_date: Optional[str] = None  # ISO format
    agent_id: Optional[str] = None
    contact: ContactInfo = Field(default_factory=ContactInfo)
    demographics: Optional[DemographicInfo] = None
    financials: Optional[FinancialInfo] = None
    health: Optional[HealthInfo] = None
    medicare_details: Optional[MedicareDetails] = None
    insurance_preferences: Optional[InsurancePreferences] = None
    interaction_history: List[InteractionHistory] = Field(default_factory=list)
    notes: List[str] = Field(default_factory=list)
    
    @validator('follow_up_date')
    def follow_up_date_must_be_valid(cls, v):
        if v is None:
            return v
        try:
            datetime.datetime.fromisoformat(v)
        except ValueError:
            try:
                # Try to parse common date formats
                dt = datetime.datetime.strptime(v, '%m/%d/%Y')
                return dt.isoformat()
            except ValueError:
                raise ValueError('Invalid date format. Use ISO format or MM/DD/YYYY')
        return v

class LeadRepository:
    """Repository for managing leads"""
    
    def __init__(self, storage_path: str = "leads.json"):
        self.storage_path = storage_path
        self.leads: Dict[str, Lead] = {}
        self._load_leads()
        
    def _load_leads(self) -> None:
        """Load leads from storage"""
        if os.path.exists(self.storage_path):
            try:
                with open(self.storage_path, "r") as f:
                    leads_data = json.load(f)
                    for lead_id, lead_dict in leads_data.items():
                        try:
                            # Handle nested models properly
                            # Contact info
                            contact = ContactInfo(**lead_dict.get("contact", {}))
                            
                            # Demographics
                            demographics = None
                            if lead_dict.get("demographics"):
                                demographics = DemographicInfo(**lead_dict.get("demographics", {}))
                                
                            # Financials
                            financials = None
                            if lead_dict.get("financials"):
                                financials = FinancialInfo(**lead_dict.get("financials", {}))
                                
                            # Health
                            health = None
                            if lead_dict.get("health"):
                                health = HealthInfo(**lead_dict.get("health", {}))
                                
                            # Medicare details
                            medicare_details = None
                            if lead_dict.get("medicare_details"):
                                medicare_details = MedicareDetails(**lead_dict.get("medicare_details", {}))
                                
                            # Insurance preferences
                            insurance_preferences = None
                            if lead_dict.get("insurance_preferences"):
                                insurance_preferences = InsurancePreferences(**lead_dict.get("insurance_preferences", {}))
                                
                            # Interaction history
                            interaction_history = []
                            for interaction_dict in lead_dict.get("interaction_history", []):
                                interaction_history.append(InteractionHistory(**interaction_dict))
                                
                            # Create the lead
                            lead = Lead(
                                id=lead_id,
                                first_name=lead_dict.get("first_name", ""),
                                last_name=lead_dict.get("last_name", ""),
                                created_at=lead_dict.get("created_at", datetime.datetime.now().isoformat()),
                                updated_at=lead_dict.get("updated_at", datetime.datetime.now().isoformat()),
                                source=lead_dict.get("source"),
                                status=lead_dict.get("status", "new"),
                                tags=lead_dict.get("tags", []),
                                follow_up_date=lead_dict.get("follow_up_date"),
                                agent_id=lead_dict.get("agent_id"),
                                contact=contact,
                                demographics=demographics,
                                financials=financials,
                                health=health,
                                medicare_details=medicare_details,
                                insurance_preferences=insurance_preferences,
                                interaction_history=interaction_history,
                                notes=lead_dict.get("notes", [])
                            )
                            self.leads[lead_id] = lead
                        except Exception as e:
                            logger.error(f"Error loading lead {lead_id}: {e}")
                            continue
                            
                logger.info(f"Loaded {len(self.leads)} leads from {self.storage_path}")
            except Exception as e:
                logger.error(f"Error loading leads: {e}")
                self.leads = {}
    
    def _save_leads(self) -> None:
        """Save leads to storage"""
        try:
            leads_dict = {lead_id: json.loads(lead.json()) for lead_id, lead in self.leads.items()}
            with open(self.storage_path, "w") as f:
                json.dump(leads_dict, f, indent=2)
            logger.info(f"Saved {len(self.leads)} leads to {self.storage_path}")
        except Exception as e:
            logger.error(f"Error saving leads: {e}")
    
    def add_lead(self, lead: Lead) -> str:
        """Add a new lead"""
        self.leads[lead.id] = lead
        self._save_leads()
        logger.info(f"Added lead {lead.id}: {lead.first_name} {lead.last_name}")
        return lead.id
    
    def get_lead(self, lead_id: str) -> Optional[Lead]:
        """Get a lead by ID"""
        return self.leads.get(lead_id)
    
    def get_all_leads(self) -> List[Lead]:
        """Get all leads"""
        return list(self.leads.values())
    
    def search_leads(self, **kwargs) -> List[Lead]:
        """Search leads by various criteria"""
        results = list(self.leads.values())
        
        for key, value in kwargs.items():
            if key == "name":
                # Special case for searching by name (first or last)
                name_lower = value.lower()
                results = [
                    lead for lead in results
                    if name_lower in lead.first_name.lower() or name_lower in lead.last_name.lower()
                ]
            elif key == "email":
                results = [
                    lead for lead in results
                    if lead.contact.email and value.lower() in lead.contact.email.lower()
                ]
            elif key == "phone":
                results = [
                    lead for lead in results
                    if lead.contact.phone and value in lead.contact.phone
                ]
            elif key == "status":
                results = [lead for lead in results if lead.status == value]
            elif key == "tag":
                results = [lead for lead in results if value in lead.tags]
            elif key == "source":
                results = [lead for lead in results if lead.source and lead.source == value]
            elif key == "agent_id":
                results = [lead for lead in results if lead.agent_id and lead.agent_id == value]
                
        return results
    
    def update_lead(self, lead_id: str, updated_data: Dict) -> Optional[Lead]:
        """Update a lead"""
        if lead_id not in self.leads:
            logger.warning(f"Lead {lead_id} not found for update")
            return None
        
        lead = self.leads[lead_id]
        # Update basic attributes
        for key, value in updated_data.items():
            if key in ["first_name", "last_name", "source", "status", "follow_up_date", "agent_id"]:
                setattr(lead, key, value)
            elif key == "tags" and isinstance(value, list):
                lead.tags = value
            elif key == "notes" and isinstance(value, list):
                lead.notes = value
                
        # Update nested models
        if "contact" in updated_data and isinstance(updated_data["contact"], dict):
            for k, v in updated_data["contact"].items():
                setattr(lead.contact, k, v)
                
        if "demographics" in updated_data and isinstance(updated_data["demographics"], dict):
            if not lead.demographics:
                lead.demographics = DemographicInfo()
            for k, v in updated_data["demographics"].items():
                setattr(lead.demographics, k, v)
                
        if "financials" in updated_data and isinstance(updated_data["financials"], dict):
            if not lead.financials:
                lead.financials = FinancialInfo()
            for k, v in updated_data["financials"].items():
                setattr(lead.financials, k, v)
                
        if "health" in updated_data and isinstance(updated_data["health"], dict):
            if not lead.health:
                lead.health = HealthInfo()
            for k, v in updated_data["health"].items():
                setattr(lead.health, k, v)
                
        if "medicare_details" in updated_data and isinstance(updated_data["medicare_details"], dict):
            if not lead.medicare_details:
                lead.medicare_details = MedicareDetails()
            for k, v in updated_data["medicare_details"].items():
                setattr(lead.medicare_details, k, v)
                
        if "insurance_preferences" in updated_data and isinstance(updated_data["insurance_preferences"], dict):
            if not lead.insurance_preferences:
                lead.insurance_preferences = InsurancePreferences()
            for k, v in updated_data["insurance_preferences"].items():
                setattr(lead.insurance_preferences, k, v)
                
        # Add interaction if provided
        if "interaction" in updated_data and isinstance(updated_data["interaction"], dict):
            interaction = InteractionHistory(**updated_data["interaction"])
            lead.interaction_history.append(interaction)
                
        # Update timestamp
        lead.updated_at = datetime.datetime.now().isoformat()
        
        self._save_leads()
        logger.info(f"Updated lead {lead_id}")
        return lead
    
    def delete_lead(self, lead_id: str) -> bool:
        """Delete a lead"""
        if lead_id in self.leads:
            del self.leads[lead_id]
            self._save_leads()
            logger.info(f"Deleted lead {lead_id}")
            return True
        logger.warning(f"Lead {lead_id} not found for deletion")
        return False
    
    def add_interaction(self, lead_id: str, interaction: InteractionHistory) -> bool:
        """Add an interaction to a lead's history"""
        if lead_id not in self.leads:
            logger.warning(f"Lead {lead_id} not found for interaction")
            return False
            
        lead = self.leads[lead_id]
        lead.interaction_history.append(interaction)
        lead.updated_at = datetime.datetime.now().isoformat()
        
        self._save_leads()
        logger.info(f"Added interaction to lead {lead_id}")
        return True
    
    def add_note(self, lead_id: str, note: str) -> bool:
        """Add a note to a lead"""
        if lead_id not in self.leads:
            logger.warning(f"Lead {lead_id} not found for note")
            return False
            
        lead = self.leads[lead_id]
        lead.notes.append(note)
        lead.updated_at = datetime.datetime.now().isoformat()
        
        self._save_leads()
        logger.info(f"Added note to lead {lead_id}")
        return True
    
    def export_to_csv(self, file_path: str) -> bool:
        """Export leads to a CSV file"""
        try:
            # Convert leads to a flat structure for CSV
            flat_leads = []
            for lead in self.leads.values():
                flat_lead = {
                    "id": lead.id,
                    "first_name": lead.first_name,
                    "last_name": lead.last_name,
                    "created_at": lead.created_at,
                    "updated_at": lead.updated_at,
                    "source": lead.source,
                    "status": lead.status,
                    "tags": ",".join(lead.tags),
                    "follow_up_date": lead.follow_up_date,
                    "agent_id": lead.agent_id,
                    "email": lead.contact.email,
                    "phone": lead.contact.phone,
                    "address": lead.contact.address,
                    "city": lead.contact.city,
                    "state": lead.contact.state,
                    "zip_code": lead.contact.zip_code,
                }
                
                # Add demographics
                if lead.demographics:
                    flat_lead.update({
                        "date_of_birth": lead.demographics.date_of_birth,
                        "age": lead.demographics.age,
                        "gender": lead.demographics.gender,
                        "marital_status": lead.demographics.marital_status,
                        "occupation": lead.demographics.occupation,
                    })
                    
                # Add financials
                if lead.financials:
                    flat_lead.update({
                        "income": lead.financials.income,
                        "budget": lead.financials.budget,
                        "credit_score": lead.financials.credit_score,
                    })
                    
                # Add health info
                if lead.health:
                    flat_lead.update({
                        "height": lead.health.height,
                        "weight": lead.health.weight,
                        "smoker": lead.health.smoker,
                        "pre_existing_conditions": ",".join(lead.health.pre_existing_conditions),
                        "medications": ",".join(lead.health.medications),
                    })
                    
                # Add Medicare details
                if lead.medicare_details:
                    flat_lead.update({
                        "medicare_eligible": lead.medicare_details.eligible,
                        "medicare_part_a": lead.medicare_details.part_a,
                        "medicare_part_b": lead.medicare_details.part_b,
                        "medicare_part_c": lead.medicare_details.part_c,
                        "medicare_part_d": lead.medicare_details.part_d,
                        "medicare_medications": ",".join(lead.medicare_details.medications),
                        "medicare_preferred_doctors": ",".join(lead.medicare_details.preferred_doctors),
                        "medicare_preferred_hospitals": ",".join(lead.medicare_details.preferred_hospitals),
                        "medicare_enrollment_date": lead.medicare_details.enrollment_date,
                    })
                    
                # Add insurance preferences
                if lead.insurance_preferences:
                    flat_lead.update({
                        "preferred_carriers": ",".join(lead.insurance_preferences.preferred_carriers),
                        "current_carrier": lead.insurance_preferences.current_carrier,
                        "current_premium": lead.insurance_preferences.current_premium,
                        "current_coverage_end_date": lead.insurance_preferences.current_coverage_end_date,
                        "coverage_needs": ",".join(lead.insurance_preferences.coverage_needs),
                    })
                    
                flat_leads.append(flat_lead)
                
            # Create DataFrame and save to CSV
            df = pd.DataFrame(flat_leads)
            df.to_csv(file_path, index=False)
            
            logger.info(f"Exported {len(flat_leads)} leads to {file_path}")
            return True
            
        except Exception as e:
            logger.error(f"Error exporting leads to CSV: {e}")
            return False
    
    def import_from_csv(self, file_path: str) -> int:
        """Import leads from a CSV file"""
        try:
            df = pd.read_csv(file_path)
            imported_count = 0
            
            for _, row in df.iterrows():
                try:
                    # Convert row to dict
                    row_dict = row.to_dict()
                    
                    # Create ContactInfo
                    contact = ContactInfo(
                        email=row_dict.get("email"),
                        phone=row_dict.get("phone"),
                        address=row_dict.get("address"),
                        city=row_dict.get("city"),
                        state=row_dict.get("state"),
                        zip_code=row_dict.get("zip_code")
                    )
                    
                    # Create DemographicInfo
                    demographics = None
                    if "date_of_birth" in row_dict or "age" in row_dict:
                        demographics = DemographicInfo(
                            date_of_birth=row_dict.get("date_of_birth"),
                            age=row_dict.get("age"),
                            gender=row_dict.get("gender"),
                            marital_status=row_dict.get("marital_status"),
                            occupation=row_dict.get("occupation")
                        )
                        
                    # Create FinancialInfo
                    financials = None
                    if "income" in row_dict or "budget" in row_dict:
                        financials = FinancialInfo(
                            income=row_dict.get("income"),
                            budget=row_dict.get("budget"),
                            credit_score=row_dict.get("credit_score")
                        )
                        
                    # Create HealthInfo
                    health = None
                    if "height" in row_dict or "weight" in row_dict:
                        pre_existing_conditions = []
                        if row_dict.get("pre_existing_conditions"):
                            pre_existing_conditions = str(row_dict.get("pre_existing_conditions")).split(",")
                            
                        medications = []
                        if row_dict.get("medications"):
                            medications = str(row_dict.get("medications")).split(",")
                            
                        health = HealthInfo(
                            height=row_dict.get("height"),
                            weight=row_dict.get("weight"),
                            smoker=row_dict.get("smoker"),
                            pre_existing_conditions=pre_existing_conditions,
                            medications=medications
                        )
                        
                    # Create MedicareDetails
                    medicare_details = None
                    if "medicare_eligible" in row_dict:
                        medicare_medications = []
                        if row_dict.get("medicare_medications"):
                            medicare_medications = str(row_dict.get("medicare_medications")).split(",")
                            
                        preferred_doctors = []
                        if row_dict.get("medicare_preferred_doctors"):
                            preferred_doctors = str(row_dict.get("medicare_preferred_doctors")).split(",")
                            
                        preferred_hospitals = []
                        if row_dict.get("medicare_preferred_hospitals"):
                            preferred_hospitals = str(row_dict.get("medicare_preferred_hospitals")).split(",")
                            
                        medicare_details = MedicareDetails(
                            eligible=row_dict.get("medicare_eligible"),
                            part_a=row_dict.get("medicare_part_a"),
                            part_b=row_dict.get("medicare_part_b"),
                            part_c=row_dict.get("medicare_part_c"),
                            part_d=row_dict.get("medicare_part_d"),
                            medications=medicare_medications,
                            preferred_doctors=preferred_doctors,
                            preferred_hospitals=preferred_hospitals,
                            enrollment_date=row_dict.get("medicare_enrollment_date")
                        )
                        
                    # Create InsurancePreferences
                    insurance_preferences = None
                    if "preferred_carriers" in row_dict or "current_carrier" in row_dict:
                        preferred_carriers = []
                        if row_dict.get("preferred_carriers"):
                            preferred_carriers = str(row_dict.get("preferred_carriers")).split(",")
                            
                        coverage_needs = []
                        if row_dict.get("coverage_needs"):
                            coverage_needs = str(row_dict.get("coverage_needs")).split(",")
                            
                        insurance_preferences = InsurancePreferences(
                            preferred_carriers=preferred_carriers,
                            current_carrier=row_dict.get("current_carrier"),
                            current_premium=row_dict.get("current_premium"),
                            current_coverage_end_date=row_dict.get("current_coverage_end_date"),
                            coverage_needs=coverage_needs
                        )
                        
                    # Create tags
                    tags = []
                    if row_dict.get("tags"):
                        tags = str(row_dict.get("tags")).split(",")
                        
                    # Create Lead
                    lead = Lead(
                        id=row_dict.get("id", str(uuid.uuid4())),
                        first_name=row_dict.get("first_name", ""),
                        last_name=row_dict.get("last_name", ""),
                        created_at=row_dict.get("created_at", datetime.datetime.now().isoformat()),
                        updated_at=row_dict.get("updated_at", datetime.datetime.now().isoformat()),
                        source=row_dict.get("source"),
                        status=row_dict.get("status", "new"),
                        tags=tags,
                        follow_up_date=row_dict.get("follow_up_date"),
                        agent_id=row_dict.get("agent_id"),
                        contact=contact,
                        demographics=demographics,
                        financials=financials,
                        health=health,
                        medicare_details=medicare_details,
                        insurance_preferences=insurance_preferences
                    )
                    
                    # Add to repository
                    self.leads[lead.id] = lead
                    imported_count += 1
                    
                except Exception as e:
                    logger.error(f"Error importing lead row: {e}")
                    continue
                    
            self._save_leads()
            logger.info(f"Imported {imported_count} leads from {file_path}")
            return imported_count
            
        except Exception as e:
            logger.error(f"Error importing leads from CSV: {e}")
            return 0

def create_test_lead_paul_edwards() -> Lead:
    """Create a test lead for Paul Edwards"""
    # Create contact info
    contact = ContactInfo(
        email="<EMAIL>",
        phone="**********",
        address="2426 SE Whitehorse Street",
        city="Port Saint Lucie",
        state="FL",
        zip_code="34952"
    )
    
    # Create demographics
    dob = datetime.datetime.strptime("02/22/1993", "%m/%d/%Y").isoformat()
    current_date = datetime.datetime.now()
    dob_date = datetime.datetime.fromisoformat(dob)
    age = current_date.year - dob_date.year - ((current_date.month, current_date.day) < (dob_date.month, dob_date.day))
    
    demographics = DemographicInfo(
        date_of_birth=dob,
        age=age,
        gender="Male",
        marital_status="Single",
        occupation="Software Developer"
    )
    
    # Create financial info
    financials = FinancialInfo(
        income=75000.0,
        budget=200.0,
        credit_score=720
    )
    
    # Create health info
    health = HealthInfo(
        height="5'10\"",
        weight=180.0,
        smoker=False,
        pre_existing_conditions=[],
        medications=[]
    )
    
    # Create Medicare details
    medicare_details = MedicareDetails(
        eligible=True,
        part_a=True,
        part_b=True,
        part_c=False,
        part_d=False,
        medications=[],
        preferred_doctors=[],
        preferred_hospitals=[],
        enrollment_date=None
    )
    
    # Create insurance preferences
    insurance_preferences = InsurancePreferences(
        preferred_carriers=["Blue Cross Blue Shield", "United Healthcare", "Aetna"],
        current_carrier=None,
        current_premium=None,
        current_coverage_end_date=None,
        coverage_needs=["Health", "Vision", "Dental"]
    )
    
    # Create lead
    lead = Lead(
        first_name="Paul",
        last_name="Edwards",
        source="Test",
        status="new",
        tags=["Test", "Medicare", "Priority"],
        contact=contact,
        demographics=demographics,
        financials=financials,
        health=health,
        medicare_details=medicare_details,
        insurance_preferences=insurance_preferences,
        notes=["Test lead for Paul Edwards", "Budget: $200/month", "SSN: ***********"]
    )
    
    # Add a test interaction
    interaction = InteractionHistory(
        timestamp=datetime.datetime.now().isoformat(),
        type="note",
        summary="Created test lead",
        details="Test lead created for Paul Edwards for system testing",
        success=True
    )
    lead.interaction_history.append(interaction)
    
    return lead

def main():
    """Main function for testing"""
    lead_repo = LeadRepository()
    
    # Check if test lead exists
    existing_pauls = lead_repo.search_leads(name="Paul Edwards")
    
    if existing_pauls:
        print(f"Test lead already exists: {existing_pauls[0].id}")
        lead_id = existing_pauls[0].id
    else:
        # Create test lead
        test_lead = create_test_lead_paul_edwards()
        lead_id = lead_repo.add_lead(test_lead)
        print(f"Created test lead: {lead_id}")
    
    # Get the lead
    lead = lead_repo.get_lead(lead_id)
    
    # Print lead details
    print("\nLead Details:")
    print(f"Name: {lead.first_name} {lead.last_name}")
    print(f"Email: {lead.contact.email}")
    print(f"Phone: {lead.contact.phone}")
    print(f"Address: {lead.contact.address}, {lead.contact.city}, {lead.contact.state} {lead.contact.zip_code}")
    print(f"DOB: {lead.demographics.date_of_birth}")
    print(f"Age: {lead.demographics.age}")
    print(f"Budget: ${lead.financials.budget}")
    print(f"Smoker: {lead.health.smoker}")
    print(f"Medications: {', '.join(lead.health.medications) if lead.health.medications else 'None'}")
    
    # Export leads to CSV
    lead_repo.export_to_csv("leads_export.csv")
    print("\nExported leads to leads_export.csv")
    
if __name__ == "__main__":
    main()