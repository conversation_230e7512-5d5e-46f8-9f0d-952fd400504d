"""
Send Email with OpenAI Agent

This script uses the OpenAI Agents SDK to:
1. Generate a personalized message for <PERSON>
2. Open your default email client with the message
3. Provide instructions for attaching the audio file

No Twilio authentication required.
"""

import os
import webbrowser
import urllib.parse
import platform
import subprocess
from dotenv import load_dotenv
from agents import Agent, function_tool, Runner

# Load environment variables
load_dotenv()

# <PERSON> contact information from environment variables
PAUL_EDWARDS = {
    "first_name": os.getenv("PAUL_FIRST_NAME", "<PERSON>"),
    "last_name": os.getenv("PAUL_LAST_NAME", "Edwards"),
    "email": os.getenv("PAUL_EMAIL", "<EMAIL>"),
    "primary_phone": os.getenv("PAUL_PRIMARY_PHONE", "+17722089646"),
    "secondary_phone": os.getenv("PAUL_SECONDARY_PHONE", "+17725395908")
}

# Agent information from environment variables
AGENT_INFO = {
    "name": os.getenv("AGENT_NAME", "<PERSON>"),
    "agency": os.getenv("AGENT_AGENCY", "Flo Faction Insurance"),
    "email": os.getenv("AGENT_EMAIL", "<EMAIL>"),
    "website": os.getenv("AGENT_WEBSITE", "https://www.flofaction.com/insurance"),
}

# Insurance product information
INSURANCE_PRODUCTS = {
    "iul": {
        "name": "Indexed Universal Life Insurance",
        "description": "A permanent life insurance policy that offers both a death benefit and cash value accumulation potential tied to a stock market index.",
        "benefits": [
            "Tax-free retirement income potential",
            "Death benefit protection for your family",
            "Cash value growth without direct market risk",
            "Access to funds via policy loans"
        ],
        "monthly_income_potential": "$5,000",
        "death_benefit": "$720,000"
    }
}

# Create tools for the agent
@function_tool
def get_client_info():
    """Get information about the client Paul Edwards."""
    return {
        "name": f"{PAUL_EDWARDS['first_name']} {PAUL_EDWARDS['last_name']}",
        "email": PAUL_EDWARDS['email'],
        "phone": PAUL_EDWARDS['primary_phone'],
        "location": "Florida",
        "age": 45,
        "occupation": "Business Owner"
    }

@function_tool
def get_insurance_product_info(product_type: str):
    """Get information about a specific insurance product.
    
    Args:
        product_type: The type of insurance product (iul, term, annuity)
    """
    if product_type.lower() in INSURANCE_PRODUCTS:
        return INSURANCE_PRODUCTS[product_type.lower()]
    else:
        return {"error": f"Product type '{product_type}' not found"}

@function_tool
def generate_personalized_quote():
    """Generate a personalized insurance quote for Paul Edwards."""
    return {
        "product": "Indexed Universal Life Insurance",
        "monthly_premium": "$450",
        "death_benefit": "$720,000",
        "cash_value_projection": {
            "10_years": "$65,000",
            "20_years": "$210,000",
            "30_years": "$450,000"
        },
        "retirement_income_potential": "$5,000 per month",
        "tax_advantages": "Tax-free growth and tax-free income when structured properly"
    }

@function_tool
def open_email_client(subject: str, body: str):
    """Open the default email client with a pre-populated email to Paul Edwards.
    
    Args:
        subject: The email subject
        body: The email body text
    """
    print("=" * 80)
    print("OPENING EMAIL CLIENT")
    print("=" * 80)
    
    # Check if the audio file exists
    audio_file = "audio/paul_edwards_voicemail.mp3"
    if not os.path.exists(audio_file):
        print(f"Warning: Audio file '{audio_file}' not found.")
        print("Please run 'generate_elevenlabs_audio.py' first to generate the audio file.")
    else:
        print(f"Audio file '{audio_file}' found. Please attach it to the email manually.")
        print(f"Full path to audio file: {os.path.abspath(audio_file)}")
    
    # Add note about attachment
    body += f"\n\nIMPORTANT: Please remember to attach the audio file '{os.path.basename(audio_file)}' to this email before sending."
    
    # URL encode the subject and body
    subject_encoded = urllib.parse.quote(subject)
    body_encoded = urllib.parse.quote(body)
    
    # Create mailto URL
    mailto_url = f"mailto:{PAUL_EDWARDS['email']}?subject={subject_encoded}&body={body_encoded}"
    
    try:
        # Open the default email client
        webbrowser.open(mailto_url)
        print(f"Default email client opened with message to {PAUL_EDWARDS['email']}")
        print("Please attach the audio file and complete the email sending process in your email client.")
        
        # Try to open the folder containing the audio file
        if os.path.exists(audio_file):
            if platform.system() == "Darwin":  # macOS
                subprocess.run(["open", "-R", audio_file])
                print(f"Opened Finder with '{audio_file}' selected.")
            elif platform.system() == "Windows":
                subprocess.run(["explorer", "/select,", os.path.abspath(audio_file)])
                print(f"Opened Explorer with '{audio_file}' selected.")
            elif platform.system() == "Linux":
                subprocess.run(["xdg-open", os.path.dirname(os.path.abspath(audio_file))])
                print(f"Opened file manager to the folder containing '{audio_file}'.")
        
        return {"status": "success", "message": "Email client opened successfully"}
    except Exception as e:
        print(f"Error opening default email client: {str(e)}")
        return {"status": "error", "message": f"Error opening email client: {str(e)}"}

# Create the main insurance agent
def create_insurance_agent():
    """Create the main insurance agent with tools and instructions."""
    instructions = f"""
    You are {AGENT_INFO['name']}, an insurance agent with {AGENT_INFO['agency']}. 
    Your goal is to help clients like {PAUL_EDWARDS['first_name']} {PAUL_EDWARDS['last_name']} 
    create tax-free retirement income without the risk of the stock market.
    
    You specialize in Indexed Universal Life Insurance (IUL) policies that offer:
    1. Tax-free retirement income potential
    2. Death benefit protection for the family
    3. Cash value growth without direct market risk
    4. Access to funds via policy loans
    
    When communicating with clients:
    - Be professional, friendly, and knowledgeable
    - Focus on the benefits, not just features
    - Personalize your message based on client information
    - Always include a clear call to action
    
    Your task is to create a personalized email for Paul Edwards and open the email client.
    """
    
    agent = Agent(
        name=AGENT_INFO['name'],
        instructions=instructions,
        tools=[
            get_client_info,
            get_insurance_product_info,
            generate_personalized_quote,
            open_email_client
        ]
    )
    
    return agent

async def main():
    """Main function to run the OpenAI Agent for Paul Edwards."""
    print("=" * 80)
    print(f"RUNNING OPENAI AGENT FOR {PAUL_EDWARDS['first_name']} {PAUL_EDWARDS['last_name']}")
    print("=" * 80)
    
    # Create the insurance agent
    agent = create_insurance_agent()
    
    # Run the agent with a specific task
    user_input = """
    Create a personalized email for Paul Edwards about IUL policies. 
    Include information about tax-free retirement income, death benefits, and cash value growth.
    Then open the email client so I can send it with the audio attachment.
    """
    
    # Run the agent
    result = await Runner.run(agent, user_input)
    
    # Print the final output
    print("\nAgent Final Output:")
    print("-" * 80)
    print(result.final_output)
    print("-" * 80)

if __name__ == "__main__":
    import asyncio
    asyncio.run(main())
