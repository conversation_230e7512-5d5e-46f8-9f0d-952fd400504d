#!/usr/bin/env python3
"""
Credential Manager

This module provides secure storage and retrieval of credentials for
various services including insurance carriers.
"""

import os
import sys
import json
import base64
import getpass
from pathlib import Path
from typing import Dict, List, Optional, Union, Any
import logging
from cryptography.fernet import Fernet
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.kdf.pbkdf2 import PB<PERSON>DF2HMAC

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s [%(levelname)s] %(name%s: %(message)s",
    handlers=[logging.StreamHandler()]
)
logger = logging.getLogger("credential-manager")

class CredentialManager:
    """Manages secure storage and retrieval of credentials"""
    
    def __init__(self, master_password: Optional[str] = None):
        """Initialize the credential manager"""
        self.script_dir = Path(os.path.dirname(os.path.abspath(__file__)))
        self.credential_file = self.script_dir / "credential_store.json"
        self.fernet_key_file = self.script_dir / "fernet_config.txt"
        self.credentials = {}
        self.cipher = None
        
        # Initialize encryption
        self._setup_encryption(master_password)
        
        # Load existing credentials
        self.load_credentials()
    
    def _setup_encryption(self, master_password: Optional[str]) -> None:
        """Set up encryption using Fernet"""
        # Check if fernet key exists
        if self.fernet_key_file.exists():
            with open(self.fernet_key_file, 'rb') as f:
                key = f.read()
            self.cipher = Fernet(key)
        else:
            # Generate a new key and save it
            key = Fernet.generate_key()
            with open(self.fernet_key_file, 'wb') as f:
                f.write(key)
            self.cipher = Fernet(key)
    
    def load_credentials(self) -> None:
        """Load credentials from file"""
        if not self.credential_file.exists():
            self.credentials = {
                "api_keys": {},
                "insurance_carriers": {},
                "databases": {},
                "web_services": {}
            }
            return
            
        try:
            with open(self.credential_file, 'r') as f:
                self.credentials = json.load(f)
        except Exception as e:
            logger.error(f"Error loading credentials: {e}")
            self.credentials = {
                "api_keys": {},
                "insurance_carriers": {},
                "databases": {},
                "web_services": {}
            }
    
    def save_credentials(self) -> bool:
        """Save credentials to file"""
        try:
            with open(self.credential_file, 'w') as f:
                json.dump(self.credentials, f, indent=4)
            return True
        except Exception as e:
            logger.error(f"Error saving credentials: {e}")
            return False
    
    def add_api_key(self, service: str, key: str) -> bool:
        """Add an API key for a service"""
        if "api_keys" not in self.credentials:
            self.credentials["api_keys"] = {}
            
        self.credentials["api_keys"][service] = key
        return self.save_credentials()
    
    def get_api_key(self, service: str) -> Optional[str]:
        """Get an API key for a service"""
        return self.credentials.get("api_keys", {}).get(service)
    
    def add_insurance_carrier(self, 
                              carrier_name: str, 
                              credentials: Dict[str, Dict[str, str]]) -> bool:
        """
        Add credentials for an insurance carrier
        
        Parameters:
        - carrier_name: Name of the insurance carrier
        - credentials: Dictionary of portal/service credentials
          Example: {
            "main_portal": {
                "url": "https://example.com",
                "username": "user123",
                "password": "pass123",
                "notes": "Optional notes"
            },
            "agent_portal": {
                ...
            }
          }
        """
        if "insurance_carriers" not in self.credentials:
            self.credentials["insurance_carriers"] = {}
            
        self.credentials["insurance_carriers"][carrier_name] = credentials
        return self.save_credentials()
    
    def get_insurance_carrier(self, carrier_name: str) -> Optional[Dict[str, Dict[str, str]]]:
        """Get credentials for an insurance carrier"""
        return self.credentials.get("insurance_carriers", {}).get(carrier_name)
    
    def get_all_insurance_carriers(self) -> Dict[str, Dict[str, Dict[str, str]]]:
        """Get all insurance carrier credentials"""
        return self.credentials.get("insurance_carriers", {})
    
    def search_credentials(self, query: str) -> Dict[str, List[Dict[str, Any]]]:
        """
        Search for credentials matching the query
        
        Returns a dictionary with categories as keys and lists of matching items as values
        """
        results = {
            "api_keys": [],
            "insurance_carriers": [],
            "databases": [],
            "web_services": []
        }
        
        query = query.lower()
        
        # Search API keys
        for service, key in self.credentials.get("api_keys", {}).items():
            if query in service.lower():
                results["api_keys"].append({
                    "service": service,
                    "key": key
                })
        
        # Search insurance carriers
        for carrier, portals in self.credentials.get("insurance_carriers", {}).items():
            if query in carrier.lower():
                results["insurance_carriers"].append({
                    "carrier": carrier,
                    "portals": portals
                })
                continue
                
            # Search within portals
            for portal_name, portal_creds in portals.items():
                if (query in portal_name.lower() or 
                    any(query in str(v).lower() for v in portal_creds.values())):
                    results["insurance_carriers"].append({
                        "carrier": carrier,
                        "portals": {portal_name: portal_creds}
                    })
                    break
        
        # Search databases
        for db_name, db_creds in self.credentials.get("databases", {}).items():
            if query in db_name.lower() or any(query in str(v).lower() for v in db_creds.values()):
                results["databases"].append({
                    "database": db_name,
                    "credentials": db_creds
                })
        
        # Search web services
        for service_name, service_creds in self.credentials.get("web_services", {}).items():
            if query in service_name.lower() or any(query in str(v).lower() for v in service_creds.values()):
                results["web_services"].append({
                    "service": service_name,
                    "credentials": service_creds
                })
        
        return results
    
    def import_insurance_carriers(self, carriers_data: Dict[str, Dict[str, Dict[str, str]]]) -> bool:
        """
        Import insurance carrier credentials from a data dictionary
        
        Format should match the structure in self.credentials["insurance_carriers"]
        """
        if "insurance_carriers" not in self.credentials:
            self.credentials["insurance_carriers"] = {}
            
        for carrier_name, carrier_creds in carriers_data.items():
            self.credentials["insurance_carriers"][carrier_name] = carrier_creds
            
        return self.save_credentials()

    def add_google_cloud_credentials(self, credentials: Dict[str, Any]) -> bool:
        """Add Google Cloud service account credentials"""
        self.credentials["google_cloud_service_account"] = credentials
        return self.save_credentials()

    def get_google_cloud_credentials(self) -> Optional[Dict[str, Any]]:
        """Retrieve Google Cloud service account credentials"""
        return self.credentials.get("google_cloud_service_account")

def initialize_insurance_credentials():
    """Initialize with the provided insurance carrier credentials"""
    manager = CredentialManager()
    
    # Add the Operative API key
    manager.add_api_key("operative", "op-sMxrcBrpW8uyzqeL8mg9N9SiDNY_ijY6kB8QUbiFj1I")
    
    # Define the insurance carriers
    carriers = {
        "Foresters Financial": {
            "Secure Mail": {
                "url": "mail.foresters.com",
                "username": "<EMAIL>",
                "password": "GodisSoGood!777",
                "notes": "Alternate password may be GodisSoGood!7"
            },
            "MyEzbiz": {
                "url": "MyEzbiz.foresters.com",
                "username": "<EMAIL>",
                "password": "GodisSoGood!777"
            },
            "ezbiz": {
                "url": "ezbiz.forester.com",
                "username": "<EMAIL>",
                "notes": "Username may have a typo (insuranbce)"
            },
            "iGoForms": {
                "url": "https://igoforms2.ipipeline.com/CossEnterpriseSuite/(S(ysfzpxbnb1gxugtldlypw0ig))/webforms/StartUpResp.aspx",
                "notes": "For new case/application via foresters financial"
            }
        },
        "Accurate.com": {
            "MyPortal": {
                "url": "Myportal.accurate.com",
                "username": "<EMAIL>",
                "password": "GodisSoGood!7"
            }
        },
        "Allianz Life": {
            "Main Portal": {
                "url": "allianzlife.com",
                "username": "<EMAIL>",
                "password": "GodisSoGood!777"
            }
        },
        "Ameritas": {
            "Service Portal": {
                "url": "service.ameritas",
                "agency": "en00007034",
                "agent": "**********",
                "situation_code": "01",
                "email": "<EMAIL>",
                "username": "SG1959PE",
                "password": "GodisSoGood!777",
                "security_question": "first pet name (ny)",
                "security_answer": "Kika"
            }
        },
        "Guardian Insurance": {
            "Annuity Company": {
                "notes": "Guardian Insurance & Annuity Company (GIAC) - fixed annuities and life insurance"
            },
            "Training Portal": {
                "url": "Naic.pinpointglobal.com/GuardianReg187Life/apps/Default.aspx",
                "username": "20255168",
                "password": "GodisSoGood!777"
            }
        },
        "Nationwide": {
            "Financial Portal": {
                "url": "Nationwidefinancial.com / Financial.nationwide.com/login",
                "email": "<EMAIL>",
                "username": "SG1959PE2025",
                "password": "GodisSoGood!777",
                "notes": "Tool downloaded to run nationwide quotes off line"
            }
        },
        "Mass Mutual": {
            "Agent Portal": {
                "agent_id": "853518"
            }
        },
        "American National Life Insurance": {
            "NY Company": {
                "producer_code": "F11K1",
                "expert_office_login": "D30681",
                "username": "SG1959PE",
                "password": "GodisSoGood!777",
                "region_name": "CRUMP LIFE INS SERVICES INC",
                "region_code": "0A7N",
                "business_segment_code": "74"
            },
            "LAD Portal": {
                "url": "http://lad.americannational.com/content/lad/profilecenter.html"
            }
        },
        "Select Quote": {
            "Portal": {}
        },
        "Prudential": {
            "Portal": {}
        },
        "Protective Life Insurance": {
            "MyPortal": {
                "url": "Myportal.accurate.com",
                "auth_method": "Sign in with Gmail",
                "email": "<EMAIL>"
            }
        },
        "John Hancock": {
            "Sales Hub": {
                "url": "Jhsaleshub.com",
                "email": "<EMAIL>",
                "username": "SG1959PE",
                "password": "GodisSoGood!777",
                "security_questions": {
                    "Favorite pet": "kika",
                    "Nickname": "flaka",
                    "Favorite singer": "Barry white"
                }
            }
        },
        "Compass Health": {
            "CRM": {
                "url": "Apps.topbrokercrm.com/dashboard",
                "username": "<EMAIL>",
                "password": "NewTop@65"
            }
        }
    }
    
    # Import the carriers
    manager.import_insurance_carriers(carriers)
    logger.info("Insurance carrier credentials imported successfully")

if __name__ == "__main__":
    manager = CredentialManager()
    google_cloud_credentials = **********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
    manager.add_google_cloud_credentials(google_cloud_credentials)
    print("Google Cloud credentials added successfully.")