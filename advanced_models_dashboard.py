#!/usr/bin/env python3
"""
Advanced Models Dashboard Template
=================================

Template dashboard that showcases all advanced AI models.
"""

import asyncio
import streamlit as st
from advanced_models_integration import query_advanced_models, get_advanced_models_status

def main():
    st.title("🚀 Advanced AI Models Dashboard")
    st.write("Powered by multiple state-of-the-art AI models")
    
    # Show model status
    status = get_advanced_models_status()
    
    if status['available']:
        st.success("✅ Advanced models are available")
        
        # Model selection
        st.sidebar.header("Model Configuration")
        
        # Strategy selection
        strategies = [s.split(' - ')[0] for s in status['strategies']]
        selected_strategy = st.sidebar.selectbox("Response Strategy", strategies)
        
        # Query input
        st.header("Query Interface")
        query = st.text_area("Enter your query:", height=100)
        
        # Image upload for vision queries
        uploaded_file = st.file_uploader("Upload image (optional)", type=['png', 'jpg', 'jpeg'])
        image_data = None
        if uploaded_file:
            image_data = uploaded_file.read()
            st.image(uploaded_file, caption="Uploaded Image", use_column_width=True)
        
        # Process query
        if st.button("🔍 Process Query"):
            if query:
                with st.spinner("Processing with advanced models..."):
                    result = asyncio.run(query_advanced_models(
                        query=query,
                        image_data=image_data,
                        strategy=selected_strategy
                    ))
                
                # Display results
                st.header("Results")
                st.write("**Response:**")
                st.write(result['response'])
                
                col1, col2, col3 = st.columns(3)
                with col1:
                    st.metric("Confidence", f"{result['confidence']:.2f}")
                with col2:
                    st.metric("Processing Time", f"{result.get('processing_time', 0):.2f}s")
                with col3:
                    st.metric("Models Used", len(result['models_used']))
                
                # Show model details
                if result['models_used']:
                    st.subheader("Models Used")
                    for model in result['models_used']:
                        st.write(f"• {model}")
                
                # Show alternative responses if available
                if result.get('alternative_responses'):
                    st.subheader("Alternative Responses")
                    for i, alt in enumerate(result['alternative_responses'], 1):
                        with st.expander(f"Alternative {i}"):
                            st.write(alt.get('response', 'No response'))
            else:
                st.warning("Please enter a query")
    
    else:
        st.error("❌ Advanced models not available")
        st.write("Run: `python install_advanced_models.py`")
    
    # Show available models
    st.sidebar.header("Available Models")
    for model in status['models']:
        st.sidebar.write(f"• {model}")

if __name__ == "__main__":
    main()
