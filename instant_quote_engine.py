#!/usr/bin/env python3
"""
Advanced Instant Quote Engine

This system provides a hyper-optimized instant quote engine for insurance products
that significantly outperforms competitor systems with:

1. Sub-second quote generation (vs industry 30+ seconds)
2. Minimal input fields (3-5 vs industry 15-20)
3. Smart data enrichment via third-party APIs
4. Direct integration with Facebook/TikTok lead forms
5. Multi-product quoting in one interface
6. Interactive adjustable quotes
7. AI-driven product recommendations
8. Pixel tracking for retargeting abandoned quotes

This engine integrates seamlessly with carrier APIs and our ad platforms.
"""

import os
import sys
import json
import logging
import datetime
import time
import random
import hashlib
import uuid
import hmac
import base64
import re
from typing import Dict, List, Any, Optional, Union, Tuple
from pathlib import Path

# Configure logging
logging.basicConfig(level=logging.INFO,
                    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Try to import other components
try:
    from insurance_carrier_access import InsuranceCarrierAccess
    CARRIER_ACCESS_AVAILABLE = True
except ImportError:
    CARRIER_ACCESS_AVAILABLE = False
    logger.warning("Insurance Carrier Access not available - using mock quoting")
    
try:
    from insurance_quote_form import InsuranceQuoteForm
    QUOTE_FORM_AVAILABLE = True
except ImportError:
    QUOTE_FORM_AVAILABLE = False
    logger.warning("Insurance Quote Form not available - using basic form")


class InstantQuoteEngine:
    """
    Advanced instant quote engine with carrier integrations and
    Facebook/TikTok ad form direct connections
    """
    
    def __init__(self):
        """Initialize the instant quote engine"""
        # Core configuration
        self.config = {
            "quote_timeout_seconds": 0.8,  # Sub-second quotes
            "max_input_fields": 5,         # Minimal input required
            "use_data_enrichment": True,   # Enhance sparse user data
            "use_direct_carrier_apis": True, # Real-time carrier data
            "enable_lead_capture": True,   # Always capture leads
            "store_incomplete_quotes": True, # Capture partial submissions
            "enable_analytics": True,      # Track conversion metrics
            "enable_retargeting": True     # Set pixels for abandoned quotes
        }
        
        # Initialize carrier access if available
        self.carrier_access = None
        if CARRIER_ACCESS_AVAILABLE:
            self.carrier_access = InsuranceCarrierAccess()
            logger.info("Carrier API access initialized")
            
        # Initialize quote form if available
        self.quote_form = None
        if QUOTE_FORM_AVAILABLE:
            self.quote_form = InsuranceQuoteForm()
            logger.info("Quote form initialized")
            
        # Load rate tables (normally these would come from carriers)
        self._load_rate_tables()
        
        # Initialize analytics trackers
        self.analytics = {
            "quotes_started": 0,
            "quotes_completed": 0,
            "conversion_rate": 0.0,
            "average_quote_time": 0.0,
            "total_quote_time": 0.0,
            "lead_capture_rate": 0.0,
            "quotes_by_product": {},
            "quotes_by_source": {},
            "ab_test_results": {}
        }
        
        # Ad platform integrations
        self.ad_integrations = {
            "facebook": {
                "enabled": True,
                "pixel_id": "FB_PIXEL_ID",
                "lead_form_integration": True,
                "direct_api_access": True,
                "dynamic_retargeting": True,
                "custom_audience_sync": True
            },
            "tiktok": {
                "enabled": True,
                "pixel_id": "TT_PIXEL_ID",
                "lead_form_integration": True,
                "direct_api_access": True,
                "dynamic_retargeting": True,
                "custom_audience_sync": True
            },
            "google": {
                "enabled": True,
                "conversion_id": "G_CONVERSION_ID",
                "remarketing_tag": True,
                "dynamic_retargeting": True
            },
            "instagram": {
                "enabled": True,
                "pixel_id": "IG_PIXEL_ID",  # Usually same as Facebook
                "lead_form_integration": True
            }
        }
        
        # Initialize database paths
        self.quote_db_path = "data/quotes/quotes.json"
        self.lead_db_path = "data/quotes/leads.json"
        
        # Create directories if they don't exist
        os.makedirs(os.path.dirname(self.quote_db_path), exist_ok=True)
        os.makedirs(os.path.dirname(self.lead_db_path), exist_ok=True)
        
        # Quote data and lead storage
        self.quotes = self._load_quotes()
        self.leads = self._load_leads()
        
        logger.info("Instant Quote Engine initialized")
        
    def _load_rate_tables(self):
        """Load insurance rate tables for quick quoting"""
        self.rate_tables = {
            "term_life": {
                "factors": {
                    "age": {
                        "18-25": 0.1,
                        "26-30": 0.12,
                        "31-35": 0.15,
                        "36-40": 0.2,
                        "41-45": 0.25,
                        "46-50": 0.35,
                        "51-55": 0.5,
                        "56-60": 0.75,
                        "61-65": 1.0,
                        "66-70": 1.5
                    },
                    "gender": {
                        "M": 1.2,
                        "F": 1.0
                    },
                    "tobacco": {
                        "Y": 2.0,
                        "N": 1.0
                    },
                    "health": {
                        "excellent": 0.85,
                        "good": 1.0,
                        "average": 1.2,
                        "below_average": 1.5,
                        "poor": 2.0
                    }
                },
                "base_rate_per_thousand": 0.15,  # $0.15 per $1000 of coverage
                "term_multipliers": {
                    "10": 1.0,
                    "15": 1.1,
                    "20": 1.25,
                    "30": 1.5
                },
                "carriers": {
                    "Mutual of Omaha": 1.05,
                    "Americo": 0.95,
                    "Foresters": 1.0,
                    "National Life": 1.1,
                    "AIG": 0.9
                }
            },
            "whole_life": {
                "factors": {
                    "age": {
                        "18-25": 0.3,
                        "26-30": 0.35,
                        "31-35": 0.4,
                        "36-40": 0.5,
                        "41-45": 0.6,
                        "46-50": 0.7,
                        "51-55": 0.85,
                        "56-60": 1.0,
                        "61-65": 1.2,
                        "66-70": 1.4,
                        "71-75": 1.7,
                        "76-80": 2.0
                    },
                    "gender": {
                        "M": 1.15,
                        "F": 1.0
                    },
                    "tobacco": {
                        "Y": 1.8,
                        "N": 1.0
                    },
                    "health": {
                        "excellent": 0.9,
                        "good": 1.0,
                        "average": 1.15,
                        "below_average": 1.3,
                        "poor": 1.5
                    }
                },
                "base_rate_per_thousand": 0.9,  # $0.90 per $1000 of coverage
                "carriers": {
                    "Mutual of Omaha": 1.0,
                    "Foresters": 0.95,
                    "Americo": 1.05,
                    "Royal Neighbors": 0.98,
                    "Liberty Bankers": 1.02
                }
            },
            "iul": {
                "factors": {
                    "age": {
                        "18-25": 0.25,
                        "26-30": 0.3,
                        "31-35": 0.35,
                        "36-40": 0.42,
                        "41-45": 0.5,
                        "46-50": 0.6,
                        "51-55": 0.75,
                        "56-60": 0.9,
                        "61-65": 1.1,
                        "66-70": 1.3
                    },
                    "gender": {
                        "M": 1.1,
                        "F": 1.0
                    },
                    "tobacco": {
                        "Y": 1.9,
                        "N": 1.0
                    },
                    "health": {
                        "excellent": 0.85,
                        "good": 1.0,
                        "average": 1.15,
                        "below_average": 1.35,
                        "poor": 1.6
                    }
                },
                "base_rate_per_thousand": 0.75,  # $0.75 per $1000 of coverage
                "carriers": {
                    "National Life": 1.0,
                    "North American": 1.05,
                    "Athene": 0.95,
                    "American Equity": 1.02,
                    "Global Atlantic": 0.98
                }
            },
            "medicare_supplement": {
                "factors": {
                    "age": {
                        "65": 1.0,
                        "66": 1.02,
                        "67": 1.04,
                        "68": 1.06,
                        "69": 1.08,
                        "70": 1.1,
                        "71": 1.12,
                        "72": 1.14,
                        "73": 1.16,
                        "74": 1.18,
                        "75": 1.2,
                        "76": 1.22,
                        "77": 1.24,
                        "78": 1.26,
                        "79": 1.28,
                        "80+": 1.3
                    },
                    "gender": {
                        "M": 1.1,
                        "F": 1.0
                    },
                    "tobacco": {
                        "Y": 1.2,
                        "N": 1.0
                    },
                    "state": {
                        "FL": 1.2,
                        "NY": 1.3,
                        "CA": 1.25,
                        "TX": 0.9,
                        "AZ": 0.85,
                        "default": 1.0
                    }
                },
                "plan_base_rates": {
                    "A": 90,
                    "B": 110,
                    "C": 140,  # Legacy plan
                    "D": 125,
                    "F": 170,  # Legacy plan
                    "G": 130,
                    "K": 70,
                    "L": 90,
                    "M": 100,
                    "N": 100
                },
                "carriers": {
                    "Aetna": 1.05,
                    "Cigna": 1.0,
                    "Humana": 0.95,
                    "Mutual of Omaha": 1.02,
                    "United Healthcare": 1.08
                }
            }
        }
        
        logger.info("Rate tables loaded")
    
    def _load_quotes(self) -> List[Dict[str, Any]]:
        """Load saved quotes from database"""
        if not os.path.exists(self.quote_db_path):
            return []
            
        try:
            with open(self.quote_db_path, 'r') as f:
                return json.load(f)
        except Exception as e:
            logger.error(f"Error loading quotes: {e}")
            return []
    
    def _save_quotes(self):
        """Save quotes to database"""
        try:
            with open(self.quote_db_path, 'w') as f:
                json.dump(self.quotes, f, indent=2)
        except Exception as e:
            logger.error(f"Error saving quotes: {e}")
    
    def _load_leads(self) -> List[Dict[str, Any]]:
        """Load saved leads from database"""
        if not os.path.exists(self.lead_db_path):
            return []
            
        try:
            with open(self.lead_db_path, 'r') as f:
                return json.load(f)
        except Exception as e:
            logger.error(f"Error loading leads: {e}")
            return []
    
    def _save_leads(self):
        """Save leads to database"""
        try:
            with open(self.lead_db_path, 'w') as f:
                json.dump(self.leads, f, indent=2)
        except Exception as e:
            logger.error(f"Error saving leads: {e}")
    
    def process_facebook_lead(self, lead_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Process a lead from Facebook Lead Form integration
        
        Args:
            lead_data: Lead data from Facebook
            
        Returns:
            Dictionary with processing results and quotes
        """
        logger.info(f"Processing Facebook lead: {lead_data.get('id', 'unknown')}")
        
        # Extract lead information
        extracted_data = self._extract_facebook_lead_data(lead_data)
        
        # Generate quotes
        quotes = self.generate_quotes(extracted_data)
        
        # Store lead
        lead_id = self._store_lead(extracted_data, "facebook", quotes)
        
        # Fire conversion pixel
        if self.config["enable_analytics"]:
            self._fire_conversion_pixel("facebook", "lead_capture", extracted_data)
            
        return {
            "success": True,
            "lead_id": lead_id,
            "quotes": quotes,
            "lead_data": extracted_data
        }
    
    def process_tiktok_lead(self, lead_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Process a lead from TikTok Lead Form integration
        
        Args:
            lead_data: Lead data from TikTok
            
        Returns:
            Dictionary with processing results and quotes
        """
        logger.info(f"Processing TikTok lead: {lead_data.get('id', 'unknown')}")
        
        # Extract lead information
        extracted_data = self._extract_tiktok_lead_data(lead_data)
        
        # Generate quotes
        quotes = self.generate_quotes(extracted_data)
        
        # Store lead
        lead_id = self._store_lead(extracted_data, "tiktok", quotes)
        
        # Fire conversion pixel
        if self.config["enable_analytics"]:
            self._fire_conversion_pixel("tiktok", "lead_capture", extracted_data)
            
        return {
            "success": True,
            "lead_id": lead_id,
            "quotes": quotes,
            "lead_data": extracted_data
        }
    
    def process_web_form_lead(self, form_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Process a lead from website form
        
        Args:
            form_data: Form data from website
            
        Returns:
            Dictionary with processing results and quotes
        """
        logger.info("Processing web form lead")
        
        # Check if we need to enrich data
        if self.config["use_data_enrichment"] and self._should_enrich_data(form_data):
            form_data = self._enrich_lead_data(form_data)
            
        # Track source
        source = form_data.get("source", "website")
        
        # Generate quotes
        quotes = self.generate_quotes(form_data)
        
        # Store lead
        lead_id = self._store_lead(form_data, source, quotes)
        
        # Fire conversion pixel if source is known ad platform
        if self.config["enable_analytics"]:
            if source in ["facebook", "tiktok", "google", "instagram"]:
                self._fire_conversion_pixel(source, "lead_capture", form_data)
                
        return {
            "success": True,
            "lead_id": lead_id,
            "quotes": quotes,
            "lead_data": form_data
        }
    
    def _extract_facebook_lead_data(self, lead_data: Dict[str, Any]) -> Dict[str, Any]:
        """Extract structured data from Facebook lead format"""
        extracted = {
            "source": "facebook",
            "lead_id": lead_data.get("id", f"fb_{int(time.time())}")
        }
        
        # Extract field data
        if "field_data" in lead_data:
            for field in lead_data["field_data"]:
                name = field.get("name", "").lower().replace(" ", "_")
                value = field.get("values", [""])[0] if field.get("values") else ""
                
                # Map common Facebook form fields to our standard format
                if name in ["full_name", "fullname", "full name"]:
                    extracted["full_name"] = value
                    # Try to split name
                    if " " in value:
                        parts = value.split(" ", 1)
                        extracted["first_name"] = parts[0]
                        extracted["last_name"] = parts[1]
                elif name in ["first_name", "firstname", "first name"]:
                    extracted["first_name"] = value
                elif name in ["last_name", "lastname", "last name"]:
                    extracted["last_name"] = value
                elif name in ["email", "email_address", "email address"]:
                    extracted["email"] = value
                elif name in ["phone", "phone_number", "phone number"]:
                    extracted["phone"] = self._format_phone_number(value)
                elif name in ["birth_date", "birthdate", "date_of_birth", "birth date", "date of birth"]:
                    extracted["date_of_birth"] = self._format_date(value)
                elif name in ["age"]:
                    extracted["age"] = value
                elif name in ["gender"]:
                    extracted["gender"] = value
                elif name in ["zip", "zip_code", "zipcode", "postal_code", "postal code"]:
                    extracted["zip_code"] = value
                elif name in ["coverage_amount", "coverage amount", "coverage"]:
                    extracted["coverage_amount"] = self._extract_numeric_value(value)
                elif name in ["product", "insurance_type", "policy_type", "insurance type", "policy type"]:
                    extracted["product"] = value.lower()
                else:
                    # Store other fields with their original names
                    extracted[name] = value
        
        # Add metadata
        if "created_time" in lead_data:
            extracted["created_time"] = lead_data["created_time"]
        else:
            extracted["created_time"] = datetime.datetime.now().isoformat()
            
        # Ad metadata
        if "ad_id" in lead_data:
            extracted["ad_id"] = lead_data["ad_id"]
        if "ad_name" in lead_data:
            extracted["ad_name"] = lead_data["ad_name"]
        if "campaign_id" in lead_data:
            extracted["campaign_id"] = lead_data["campaign_id"]
        if "campaign_name" in lead_data:
            extracted["campaign_name"] = lead_data["campaign_name"]
            
        return extracted
    
    def _extract_tiktok_lead_data(self, lead_data: Dict[str, Any]) -> Dict[str, Any]:
        """Extract structured data from TikTok lead format"""
        extracted = {
            "source": "tiktok",
            "lead_id": lead_data.get("id", f"tt_{int(time.time())}")
        }
        
        # Extract field data (TikTok has slightly different format)
        if "lead_data" in lead_data:
            fields = lead_data["lead_data"].get("fields", [])
            for field in fields:
                key = field.get("key", "").lower().replace(" ", "_")
                value = field.get("value", "")
                
                # Map common TikTok form fields to our standard format (similar to Facebook)
                if key in ["full_name", "fullname", "full name"]:
                    extracted["full_name"] = value
                    # Try to split name
                    if " " in value:
                        parts = value.split(" ", 1)
                        extracted["first_name"] = parts[0]
                        extracted["last_name"] = parts[1]
                elif key in ["first_name", "firstname", "first name"]:
                    extracted["first_name"] = value
                elif key in ["last_name", "lastname", "last name"]:
                    extracted["last_name"] = value
                elif key in ["email", "email_address", "email address"]:
                    extracted["email"] = value
                elif key in ["phone", "phone_number", "phone number"]:
                    extracted["phone"] = self._format_phone_number(value)
                elif key in ["birth_date", "birthdate", "date_of_birth", "birth date", "date of birth"]:
                    extracted["date_of_birth"] = self._format_date(value)
                elif key in ["age"]:
                    extracted["age"] = value
                elif key in ["gender"]:
                    extracted["gender"] = value
                elif key in ["zip", "zip_code", "zipcode", "postal_code", "postal code"]:
                    extracted["zip_code"] = value
                elif key in ["coverage_amount", "coverage amount", "coverage"]:
                    extracted["coverage_amount"] = self._extract_numeric_value(value)
                elif key in ["product", "insurance_type", "policy_type", "insurance type", "policy type"]:
                    extracted["product"] = value.lower()
                else:
                    # Store other fields with their original names
                    extracted[key] = value
        
        # Add metadata
        if "created_time" in lead_data:
            extracted["created_time"] = lead_data["created_time"]
        else:
            extracted["created_time"] = datetime.datetime.now().isoformat()
            
        # Ad metadata
        if "ad_info" in lead_data:
            ad_info = lead_data["ad_info"]
            extracted["ad_id"] = ad_info.get("ad_id")
            extracted["ad_name"] = ad_info.get("ad_name")
            extracted["campaign_id"] = ad_info.get("campaign_id")
            extracted["campaign_name"] = ad_info.get("campaign_name")
            
        return extracted
    
    def _format_phone_number(self, phone: str) -> str:
        """
        Format phone number to standard format
        
        Args:
            phone: Phone number in any format
            
        Returns:
            Formatted phone number
        """
        # Remove all non-digit characters
        digits_only = re.sub(r'\D', '', phone)
        
        # Handle US phone numbers (assumes US)
        if len(digits_only) == 10:
            return f"({digits_only[:3]}) {digits_only[3:6]}-{digits_only[6:]}"
        elif len(digits_only) == 11 and digits_only[0] == '1':
            return f"({digits_only[1:4]}) {digits_only[4:7]}-{digits_only[7:]}"
        else:
            return phone  # Return original if we can't format
    
    def _format_date(self, date_str: str) -> str:
        """
        Format date to standard ISO format
        
        Args:
            date_str: Date in any common format
            
        Returns:
            ISO formatted date (YYYY-MM-DD)
        """
        # Try common formats
        formats = [
            "%Y-%m-%d",     # 2023-04-30
            "%m/%d/%Y",     # 04/30/2023
            "%d/%m/%Y",     # 30/04/2023
            "%m-%d-%Y",     # 04-30-2023
            "%d-%m-%Y",     # 30-04-2023
            "%b %d, %Y",    # Apr 30, 2023
            "%d %b %Y",     # 30 Apr 2023
            "%B %d, %Y",    # April 30, 2023
            "%d %B %Y"      # 30 April 2023
        ]
        
        for fmt in formats:
            try:
                dt = datetime.datetime.strptime(date_str, fmt)
                return dt.strftime("%Y-%m-%d")
            except ValueError:
                continue
                
        return date_str  # Return original if we can't parse
    
    def _extract_numeric_value(self, value_str: str) -> float:
        """
        Extract numeric value from string (e.g., '$100,000' -> 100000)
        
        Args:
            value_str: String possibly containing a numeric value
            
        Returns:
            Extracted numeric value or 0
        """
        if not value_str:
            return 0
            
        # Remove all non-digit characters except decimal point
        numeric_str = re.sub(r'[^\d.]', '', value_str)
        
        try:
            return float(numeric_str)
        except ValueError:
            return 0
    
    def _should_enrich_data(self, data: Dict[str, Any]) -> bool:
        """
        Determine if data should be enriched
        
        Args:
            data: Lead data
            
        Returns:
            True if data should be enriched
        """
        # Minimum required fields for quoting
        required_fields = ["age", "gender", "zip_code"]
        
        # Check for email or phone (need at least one for lead)
        has_contact = "email" in data or "phone" in data
        
        # Count missing required fields
        missing_required = sum(1 for field in required_fields if field not in data)
        
        # Enrich if we have contact info but missing other required fields
        return has_contact and missing_required > 0
    
    def _enrich_lead_data(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Enrich lead data with third-party data
        
        Args:
            data: Original lead data
            
        Returns:
            Enriched lead data
        """
        logger.info("Enriching lead data")
        
        enriched_data = data.copy()
        
        # Data that could be derived if missing
        if "age" not in data and "date_of_birth" in data:
            try:
                dob = datetime.datetime.strptime(data["date_of_birth"], "%Y-%m-%d")
                today = datetime.datetime.now()
                age = today.year - dob.year - ((today.month, today.day) < (dob.month, dob.day))
                enriched_data["age"] = age
            except (ValueError, TypeError):
                pass
                
        # Typically we'd call a third-party API here based on email/phone
        # For example Clearbit, FullContact, etc.
        # For now, we'll simulate this with intelligent guesses
        
        if "email" in data and "zip_code" not in data:
            # In real implementation, we'd look up by email/phone
            # Mock implementation assigns common zip code
            enriched_data["zip_code"] = "33155"  # Miami
            
        if "gender" not in data and "first_name" in data:
            # In real implementation, we'd use a gender prediction API
            # Mock implementation uses common naming patterns
            name = data["first_name"].lower()
            if name in ["james", "john", "robert", "michael", "william", "david"]:
                enriched_data["gender"] = "M"
            elif name in ["mary", "patricia", "jennifer", "linda", "elizabeth", "susan"]:
                enriched_data["gender"] = "F"
            else:
                # Default guess
                enriched_data["gender"] = "M"
                
        if "age" not in data:
            # Make an educated guess based on insurance product
            if "product" in data:
                product = data["product"].lower()
                if product in ["medicare", "medicare supplement", "medicare advantage"]:
                    enriched_data["age"] = 65
                elif product in ["final expense", "burial"]:
                    enriched_data["age"] = 72
                else:
                    enriched_data["age"] = 45  # Default for most products
            else:
                enriched_data["age"] = 45  # General default

        # Fields added through enrichment are marked
        enriched_fields = [k for k in enriched_data if k not in data]
        if enriched_fields:
            enriched_data["_enriched_fields"] = enriched_fields
            
        return enriched_data
    
    def _store_lead(self, lead_data: Dict[str, Any], source: str, quotes: List[Dict[str, Any]]) -> str:
        """
        Store lead in database
        
        Args:
            lead_data: Lead data
            source: Lead source
            quotes: Generated quotes
            
        Returns:
            Lead ID
        """
        # Generate lead ID if not present
        lead_id = lead_data.get("lead_id", f"{source}_{int(time.time())}_{uuid.uuid4().hex[:8]}")
        
        # Create lead record
        lead_record = {
            "id": lead_id,
            "created_at": datetime.datetime.now().isoformat(),
            "source": source,
            "data": lead_data,
            "quotes": quotes,
            "status": "new"
        }
        
        # Add to leads database
        self.leads.append(lead_record)
        self._save_leads()
        
        logger.info(f"Stored lead {lead_id} from {source}")
        return lead_id
    
    def _fire_conversion_pixel(self, platform: str, event_type: str, data: Dict[str, Any]):
        """
        Fire conversion pixel to ad platform
        
        Args:
            platform: Ad platform name
            event_type: Event type (e.g., 'lead_capture', 'quote_complete')
            data: Event data
        """
        if platform not in self.ad_integrations or not self.ad_integrations[platform]["enabled"]:
            return
            
        # In a real implementation, this would use platform-specific APIs
        # For example, Facebook Conversion API or Google Conversion API
        
        pixel_id = self.ad_integrations[platform].get("pixel_id")
        if not pixel_id:
            return
            
        logger.info(f"Firing {event_type} conversion pixel for {platform}")
        
        # Mock implementation logs the event
        event_data = {
            "platform": platform,
            "pixel_id": pixel_id,
            "event_type": event_type,
            "timestamp": datetime.datetime.now().isoformat(),
            "user_data": {
                "email": data.get("email", ""),
                "phone": data.get("phone", ""),
                "first_name": data.get("first_name", ""),
                "last_name": data.get("last_name", ""),
                "zip": data.get("zip_code", "")
            }
        }
        
        # In real implementation: Send to Facebook/Google/TikTok API
        logger.info(f"Conversion event data: {json.dumps(event_data)}")
    
    def generate_quotes(self, data: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        Generate quotes based on lead data
        
        Args:
            data: Lead data
            
        Returns:
            List of quotes
        """
        logger.info("Generating quotes")
        start_time = time.time()
        
        # Track analytics
        self.analytics["quotes_started"] += 1
        
        # Determine which products to quote
        products_to_quote = self._determine_products_to_quote(data)
        
        quotes = []
        
        # Generate quote for each product
        for product in products_to_quote:
            quote_result = self._generate_quote_for_product(data, product)
            if quote_result:
                quotes.append(quote_result)
                
                # Track by product
                product_key = product.lower().replace(" ", "_")
                if product_key not in self.analytics["quotes_by_product"]:
                    self.analytics["quotes_by_product"][product_key] = 0
                self.analytics["quotes_by_product"][product_key] += 1
        
        # Store source
        source = data.get("source", "direct")
        if source not in self.analytics["quotes_by_source"]:
            self.analytics["quotes_by_source"][source] = 0
        self.analytics["quotes_by_source"][source] += 1
        
        # Track analytics
        if quotes:
            self.analytics["quotes_completed"] += 1
            
        # Track timing
        end_time = time.time()
        quote_time = end_time - start_time
        self.analytics["total_quote_time"] += quote_time
        total_quotes = self.analytics["quotes_completed"] + self.analytics["quotes_started"]
        if total_quotes > 0:
            self.analytics["average_quote_time"] = self.analytics["total_quote_time"] / total_quotes
            
        # Calculate conversion rate
        if self.analytics["quotes_started"] > 0:
            self.analytics["conversion_rate"] = self.analytics["quotes_completed"] / self.analytics["quotes_started"]
            
        # Store quote data
        quote_record = {
            "id": f"quote_{int(time.time())}_{uuid.uuid4().hex[:8]}",
            "created_at": datetime.datetime.now().isoformat(),
            "lead_data": data,
            "quotes": quotes,
            "quote_time": quote_time
        }
        
        self.quotes.append(quote_record)
        self._save_quotes()
        
        logger.info(f"Generated {len(quotes)} quotes in {quote_time:.2f} seconds")
        return quotes
    
    def _determine_products_to_quote(self, data: Dict[str, Any]) -> List[str]:
        """
        Determine which products to quote based on lead data
        
        Args:
            data: Lead data
            
        Returns:
            List of product types to quote
        """
        # If product is specified, use that
        if "product" in data:
            product = data["product"].lower()
            
            # Map common product names to standard products
            product_mapping = {
                "term": "term_life",
                "term life": "term_life",
                "whole": "whole_life",
                "whole life": "whole_life",
                "permanent": "whole_life",
                "iul": "iul",
                "indexed universal life": "iul",
                "universal life": "iul",
                "indexed": "iul",
                "medicare": "medicare_supplement",
                "medicare supplement": "medicare_supplement",
                "medigap": "medicare_supplement",
                "medsup": "medicare_supplement",
                "medicare advantage": "medicare_advantage",
                "advantage": "medicare_advantage",
                "final expense": "final_expense",
                "burial": "final_expense"
            }
            
            mapped_product = product_mapping.get(product)
            if mapped_product:
                return [mapped_product]
            return [product]
            
        # Otherwise, determine based on demographics
        products = []
        
        # Get age
        age = data.get("age")
        if not age and "date_of_birth" in data:
            try:
                dob = datetime.datetime.strptime(data["date_of_birth"], "%Y-%m-%d")
                today = datetime.datetime.now()
                age = today.year - dob.year - ((today.month, today.day) < (dob.month, dob.day))
            except (ValueError, TypeError):
                age = None
                
        # Medicare products for seniors
        if age and age >= 65:
            products.append("medicare_supplement")
            products.append("final_expense")
            
        # IUL for middle-aged (focus product)
        elif age and 35 <= age <= 60:
            products.append("iul")
            products.append("term_life")
            
        # Term for younger
        elif age and age < 35:
            products.append("term_life")
            products.append("iul")
            
        # Final expense for older
        elif age and age > 60:
            products.append("final_expense")
            products.append("whole_life")
            
        # Default if we can't determine
        if not products:
            products = ["term_life", "iul"]
            
        return products
    
    def _generate_quote_for_product(self, data: Dict[str, Any], product: str) -> Optional[Dict[str, Any]]:
        """
        Generate quote for specific product
        
        Args:
            data: Lead data
            product: Product type
            
        Returns:
            Quote data or None if quote cannot be generated
        """
        # Check if we can generate a quote for this product
        if product not in self.rate_tables and self.carrier_access is None:
            logger.warning(f"No rate information available for {product}")
            return None
            
        # Direct carrier API quote if available
        if self.carrier_access is not None and self.config["use_direct_carrier_apis"]:
            try:
                return self._generate_carrier_api_quote(data, product)
            except Exception as e:
                logger.error(f"Error generating API quote for {product}: {e}")
                # Fall back to rate table quote
                
        # Rate table quote
        if product in self.rate_tables:
            return self._generate_rate_table_quote(data, product)
            
        return None
    
    def _generate_carrier_api_quote(self, data: Dict[str, Any], product: str) -> Dict[str, Any]:
        """
        Generate quote using carrier API integration
        
        Args:
            data: Lead data
            product: Product type
            
        Returns:
            Quote data
        """
        # This is a mock implementation
        # In a real system, this would call the carrier API to get a quote
        
        # For now, we'll just simulate carrier quotes with a small delay
        time.sleep(0.2)  # Simulate API call time
        
        # Get basic product info
        product_info = product.replace("_", " ").title()
        
        # Get sample carriers for this product from rate tables
        carriers = list(self.rate_tables.get(product, {}).get("carriers", {}).keys())
        if not carriers:
            carriers = ["Mutual of Omaha", "National Life", "Foresters", "Americo"]
            
        # Generate carrier quotes
        carrier_quotes = []
        for carrier in carriers:
            # Calculate a base premium (in a real system, this would come from the API)
            base_premium = self._calculate_base_premium(data, product) * random.uniform(0.9, 1.1)
            
            carrier_quotes.append({
                "carrier": carrier,
                "monthly_premium": round(base_premium, 2),
                "annual_premium": round(base_premium * 12, 2),
                "features": self._get_product_features(product, carrier),
                "rating": round(random.uniform(3.5, 5.0), 1),
                "api_source": "direct_carrier_api"
            })
            
        # Sort by premium
        carrier_quotes.sort(key=lambda x: x["monthly_premium"])
        
        return {
            "product": product_info,
            "quote_id": f"{product}_{int(time.time())}_{uuid.uuid4().hex[:8]}",
            "timestamp": datetime.datetime.now().isoformat(),
            "carrier_quotes": carrier_quotes
        }
    
    def _generate_rate_table_quote(self, data: Dict[str, Any], product: str) -> Dict[str, Any]:
        """
        Generate quote using internal rate tables
        
        Args:
            data: Lead data
            product: Product type
            
        Returns:
            Quote data
        """
        # Get rate table
        rate_table = self.rate_tables.get(product)
        if not rate_table:
            return None
            
        # Get basic product info
        product_info = product.replace("_", " ").title()
        
        # Get coverage amount
        coverage_amount = data.get("coverage_amount")
        if not coverage_amount:
            # Default coverage by product
            defaults = {
                "term_life": 250000,
                "whole_life": 50000,
                "iul": 300000,
                "final_expense": 15000,
                "medicare_supplement": 0  # Not applicable
            }
            coverage_amount = defaults.get(product, 100000)
            
        # Generate carrier quotes
        carrier_quotes = []
        carriers = rate_table.get("carriers", {})
        
        for carrier_name, carrier_factor in carriers.items():
            # Calculate premium
            premium = self._calculate_premium(data, product, coverage_amount, carrier_factor)
            
            carrier_quotes.append({
                "carrier": carrier_name,
                "monthly_premium": round(premium, 2),
                "annual_premium": round(premium * 12, 2),
                "coverage_amount": coverage_amount,
                "features": self._get_product_features(product, carrier_name),
                "rating": round(random.uniform(3.5, 5.0), 1),
                "source": "rate_table"
            })
            
        # Sort by premium
        carrier_quotes.sort(key=lambda x: x["monthly_premium"])
        
        return {
            "product": product_info,
            "quote_id": f"{product}_{int(time.time())}_{uuid.uuid4().hex[:8]}",
            "timestamp": datetime.datetime.now().isoformat(),
            "carrier_quotes": carrier_quotes,
            "coverage_amount": coverage_amount
        }
    
    def _calculate_premium(self, data: Dict[str, Any], product: str, coverage_amount: float, carrier_factor: float) -> float:
        """
        Calculate premium using rate factors
        
        Args:
            data: Lead data
            product: Product type
            coverage_amount: Coverage amount
            carrier_factor: Carrier-specific factor
            
        Returns:
            Monthly premium
        """
        rate_table = self.rate_tables.get(product)
        if not rate_table:
            return 0
            
        # Get base rate
        base_rate = rate_table.get("base_rate_per_thousand", 0)
        
        # Get factors
        factors = rate_table.get("factors", {})
        
        # Calculate multiplier from each factor
        multiplier = 1.0
        
        # Age factor
        age = data.get("age")
        if age and "age" in factors:
            age_factors = factors["age"]
            # Find appropriate age range
            for age_range, factor in age_factors.items():
                if "-" in age_range:
                    min_age, max_age = map(int, age_range.split("-"))
                    if min_age <= age <= max_age:
                        multiplier *= factor
                        break
                elif age_range.endswith("+"):
                    min_age = int(age_range[:-1])
                    if age >= min_age:
                        multiplier *= factor
                        break
                elif int(age_range) == age:
                    multiplier *= factor
                    break
        
        # Gender factor
        gender = data.get("gender")
        if gender and "gender" in factors:
            gender_factors = factors["gender"]
            if gender.upper() in gender_factors:
                multiplier *= gender_factors[gender.upper()]
        
        # Tobacco factor
        tobacco = data.get("tobacco_use")
        if tobacco and "tobacco" in factors:
            tobacco_factors = factors["tobacco"]
            tobacco_key = "Y" if tobacco.upper() in ["Y", "YES", "TRUE"] else "N"
            multiplier *= tobacco_factors.get(tobacco_key, 1.0)
        
        # Health factor
        health = data.get("health")
        if health and "health" in factors:
            health_factors = factors["health"]
            if health.lower() in health_factors:
                multiplier *= health_factors[health.lower()]
        
        # State factor
        state = data.get("state")
        if state and "state" in factors:
            state_factors = factors["state"]
            state_key = state.upper()
            multiplier *= state_factors.get(state_key, state_factors.get("default", 1.0))
        
        # Term factor (for term life)
        if product == "term_life" and "term" in data and "term_multipliers" in rate_table:
            term = str(data["term"])
            term_factors = rate_table["term_multipliers"]
            if term in term_factors:
                multiplier *= term_factors[term]
        
        # Medicare plan factor
        if product == "medicare_supplement" and "plan" in data and "plan_base_rates" in rate_table:
            plan = data["plan"].upper()
            plan_rates = rate_table["plan_base_rates"]
            if plan in plan_rates:
                # For Medicare supplements, we replace the calculation
                return plan_rates[plan] * multiplier * carrier_factor
        
        # Calculate base premium (coverage amount in thousands * base rate * multiplier * carrier factor)
        if coverage_amount > 0:
            coverage_in_thousands = coverage_amount / 1000
            premium = coverage_in_thousands * base_rate * multiplier * carrier_factor
        else:
            # For products without coverage amount (like Medicare supplements)
            premium = base_rate * multiplier * carrier_factor
        
        return premium
    
    def _calculate_base_premium(self, data: Dict[str, Any], product: str) -> float:
        """
        Calculate base premium for carrier API emulation
        
        Args:
            data: Lead data
            product: Product type
            
        Returns:
            Monthly premium
        """
        # This is similar to _calculate_premium but without carrier factor
        # and with reasonable defaults for products
        
        # Default coverage amounts by product
        default_coverage = {
            "term_life": 250000,
            "whole_life": 50000,
            "iul": 300000,
            "final_expense": 15000,
            "medicare_supplement": 0  # Will use base rate directly
        }
        
        coverage_amount = data.get("coverage_amount", default_coverage.get(product, 100000))
        
        # Get base rate by product
        base_rates = {
            "term_life": 0.15,        # Per $1000
            "whole_life": 0.9,        # Per $1000
            "iul": 0.75,              # Per $1000
            "final_expense": 0.5,     # Per $1000
            "medicare_supplement": 130.0  # Base premium
        }
        
        base_rate = base_rates.get(product, 0.5)
        
        # Simplified factors
        multiplier = 1.0
        
        # Age factor
        age = data.get("age")
        if age:
            if product == "medicare_supplement":
                # Medicare age factors
                if age >= 80:
                    multiplier *= 1.3
                elif age >= 75:
                    multiplier *= 1.2
                elif age >= 70:
                    multiplier *= 1.1
            else:
                # Standard age progression
                if age >= 70:
                    multiplier *= 2.0
                elif age >= 60:
                    multiplier *= 1.5
                elif age >= 50:
                    multiplier *= 1.2
                elif age >= 40:
