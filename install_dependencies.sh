#!/bin/bash

# Install Dependencies Script
# This script installs the required Python packages for the MCP servers

echo "🔧 Installing MCP Server Dependencies"
echo "===================================="
echo "This script will install the required Python packages for the MCP servers."
echo

# Check if pip is available
if ! command -v pip3 &> /dev/null; then
    echo "❌ Error: pip3 is not installed. Please install Python and pip first."
    exit 1
fi

# Install the required packages
echo "Installing required packages..."
pip3 install -r requirements.txt

# Check if the installation was successful
if [ $? -eq 0 ]; then
    echo "✅ Dependencies installed successfully!"
else
    echo "❌ Error: Failed to install dependencies."
    exit 1
fi

# Install specific packages that might be missing
echo "Installing specific packages..."
pip3 install aiohttp torch transformers huggingface-hub croniter

echo
echo "✅ All dependencies installed successfully!"
echo "You can now run the MCP servers using:"
echo "  ./fix_mcp_autostart.sh"
