"""
Detailed information about Medicare Supplement plans and coverage options
"""

from dataclasses import dataclass
from typing import Dict, List, Optional
from datetime import date

@dataclass
class MedicarePlan:
    plan_letter: str
    description: str
    coverage: Dict[str, str]
    eligibility: Dict[str, str]
    notes: List[str]

MEDICARE_SUPPLEMENT_PLANS = {
    "Plan G": MedicarePlan(
        plan_letter="G",
        description="Comprehensive coverage that pays for most Medicare-approved costs",
        coverage={
            "part_a_deductible": "100%",
            "part_a_coinsurance": "100%",
            "part_b_coinsurance": "100%",
            "part_b_excess_charges": "100%",
            "skilled_nursing": "100%",
            "foreign_travel_emergency": "80%",
            "blood": "100%",
            "hospice": "100%",
            "part_b_deductible": "No coverage"
        },
        eligibility={
            "age": "65+",
            "medicare_parts": "Must have Medicare Parts A & B",
            "enrollment_period": "Open enrollment (6 months from Part B effective date)"
        },
        notes=[
            "Most popular plan for new Medicare enrollees",
            "Only out-of-pocket is Part B deductible",
            "Lower premiums than Plan F",
            "Available to all Medicare beneficiaries"
        ]
    ),
    
    "High Deductible Plan G": MedicarePlan(
        plan_letter="HDG",
        description="Same coverage as Plan G after meeting annual deductible",
        coverage={
            "annual_deductible": "$2,700 (2024)",
            "part_a_deductible": "100% after deductible",
            "part_a_coinsurance": "100% after deductible",
            "part_b_coinsurance": "100% after deductible",
            "part_b_excess_charges": "100% after deductible",
            "skilled_nursing": "100% after deductible",
            "foreign_travel_emergency": "80% after deductible",
            "blood": "100% after deductible",
            "hospice": "100% after deductible",
            "part_b_deductible": "No coverage"
        },
        eligibility={
            "age": "65+",
            "medicare_parts": "Must have Medicare Parts A & B",
            "enrollment_period": "Open enrollment (6 months from Part B effective date)"
        },
        notes=[
            "Lower monthly premiums than standard Plan G",
            "Good for those who don't expect many medical expenses",
            "Deductible applies to both Part A and B services",
            "Available to all Medicare beneficiaries"
        ]
    ),
    
    "Plan N": MedicarePlan(
        plan_letter="N",
        description="Lower-premium option with some cost-sharing",
        coverage={
            "part_a_deductible": "100%",
            "part_a_coinsurance": "100%",
            "part_b_coinsurance": "100% after copay",
            "copays": "Up to $20 for office visits, $50 for ER",
            "part_b_excess_charges": "No coverage",
            "skilled_nursing": "100%",
            "foreign_travel_emergency": "80%",
            "blood": "100%",
            "hospice": "100%",
            "part_b_deductible": "No coverage"
        },
        eligibility={
            "age": "65+",
            "medicare_parts": "Must have Medicare Parts A & B",
            "enrollment_period": "Open enrollment (6 months from Part B effective date)"
        },
        notes=[
            "Lower premiums than Plan G",
            "Good for those who don't mind copays",
            "Must pay Part B excess charges",
            "Available to all Medicare beneficiaries"
        ]
    )
}

def get_plan_details(plan_letter: str) -> Optional[MedicarePlan]:
    """Get detailed information about a specific Medicare Supplement plan"""
    return MEDICARE_SUPPLEMENT_PLANS.get(plan_letter)

def compare_plans(plan_letters: List[str]) -> Dict[str, Dict]:
    """Compare coverage between multiple Medicare Supplement plans"""
    comparison = {}
    
    for letter in plan_letters:
        plan = MEDICARE_SUPPLEMENT_PLANS.get(letter)
        if plan:
            comparison[letter] = {
                "coverage": plan.coverage,
                "notes": plan.notes
            }
            
    return comparison

def recommend_plan(age: int, budget: str, health_status: str) -> List[str]:
    """Recommend Medicare Supplement plans based on criteria"""
    recommendations = []
    
    if age >= 65:
        if budget.lower() == "high":
            recommendations.append("Plan G")
        elif budget.lower() == "medium":
            recommendations.append("Plan N")
        elif budget.lower() == "low":
            recommendations.append("High Deductible Plan G")
            
        # Add secondary recommendations
        if health_status.lower() == "frequent_visits":
            if "Plan N" in recommendations:
                recommendations.append("Plan G")  # Might be better despite higher premium
        elif health_status.lower() == "healthy":
            if "Plan G" in recommendations:
                recommendations.append("High Deductible Plan G")
                
    return recommendations

def calculate_eligibility(birth_date: date, part_b_date: date) -> Dict[str, bool]:
    """Calculate eligibility for Medicare Supplement plans"""
    today = date.today()
    age = today.year - birth_date.year - ((today.month, today.day) < (birth_date.month, birth_date.day))
    
    # Calculate if in Medigap Open Enrollment Period
    days_since_part_b = (today - part_b_date).days
    in_open_enrollment = days_since_part_b <= 180
    
    return {
        "age_eligible": age >= 65,
        "in_open_enrollment": in_open_enrollment,
        "guaranteed_issue": in_open_enrollment,
        "can_apply": age >= 65
    }

if __name__ == "__main__":
    # Example usage
    print("\nMedicare Supplement Plan Details:")
    for plan_name, plan in MEDICARE_SUPPLEMENT_PLANS.items():
        print(f"\n{plan_name}:")
        print(f"Description: {plan.description}")
        print("Key Coverage:")
        for benefit, coverage in plan.coverage.items():
            print(f"- {benefit.replace('_', ' ').title()}: {coverage}")
        print("Notes:")
        for note in plan.notes:
            print(f"- {note}")
            
    # Example plan comparison
    print("\nComparing Plan G and Plan N:")
    comparison = compare_plans(["Plan G", "Plan N"])
    for plan, details in comparison.items():
        print(f"\n{plan} Coverage:")
        for benefit, coverage in details['coverage'].items():
            print(f"- {benefit.replace('_', ' ').title()}: {coverage}")
            
    # Example recommendation
    print("\nPlan Recommendations:")
    recommendations = recommend_plan(67, "medium", "healthy")
    print(f"Recommended plans: {', '.join(recommendations)}")