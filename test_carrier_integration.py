import asyncio
import logging
from datetime import datetime
from insurance_carriers import CarrierManager
from secure_credentials import SecureCredentialsManager
from client_template import ClientManager

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

async def test_carrier_connections():
    """Test basic connectivity to all carrier portals"""
    try:
        carrier_manager = CarrierManager()
        results = {}
        
        logger.info("Testing carrier portal connections...")
        for carrier_id, portal in carrier_manager.carriers.items():
            try:
                connected = await portal.test_connection()
                results[carrier_id] = "Connected" if connected else "Failed"
                logger.info(f"{carrier_id.upper()}: {results[carrier_id]}")
            except Exception as e:
                results[carrier_id] = f"Error: {str(e)}"
                logger.error(f"Error testing {carrier_id}: {str(e)}")
                
        return results
    except Exception as e:
        logger.error(f"Failed to test carrier connections: {str(e)}")
        return None

async def test_quote_generation():
    """Test quote generation with sample client data"""
    try:
        # Initialize managers
        carrier_manager = CarrierManager()
        client_manager = ClientManager()
        
        # Create test client
        test_client = {
            "name": "Test Client",
            "dob": "1955-06-15",
            "height": "5'10\"",
            "weight": "180",
            "phone": "************",
            "email": "<EMAIL>",
            "address": "123 Test St, Port St. Lucie, FL 34952",
            "ssn": "***********",
            "drivers_license": "**********",
            
            "medications": [
                {
                    "drug_name": "Metformin",
                    "dosage": "500mg",
                    "frequency": "twice daily"
                }
            ],
            
            "tobacco_use": False,
            "marijuana_use": False,
            
            "bank_info": {
                "bank_name": "Test Bank",
                "routing_number": "*********",
                "account_number": "*********"
            },
            
            "budget_range": "$200-$250",
            "family_health_history": "No significant issues",
            "reason_notes": "Testing carrier integration",
            "start_date": datetime.now().strftime("%Y-%m-%d")
        }
        
        logger.info("Creating test client record...")
        client = client_manager.create_client(test_client)
        
        # Get quotes from all carriers
        logger.info("Requesting quotes from carriers...")
        quotes = await carrier_manager.get_quotes(client, agent_id="sandra")
        
        results = {}
        for carrier, quote in quotes.items():
            if quote:
                results[carrier] = {
                    "status": "success",
                    "premium": quote.get("premium"),
                    "coverage": quote.get("coverage")
                }
            else:
                results[carrier] = {"status": "failed"}
                
        return results
        
    except Exception as e:
        logger.error(f"Failed to test quote generation: {str(e)}")
        return None

async def main():
    """Run all carrier integration tests"""
    try:
        logger.info("Starting carrier integration tests...")
        
        # Test connections
        logger.info("\n=== Testing Carrier Connections ===")
        connection_results = await test_carrier_connections()
        
        # Test quote generation
        logger.info("\n=== Testing Quote Generation ===")
        quote_results = await test_quote_generation()
        
        # Print summary
        logger.info("\nTest Results Summary:")
        logger.info("=====================")
        
        logger.info("\nConnection Tests:")
        for carrier, status in connection_results.items():
            logger.info(f"{carrier.upper()}: {status}")
            
        logger.info("\nQuote Generation Tests:")
        for carrier, result in quote_results.items():
            status = result.get("status", "unknown")
            if status == "success":
                logger.info(f"{carrier.upper()}: Success")
                logger.info(f"  Premium: ${result.get('premium')}")
                logger.info(f"  Coverage: ${result.get('coverage')}")
            else:
                logger.info(f"{carrier.upper()}: Failed")
                
    except Exception as e:
        logger.error(f"Test execution failed: {str(e)}")

if __name__ == "__main__":
    asyncio.run(main())