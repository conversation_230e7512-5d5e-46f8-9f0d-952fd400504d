# Vocode Integration Plan for Flo Faction Insurance

This document outlines the plan to integrate Vocode, an open-source voice agent framework, into the Flo Faction Insurance system to enable voice calls, voicemails, and text messages to clients like <PERSON>.

## 1. Overview

Vocode is an open-source library that makes it easy to build voice-based LLM apps. It provides integrations with:
- Transcription services (Whisper, Deepgram, etc.)
- LLMs (OpenAI, Anthropic)
- Text-to-speech services (Eleven Labs, Azure, etc.)
- Telephony services (Twilio)

## 2. Installation and Setup

### 2.1 Install Vocode

```bash
pip install vocode
```

### 2.2 Set up environment variables

Create a `.env` file with the following credentials:
```
OPENAI_API_KEY=sk_531ff39d6099a4191b57e4935654c909d6196b59d9339015
TWILIO_ACCOUNT_SID=AC187c871afa232bbbc978caf33f3e25d9
TWILIO_AUTH_TOKEN=CqpVewwter1BEMdFIFHrN2XmUyt22wBP
ELEVEN_LABS_API_KEY=sk_531ff39d6099a4191b57e4935654c909d6196b59d9339015
```

## 3. Implementation Plan

### 3.1 Create a Telephony Server

1. Set up a self-hosted telephony server using Vocode
2. Configure Twilio to use your phone number (**********) for outbound calls
3. Create agent scripts for different scenarios (initial contact, follow-up, etc.)

### 3.2 Implement Voice Agent for Paul Edwards

Create a specialized agent for Paul Edwards with:
- Personalized greeting
- Information about tax-free retirement income
- Responses to common objections
- Call to action (schedule a meeting)

### 3.3 Set up Text Messaging

Configure the system to send text messages to Paul Edwards using:
- Twilio SMS integration through Vocode
- Personalized text templates
- Follow-up sequence

### 3.4 Implement Voicemail Capability

Set up the system to leave voicemails when calls aren't answered:
- Create voicemail scripts
- Configure Twilio to detect when calls go to voicemail
- Use Eleven Labs for natural-sounding voicemail messages

## 4. Integration with Existing System

### 4.1 Connect to CRM

Integrate Vocode with your existing CRM to:
- Pull client information (name, contact details, etc.)
- Log call outcomes
- Schedule follow-up actions

### 4.2 Connect to Wix Website

Integrate with the Flo Faction Wix website to:
- Log website visits from clients who received calls
- Track conversion rates
- Update client information

### 4.3 Connect to Email System

Set up email follow-ups after calls:
- Send personalized emails based on call outcomes
- Include relevant materials (IUL information, etc.)
- Track email opens and responses

## 5. Testing Plan

1. Test with internal team members first
2. Make test calls to Paul Edwards' number
3. Refine agent responses based on feedback
4. Implement A/B testing for different scripts

## 6. Deployment Steps

### 6.1 Set up Development Environment

```bash
# Clone the repository
git clone https://github.com/vocodedev/vocode-core.git
cd vocode-core

# Install dependencies
pip install -e .

# Set up environment variables
cp .env.example .env
# Edit .env with your credentials
```

### 6.2 Create a Basic Telephony Server

```python
# telephony_server.py
import os
from fastapi import FastAPI
from vocode.streaming.telephony.server.base import BaseTelephonyServer
from vocode.streaming.telephony.server.twilio import TwilioTelephonyServer
from vocode.streaming.models.agent import ChatGPTAgentConfig
from vocode.streaming.models.message import BaseMessage

app = FastAPI()

# Initialize the telephony server
telephony_server = TwilioTelephonyServer(
    base_url="YOUR_SERVER_URL",
    agent_config=ChatGPTAgentConfig(
        initial_message=BaseMessage(text="Hello, this is Sandra Smith from Flo Faction Insurance. How are you today?"),
        prompt_preamble="""
        You are Sandra Smith, an insurance agent from Flo Faction Insurance. 
        You're calling to discuss tax-free retirement income options through Indexed Universal Life policies.
        Be friendly, professional, and focus on how you can help the client achieve their retirement goals.
        """,
        openai_api_key=os.environ.get("OPENAI_API_KEY"),
    ),
)

# Add the telephony server to the FastAPI app
app = telephony_server.get_app()

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
```

### 6.3 Create an Outbound Call Script

```python
# make_call.py
import os
import asyncio
from vocode.streaming.telephony.conversation.outbound_call import OutboundCall
from vocode.streaming.telephony.conversation.twilio_conversation import TwilioConversation
from vocode.streaming.models.agent import ChatGPTAgentConfig
from vocode.streaming.models.message import BaseMessage

async def make_call_to_paul():
    # Configure the agent
    agent_config = ChatGPTAgentConfig(
        initial_message=BaseMessage(text="Hello, this is Sandra Smith from Flo Faction Insurance. How are you today?"),
        prompt_preamble="""
        You are Sandra Smith, an insurance agent from Flo Faction Insurance. 
        You're calling to discuss tax-free retirement income options through Indexed Universal Life policies.
        Be friendly, professional, and focus on how you can help the client achieve their retirement goals.
        """,
        openai_api_key=os.environ.get("OPENAI_API_KEY"),
    )
    
    # Make the outbound call
    outbound_call = OutboundCall(
        recipient_phone_number="+***********",  # Paul Edwards' primary number
        caller_phone_number="+1**********",     # Your verified Twilio number
        agent_config=agent_config,
        twilio_account_sid=os.environ.get("TWILIO_ACCOUNT_SID"),
        twilio_auth_token=os.environ.get("TWILIO_AUTH_TOKEN"),
    )
    
    # Start the call
    await outbound_call.start()
    
    # Wait for the call to complete
    while outbound_call.is_active():
        await asyncio.sleep(1)
    
    print("Call completed")

if __name__ == "__main__":
    asyncio.run(make_call_to_paul())
```

## 7. Next Steps

1. Set up the development environment
2. Implement the basic telephony server
3. Create and test the outbound call script
4. Refine the agent's responses
5. Implement text messaging and voicemail capabilities
6. Integrate with existing systems
7. Deploy to production

## 8. Resources

- [Vocode Documentation](https://docs.vocode.dev/open-source)
- [Vocode GitHub Repository](https://github.com/vocodedev/vocode-core)
- [Twilio Documentation](https://www.twilio.com/docs)
- [Eleven Labs Documentation](https://docs.elevenlabs.io/)
