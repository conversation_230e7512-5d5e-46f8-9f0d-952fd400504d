import async<PERSON>
from typing import Optional, Dict, Any
from core.base_agent import BaseAgent
from core.local_tools_manager import LocalToolsManager
from core.local_llm_manager import LocalLLMManager

class EnhancedAgentSystem:
    """Enhanced agent system with support for local tools and models."""
    
    def __init__(self):
        self.agents: Dict[str, BaseAgent] = {}
        self.local_tools = LocalToolsManager()
        self.local_llm = LocalLLMManager()
        self.shared_state: Dict[str, Any] = {}
        
    async def initialize_system(self):
        """Initialize the enhanced agent system."""
        # Set up shared local capabilities
        await self._setup_shared_resources()
        
    async def _setup_shared_resources(self):
        """Set up shared local tools and models."""
        # Initialize shared state for resource tracking
        self.shared_state["active_models"] = {}
        self.shared_state["screen_actions"] = []
        
    async def create_agent(
        self,
        agent_type: str,
        name: str,
        config: Optional[Dict[str, Any]] = None
    ) -> BaseAgent:
        """Create a new agent with access to local capabilities."""
        if name in self.agents:
            raise ValueError(f"Agent with name {name} already exists")
            
        # Create agent instance
        agent = BaseAgent(name)
        await agent.initialize()
        
        # Store agent reference
        self.agents[name] = agent
        
        return agent
        
    async def process_with_local_model(
        self,
        agent_name: str,
        prompt: str,
        **kwargs
    ) -> str:
        """Process text using an agent's local model."""
        if agent_name not in self.agents:
            raise ValueError(f"Unknown agent: {agent_name}")
            
        agent = self.agents[agent_name]
        responses = await agent.process_with_local_llm(prompt, **kwargs)
        
        # Track model usage in shared state
        model_name = kwargs.get("model_name", "default")
        self.shared_state["active_models"][model_name] = self.shared_state.get("active_models", {}).get(model_name, 0) + 1
        
        return responses[0] if responses else ""
        
    async def perform_screen_action(
        self,
        agent_name: str,
        action_type: str,
        **kwargs
    ) -> Any:
        """Perform a screen action using an agent's local tools."""
        if agent_name not in self.agents:
            raise ValueError(f"Unknown agent: {agent_name}")
            
        agent = self.agents[agent_name]
        result = await agent.perform_screen_action(action_type, **kwargs)
        
        # Track action in shared state
        self.shared_state["screen_actions"].append({
            "agent": agent_name,
            "action": action_type,
            "params": kwargs
        })
        
        return result
        
    def get_system_stats(self) -> Dict[str, Any]:
        """Get statistics about system resource usage."""
        return {
            "agents": len(self.agents),
            "active_models": dict(self.shared_state["active_models"]),
            "screen_actions": len(self.shared_state["screen_actions"])
        }
        
    def cleanup(self):
        """Clean up system resources."""
        for agent in self.agents.values():
            agent.cleanup()
        self.agents.clear()
        self.shared_state.clear()