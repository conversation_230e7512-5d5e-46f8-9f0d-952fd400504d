"""
Carrier Knockout Guides

This module provides carrier-specific knockout criteria to help agents quickly determine
if a client is eligible for coverage with specific carriers.

These knockout guides are used to find the best carrier and product match for clients
based on their specific needs, health conditions, and other relevant factors rather than
defaulting to a one-size-fits-all approach.
"""

from typing import Dict, List, Tuple, Optional

# Define standard health condition categories that may affect eligibility
HEALTH_CONDITIONS = {
    "diabetes": ["type_1_diabetes", "type_2_diabetes", "gestational_diabetes", "pre_diabetes"],
    "cardiovascular": ["heart_attack", "stroke", "afib", "heart_disease", "high_blood_pressure", "high_cholesterol"],
    "respiratory": ["copd", "asthma", "sleep_apnea", "emphysema"],
    "cancer": ["cancer_history", "active_cancer", "skin_cancer"],
    "neurological": ["alzheimers", "dementia", "parkinsons", "multiple_sclerosis", "epilepsy"],
    "autoimmune": ["lupus", "rheumatoid_arthritis", "multiple_sclerosis", "psoriasis"],
    "mental_health": ["anxiety", "depression", "bipolar", "schizophrenia", "ptsd"],
    "weight": ["underweight", "overweight", "obesity"],
    "substance_use": ["tobacco_use", "marijuana_use", "alcohol_abuse", "drug_abuse"],
    "other": ["kidney_disease", "liver_disease", "hospitalization_recent", "surgery_recent"]
}

# Mutual of Omaha Knockout Guides
MOO_KNOCKOUT = {
    "medicare_supplement": {
        "age_range": (65, 99),  # Medicare Supplement is for 65+ only
        "knockout_conditions": [
            "alzheimers", 
            "dementia", 
            "active_cancer",
            "kidney_failure_requiring_dialysis",
            "organ_transplant_waiting",
            "hospitalization_recent"  # If hospitalized in past 90 days
        ],
        "preferred_criteria": {
            "tobacco_use": False,
            "bmi_range": (18.5, 30.0),
            "family_history": "no_cardiovascular_issues"
        },
        "notes": "Best for clients aged 65+ with relatively stable health conditions. Generally more flexible with most health conditions than other carriers if controlled."
    },
    "income_advantage_iul": {
        "age_range": (18, 80),
        "knockout_conditions": [
            "active_cancer",
            "organ_transplant_waiting",
            "liver_failure",
            "heart_attack_recent",  # If heart attack in past 12 months
            "stroke_recent",        # If stroke in past 12 months
            "hiv_aids"
        ],
        "preferred_criteria": {
            "tobacco_use": False,
            "bmi_range": (18.5, 30.0),
            "health": "stable_conditions"
        },
        "notes": "Excellent for young professionals seeking cash value accumulation and tax advantages. Good for clients with stable health conditions."
    },
    "life_protection_advantage_iul": {
        "age_range": (18, 85),
        "knockout_conditions": [
            "active_cancer",
            "liver_failure",
            "heart_attack_recent",  # If heart attack in past 12 months
            "kidney_failure_requiring_dialysis",
            "hiv_aids"
        ],
        "preferred_criteria": {
            "tobacco_use": False,
            "bmi_range": (18.5, 30.0)
        },
        "notes": "Better for clients seeking death benefit protection with some cash value growth. More flexible than Income Advantage for health conditions."
    },
}

# FFL Trident Knockout Guides
FFL_KNOCKOUT = {
    "mortgage_protection": {
        "age_range": (18, 65),
        "knockout_conditions": [
            "active_cancer",
            "heart_attack_recent",
            "stroke_recent",
            "hiv_aids",
            "drug_abuse"
        ],
        "preferred_criteria": {
            "has_mortgage": True,
            "mortgage_amount": (100000, 1000000)
        },
        "notes": "Specifically designed for homeowners with mortgages. Can be more flexible for minor health issues."
    },
    "final_expense": {
        "age_range": (50, 85),
        "knockout_conditions": [
            "terminal_illness",
            "hospice_care",
            "alzheimers",
            "dementia",
            "hospitalization_recent"  # If hospitalized in past 30 days
        ],
        "guaranteed_issue_available": True,  # No health questions version available
        "notes": "Great for seniors with health issues seeking simplified underwriting. Guaranteed issue available for serious health conditions."
    }
}

# American General (AIG) Knockout Guides
AIG_KNOCKOUT = {
    "max_accumulator_plus": {
        "age_range": (18, 75),
        "knockout_conditions": [
            "active_cancer",
            "insulin_dependent_diabetes",
            "heart_attack_recent",  # If heart attack in past 24 months
            "stroke_recent",        # If stroke in past 24 months
            "liver_disease",
            "kidney_disease_severe",
            "alcohol_abuse_recent",
            "drug_abuse_recent"
        ],
        "preferred_criteria": {
            "tobacco_use": False,
            "bmi_range": (18.5, 29.0),
            "lifestyle": "active"
        },
        "notes": "Excellent cash value accumulation potential. Good for business owners and high-income individuals seeking tax advantages."
    }
}

# Pacific Life Knockout Guides
PACIFIC_LIFE_KNOCKOUT = {
    "pacific_discovery_premier_iul": {
        "age_range": (18, 85),
        "knockout_conditions": [
            "active_cancer",
            "alzheimers",
            "dementia",
            "hiv_aids",
            "liver_failure",
            "kidney_failure_requiring_dialysis",
            "heart_attack_recent",
            "stroke_recent"
        ],
        "preferred_criteria": {
            "tobacco_use": False,
            "bmi_range": (18.5, 30.0),
            "income_level": "middle_to_high"
        },
        "notes": "Good balance between protection and accumulation. More flexible on certain medical conditions than some other IUL products."
    }
}

# Nationwide Knockout Guides
NATIONWIDE_KNOCKOUT = {
    "yourlife_secure_lifetime_iul": {
        "age_range": (20, 80),
        "knockout_conditions": [
            "active_cancer",
            "heart_attack_recent",
            "stroke_recent",
            "liver_failure",
            "kidney_failure_requiring_dialysis",
            "hiv_aids"
        ],
        "preferred_criteria": {
            "tobacco_use": False,
            "bmi_range": (18.5, 31.0),
            "risk_tolerance": "conservative"
        },
        "notes": "Best for conservative clients prioritizing guaranteed death benefits over cash accumulation. More lenient on weight/BMI than other carriers."
    }
}

# All carriers knockout guides
CARRIER_KNOCKOUT_GUIDES = {
    "mutual_of_omaha": MOO_KNOCKOUT,
    "ffl_trident": FFL_KNOCKOUT,
    "american_general": AIG_KNOCKOUT,
    "pacific_life": PACIFIC_LIFE_KNOCKOUT,
    "nationwide": NATIONWIDE_KNOCKOUT
}

# Product type aliases for consistent matching
PRODUCT_TYPE_ALIASES = {
    "indexed_universal_life": ["iul", "income_advantage_iul", "life_protection_advantage_iul", 
                              "max_accumulator_plus", "pacific_discovery_premier_iul",
                              "yourlife_secure_lifetime_iul"],
    "medicare_supplement": ["medicare_supplement", "medigap"],
    "mortgage_protection": ["mortgage_protection"],
    "final_expense": ["final_expense"],
    "term_life": ["term_life", "term"]
}

def check_client_eligibility(client_data: Dict, carrier: str, product: str) -> Tuple[bool, List[str]]:
    """
    Check if a client is eligible for a specific product from a carrier
    
    Args:
        client_data: Dictionary containing client information
        carrier: Carrier name
        product: Product name
        
    Returns:
        Tuple containing eligibility (True/False) and reasons for ineligibility if applicable
    """
    carrier_lower = carrier.lower().replace(" ", "_")
    product_lower = product.lower().replace(" ", "_")
    
    if carrier_lower not in CARRIER_KNOCKOUT_GUIDES:
        return False, ["Carrier not found in knockout guides"]
    
    carrier_guide = CARRIER_KNOCKOUT_GUIDES[carrier_lower]
    if product_lower not in carrier_guide:
        return False, ["Product not found in carrier's knockout guide"]
    
    product_guide = carrier_guide[product_lower]
    ineligibility_reasons = []
    
    # Check age
    age = client_data.get("age")
    if age:
        min_age, max_age = product_guide.get("age_range", (0, 999))
        if age < min_age:
            ineligibility_reasons.append(f"Client age {age} is below minimum age {min_age}")
        elif age > max_age:
            ineligibility_reasons.append(f"Client age {age} is above maximum age {max_age}")
    
    # Check knockout conditions
    health_conditions = client_data.get("health_conditions", [])
    for condition in health_conditions:
        if condition in product_guide.get("knockout_conditions", []):
            ineligibility_reasons.append(f"Health condition '{condition}' is a knockout condition")
    
    # Check BMI if available
    if "bmi" in client_data and "bmi_range" in product_guide.get("preferred_criteria", {}):
        bmi = client_data["bmi"]
        min_bmi, max_bmi = product_guide["preferred_criteria"]["bmi_range"]
        if bmi < min_bmi:
            ineligibility_reasons.append(f"BMI {bmi} is below preferred minimum {min_bmi}")
        elif bmi > max_bmi:
            ineligibility_reasons.append(f"BMI {bmi} is above preferred maximum {max_bmi}")
    
    # Check tobacco use
    if "tobacco_use" in client_data and "tobacco_use" in product_guide.get("preferred_criteria", {}):
        if client_data["tobacco_use"] and product_guide["preferred_criteria"]["tobacco_use"] is False:
            ineligibility_reasons.append("Tobacco use may affect rating or eligibility")
    
    is_eligible = len(ineligibility_reasons) == 0
    
    return is_eligible, ineligibility_reasons

def get_best_product_matches(client_data: Dict, product_type: str = None) -> List[Dict]:
    """
    Find the best product matches for a client across all carriers
    
    Args:
        client_data: Dictionary containing client information
        product_type: Optional filter for product type
        
    Returns:
        List of recommended products sorted by match quality
    """
    matches = []
    
    # Get product aliases if a product_type is specified
    product_aliases = []
    if product_type:
        for alias_type, aliases in PRODUCT_TYPE_ALIASES.items():
            if product_type.lower() == alias_type.lower() or product_type.lower() in aliases:
                product_aliases = aliases
                break
    
    for carrier, carrier_guide in CARRIER_KNOCKOUT_GUIDES.items():
        for product, product_guide in carrier_guide.items():
            # Skip if product doesn't match requested type
            if product_type and product not in product_aliases:
                continue
                
            eligible, reasons = check_client_eligibility(client_data, carrier, product)
            
            if eligible:
                # Calculate match score based on how well client fits preferred criteria
                match_score = calculate_match_score(client_data, product_guide)
                
                matches.append({
                    "carrier": carrier,
                    "product": product,
                    "match_score": match_score,
                    "notes": product_guide.get("notes", ""),
                    "age_range": product_guide.get("age_range"),
                    "preferred_for": get_preferred_client_profile(product_guide)
                })
            else:
                # Include as ineligible with reasons
                matches.append({
                    "carrier": carrier,
                    "product": product,
                    "match_score": 0.0,
                    "eligible": False,
                    "ineligibility_reasons": reasons
                })
    
    # Sort by match score (highest first)
    eligible_matches = [m for m in matches if m.get("eligible", True)]
    eligible_matches.sort(key=lambda x: x["match_score"], reverse=True)
    
    return eligible_matches

def calculate_match_score(client_data: Dict, product_guide: Dict) -> float:
    """
    Calculate how well a client matches the preferred criteria for a product
    
    Args:
        client_data: Dictionary containing client information
        product_guide: Dictionary containing product guide information
        
    Returns:
        Match score from 0.0 to 1.0
    """
    score = 0.0
    weight = 0.0
    preferred = product_guide.get("preferred_criteria", {})
    
    # Age match (more weight if in middle of range)
    if "age" in client_data and "age_range" in product_guide:
        min_age, max_age = product_guide["age_range"]
        age = client_data["age"]
        age_range = max_age - min_age
        if age_range > 0:
            # Calculate how centered the age is in the age range
            center = min_age + (age_range / 2)
            distance_from_center = abs(age - center)
            max_distance = age_range / 2
            age_score = 1.0 - (distance_from_center / max_distance)
            score += age_score * 2.0  # Age is important
            weight += 2.0
    
    # BMI match
    if "bmi" in client_data and "bmi_range" in preferred:
        min_bmi, max_bmi = preferred["bmi_range"]
        bmi = client_data["bmi"]
        if min_bmi <= bmi <= max_bmi:
            score += 1.0
        else:
            # How far outside range
            if bmi < min_bmi:
                distance = min_bmi - bmi
                bmi_score = max(0.0, 1.0 - (distance / 5.0))  # 5 BMI points away = 0 score
            else:
                distance = bmi - max_bmi
                bmi_score = max(0.0, 1.0 - (distance / 5.0))
            score += bmi_score
        weight += 1.0
    
    # Tobacco use
    if "tobacco_use" in client_data and "tobacco_use" in preferred:
        if client_data["tobacco_use"] == preferred["tobacco_use"]:
            score += 1.0
        weight += 1.0
    
    # Risk tolerance (for investment-oriented products)
    if "risk_tolerance" in client_data and "risk_tolerance" in preferred:
        if client_data["risk_tolerance"] == preferred["risk_tolerance"]:
            score += 1.0
        weight += 1.0
    
    # Mortgage protection specific
    if "has_mortgage" in client_data and "has_mortgage" in preferred:
        if client_data["has_mortgage"] == preferred["has_mortgage"]:
            score += 1.0
        weight += 1.0
    
    if "mortgage_amount" in client_data and "mortgage_amount" in preferred:
        min_amt, max_amt = preferred["mortgage_amount"]
        amt = client_data["mortgage_amount"]
        if min_amt <= amt <= max_amt:
            score += 1.0
        weight += 1.0
    
    # Income level
    if "income_level" in client_data and "income_level" in preferred:
        if client_data["income_level"] == preferred["income_level"]:
            score += 1.0
        weight += 1.0
    
    # Return normalized score
    return score / weight if weight > 0 else 0.5

def get_preferred_client_profile(product_guide: Dict) -> str:
    """
    Generate a description of the ideal client for this product
    
    Args:
        product_guide: Dictionary containing product guide information
        
    Returns:
        String description of ideal client
    """
    profile_points = []
    
    # Age range
    if "age_range" in product_guide:
        min_age, max_age = product_guide["age_range"]
        profile_points.append(f"Age {min_age}-{max_age}")
    
    # Health preferences
    preferred = product_guide.get("preferred_criteria", {})
    
    if "tobacco_use" in preferred:
        tobacco_status = "non-tobacco user" if preferred["tobacco_use"] is False else "tobacco user acceptable"
        profile_points.append(tobacco_status)
    
    if "bmi_range" in preferred:
        min_bmi, max_bmi = preferred["bmi_range"]
        profile_points.append(f"BMI between {min_bmi}-{max_bmi}")
    
    if "risk_tolerance" in preferred:
        profile_points.append(f"{preferred['risk_tolerance'].replace('_', ' ')} risk tolerance")
    
    if "has_mortgage" in preferred and preferred["has_mortgage"]:
        profile_points.append("homeowner with mortgage")
    
    if "income_level" in preferred:
        profile_points.append(f"{preferred['income_level'].replace('_', ' ')} income level")
    
    if "lifestyle" in preferred:
        profile_points.append(f"{preferred['lifestyle']} lifestyle")
    
    # Join with commas and capitalize first letter
    description = ", ".join(profile_points)
    if description:
        description = description[0].upper() + description[1:]
    
    return description

def get_product_advantages(carrier: str, product: str) -> Dict:
    """
    Get detailed information about a specific product's advantages and ideal use cases
    
    Args:
        carrier: Carrier name
        product: Product name
        
    Returns:
        Dictionary with product advantages and key differentiators
    """
    carrier_lower = carrier.lower().replace(" ", "_")
    product_lower = product.lower().replace(" ", "_")
    
    advantages = {
        "mutual_of_omaha": {
            "income_advantage_iul": {
                "cash_value_growth": "Excellent",
                "death_benefit": "Good",
                "premium_flexibility": "High",
                "retirement_income": "Excellent",
                "key_features": [
                    "Strong index crediting options",
                    "Competitive cap rates",
                    "Chronic illness rider included",
                    "Lower policy fees than many competitors"
                ],
                "best_for": [
                    "Young professionals seeking tax-advantaged retirement supplement",
                    "Business owners looking for executive benefits",
                    "Parents planning for college expenses",
                    "Individuals wanting life insurance with living benefits"
                ]
            },
            "life_protection_advantage_iul": {
                "cash_value_growth": "Good",
                "death_benefit": "Excellent",
                "premium_flexibility": "Medium",
                "retirement_income": "Good",
                "key_features": [
                    "Strong death benefit guarantees",
                    "Competitive index crediting options",
                    "Lower cost structure for death benefit focus",
                    "Terminal illness rider included"
                ],
                "best_for": [
                    "Families seeking primary death benefit protection",
                    "Mortgage protection with cash value component",
                    "Budget-conscious clients needing permanent coverage",
                    "Individuals with some health issues (more forgiving underwriting)"
                ]
            },
            "medicare_supplement": {
                "coverage_gaps": "Excellent",
                "provider_freedom": "Excellent",
                "premium_stability": "Good",
                "standardized_benefits": "Yes - follows Medicare guidelines",
                "key_features": [
                    "No network restrictions - use any Medicare provider",
                    "Guaranteed renewable coverage",
                    "Household discount available",
                    "Foreign travel emergency coverage"
                ],
                "best_for": [
                    "Medicare beneficiaries wanting freedom of provider choice",
                    "Individuals wanting predictable out-of-pocket costs",
                    "Frequent travelers within the US",
                    "Those willing to pay higher premiums for more comprehensive coverage"
                ]
            }
        },
        "ffl_trident": {
            "mortgage_protection": {
                "underwriting_ease": "High",
                "approval_speed": "Fast",
                "customization": "Limited",
                "key_features": [
                    "Options for no medical exam",
                    "Coverage designed to pay off mortgage",
                    "Simple application process",
                    "Quick approvals for many applicants"
                ],
                "best_for": [
                    "Recent homebuyers",
                    "Individuals with moderate health issues",
                    "Family breadwinners with mortgage obligations",
                    "Clients needing quick coverage"
                ]
            },
            "final_expense": {
                "underwriting_ease": "Very High",
                "approval_speed": "Very Fast",
                "guaranteed_issue_available": "Yes",
                "key_features": [
                    "Simplified underwriting",
                    "Guaranteed issue options available",
                    "Coverage doesn't expire with age",
                    "Stable premiums"
                ],
                "best_for": [
                    "Seniors with health concerns",
                    "Individuals wanting to cover funeral expenses",
                    "Those who've been declined for traditional life insurance",
                    "Applicants wanting simple, fast coverage"
                ]
            }
        }
    }
    
    if carrier_lower in advantages and product_lower in advantages[carrier_lower]:
        return advantages[carrier_lower][product_lower]
    else:
        return {}

def find_best_product_type_for_client(client_data: Dict) -> str:
    """
    Determine the best product type for a client based on their needs and demographics
    
    Args:
        client_data: Dictionary containing client information
        
    Returns:
        Recommended product type
    """
    age = client_data.get("age", 0)
    has_mortgage = client_data.get("has_mortgage", False)
    has_medicare = client_data.get("has_medicare", False)
    income = client_data.get("income", 0)
    retirement_focus = client_data.get("retirement_focus", False)
    budget = client_data.get("budget", 0)
    health_status = client_data.get("health_status", "average")
    
    # Medicare Supplement
    if age >= 65 and has_medicare:
        return "medicare_supplement"
    
    # Final Expense
    if age >= 60 and budget < 100 and (health_status == "poor" or health_status == "average"):
        return "final_expense"
    
    # IUL for Retirement - increased priority for retirement focus
    if retirement_focus and income > 50000:
        # Stronger priority for IUL with retirement focus regardless of mortgage
        return "indexed_universal_life"
    
    # Mortgage Protection - only if not focused on retirement income
    if has_mortgage and age < 65 and not retirement_focus:
        return "mortgage_protection"
    
    # Default to term life for young/middle-aged with tighter budgets
    if age < 50 and budget < 150 and not retirement_focus:
        return "term_life"
    
    # Default to IUL for others
    return "indexed_universal_life"

if __name__ == "__main__":
    # Example client
    client = {
        "age": 32,
        "gender": "Male",
        "tobacco_use": False,
        "bmi": 24.5,
        "health_conditions": ["high_blood_pressure", "high_cholesterol"],
        "medications": ["lisinopril", "lipitor"],
        "income": 85000,
        "has_mortgage": True,
        "mortgage_amount": 250000,
        "retirement_focus": True,
        "risk_tolerance": "moderate",
        "health_status": "average",
        "budget": 200
    }
    
    # Find best product type
    recommended_type = find_best_product_type_for_client(client)
    print(f"Recommended product type: {recommended_type}\n")
    
    # Find specific product matches
    matches = get_best_product_matches(client, recommended_type)
    print(f"Found {len(matches)} potential matches. Top 3 recommendations:")
    
    for i, match in enumerate(matches[:3], 1):
        print(f"\n{i}. {match['carrier'].title()} - {match['product'].replace('_', ' ').title()}")
        print(f"   Match score: {match['match_score']:.2f}")
        print(f"   Age range: {match['age_range'][0]}-{match['age_range'][1]}")
        print(f"   Best for: {match['preferred_for']}")
        print(f"   Notes: {match['notes']}")
        
        # Get specific advantages
        advantages = get_product_advantages(match['carrier'], match['product'])
        if advantages:
            print("\n   Key advantages:")
            for feature, rating in advantages.items():
                if isinstance(rating, list):
                    print(f"   - {feature.replace('_', ' ').title()}:")
                    for item in rating:
                        print(f"     * {item}")
                elif isinstance(rating, str):
                    print(f"   - {feature.replace('_', ' ').title()}: {rating}")