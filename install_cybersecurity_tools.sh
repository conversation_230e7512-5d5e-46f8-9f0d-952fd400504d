#!/bin/bash

# Install essential cybersecurity tools on macOS

echo "Installing <PERSON> the Ripper..."
brew install john

echo "Installing Nmap..."
brew install nmap

echo "Installing Wireshark..."
brew install --cask wireshark

echo "Installing Metasploit Framework..."
brew install metasploit

echo "Installing Burp Suite..."
brew install --cask burp-suite

echo "Installing Aircrack-ng..."
brew install aircrack-ng

echo "Installing sqlmap..."
brew install sqlmap

echo "Installing OWASP ZAP..."
brew install --cask owasp-zap

echo "Installing theHarvester..."
brew install theharvester

echo "Installing Nikto..."
brew install nikto

# Install AI-Enhanced Cybersecurity Tools

echo "Installing PentestGPT..."
if [ ! -d "PentestGPT" ]; then
  git clone https://github.com/GreyDGL/PentestGPT.git
  cd PentestGPT
  pip install -r requirements.txt
  cd ..
else
  echo "PentestGPT already cloned."
fi

echo "Installing DeepExploit..."
if [ ! -d "DeepExploit" ]; then
  git clone https://github.com/13o-bbr-bbq/machine_learning_security.git
  cd machine_learning_security/DeepExploit
  pip install -r requirements.txt
  cd ../../
else
  echo "DeepExploit already cloned."
fi

echo "Installing CAI (Cybersecurity AI)..."
if [ ! -d "CAI" ]; then
  git clone https://github.com/CAI-Project/CAI.git
  cd CAI
  pip install -r requirements.txt
  cd ..
else
  echo "CAI already cloned."
fi

echo "Pentera and Harmony Intelligence require commercial licenses and manual installation."

echo "Installation script completed."
