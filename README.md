# Unrestricted Agent System

This system provides a comprehensive framework with completely unrestricted access to security tools, email accounts, portals, and VPN capabilities in a controlled environment.

## Components

1. **Unrestricted Security Tools** (`unrestricted_security_tools.py`)
   - Full access to security tools like <PERSON> Ripper, Nmap, Hashcat, etc.
   - NO restrictions on tool usage or capabilities

2. **Advanced Credential Recovery** (`advanced_credential_recovery.py`)
   - Password hash cracking
   - Web form brute forcing
   - Password variation generation

3. **ProtonVPN Integration** (`protonvpn_setup.py`)
   - Automated setup and configuration
   - Persistent VPN connections
   - Connection verification

4. **Email & Portal Access** (`credential_manager.py`, `portal_automation.py`)
   - Secure credential storage
   - Automated portal access
   - Email account management

5. **Agent Security Integration** (`agent_security_integration.py`)
   - Connects all security tools with agents
   - Provides unified access interface
   - Enhances all agents with security capabilities

6. **UI Tars Integration** (`tars_integration.py`)
   - Connects the system with UI Tars Desktop
   - Configures MCP servers for tool access
   - Defines security agent profiles

## Setup & Usage

The system includes a comprehensive setup script that handles all aspects of setup and usage:

```bash
# Full system setup
./setup_and_run.sh --all

# VPN setup only
./setup_and_run.sh --vpn

# List security tools
./setup_and_run.sh --security

# Test credential recovery
./setup_and_run.sh --recovery

# Agent security integration
./setup_and_run.sh --integrate

# Tars integration
./setup_and_run.sh --tars

# See all options
./setup_and_run.sh --help
```

## Testing

Run the full integration test to verify all components are working together:

```bash
./test_security_integration.py
```

This tests the security tools, credential recovery, VPN integration, and agent integration.

## Email Accounts

The system is configured to work with the following email accounts:

- `<EMAIL>` (Main insurance email)
- `<EMAIL>` (Contracting information and requests)
- `<EMAIL>` (Permanent broker email)

## Important Notes

1. This system has **NO RESTRICTIONS** on tool usage or capabilities, as requested.
2. It is designed for use in a **controlled environment** with proper authorization.
3. All tools and integrations are fully functional and ready to use.
4. The agents have full unrestricted access to all security tools.

## Example: Running a Full System Setup

To set up the entire system with all components integrated:

```bash
# Make scripts executable
chmod +x setup_and_run.sh test_security_integration.py

# Run full setup
./setup_and_run.sh --all

# Test the integration
./test_security_integration.py
```

This will set up ProtonVPN, integrate all security tools with the agents, and ensure everything is working properly.