<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>IRIS - Intelligent Responsive Interface System</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js" defer></script>
    <script src="https://unpkg.com/axios/dist/axios.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/marked/marked.min.js"></script>
    <script src="https://unpkg.com/prismjs@1.29.0/components/prism-core.min.js"></script>
    <script src="https://unpkg.com/prismjs@1.29.0/plugins/autoloader/prism-autoloader.min.js"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.0/css/all.min.css" rel="stylesheet">
    <link href="https://unpkg.com/prismjs@1.29.0/themes/prism-tomorrow.min.css" rel="stylesheet">
    <style>
        [x-cloak] { display: none !important; }
        .gradient-bg { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }
        .gradient-purple { background: linear-gradient(135deg, #8B5CF6 0%, #A855F7 100%); }
        .gradient-blue { background: linear-gradient(135deg, #3B82F6 0%, #1D4ED8 100%); }
        .gradient-green { background: linear-gradient(135deg, #10B981 0%, #059669 100%); }
        .card-hover { transition: all 0.3s ease; }
        .card-hover:hover { transform: translateY(-2px); box-shadow: 0 15px 35px rgba(0,0,0,0.1); }
        .pulse-dot { animation: pulse 2s infinite; }
        .breathing { animation: breathing 3s ease-in-out infinite; }
        .voice-wave { animation: voice-wave 1.5s ease-in-out infinite; }
        .workflow-node { transition: all 0.3s ease; }
        .workflow-node:hover { transform: scale(1.05); }
        .chat-bubble { max-width: 80%; word-wrap: break-word; }
        .typing-indicator { animation: typing 1.4s infinite; }
        .file-drop-zone { border: 2px dashed #cbd5e1; transition: all 0.3s ease; }
        .file-drop-zone.dragover { border-color: #3b82f6; background-color: #eff6ff; }
        .status-indicator { width: 8px; height: 8px; border-radius: 50%; display: inline-block; margin-right: 8px; }
        .status-online { background-color: #10b981; }
        .status-offline { background-color: #ef4444; }
        .status-busy { background-color: #f59e0b; }
        .glass-effect { backdrop-filter: blur(10px); background: rgba(255, 255, 255, 0.1); }
        .sidebar-collapsed { width: 60px; }
        .sidebar-expanded { width: 280px; }
        .modal-backdrop { backdrop-filter: blur(5px); }

        @keyframes pulse { 0%, 100% { opacity: 1; } 50% { opacity: 0.5; } }
        @keyframes breathing { 0%, 100% { transform: scale(1); } 50% { transform: scale(1.05); } }
        @keyframes voice-wave { 0%, 100% { transform: scaleY(1); } 50% { transform: scaleY(1.5); } }
        @keyframes typing { 0%, 60%, 100% { transform: translateY(0); } 30% { transform: translateY(-10px); } }

        .workflow-canvas {
            background-image: radial-gradient(circle, #e5e7eb 1px, transparent 1px);
            background-size: 20px 20px;
        }

        .voice-button.recording {
            background: linear-gradient(45deg, #ef4444, #dc2626);
            animation: pulse 1s infinite;
        }

        .agent-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            color: white;
        }

        .message-content pre {
            background: #1f2937;
            border-radius: 8px;
            padding: 16px;
            overflow-x: auto;
        }

        .message-content code {
            background: #374151;
            padding: 2px 6px;
            border-radius: 4px;
            font-size: 0.875rem;
        }
    </style>
</head>
<body class="bg-gray-900 text-white font-sans overflow-hidden" x-data="irisInterface()" x-init="init()" @keydown.escape="closeModals()"
    <!-- Main Container -->
    <div class="flex h-screen">
        <!-- Sidebar -->
        <div class="bg-gray-800 transition-all duration-300" :class="sidebarCollapsed ? 'sidebar-collapsed' : 'sidebar-expanded'">
            <div class="p-4">
                <!-- Logo/Brand -->
                <div class="flex items-center space-x-3 mb-8">
                    <div class="w-10 h-10 bg-gradient-purple rounded-lg flex items-center justify-center">
                        <i class="fas fa-brain text-white text-lg"></i>
                    </div>
                    <div x-show="!sidebarCollapsed" class="transition-opacity duration-300">
                        <h1 class="text-xl font-bold text-white">IRIS</h1>
                        <p class="text-xs text-gray-400">Intelligent Response Interface</p>
                    </div>
                </div>

                <!-- Navigation -->
                <nav class="space-y-2">
                    <button @click="setActiveView('chat')"
                            :class="activeView === 'chat' ? 'bg-purple-600 text-white' : 'text-gray-300 hover:bg-gray-700'"
                            class="w-full flex items-center space-x-3 px-3 py-2 rounded-lg transition-colors">
                        <i class="fas fa-comments text-lg"></i>
                        <span x-show="!sidebarCollapsed">Chat Interface</span>
                    </button>

                    <button @click="setActiveView('voice')"
                            :class="activeView === 'voice' ? 'bg-purple-600 text-white' : 'text-gray-300 hover:bg-gray-700'"
                            class="w-full flex items-center space-x-3 px-3 py-2 rounded-lg transition-colors">
                        <i class="fas fa-microphone text-lg"></i>
                        <span x-show="!sidebarCollapsed">Voice Control</span>
                    </button>

                    <button @click="setActiveView('workflow')"
                            :class="activeView === 'workflow' ? 'bg-purple-600 text-white' : 'text-gray-300 hover:bg-gray-700'"
                            class="w-full flex items-center space-x-3 px-3 py-2 rounded-lg transition-colors">
                        <i class="fas fa-project-diagram text-lg"></i>
                        <span x-show="!sidebarCollapsed">Workflow Builder</span>
                    </button>

                    <button @click="setActiveView('files')"
                            :class="activeView === 'files' ? 'bg-purple-600 text-white' : 'text-gray-300 hover:bg-gray-700'"
                            class="w-full flex items-center space-x-3 px-3 py-2 rounded-lg transition-colors">
                        <i class="fas fa-folder text-lg"></i>
                        <span x-show="!sidebarCollapsed">File Manager</span>
                    </button>

                    <button @click="setActiveView('agents')"
                            :class="activeView === 'agents' ? 'bg-purple-600 text-white' : 'text-gray-300 hover:bg-gray-700'"
                            class="w-full flex items-center space-x-3 px-3 py-2 rounded-lg transition-colors">
                        <i class="fas fa-users text-lg"></i>
                        <span x-show="!sidebarCollapsed">Agent Hub</span>
                    </button>

                    <button @click="setActiveView('search')"
                            :class="activeView === 'search' ? 'bg-purple-600 text-white' : 'text-gray-300 hover:bg-gray-700'"
                            class="w-full flex items-center space-x-3 px-3 py-2 rounded-lg transition-colors">
                        <i class="fas fa-search text-lg"></i>
                        <span x-show="!sidebarCollapsed">Web Search</span>
                    </button>

                    <button @click="setActiveView('monitor')"
                            :class="activeView === 'monitor' ? 'bg-purple-600 text-white' : 'text-gray-300 hover:bg-gray-700'"
                            class="w-full flex items-center space-x-3 px-3 py-2 rounded-lg transition-colors">
                        <i class="fas fa-chart-line text-lg"></i>
                        <span x-show="!sidebarCollapsed">System Monitor</span>
                    </button>
                </nav>

                <!-- System Status -->
                <div class="mt-8 pt-4 border-t border-gray-700" x-show="!sidebarCollapsed">
                    <h3 class="text-sm font-semibold text-gray-400 mb-3">System Status</h3>
                    <div class="space-y-2 text-sm">
                        <div class="flex items-center justify-between">
                            <span class="text-gray-300">MCP Servers</span>
                            <span class="status-indicator status-online"></span>
                        </div>
                        <div class="flex items-center justify-between">
                            <span class="text-gray-300">AI Models</span>
                            <span class="status-indicator" :class="modelsOnline ? 'status-online' : 'status-offline'"></span>
                        </div>
                        <div class="flex items-center justify-between">
                            <span class="text-gray-300">Voice System</span>
                            <span class="status-indicator" :class="voiceEnabled ? 'status-online' : 'status-offline'"></span>
                        </div>
                    </div>
                </div>

                <!-- Collapse Button -->
                <button @click="sidebarCollapsed = !sidebarCollapsed"
                        class="absolute bottom-4 left-4 w-8 h-8 bg-gray-700 rounded-lg flex items-center justify-center hover:bg-gray-600 transition-colors">
                    <i class="fas fa-chevron-left text-sm" :class="sidebarCollapsed ? 'rotate-180' : ''"></i>
                </button>
            </div>
        </div>

        <!-- Main Content Area -->
        <div class="flex-1 flex flex-col overflow-hidden">
            <!-- Top Bar -->
            <div class="bg-gray-800 border-b border-gray-700 px-6 py-4">
                <div class="flex items-center justify-between">
                    <div class="flex items-center space-x-4">
                        <h2 class="text-xl font-semibold text-white" x-text="getViewTitle()"></h2>
                        <div class="flex items-center space-x-2">
                            <div class="status-indicator" :class="systemOnline ? 'status-online' : 'status-offline'"></div>
                            <span class="text-sm text-gray-400" x-text="systemOnline ? 'All Systems Online' : 'System Issues'"></span>
                        </div>
                    </div>

                    <div class="flex items-center space-x-4">
                        <!-- Voice Toggle -->
                        <button @click="toggleVoice()"
                                :class="voiceEnabled ? 'bg-green-600 hover:bg-green-700' : 'bg-gray-600 hover:bg-gray-700'"
                                class="px-4 py-2 rounded-lg transition-colors flex items-center space-x-2">
                            <i class="fas fa-microphone"></i>
                            <span x-text="voiceEnabled ? 'Voice On' : 'Voice Off'"></span>
                        </button>

                        <!-- Settings -->
                        <button @click="showSettings = true" class="p-2 rounded-lg bg-gray-700 hover:bg-gray-600 transition-colors">
                            <i class="fas fa-cog"></i>
                        </button>

                        <!-- User Menu -->
                        <div class="relative" x-data="{ open: false }">
                            <button @click="open = !open" class="flex items-center space-x-2 p-2 rounded-lg bg-gray-700 hover:bg-gray-600 transition-colors">
                                <div class="w-8 h-8 bg-gradient-purple rounded-full flex items-center justify-center">
                                    <i class="fas fa-user text-sm"></i>
                                </div>
                                <span class="text-sm">Admin</span>
                            </button>

                            <div x-show="open" @click.away="open = false"
                                 class="absolute right-0 mt-2 w-48 bg-gray-800 rounded-lg shadow-lg border border-gray-700 z-50">
                                <div class="py-2">
                                    <a href="#" class="block px-4 py-2 text-sm text-gray-300 hover:bg-gray-700">Profile</a>
                                    <a href="#" class="block px-4 py-2 text-sm text-gray-300 hover:bg-gray-700">Settings</a>
                                    <a href="#" class="block px-4 py-2 text-sm text-gray-300 hover:bg-gray-700">Logout</a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Content Views -->
            <div class="flex-1 overflow-hidden">
                <!-- Chat Interface -->
                <div x-show="activeView === 'chat'" class="h-full flex flex-col">
                    <div class="flex-1 overflow-y-auto p-6 space-y-4" id="chatContainer">
                        <!-- Welcome Message -->
                        <div class="flex items-start space-x-3" x-show="messages.length === 0">
                            <div class="agent-avatar bg-gradient-purple">
                                <i class="fas fa-brain"></i>
                            </div>
                            <div class="bg-gray-800 rounded-lg p-4 max-w-md">
                                <p class="text-gray-300">Hello! I'm IRIS, your intelligent assistant. I can help you with:</p>
                                <ul class="mt-2 text-sm text-gray-400 space-y-1">
                                    <li>• Natural language conversations</li>
                                    <li>• File analysis and processing</li>
                                    <li>• Web search and research</li>
                                    <li>• Workflow automation</li>
                                    <li>• Agent coordination</li>
                                </ul>
                                <p class="mt-3 text-gray-300">How can I assist you today?</p>
                            </div>
                        </div>

                        <!-- Chat Messages -->
                        <template x-for="message in messages" :key="message.id">
                            <div class="flex items-start space-x-3" :class="message.sender === 'user' ? 'flex-row-reverse space-x-reverse' : ''">
                                <div class="agent-avatar" :class="message.sender === 'user' ? 'bg-blue-600' : 'bg-gradient-purple'">
                                    <i :class="message.sender === 'user' ? 'fas fa-user' : 'fas fa-brain'"></i>
                                </div>
                                <div class="chat-bubble rounded-lg p-4"
                                     :class="message.sender === 'user' ? 'bg-blue-600 text-white' : 'bg-gray-800 text-gray-300'">
                                    <div class="message-content" x-html="formatMessage(message.content)"></div>
                                    <div class="text-xs opacity-70 mt-2" x-text="formatTime(message.timestamp)"></div>
                                </div>
                            </div>
                        </template>

                        <!-- Typing Indicator -->
                        <div x-show="isTyping" class="flex items-start space-x-3">
                            <div class="agent-avatar bg-gradient-purple">
                                <i class="fas fa-brain"></i>
                            </div>
                            <div class="bg-gray-800 rounded-lg p-4">
                                <div class="flex space-x-1">
                                    <div class="w-2 h-2 bg-gray-500 rounded-full typing-indicator"></div>
                                    <div class="w-2 h-2 bg-gray-500 rounded-full typing-indicator" style="animation-delay: 0.2s;"></div>
                                    <div class="w-2 h-2 bg-gray-500 rounded-full typing-indicator" style="animation-delay: 0.4s;"></div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Chat Input -->
                    <div class="border-t border-gray-700 p-4">
                        <div class="flex items-end space-x-4">
                            <!-- File Upload -->
                            <div class="relative">
                                <input type="file" id="fileInput" multiple class="hidden" @change="handleFileUpload($event)">
                                <button @click="$refs.fileInput.click()" class="p-3 bg-gray-700 hover:bg-gray-600 rounded-lg transition-colors">
                                    <i class="fas fa-paperclip"></i>
                                </button>
                            </div>

                            <!-- Text Input -->
                            <div class="flex-1 relative">
                                <textarea x-model="currentMessage"
                                         @keydown.enter.prevent="sendMessage()"
                                         @keydown.shift.enter.prevent="currentMessage += '\n'"
                                         placeholder="Type your message... (Enter to send, Shift+Enter for new line)"
                                         class="w-full bg-gray-800 border border-gray-600 rounded-lg px-4 py-3 text-white placeholder-gray-400 resize-none focus:outline-none focus:border-purple-500"
                                         rows="1"></textarea>
                            </div>

                            <!-- Voice Input -->
                            <button @click="toggleRecording()"
                                    :class="isRecording ? 'voice-button recording' : 'bg-gray-700 hover:bg-gray-600'"
                                    class="p-3 rounded-lg transition-colors">
                                <i class="fas fa-microphone"></i>
                            </button>

                            <!-- Send Button -->
                            <button @click="sendMessage()"
                                    :disabled="!currentMessage.trim()"
                                    :class="currentMessage.trim() ? 'bg-purple-600 hover:bg-purple-700' : 'bg-gray-700 cursor-not-allowed'"
                                    class="px-6 py-3 rounded-lg transition-colors">
                                <i class="fas fa-paper-plane"></i>
                            </button>
                        </div>

                        <!-- Quick Actions -->
                        <div class="flex flex-wrap gap-2 mt-3">
                            <button @click="quickAction('analyze_file')" class="px-3 py-1 bg-gray-700 hover:bg-gray-600 rounded-full text-sm transition-colors">
                                📄 Analyze File
                            </button>
                            <button @click="quickAction('web_search')" class="px-3 py-1 bg-gray-700 hover:bg-gray-600 rounded-full text-sm transition-colors">
                                🔍 Web Search
                            </button>
                            <button @click="quickAction('create_workflow')" class="px-3 py-1 bg-gray-700 hover:bg-gray-600 rounded-full text-sm transition-colors">
                                ⚡ Create Workflow
                            </button>
                            <button @click="quickAction('call_paul')" class="px-3 py-1 bg-gray-700 hover:bg-gray-600 rounded-full text-sm transition-colors">
                                📞 Call Paul Edwards
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Voice Interface -->
                <div x-show="activeView === 'voice'" class="h-full flex flex-col items-center justify-center p-8">
                    <div class="text-center max-w-md">
                        <div class="mb-8">
                            <div class="w-32 h-32 mx-auto bg-gradient-purple rounded-full flex items-center justify-center mb-4"
                                 :class="isListening ? 'breathing' : ''">
                                <i class="fas fa-microphone text-4xl text-white"></i>
                            </div>
                            <h3 class="text-2xl font-bold text-white mb-2">Voice Control</h3>
                            <p class="text-gray-400">Speak naturally to interact with IRIS</p>
                        </div>

                        <div class="space-y-4">
                            <button @click="startVoiceSession()"
                                    :class="isListening ? 'bg-red-600 hover:bg-red-700' : 'bg-green-600 hover:bg-green-700'"
                                    class="w-full py-4 px-6 rounded-lg font-semibold transition-colors">
                                <i :class="isListening ? 'fas fa-stop' : 'fas fa-microphone'" class="mr-2"></i>
                                <span x-text="isListening ? 'Stop Listening' : 'Start Voice Session'"></span>
                            </button>

                            <div x-show="isListening" class="bg-gray-800 rounded-lg p-4">
                                <div class="flex items-center space-x-2 mb-2">
                                    <div class="w-3 h-3 bg-red-500 rounded-full pulse-dot"></div>
                                    <span class="text-sm text-gray-400">Listening...</span>
                                </div>
                                <p class="text-white" x-text="currentTranscript || 'Say something...'"></p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Agent Hub -->
                <div x-show="activeView === 'agents'" class="h-full p-6">
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                        <template x-for="agent in availableAgents" :key="agent.id">
                            <div class="bg-gray-800 rounded-lg p-6 hover:bg-gray-700 transition-colors">
                                <div class="flex items-center space-x-4 mb-4">
                                    <div class="w-12 h-12 rounded-lg flex items-center justify-center" :class="agent.color">
                                        <i :class="agent.icon" class="text-white text-xl"></i>
                                    </div>
                                    <div>
                                        <h3 class="text-lg font-semibold text-white" x-text="agent.name"></h3>
                                        <p class="text-sm text-gray-400" x-text="agent.description"></p>
                                    </div>
                                </div>
                                <div class="space-y-2">
                                    <div class="flex justify-between text-sm">
                                        <span class="text-gray-400">Status:</span>
                                        <span :class="agent.online ? 'text-green-400' : 'text-red-400'"
                                              x-text="agent.online ? 'Online' : 'Offline'"></span>
                                    </div>
                                    <div class="flex justify-between text-sm">
                                        <span class="text-gray-400">Tasks:</span>
                                        <span class="text-white" x-text="agent.taskCount"></span>
                                    </div>
                                </div>
                                <button @click="interactWithAgent(agent)"
                                        class="w-full mt-4 py-2 bg-purple-600 hover:bg-purple-700 rounded-lg transition-colors">
                                    Interact
                                </button>
                            </div>
                        </template>
                    </div>
                </div>

        <!-- Request Input -->
        <div class="mb-8">
            <div class="bg-white rounded-lg shadow-md p-6">
                <h2 class="text-xl font-semibold mb-4 text-gray-800">Natural Language Request</h2>
                <div class="flex space-x-4">
                    <div class="flex-1">
                        <textarea 
                            x-model="requestText" 
                            @keydown.ctrl.enter="submitRequest()"
                            placeholder="Tell me what you need... (e.g., 'Call Paul Edwards about his IUL quote', 'Run a security scan on example.com', 'Generate a Medicare quote for a 65-year-old')"
                            class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none"
                            rows="3"
                        ></textarea>
                        <div class="text-sm text-gray-500 mt-1">Press Ctrl+Enter to submit</div>
                    </div>
                    <button 
                        @click="submitRequest()" 
                        :disabled="!requestText.trim() || isProcessing"
                        class="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-all"
                    >
                        <i class="fas fa-paper-plane" :class="{'fa-spin': isProcessing}"></i>
                        <span x-text="isProcessing ? 'Processing...' : 'Submit'"></span>
                    </button>
                </div>
            </div>
        </div>

        <!-- Agent Status Grid -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
            <!-- Active Agents -->
            <div class="bg-white rounded-lg shadow-md p-6">
                <h2 class="text-xl font-semibold mb-4 text-gray-800">Agent Status</h2>
                <div class="space-y-3">
                    <template x-for="agent in agents" :key="agent.name">
                        <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                            <div class="flex items-center space-x-3">
                                <div class="w-3 h-3 rounded-full" :class="getStatusColor(agent.status)"></div>
                                <div>
                                    <div class="font-medium" x-text="agent.name"></div>
                                    <div class="text-sm text-gray-500" x-text="agent.description"></div>
                                </div>
                            </div>
                            <div class="text-sm text-gray-400" x-text="agent.last_activity"></div>
                        </div>
                    </template>
                </div>
            </div>

            <!-- Recent Requests -->
            <div class="bg-white rounded-lg shadow-md p-6">
                <h2 class="text-xl font-semibold mb-4 text-gray-800">Recent Requests</h2>
                <div class="space-y-3">
                    <template x-for="request in recentRequests" :key="request.id">
                        <div class="p-3 bg-gray-50 rounded-lg">
                            <div class="flex items-center justify-between mb-2">
                                <span class="text-sm font-medium" x-text="request.type"></span>
                                <span class="text-xs px-2 py-1 rounded-full" 
                                      :class="getRequestStatusClass(request.status)"
                                      x-text="request.status">
                                </span>
                            </div>
                            <div class="text-sm text-gray-600" x-text="request.text"></div>
                            <div class="text-xs text-gray-400 mt-1" x-text="request.timestamp"></div>
                        </div>
                    </template>
                </div>
            </div>
        </div>

        <!-- System Metrics -->
        <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-8">
            <div class="bg-white rounded-lg shadow-md p-4 text-center">
                <div class="text-2xl font-bold text-blue-600" x-text="metrics.activeRequests"></div>
                <div class="text-sm text-gray-500">Active Requests</div>
            </div>
            <div class="bg-white rounded-lg shadow-md p-4 text-center">
                <div class="text-2xl font-bold text-green-600" x-text="metrics.completedToday"></div>
                <div class="text-sm text-gray-500">Completed Today</div>
            </div>
            <div class="bg-white rounded-lg shadow-md p-4 text-center">
                <div class="text-2xl font-bold text-purple-600" x-text="metrics.connectedClients"></div>
                <div class="text-sm text-gray-500">Connected Clients</div>
            </div>
            <div class="bg-white rounded-lg shadow-md p-4 text-center">
                <div class="text-2xl font-bold text-orange-600" x-text="metrics.systemUptime"></div>
                <div class="text-sm text-gray-500">System Uptime</div>
            </div>
        </div>
    </div>

    <!-- Settings Modal -->
    <div x-show="showSettings" x-cloak class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
        <div class="bg-white rounded-lg p-6 w-full max-w-md mx-4">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-semibold">Settings</h3>
                <button @click="showSettings = false" class="text-gray-400 hover:text-gray-600">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="space-y-4">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">Auto-refresh interval</label>
                    <select x-model="settings.refreshInterval" class="w-full p-2 border border-gray-300 rounded-lg">
                        <option value="5000">5 seconds</option>
                        <option value="10000">10 seconds</option>
                        <option value="30000">30 seconds</option>
                        <option value="60000">1 minute</option>
                    </select>
                </div>
                <div>
                    <label class="flex items-center">
                        <input type="checkbox" x-model="settings.notifications" class="mr-2">
                        <span class="text-sm">Enable notifications</span>
                    </label>
                </div>
                <div>
                    <label class="flex items-center">
                        <input type="checkbox" x-model="settings.darkMode" class="mr-2">
                        <span class="text-sm">Dark mode</span>
                    </label>
                </div>
            </div>
            <div class="mt-6 flex justify-end space-x-3">
                <button @click="showSettings = false" class="px-4 py-2 text-gray-600 hover:text-gray-800">Cancel</button>
                <button @click="saveSettings()" class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700">Save</button>
            </div>
        </div>
    </div>

    <!-- Notification Toast -->
    <div x-show="notification.show" x-cloak 
         x-transition:enter="transition ease-out duration-300"
         x-transition:enter-start="opacity-0 transform translate-y-2"
         x-transition:enter-end="opacity-100 transform translate-y-0"
         x-transition:leave="transition ease-in duration-200"
         x-transition:leave-start="opacity-100 transform translate-y-0"
         x-transition:leave-end="opacity-0 transform translate-y-2"
         class="fixed top-4 right-4 z-50">
        <div class="bg-white rounded-lg shadow-lg p-4 border-l-4" :class="notification.type === 'success' ? 'border-green-500' : 'border-red-500'">
            <div class="flex items-center">
                <i class="fas" :class="notification.type === 'success' ? 'fa-check-circle text-green-500' : 'fa-exclamation-circle text-red-500'"></i>
                <span class="ml-2" x-text="notification.message"></span>
            </div>
        </div>
    </div>

            </div>
        </div>
    </div>

    <script>
        function irisInterface() {
            return {
                // Core State
                activeView: 'chat',
                sidebarCollapsed: false,
                systemOnline: true,
                modelsOnline: true,
                voiceEnabled: false,
                isListening: false,
                isRecording: false,
                isTyping: false,
                showSettings: false,

                // Chat State
                messages: [],
                currentMessage: '',
                currentTranscript: '',

                // File State
                uploadedFiles: [],

                // Agent State
                availableAgents: [
                    {
                        id: 'insurance',
                        name: 'Insurance Agent',
                        description: 'Policy management and claims',
                        icon: 'fas fa-shield-alt',
                        color: 'bg-blue-600',
                        online: true,
                        taskCount: 3
                    },
                    {
                        id: 'communication',
                        name: 'Communication Agent',
                        description: 'Email, SMS, and calls',
                        icon: 'fas fa-envelope',
                        color: 'bg-green-600',
                        online: true,
                        taskCount: 1
                    },
                    {
                        id: 'content',
                        name: 'Content Agent',
                        description: 'Marketing and content creation',
                        icon: 'fas fa-edit',
                        color: 'bg-purple-600',
                        online: true,
                        taskCount: 2
                    },
                    {
                        id: 'research',
                        name: 'Research Agent',
                        description: 'Web search and analysis',
                        icon: 'fas fa-search',
                        color: 'bg-yellow-600',
                        online: true,
                        taskCount: 0
                    },
                    {
                        id: 'security',
                        name: 'Security Agent',
                        description: 'Vulnerability scanning',
                        icon: 'fas fa-lock',
                        color: 'bg-red-600',
                        online: false,
                        taskCount: 0
                    },
                    {
                        id: 'workflow',
                        name: 'Workflow Agent',
                        description: 'Process automation',
                        icon: 'fas fa-cogs',
                        color: 'bg-indigo-600',
                        online: true,
                        taskCount: 5
                    }
                ],

                // Speech Recognition
                recognition: null,
                speechSynthesis: null,
                geminiLiveSocket: null,

                // Initialize
                init() {
                    this.initializeSpeech();
                    this.checkSystemStatus();
                    this.loadSampleData();
                    this.startHeartbeat();
                    // this.connectGeminiLiveWebSocket(); // Optionally connect on init
                },

                // View Management
                setActiveView(view) {
                    this.activeView = view;
                },

                getViewTitle() {
                    const titles = {
                        'chat': 'Chat Interface',
                        'voice': 'Voice Control',
                        'workflow': 'Workflow Builder',
                        'files': 'File Manager',
                        'agents': 'Agent Hub',
                        'search': 'Web Search',
                        'monitor': 'System Monitor'
                    };
                    return titles[this.activeView] || 'IRIS Dashboard';
                },

                // Chat Functions
                async sendMessage() {
                    if (!this.currentMessage.trim()) return;

                    const userMessage = {
                        id: Date.now(),
                        sender: 'user',
                        content: this.currentMessage,
                        timestamp: new Date()
                    };

                    this.messages.push(userMessage);
                    const messageText = this.currentMessage;
                    this.currentMessage = '';
                    this.isTyping = true;

                    // Scroll to bottom
                    this.$nextTick(() => {
                        const container = document.getElementById('chatContainer');
                        container.scrollTop = container.scrollHeight;
                    });

                    try {
                        // const response = await this.processMessage(messageText); // Old mock processing
                        const apiResponse = await axios.post('/api/chat/request', { message: messageText });
                        let response = "Sorry, I couldn't get a response.";
                        if (apiResponse.data && apiResponse.data.response) {
                            response = apiResponse.data.response;
                        }


                        const aiMessage = {
                            id: Date.now() + 1,
                            sender: 'iris',
                            content: response,
                            timestamp: new Date()
                        };

                        this.messages.push(aiMessage);
                    } catch (error) {
                        const errorMessage = {
                            id: Date.now() + 1,
                            sender: 'iris',
                            content: 'Sorry, I encountered an error processing your request. Please try again.',
                            timestamp: new Date()
                        };
                        this.messages.push(errorMessage);
                    } finally {
                        this.isTyping = false;
                        this.$nextTick(() => {
                            const container = document.getElementById('chatContainer');
                            container.scrollTop = container.scrollHeight;
                        });
                    }
                },

                // processMessage is now largely handled by the backend.
                // The frontend sends the message, backend processes with LangGraph/Tools,
                // and sends back a response.
                // Specific client-side handlers below might still be useful for UI interactions
                // or formatting, but core logic moves to backend.

                // async processMessage(message) {
                //     // Simulate processing delay
                //     await new Promise(resolve => setTimeout(resolve, 1000 + Math.random() * 2000));
                //     // Route message to appropriate handler
                //     const lowerMessage = message.toLowerCase();
                //     if (lowerMessage.includes('paul edwards') || lowerMessage.includes('call paul')) {
                //         return await this.handlePaulEdwardsRequest(message);
                //     } else if (lowerMessage.includes('insurance') || lowerMessage.includes('quote')) {
                //         return await this.handleInsuranceRequest(message);
                //     } else if (lowerMessage.includes('file') || lowerMessage.includes('analyze')) {
                //         return await this.handleFileRequest(message);
                //     } else if (lowerMessage.includes('search') || lowerMessage.includes('find')) {
                //         return await this.handleSearchRequest(message);
                //     } else {
                //         return await this.handleGeneralRequest(message);
                //     }
                // },

                // Message Handlers
                async handlePaulEdwardsRequest(message) {
                    // This logic should now primarily be on the backend.
                    // The frontend can still display a message indicating action.
                    // The actual API call to '/api/contact/paul' would be part of the
                    // backend's toolset invoked by LangGraph.
                    // For now, the backend /api/chat/request has a mock for this.
                    return '📞 Request to contact Paul Edwards sent to the system.';
                },

                async handleInsuranceRequest(message) {
                    // Logic moves to backend.
                    return '🏢 I\'m analyzing your insurance request. Our insurance agent can help with:\n\n• Life Insurance Quotes\n• IUL Policies\n• Medicare Plans\n• Claims Processing\n\nWould you like me to generate a specific quote or connect you with our insurance specialist?';
                },

                async handleFileRequest(message) {
                    // Logic moves to backend.
                    return '📄 I can analyze various file types including:\n\n• Documents (PDF, Word, Excel)\n• Images (JPG, PNG, GIF)\n• Audio files (MP3, WAV)\n• Video files (MP4, AVI)\n\nPlease upload a file using the paperclip icon, and I\'ll provide detailed analysis.';
                },

                async handleSearchRequest(message) {
                    return '🔍 I\'m searching for information related to your query. I can search:\n\n• Web content and articles\n• Insurance databases\n• Legal documents\n• Market data\n\nWhat specific information are you looking for?';
                },

                async handleGeneralRequest(message) {
                    // General requests are handled by the backend orchestrator.
                    return `I understand you're asking about: "${message}"\n\nI can help you with:\n• Insurance and financial services\n• Document analysis\n• Communication with clients\n• Web research\n• Workflow automation\n\nHow would you like me to assist you further?`;
                },

                // Voice Functions
                initializeSpeech() {
                    if ('webkitSpeechRecognition' in window) {
                        this.recognition = new webkitSpeechRecognition();
                        this.recognition.continuous = true;
                        this.recognition.interimResults = true;

                        this.recognition.onresult = (event) => {
                            let transcript = '';
                            for (let i = event.resultIndex; i < event.results.length; i++) {
                                transcript += event.results[i][0].transcript;
                            }
                            this.currentTranscript = transcript;
                        };

                        this.recognition.onend = () => {
                            if (this.isListening) {
                                this.recognition.start();
                            }
                        };
                    }

                    this.speechSynthesis = window.speechSynthesis;
                },

                connectGeminiLiveWebSocket() {
                    if (this.geminiLiveSocket && this.geminiLiveSocket.readyState === WebSocket.OPEN) {
                        console.log("Gemini Live WebSocket already connected.");
                        return;
                    }
                    // Ensure the port matches your uvicorn server
                    this.geminiLiveSocket = new WebSocket('ws://localhost:8080/ws/gemini_live');

                    this.geminiLiveSocket.onopen = (event) => {
                        console.log("Gemini Live WebSocket connection established.");
                        // You might want to send an initial message or auth token if required
                    };

                    this.geminiLiveSocket.onmessage = (event) => {
                        console.log("Message from Gemini Live WebSocket: ", event.data);
                        // Handle incoming messages from the agent (e.g., transcribed text, audio data)
                        // This could update this.currentTranscript or play audio
                        this.currentTranscript = event.data; // Example for text
                    };

                    this.geminiLiveSocket.onclose = (event) => {
                        console.log(`Gemini Live WebSocket connection closed: ${event.code} ${event.reason}`);
                    };
                    this.geminiLiveSocket.onerror = (error) => {
                        console.error(`Gemini Live WebSocket Error: ${error.message}`);
                    };
                },

                toggleVoice() {
                    this.voiceEnabled = !this.voiceEnabled;
                },

                startVoiceSession() {
                    if (!this.recognition) {
                        // Fallback or connect to WebSocket if WebSpeech API is not the primary path for Gemini Live
                        alert('Speech recognition not supported in this browser');
                        return;
                    }

                    if (this.isListening) {
                        this.isListening = false;
                        this.recognition.stop();
                        if (this.geminiLiveSocket && this.geminiLiveSocket.readyState === WebSocket.OPEN) {
                            // Notify backend that listening stopped, if necessary
                        }
                    } else {
                        this.isListening = true;
                        this.currentTranscript = '';
                        if (!this.geminiLiveSocket || this.geminiLiveSocket.readyState !== WebSocket.OPEN) {
                            this.connectGeminiLiveWebSocket(); // Ensure connection
                        }
                        this.recognition.start();
                    }
                },

                toggleRecording() {
                    this.isRecording = !this.isRecording;
                    if (this.isRecording) {
                        this.startVoiceSession();
                    } else {
                        if (this.currentTranscript) {
                            this.currentMessage = this.currentTranscript;
                            this.sendMessage();
                        }
                        this.isListening = false;
                        if (this.recognition) {
                            this.recognition.stop();
                        }
                    }
                },

                // File Functions
                handleFileUpload(event) {
                    const files = Array.from(event.target.files);
                    files.forEach(file => {
                        const fileObj = {
                            id: Date.now() + Math.random(),
                            name: file.name,
                            size: file.size,
                            type: file.type,
                            file: file
                        };
                        this.uploadedFiles.push(fileObj);
                    });
                },

                handleFileDrop(event) {
                    event.currentTarget.classList.remove('dragover');
                    const files = Array.from(event.dataTransfer.files);
                    files.forEach(file => {
                        const fileObj = {
                            id: Date.now() + Math.random(),
                            name: file.name,
                            size: file.size,
                            type: file.type,
                            file: file
                        };
                        this.uploadedFiles.push(fileObj);
                    });
                },

                getFileIcon(type) {
                    if (type.startsWith('image/')) return 'fas fa-image';
                    if (type.startsWith('video/')) return 'fas fa-video';
                    if (type.startsWith('audio/')) return 'fas fa-music';
                    if (type.includes('pdf')) return 'fas fa-file-pdf';
                    if (type.includes('word')) return 'fas fa-file-word';
                    if (type.includes('excel') || type.includes('spreadsheet')) return 'fas fa-file-excel';
                    return 'fas fa-file';
                },

                formatFileSize(bytes) {
                    if (bytes === 0) return '0 Bytes';
                    const k = 1024;
                    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
                    const i = Math.floor(Math.log(bytes) / Math.log(k));
                    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
                },

                async analyzeFile(file) {
                    this.currentMessage = `Analyze this file: ${file.name}`;
                    this.setActiveView('chat');
                    await this.sendMessage();
                },

                // Quick Actions
                async quickAction(action) {
                    const actions = {
                        'analyze_file': 'Please upload a file for analysis',
                        'web_search': 'What would you like me to search for?',
                        'create_workflow': 'Let\'s create a new workflow. What process would you like to automate?',
                        'call_paul': 'Initiating contact with Paul Edwards...'
                    };

                    this.currentMessage = actions[action] || 'How can I help you?';
                    this.setActiveView('chat');

                    if (action === 'call_paul') {
                        await this.sendMessage();
                    }
                },

                // Agent Functions
                async interactWithAgent(agent) {
                    this.currentMessage = `I'd like to work with the ${agent.name}`;
                    this.setActiveView('chat');
                    await this.sendMessage();
                },

                // Utility Functions
                formatMessage(content) {
                    return marked.parse(content);
                },

                formatTime(timestamp) {
                    return new Date(timestamp).toLocaleTimeString();
                },

                async checkSystemStatus() {
                    try {
                        const checks = await Promise.all([
                            fetch('/health').then(r => r.ok),
                            fetch('http://localhost:8085/health').then(r => r.ok),
                            fetch('http://localhost:8082/health').then(r => r.ok)
                        ]);
                        this.systemOnline = checks.every(check => check);
                        this.modelsOnline = checks[1];
                    } catch (error) {
                        this.systemOnline = false;
                    }
                },

                loadSampleData() {
                    // Add welcome message if no messages exist
                    if (this.messages.length === 0) {
                        this.messages.push({
                            id: 1,
                            sender: 'iris',
                            content: 'Hello! I\'m IRIS, your intelligent assistant. I\'m connected to all your MCP servers and ready to help with insurance, communication, file analysis, and more. How can I assist you today?',
                            timestamp: new Date()
                        });
                    }
                },

                startHeartbeat() {
                    setInterval(() => {
                        this.checkSystemStatus();
                    }, 30000);
                },

                closeModals() {
                    this.showSettings = false;
                }
            }
        }
    </script>
</body>
</html>
