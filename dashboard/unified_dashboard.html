<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Unified Web Automation Dashboard</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; background: #0f172a; color: #e2e8f0; }
        .container { max-width: 1400px; margin: 0 auto; padding: 20px; }
        .header { background: linear-gradient(135deg, #1e293b, #334155); padding: 30px; border-radius: 12px; margin-bottom: 30px; }
        .header h1 { font-size: 2.5rem; margin-bottom: 10px; background: linear-gradient(45deg, #3b82f6, #8b5cf6); -webkit-background-clip: text; -webkit-text-fill-color: transparent; }
        .header p { color: #94a3b8; font-size: 1.1rem; }
        .grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; margin-bottom: 30px; }
        .card { background: #1e293b; border: 1px solid #334155; border-radius: 12px; padding: 25px; transition: all 0.3s ease; }
        .card:hover { border-color: #3b82f6; transform: translateY(-2px); }
        .card h3 { color: #3b82f6; margin-bottom: 15px; font-size: 1.3rem; }
        .status-indicator { display: inline-block; width: 12px; height: 12px; border-radius: 50%; margin-right: 8px; }
        .status-online { background: #10b981; }
        .status-offline { background: #ef4444; }
        .btn { background: #3b82f6; color: white; padding: 12px 24px; border: none; border-radius: 8px; cursor: pointer; font-size: 14px; transition: all 0.3s ease; }
        .btn:hover { background: #2563eb; transform: translateY(-1px); }
        .btn-secondary { background: #6b7280; }
        .btn-secondary:hover { background: #4b5563; }
        .btn-danger { background: #ef4444; }
        .btn-danger:hover { background: #dc2626; }
        .task-list { max-height: 400px; overflow-y: auto; }
        .task-item { background: #0f172a; border: 1px solid #334155; border-radius: 8px; padding: 15px; margin-bottom: 10px; }
        .task-status { padding: 4px 12px; border-radius: 20px; font-size: 12px; font-weight: bold; }
        .status-pending { background: #374151; color: #9ca3af; }
        .status-running { background: #f59e0b; color: #fbbf24; }
        .status-completed { background: #10b981; color: #34d399; }
        .status-failed { background: #ef4444; color: #f87171; }
        .form-group { margin-bottom: 15px; }
        .form-group label { display: block; margin-bottom: 5px; color: #94a3b8; }
        .form-group input, .form-group select, .form-group textarea { width: 100%; padding: 10px; background: #0f172a; border: 1px solid #334155; border-radius: 6px; color: #e2e8f0; }
        .modal { display: none; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.8); z-index: 1000; }
        .modal-content { background: #1e293b; margin: 5% auto; padding: 30px; border-radius: 12px; width: 90%; max-width: 600px; }
        .close { float: right; font-size: 28px; cursor: pointer; color: #94a3b8; }
        .close:hover { color: #e2e8f0; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 Unified Web Automation Dashboard</h1>
            <p>Control and monitor all your web automation components from one place</p>
        </div>

        <div class="grid">
            <div class="card">
                <h3>System Status</h3>
                <div id="system-status">Loading...</div>
            </div>

            <div class="card">
                <h3>Component Health</h3>
                <div id="component-status">Loading...</div>
            </div>

            <div class="card">
                <h3>Quick Actions</h3>
                <button class="btn" onclick="openTaskModal()">Create New Task</button>
                <button class="btn btn-secondary" onclick="refreshDashboard()">Refresh</button>
                <button class="btn btn-secondary" onclick="openConfigModal()">Settings</button>
            </div>
        </div>

        <div class="card">
            <h3>Active Tasks</h3>
            <div id="tasks-container">Loading...</div>
        </div>
    </div>

    <!-- Task Creation Modal -->
    <div id="taskModal" class="modal">
        <div class="modal-content">
            <span class="close" onclick="closeTaskModal()">&times;</span>
            <h2>Create New Automation Task</h2>
            <form id="taskForm">
                <div class="form-group">
                    <label>Task Name:</label>
                    <input type="text" id="taskName" required>
                </div>
                <div class="form-group">
                    <label>Component:</label>
                    <select id="taskType" required>
                        <option value="webrover">WebRover (Crawling)</option>
                        <option value="midscene">MidScene (AI Automation)</option>
                        <option value="web_vision">Web Vision (Visual AI)</option>
                        <option value="web_ui">Web UI (Interface)</option>
                    </select>
                </div>
                <div class="form-group">
                    <label>Target URL:</label>
                    <input type="url" id="taskUrl" required>
                </div>
                <div class="form-group">
                    <label>Configuration (JSON):</label>
                    <textarea id="taskConfig" rows="5" placeholder='{"action": "crawl", "max_pages": 10}'></textarea>
                </div>
                <button type="submit" class="btn">Create Task</button>
                <button type="button" class="btn btn-secondary" onclick="closeTaskModal()">Cancel</button>
            </form>
        </div>
    </div>

    <script>
        // WebSocket connection
        const ws = new WebSocket(`ws://${window.location.host}/ws`);

        ws.onopen = function() {
            console.log('Connected to unified dashboard');
            ws.send(JSON.stringify({type: 'subscribe'}));
        };

        ws.onmessage = function(event) {
            const data = JSON.parse(event.data);
            console.log('Dashboard update:', data);

            if (data.type === 'task_created' || data.type === 'task_updated' ||
                data.type === 'task_completed' || data.type === 'task_failed') {
                loadTasks();
            }
        };

        // Load system status
        async function loadSystemStatus() {
            try {
                const response = await fetch('/api/status');
                const data = await response.json();

                document.getElementById('system-status').innerHTML = `
                    <p><strong>Status:</strong> ${data.status}</p>
                    <p><strong>Total Tasks:</strong> ${data.tasks.total}</p>
                    <p><strong>Running:</strong> ${data.tasks.running}</p>
                    <p><strong>Completed:</strong> ${data.tasks.completed}</p>
                    <p><strong>WebSockets:</strong> ${data.websockets}</p>
                `;

                const componentHtml = Object.entries(data.components).map(([name, status]) => `
                    <div style="margin: 5px 0;">
                        <span class="status-indicator ${status ? 'status-online' : 'status-offline'}"></span>
                        ${name}: ${status ? 'Online' : 'Offline'}
                    </div>
                `).join('');

                document.getElementById('component-status').innerHTML = componentHtml;

            } catch (error) {
                console.error('Error loading status:', error);
            }
        }

        // Load tasks
        async function loadTasks() {
            try {
                const response = await fetch('/api/tasks');
                const data = await response.json();

                const tasksHtml = data.tasks.map(task => `
                    <div class="task-item">
                        <div style="display: flex; justify-content: space-between; align-items: center;">
                            <div>
                                <h4>${task.name} <span class="task-status status-${task.status}">${task.status}</span></h4>
                                <p><strong>Type:</strong> ${task.type}</p>
                                <p><strong>Created:</strong> ${new Date(task.created_at).toLocaleString()}</p>
                                ${task.progress > 0 ? `<p><strong>Progress:</strong> ${task.progress}%</p>` : ''}
                            </div>
                            <div>
                                ${task.status === 'pending' ? `<button class="btn" onclick="startTask('${task.id}')">Start</button>` : ''}
                                ${task.status === 'running' ? `<button class="btn btn-secondary" onclick="stopTask('${task.id}')">Stop</button>` : ''}
                                <button class="btn btn-danger" onclick="deleteTask('${task.id}')">Delete</button>
                            </div>
                        </div>
                        ${task.error ? `<p style="color: #ef4444; margin-top: 10px;">Error: ${task.error}</p>` : ''}
                    </div>
                `).join('');

                document.getElementById('tasks-container').innerHTML = tasksHtml || '<p>No tasks found</p>';

            } catch (error) {
                console.error('Error loading tasks:', error);
            }
        }

        // Task management functions
        function openTaskModal() {
            document.getElementById('taskModal').style.display = 'block';
        }

        function closeTaskModal() {
            document.getElementById('taskModal').style.display = 'none';
        }

        document.getElementById('taskForm').onsubmit = async function(e) {
            e.preventDefault();

            const taskData = {
                name: document.getElementById('taskName').value,
                type: document.getElementById('taskType').value,
                component_data: {
                    url: document.getElementById('taskUrl').value,
                    ...JSON.parse(document.getElementById('taskConfig').value || '{}')
                }
            };

            try {
                const response = await fetch('/api/tasks', {
                    method: 'POST',
                    headers: {'Content-Type': 'application/json'},
                    body: JSON.stringify(taskData)
                });

                if (response.ok) {
                    closeTaskModal();
                    loadTasks();
                    document.getElementById('taskForm').reset();
                }
            } catch (error) {
                console.error('Error creating task:', error);
            }
        };

        async function startTask(taskId) {
            try {
                const response = await fetch(`/api/tasks/${taskId}/start`, {method: 'POST'});
                if (response.ok) loadTasks();
            } catch (error) {
                console.error('Error starting task:', error);
            }
        }

        async function stopTask(taskId) {
            try {
                const response = await fetch(`/api/tasks/${taskId}/stop`, {method: 'POST'});
                if (response.ok) loadTasks();
            } catch (error) {
                console.error('Error stopping task:', error);
            }
        }

        async function deleteTask(taskId) {
            if (confirm('Are you sure you want to delete this task?')) {
                try {
                    const response = await fetch(`/api/tasks/${taskId}`, {method: 'DELETE'});
                    if (response.ok) loadTasks();
                } catch (error) {
                    console.error('Error deleting task:', error);
                }
            }
        }

        function refreshDashboard() {
            loadSystemStatus();
            loadTasks();
        }

        // Initial load
        loadSystemStatus();
        loadTasks();

        // Auto-refresh every 10 seconds
        setInterval(refreshDashboard, 10000);
    </script>
</body>
</html>