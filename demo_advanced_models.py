#!/usr/bin/env python3
"""
Advanced Models Demo
===================

Demonstrates the advanced AI models integration with various query types.
"""

import asyncio
import logging
import time
import json
from typing import Dict, Any, Optional

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Import advanced models
try:
    from advanced_models.unified_interface import UnifiedModelInterface, ResponseStrategy, QueryType
    from advanced_models.model_manager import AdvancedModelManager, ModelType
    ADVANCED_MODELS_AVAILABLE = True
except ImportError as e:
    ADVANCED_MODELS_AVAILABLE = False
    logger.error(f"Advanced models not available: {e}")

async def demo_individual_models():
    """Demo individual model capabilities"""
    print("\\n" + "="*60)
    print("INDIVIDUAL MODEL DEMONSTRATIONS")
    print("="*60)
    
    if not ADVANCED_MODELS_AVAILABLE:
        print("Advanced models not available. Please run: python install_advanced_models.py")
        return
    
    manager = AdvancedModelManager()
    await manager.initialize()
    
    test_query = "What are the key benefits of artificial intelligence in modern business?"
    
    # Test each model individually
    models_to_test = [
        (ModelType.MANUS, "MANUS (OpenManus) - Autonomous Reasoning"),
        (ModelType.MIMO_VL, "MiMo-VL-7B - Vision-Language Model"),
        (ModelType.DETAIL_FLOW, "Detail Flow - ByteDance Flow Processing"),
        (ModelType.GIGA_AGENT, "Giga Agent - Abacus.ai Autonomous Agent"),
        (ModelType.HONEST_AI, "Honest AI - Google Research Agent")
    ]
    
    for model_type, description in models_to_test:
        print(f"\\n{'-'*40}")
        print(f"Testing: {description}")
        print(f"{'-'*40}")
        
        start_time = time.time()
        response = await manager.query_single_model(model_type, test_query)
        processing_time = time.time() - start_time
        
        print(f"Response: {response.response[:200]}...")
        print(f"Confidence: {response.confidence:.2f}")
        print(f"Processing Time: {processing_time:.2f}s")
        if response.error:
            print(f"Error: {response.error}")
    
    await manager.cleanup()

async def demo_unified_interface():
    """Demo unified interface with different strategies"""
    print("\\n" + "="*60)
    print("UNIFIED INTERFACE DEMONSTRATIONS")
    print("="*60)
    
    if not ADVANCED_MODELS_AVAILABLE:
        print("Advanced models not available. Please run: python install_advanced_models.py")
        return
    
    interface = UnifiedModelInterface()
    await interface.initialize()
    
    # Test different strategies
    test_cases = [
        {
            'query': "Explain machine learning in simple terms",
            'strategy': ResponseStrategy.BEST_SINGLE,
            'description': "Best Single Model Strategy"
        },
        {
            'query': "Analyze the pros and cons of renewable energy",
            'strategy': ResponseStrategy.CONSENSUS,
            'description': "Consensus Strategy (Multiple Models)"
        },
        {
            'query': "Create a step-by-step guide for starting a business",
            'strategy': ResponseStrategy.SPECIALIZED,
            'query_type': QueryType.FLOW_BASED,
            'description': "Specialized Strategy (Flow-Based)"
        },
        {
            'query': "Research the latest developments in quantum computing",
            'strategy': ResponseStrategy.PARALLEL_ALL,
            'query_type': QueryType.RESEARCH,
            'description': "Parallel All Strategy (Research)"
        }
    ]
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\\n{'-'*50}")
        print(f"Test {i}: {test_case['description']}")
        print(f"Query: {test_case['query']}")
        print(f"{'-'*50}")
        
        start_time = time.time()
        response = await interface.query(
            query=test_case['query'],
            strategy=test_case['strategy'],
            query_type=test_case.get('query_type')
        )
        processing_time = time.time() - start_time
        
        print(f"Primary Response: {response.primary_response[:300]}...")
        print(f"Confidence: {response.confidence:.2f}")
        print(f"Processing Time: {processing_time:.2f}s")
        print(f"Strategy Used: {response.strategy_used.value}")
        print(f"Models Used: {len(response.model_responses)}")
        
        # Show model breakdown
        for model_response in response.model_responses:
            status = "✓" if model_response.error is None else "✗"
            print(f"  {status} {model_response.model_type.value}: {model_response.confidence:.2f}")
    
    # Show performance metrics
    print(f"\\n{'-'*50}")
    print("PERFORMANCE METRICS")
    print(f"{'-'*50}")
    metrics = interface.get_performance_metrics()
    for strategy, data in metrics.items():
        print(f"{strategy}:")
        print(f"  Queries: {data['total_queries']}")
        print(f"  Avg Confidence: {data['average_confidence']:.2f}")
        print(f"  Success Rate: {data['success_rate']:.2f}")
    
    await interface.cleanup()

async def demo_vision_capabilities():
    """Demo vision capabilities (simulated)"""
    print("\\n" + "="*60)
    print("VISION CAPABILITIES DEMONSTRATION")
    print("="*60)
    
    if not ADVANCED_MODELS_AVAILABLE:
        print("Advanced models not available. Please run: python install_advanced_models.py")
        return
    
    interface = UnifiedModelInterface()
    await interface.initialize()
    
    # Simulate image data (in real usage, this would be actual image bytes)
    fake_image_data = b"fake_image_data_for_demo"
    
    vision_queries = [
        "Describe what you see in this image",
        "Extract any text visible in this image",
        "Count the number of objects in this image"
    ]
    
    for query in vision_queries:
        print(f"\\nVision Query: {query}")
        print("-" * 40)
        
        response = await interface.query(
            query=query,
            image_data=fake_image_data,
            strategy=ResponseStrategy.SPECIALIZED,
            query_type=QueryType.VISION
        )
        
        print(f"Response: {response.primary_response[:200]}...")
        print(f"Confidence: {response.confidence:.2f}")
        print(f"Models Used: {[r.model_type.value for r in response.model_responses]}")
    
    await interface.cleanup()

async def demo_autonomous_capabilities():
    """Demo autonomous agent capabilities"""
    print("\\n" + "="*60)
    print("AUTONOMOUS AGENT DEMONSTRATION")
    print("="*60)
    
    if not ADVANCED_MODELS_AVAILABLE:
        print("Advanced models not available. Please run: python install_advanced_models.py")
        return
    
    interface = UnifiedModelInterface()
    await interface.initialize()
    
    autonomous_tasks = [
        "Independently research and analyze market trends in electric vehicles",
        "Autonomously create a comprehensive business plan for a tech startup",
        "Self-directed analysis of climate change impacts on agriculture"
    ]
    
    for task in autonomous_tasks:
        print(f"\\nAutonomous Task: {task}")
        print("-" * 50)
        
        response = await interface.query(
            query=task,
            strategy=ResponseStrategy.SPECIALIZED,
            query_type=QueryType.AUTONOMOUS
        )
        
        print(f"Response: {response.primary_response[:250]}...")
        print(f"Confidence: {response.confidence:.2f}")
        print(f"Processing Time: {response.processing_time:.2f}s")
    
    await interface.cleanup()

def show_system_status():
    """Show system status and available models"""
    print("\\n" + "="*60)
    print("SYSTEM STATUS")
    print("="*60)
    
    print(f"Advanced Models Available: {ADVANCED_MODELS_AVAILABLE}")
    
    if ADVANCED_MODELS_AVAILABLE:
        print("\\nAvailable Models:")
        print("- MANUS (OpenManus): Autonomous reasoning and complex problem solving")
        print("- MiMo-VL-7B: Vision-language understanding with native resolution processing")
        print("- Detail Flow: ByteDance flow-based processing with step-by-step analysis")
        print("- Giga Agent: Abacus.ai fully autonomous agent with independent reasoning")
        print("- Honest AI: Google research agent with emphasis on accuracy and truthfulness")
        
        print("\\nAvailable Strategies:")
        print("- Best Single: Select the best performing model for the query")
        print("- Consensus: Aggregate responses from multiple models")
        print("- Parallel All: Query all models simultaneously")
        print("- Sequential: Query models in order until satisfactory result")
        print("- Specialized: Route to the most appropriate model for the query type")
        
        print("\\nSupported Query Types:")
        print("- General: Standard text queries")
        print("- Vision: Image analysis and visual question answering")
        print("- Research: In-depth analysis and investigation")
        print("- Autonomous: Self-directed task execution")
        print("- Flow-based: Step-by-step process execution")
    else:
        print("\\nTo install advanced models, run:")
        print("python install_advanced_models.py")

async def main():
    """Main demo function"""
    print("ADVANCED AI MODELS DEMONSTRATION")
    print("="*60)
    print("This demo showcases the integration of multiple advanced AI models:")
    print("- MANUS/OpenManus (Autonomous Reasoning)")
    print("- MiMo-VL-7B (Vision-Language Model)")
    print("- Detail Flow (ByteDance Flow Processing)")
    print("- Giga Agent (Abacus.ai Autonomous Agent)")
    print("- Honest AI Agent (Google Research Agent)")
    
    # Show system status
    show_system_status()
    
    if ADVANCED_MODELS_AVAILABLE:
        try:
            # Run demonstrations
            await demo_individual_models()
            await demo_unified_interface()
            await demo_vision_capabilities()
            await demo_autonomous_capabilities()
            
            print("\\n" + "="*60)
            print("DEMONSTRATION COMPLETED")
            print("="*60)
            print("All advanced models are working and integrated successfully!")
            print("\\nNext steps:")
            print("1. Set up API keys for enhanced functionality:")
            print("   - GOOGLE_API_KEY (for Honest AI Agent)")
            print("   - ABACUS_API_KEY (for Giga Agent)")
            print("   - OPENAI_API_KEY (for MANUS)")
            print("\\n2. Use the enhanced interface in your applications:")
            print("   from advanced_models.unified_interface import UnifiedModelInterface")
            print("\\n3. Integrate with your existing agent system:")
            print("   from enhanced_agent_interface import EnhancedAgentInterface")
            
        except Exception as e:
            logger.error(f"Demo failed: {e}")
            print(f"\\nDemo encountered an error: {e}")
            print("Please check the installation and try again.")
    else:
        print("\\nPlease install the advanced models first:")
        print("python install_advanced_models.py")

if __name__ == "__main__":
    asyncio.run(main())
