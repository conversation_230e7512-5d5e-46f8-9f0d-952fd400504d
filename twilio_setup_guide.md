# Twilio Setup Guide for Flo Faction Insurance

This guide will help you properly set up <PERSON><PERSON><PERSON> to send calls, voicemails, and text messages to clients like <PERSON>.

## Step 1: Upgrade Your Twilio Account

To remove trial restrictions and enable full functionality:

1. Log in to your Twilio account at https://www.twilio.com/console
2. Click on "Upgrade" in the left sidebar
3. Enter your payment information
4. Complete the upgrade process
5. Verify your identity if prompted

## Step 2: Purchase a Dedicated Phone Number

To send communications from a consistent number:

1. In the Twilio console, go to "Phone Numbers" > "Manage" > "Buy a Number"
2. Search for a number in your desired area code
3. Make sure the number has Voice and SMS capabilities
4. Purchase the number
5. Note: This will be your official Flo Faction business number

## Step 3: Configure Your Twilio Number

Set up your new number with proper settings:

1. Go to "Phone Numbers" > "Manage" > "Active Numbers"
2. Click on your new number
3. Under "Voice & Fax":
   - Set "A CALL COMES IN" to "TwiML Bin" and create a new bin with:
     ```xml
     <Response>
         <Say>Thank you for calling Flo Faction Insurance. Please leave a message after the tone.</Say>
         <Record maxLength="60" playBeep="true" />
     </Response>
     ```
   - Set "CALL STATUS CHANGES" to your webhook URL (if you have one)
4. Under "Messaging":
   - Set "A MESSAGE COMES IN" to "TwiML Bin" and create a new bin with:
     ```xml
     <Response>
         <Message>Thank you for contacting Flo Faction Insurance. An agent will respond to your message shortly.</Message>
     </Response>
     ```

## Step 4: Set Up Caller ID

To ensure your calls look professional:

1. Go to "Settings" > "General"
2. Under "Caller ID", add your business name
3. This will display as "Flo Faction Ins" on caller ID systems that support it

## Step 5: Update Your Twilio Credentials in the Script

Open the `send_paul_edwards_communications_fixed.py` script and update:

```python
# Twilio credentials
TWILIO_ACCOUNT_SID = "AC187c871afa232bbbc978caf33f3e25d9"
TWILIO_AUTH_TOKEN = "CqpVewwter1BEMdFIFHrN2XmUyt22wBP"
TWILIO_PHONE_NUMBER = "+1YourNewTwilioNumber"  # Your purchased Twilio number
```

## Step 6: Set Up Eleven Labs for Voice Synthesis

For professional voice communications:

1. Log in to your Eleven Labs account at https://elevenlabs.io/
2. Go to "Voice Library"
3. Choose a professional-sounding voice for your agency
4. Note the Voice ID
5. Update your script with:
   ```python
   ELEVEN_LABS_API_KEY = "***************************************************"
   ELEVEN_LABS_VOICE_ID = "your_chosen_voice_id"  # Replace with your selected voice
   ```

## Step 7: Create a Webhook Server for Audio Files

To use Eleven Labs audio with Twilio:

1. Set up a simple web server (using services like Heroku, AWS, or Vercel)
2. Create an endpoint that serves your audio files
3. Update your script to upload audio files to this server
4. Use the public URL in your Twilio calls

Example server code (Node.js/Express):
```javascript
const express = require('express');
const app = express();
const port = process.env.PORT || 3000;

app.use('/audio', express.static('audio_files'));

app.listen(port, () => {
  console.log(`Server running on port ${port}`);
});
```

## Step 8: Set Up SMS Templates

Create reusable SMS templates:

1. In the Twilio console, go to "Messaging" > "Services"
2. Create a new Messaging Service
3. Add your phone number to this service
4. Go to "Content" > "Templates"
5. Create templates for different scenarios (introduction, follow-up, appointment reminder)

## Step 9: Set Up Call Recording

To record calls for quality assurance:

1. In your TwiML for voice calls, add:
   ```xml
   <Record action="/handle-recording" method="POST" />
   ```
2. Set up a webhook endpoint to receive and store recordings
3. Ensure you have proper consent disclosures in your call scripts

## Step 10: Integrate with Wix Website

To connect Twilio to your Flo Faction website:

1. In your Wix dashboard, go to "Settings" > "API Connections"
2. Add a custom integration
3. Create Velo (Wix Code) functions to call the Twilio API
4. Connect these functions to your website forms

Example Wix Velo code:
```javascript
import {fetch} from 'wix-fetch';

export function sendSMS(to, message) {
  const accountSid = 'AC187c871afa232bbbc978caf33f3e25d9';
  const authToken = 'CqpVewwter1BEMdFIFHrN2XmUyt22wBP';
  const from = '+1YourTwilioNumber';
  
  const url = `https://api.twilio.com/2010-04-01/Accounts/${accountSid}/Messages.json`;
  
  const options = {
    method: 'post',
    headers: {
      'Authorization': 'Basic ' + Buffer.from(`${accountSid}:${authToken}`).toString('base64'),
      'Content-Type': 'application/x-www-form-urlencoded'
    },
    body: `From=${from}&To=${to}&Body=${message}`
  };
  
  return fetch(url, options)
    .then(response => response.json());
}
```

## Step 11: Set Up Automated Workflows

Create automated communication workflows:

1. Use Twilio Studio to create visual workflows
2. Go to "Studio" > "Flows" in the Twilio console
3. Create a new flow for client onboarding
4. Add triggers, SMS messages, calls, and conditional logic
5. Connect this flow to your Wix website forms

## Step 12: Test Your Communications

Before sending to real clients:

1. Send test messages to your own phone
2. Make test calls to verify voice quality
3. Check that all automated responses work correctly
4. Verify that recordings and logs are being stored properly

## Step 13: Monitor and Analyze

Set up monitoring for your communications:

1. In the Twilio console, go to "Monitor" > "Logs"
2. Set up alerts for failed messages or calls
3. Review analytics to optimize your communication strategy
4. Track costs and usage to stay within budget

## Step 14: Compliance Considerations

Ensure legal compliance:

1. Include opt-out instructions in all SMS messages ("Reply STOP to unsubscribe")
2. Ensure call recordings have proper consent
3. Maintain records of communication consent
4. Follow insurance industry regulations for client communications

## Next Steps

After setting up Twilio:

1. Create a communication calendar for regular client outreach
2. Develop a system for tracking client responses
3. Train your team on using the Twilio system
4. Regularly update your scripts and templates based on client feedback

For any questions or assistance, contact your IT support team or Twilio support.
