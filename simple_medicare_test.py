import asyncio
import logging
from datetime import datetime
from insurance_carriers import CarrierManager
from client_template import ClientManager
from secure_credentials import SecureCredentialsManager

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def test_medicare_quotes():
    """Test Medicare quote generation with working carriers"""
    
    try:
        # Initialize managers
        carrier_manager = CarrierManager()
        client_manager = ClientManager()
        
        # Test client data for Medicare
        test_client = {
            "name": "John Medicare Test",
            "dob": "1955-06-15",
            "height": "5'10\"",
            "weight": "175",
            "phone": "************",
            "email": "<EMAIL>",
            "address": "123 Senior Way, Port St. Lucie, FL 34952",
            "ssn": "***********",
            "drivers_license": "FL*********",
            
            "medications": [
                {
                    "drug_name": "Metformin",
                    "dosage": "500mg",
                    "frequency": "twice daily"
                }
            ],
            
            "tobacco_use": False,
            "marijuana_use": False,
            
            "bank_info": {
                "bank_name": "Wells Fargo",
                "routing_number": "*********",
                "account_number": "*********"
            },
            
            "beneficiaries": [
                {
                    "name": "Mary Test",
                    "dob": "1957-08-20",
                    "relationship": "spouse"
                }
            ],
            
            "budget_range": "$200-$250",
            "family_health_history": "No significant issues",
            "reason_notes": "Looking for Medicare Advantage plan",
            "start_date": datetime.now().strftime("%Y-%m-%d")
        }
        
        # Create client record
        logger.info("Creating client record...")
        client = client_manager.create_client(test_client)
        
        # Test working carriers
        working_carriers = ["mutual_of_omaha", "americo", "aetna"]
        results = {}
        
        for carrier in working_carriers:
            logger.info(f"\nTesting {carrier.upper()} quote generation...")
            
            try:
                quotes = await carrier_manager.get_quotes(client, "sandra")
                if carrier in quotes:
                    quote = quotes[carrier]
                    results[carrier] = {
                        "status": "success",
                        "premium": quote.get('premium'),
                        "coverage": quote.get('coverage'),
                        "details": quote.get('details', {})
                    }
                    logger.info(f"Successfully received quote from {carrier.upper()}")
                    logger.info(f"Premium: ${quote.get('premium')}")
                    logger.info(f"Coverage: ${quote.get('coverage')}")
                else:
                    results[carrier] = {
                        "status": "failed",
                        "error": "No quote received"
                    }
                    logger.error(f"No quote received from {carrier.upper()}")
            except Exception as e:
                results[carrier] = {
                    "status": "error",
                    "error": str(e)
                }
                logger.error(f"Error getting quote from {carrier.upper()}: {str(e)}")
        
        # Print summary
        logger.info("\nQuote Summary:")
        logger.info("=============")
        
        for carrier, result in results.items():
            logger.info(f"\n{carrier.upper()}:")
            if result['status'] == 'success':
                logger.info(f"Premium: ${result['premium']}")
                logger.info(f"Coverage: ${result['coverage']}")
                if result['details']:
                    logger.info("Additional Benefits:")
                    for benefit, value in result['details'].items():
                        logger.info(f"  - {benefit}: {value}")
            else:
                logger.info(f"Status: {result['status']}")
                if 'error' in result:
                    logger.info(f"Error: {result['error']}")
                    
        return results
        
    except Exception as e:
        logger.error(f"Test execution failed: {str(e)}")
        return None

async def main():
    """Run Medicare quote tests"""
    logger.info("=== Starting Medicare Quote Tests ===\n")
    
    results = await test_medicare_quotes()
    
    if results:
        success_count = sum(1 for r in results.values() if r['status'] == 'success')
        logger.info(f"\nTest complete: {success_count} of {len(results)} carriers successful")
    else:
        logger.error("Test failed!")

if __name__ == "__main__":
    asyncio.run(main())