<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
    <key>Label</key>
    <string>com.flofaction.mcpservers</string>
    <key>ProgramArguments</key>
    <array>
        <string>/bin/bash</string>
        <string>-c</string>
        <string>cd /Users/<USER>/AIAgentProjects/PaulEdwardsAI && python3 start_mcp_servers.py</string>
    </array>
    <key>RunAtLoad</key>
    <true/>
    <key>KeepAlive</key>
    <true/>
    <key>StandardErrorPath</key>
    <string>/Users/<USER>/AIAgentProjects/PaulEdwardsAI/mcp_servers_error.log</string>
    <key>StandardOutPath</key>
    <string>/Users/<USER>/AIAgentProjects/PaulEdwardsAI/mcp_servers_output.log</string>
    <key>WorkingDirectory</key>
    <string>/Users/<USER>/AIAgentProjects/PaulEdwardsAI</string>
    <key>EnvironmentVariables</key>
    <dict>
        <key>PATH</key>
        <string>/usr/local/bin:/usr/bin:/bin:/usr/sbin:/sbin:/opt/homebrew/bin</string>
    </dict>
</dict>
</plist>
