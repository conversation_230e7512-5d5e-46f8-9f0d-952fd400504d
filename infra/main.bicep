targetScope = 'resourceGroup'

param environmentName string
param location string

var resourceToken = uniqueString(subscription().id, resourceGroup().id, environmentName)

output RESOURCE_GROUP_ID string = resourceGroup().id
output AZURE_CONTAINER_REGISTRY_ENDPOINT string = containerRegistry.properties.loginServer

resource containerRegistry 'Microsoft.ContainerRegistry/registries@2023-01-01' existing = {
  name: '${environmentName}-acr'
}