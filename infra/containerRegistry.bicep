resource containerRegistry 'Microsoft.ContainerRegistry/registries@2023-01-01' = {
  name: '${environmentName}-acr'
  location: location
  sku: 'Basic'
  properties: {
    adminUserEnabled: false
  }
}

resource acrRoleAssignment 'Microsoft.Authorization/roleAssignments@2020-04-01-preview' = {
  name: guid(containerRegistry.id, 'AcrPull')
  scope: containerRegistry
  properties: {
    roleDefinitionId: subscriptionResourceId('Microsoft.Authorization/roleDefinitions', '7f951dda-4ed3-4680-a7ca-43fe172d538d')
    principalId: userAssignedIdentityId
    principalType: 'ServicePrincipal'
  }
}