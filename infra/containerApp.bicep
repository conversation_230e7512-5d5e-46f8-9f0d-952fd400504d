resource containerApp 'Microsoft.App/containerApps@2023-03-01' = {
  name: '${environmentName}-containerapp'
  location: location
  properties: {
    configuration: {
      ingress: {
        external: true
        targetPort: 8080
        corsPolicy: {
          allowedOrigins: ['*']
          allowedMethods: ['GET', 'POST', 'PUT', 'DELETE']
          allowedHeaders: ['*']
        }
      }
      registries: [
        {
          server: containerRegistry.properties.loginServer
          identity: 'userAssigned'
        }
      ]
      secrets: [
        {
          name: 'GOOGLE_APPLICATION_CREDENTIALS'
          value: '/path/to/actual/credentials.json' // Replace with the actual path to your credentials file
        }
        {
          name: 'DATABASE_URL'
          value: 'postgresql://username:password@hostname:port/database' // Replace with your actual database URL
        }
        {
          name: 'API_KEYS'
          value: 'your-actual-api-keys' // Replace with your actual API keys
        }
      ]
    }
    template: {
      containers: [
        {
          image: 'mcr.microsoft.com/azuredocs/containerapps-helloworld:latest' // Updated to use the required base image
          name: 'agentic-ai-system'
          resources: {
            cpu: 0.5
            memory: '1Gi'
          }
        }
      ]
    }
  }
  identity: {
    type: 'UserAssigned'
    userAssignedIdentities: {
      '${userAssignedIdentityId}': {}
    }
  }
  tags: {
    'azd-service-name': 'agentic-ai-system'
  }
}