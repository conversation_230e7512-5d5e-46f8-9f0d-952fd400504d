"""
Open Email Client to Send Email to <PERSON>

This script opens the default email client with a pre-populated email to <PERSON>.
"""

import webbrowser
import urllib.parse

# <PERSON> contact information
PAUL_EMAIL = "<EMAIL>"
PAUL_NAME = "<PERSON>"
PAUL_PHONE = "+17722089646"

def open_email_client():
    """Open the default email client with a pre-populated email to <PERSON>"""
    print("=" * 80)
    print("OPENING EMAIL CLIENT TO SEND EMAIL TO PAUL EDWARDS")
    print("=" * 80)
    
    # Create email content
    subject = "Creating Tax-Free Retirement Income Without Market Risk"
    body = f"""
Dear {PAUL_NAME},

I hope this email finds you well. My name is <PERSON> with Flo Faction Insurance, and I specialize in helping people like you create tax-free retirement income without the risk of the stock market.

Based on the information I have, I've prepared a personalized analysis that shows how you could potentially:

1. Generate approximately $5,000 per month in tax-free retirement income
2. Build cash value that grows without the risk of market losses
3. Provide a death benefit of around $720,000 to protect your family
4. Create a financial strategy that offers both protection and growth potential

Many of my clients are concerned about:
- Market volatility affecting their retirement savings
- Taxes reducing their retirement income
- Having enough money to maintain their lifestyle in retirement
- Protecting their family financially

The strategy I'd like to share with you addresses all these concerns through an Indexed Universal Life policy specifically designed for your situation.

Would you be interested in a brief 30-minute conversation to review this analysis? If so, please call me or reply to this email with a good time to connect.

You can also learn more about our services by visiting our website at https://www.flofaction.com/insurance.

Thank you for your time, and I look forward to speaking with you soon.

Best regards,
<PERSON> <PERSON>
Flo Faction Insurance
https://www.flofaction.com/insurance
    """
    
    # URL encode the subject and body
    subject_encoded = urllib.parse.quote(subject)
    body_encoded = urllib.parse.quote(body)
    
    # Create mailto URL
    mailto_url = f"mailto:{PAUL_EMAIL}?subject={subject_encoded}&body={body_encoded}"
    
    try:
        # Open the default email client
        webbrowser.open(mailto_url)
        print(f"Default email client opened with message to {PAUL_EMAIL}")
        print("Please complete the email sending process in your email client.")
        return True
    except Exception as e:
        print(f"Error opening default email client: {str(e)}")
        return False

if __name__ == "__main__":
    print("This script will open your default email client with a pre-populated email to Paul Edwards.")
    print(f"Recipient: {PAUL_EMAIL}")
    
    proceed = input("Do you want to proceed? (yes/no): ")
    
    if proceed.lower() == "yes":
        open_email_client()
    else:
        print("Operation cancelled.")
