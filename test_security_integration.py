#!/usr/bin/env python3
"""
Security Integration Test

This script tests the integration between all security components:
- Unrestricted security tools
- Advanced credential recovery
- ProtonVPN integration
- Agent system

It demonstrates how the components work together and verifies that
all agents have unrestricted access to the security tools.
"""

import os
import sys
import logging
import json
from typing import Dict, Any

# Configure logging
logging.basicConfig(level=logging.INFO,
                    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Try to import our components - note that some might not be available
try:
    from unrestricted_security_tools import security_tools
    SECURITY_TOOLS_AVAILABLE = True
except ImportError:
    SECURITY_TOOLS_AVAILABLE = False
    logger.warning("Unrestricted security tools not available")

try:
    from advanced_credential_recovery import AdvancedCredentialRecovery
    CREDENTIAL_RECOVERY_AVAILABLE = True
except ImportError:
    CREDENTIAL_RECOVERY_AVAILABLE = False
    logger.warning("Advanced credential recovery not available")

try:
    from protonvpn_setup import ProtonVPNManager
    VPN_AVAILABLE = True
except ImportError:
    VPN_AVAILABLE = False
    logger.warning("ProtonVPN manager not available")

try:
    from agent_security_integration import AgentSecurityIntegration
    INTEGRATION_AVAILABLE = True
except ImportError:
    INTEGRATION_AVAILABLE = False
    logger.warning("Agent security integration not available")


def test_security_tools():
    """Test security tools functionality"""
    if not SECURITY_TOOLS_AVAILABLE:
        logger.error("Cannot test security tools: Module not available")
        return False
        
    logger.info("Testing security tools...")
    
    # Test tool detection
    installed_tools = security_tools.get_installed_tools()
    logger.info(f"Detected {len(installed_tools)} security tools")
    for tool in installed_tools:
        logger.info(f"- {tool}")
        
    # Test command execution (simple, harmless commands)
    if "nmap" in installed_tools:
        logger.info("Testing Nmap with simple scan...")
        result = security_tools.scan_network("127.0.0.1", "basic", "22,80,443")
        logger.info(f"Nmap scan result: {result['success']}")
        
    # Test command execution directly
    try:
        result = security_tools.execute_raw_command("echo", "Security tools test successful")
        if result.returncode == 0:
            logger.info(f"Command execution test: {result.stdout.strip()}")
        else:
            logger.error(f"Command execution test failed: {result.stderr}")
    except Exception as e:
        logger.error(f"Error executing test command: {e}")
        
    return True


def test_credential_recovery():
    """Test credential recovery functionality"""
    if not CREDENTIAL_RECOVERY_AVAILABLE:
        logger.error("Cannot test credential recovery: Module not available")
        return False
        
    logger.info("Testing credential recovery...")
    
    # Initialize credential recovery
    recovery = AdvancedCredentialRecovery()
    
    # Generate password variations
    logger.info("Testing password variation generation...")
    variations = recovery.generate_password_variations(["admin", "test"], special_chars=True, numbers=True)
    logger.info(f"Generated {len(variations)} password variations")
    logger.info(f"Sample variations: {variations[:5]}")
    
    return True


def test_vpn_integration():
    """Test VPN integration"""
    if not VPN_AVAILABLE:
        logger.error("Cannot test VPN integration: Module not available")
        return False
        
    logger.info("Testing VPN integration...")
    
    # Initialize VPN manager
    vpn = ProtonVPNManager()
    
    # Check if VPN is installed
    if vpn.check_installation():
        logger.info("ProtonVPN is installed")
        
        # Verify connection
        connection = vpn.verify_connection()
        logger.info(f"VPN connection check: {connection}")
    else:
        logger.info("ProtonVPN is not installed (run setup_and_run.sh --vpn to install)")
        
    return True


def test_agent_integration():
    """Test agent integration"""
    if not INTEGRATION_AVAILABLE:
        logger.error("Cannot test agent integration: Module not available")
        return False
        
    logger.info("Testing agent integration...")
    
    # Initialize integration
    integration = AgentSecurityIntegration()
    
    # Check component availability
    availability = integration.check_component_availability()
    for component, available in availability.items():
        logger.info(f"{component}: {'Available' if available else 'Not available'}")
        
    # Create methods dictionary
    methods = integration.create_agent_methods_dictionary()
    logger.info(f"Generated {len(methods)} agent methods")
    
    # List available methods
    for method_name in methods.keys():
        logger.info(f"- {method_name}")
        
    return True


def run_all_tests():
    """Run all tests"""
    logger.info("Running all security integration tests...")
    
    results = {
        "security_tools": test_security_tools(),
        "credential_recovery": test_credential_recovery(),
        "vpn_integration": test_vpn_integration(),
        "agent_integration": test_agent_integration()
    }
    
    # Display summary
    logger.info("\nTest Results Summary:")
    for test, result in results.items():
        logger.info(f"- {test}: {'PASSED' if result else 'FAILED'}")
        
    # Overall result
    all_passed = all(results.values())
    logger.info(f"\nOverall Result: {'ALL TESTS PASSED' if all_passed else 'SOME TESTS FAILED'}")
    
    return all_passed


if __name__ == "__main__":
    try:
        success = run_all_tests()
        sys.exit(0 if success else 1)
    except Exception as e:
        logger.error(f"Error running tests: {e}")
        sys.exit(1)