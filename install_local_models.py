#!/usr/bin/env python3
"""
Enhanced Local Model Installation Script for LLM Models

This script handles downloading and configuring local versions of:
- Qwen models (Qwen3-235B-A22B, Qwen3-30B, Qwen3-8B, etc.)
- Microsoft Phi models (Phi-4-reasoning, Phi-4-reasoning-plus, etc.)
- DeepSeek Prover models
- NVIDIA Parakeet models
- Text-to-Speech and Text-to-Image models
- Other specialized models

The script configures models for use with MCP servers and the agent system.
"""

import os
import sys
import shutil
import requests
import hashlib
from pathlib import Path
import json
import subprocess
import importlib.util
import argparse
import logging
import time
from typing import Dict, List, Optional, Tuple, Any

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s [%(levelname)s] %(name)s: %(message)s",
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler("model_installation.log")
    ]
)
logger = logging.getLogger("model-installer")

# Configuration
MODELS_DIR = Path("/Volumes/G Drive/Models")
CONFIG_DIR = Path.home() / ".config" / "llm_models"
MCP_CONFIG_FILE = Path("mcp_config.json")
MCP_REGISTRY_FILE = Path("mcp_registry.json")

# NVIDIA tools directory
NVIDIA_TOOLS_DIR = Path("/Volumes/G Drive/nvidia-tools")

# Define models
MODEL_URLS = {
    # NVIDIA models
    "nvidia-parakeet-tdt": {
        "url": None,  # Using HF Hub instead
        "size": "0.6GB",
        "type": "huggingface",
        "repo_id": "nvidia/parakeet-tdt-0.6b-v2",
        "task": "speech-recognition",
        "priority": "medium",
        "description": "NVIDIA's speech recognition model"
    },

    # DeepSeek Prover models
    "deepseek-prover-v2-7b": {
        "url": None,
        "size": "7GB",
        "type": "huggingface",
        "repo_id": "deepseek-ai/DeepSeek-Prover-V2-7B",
        "task": "reasoning",
        "priority": "high",
        "description": "Specialized reasoning model for mathematical proofs"
    },
    "deepseek-prover-v2-671b": {
        "url": None,
        "size": "671GB",
        "type": "huggingface",
        "repo_id": "deepseek-ai/DeepSeek-Prover-V2-671B",
        "task": "reasoning",
        "priority": "low",  # Very large model, low priority
        "description": "Large-scale reasoning model for complex mathematical proofs"
    },

    # Text-to-Speech models
    "nari-dia-1-6b": {
        "url": None,
        "size": "1.6GB",
        "type": "huggingface",
        "repo_id": "nari-labs/Dia-1.6B",
        "task": "text-to-speech",
        "priority": "high",
        "description": "Advanced text-to-speech model"
    },

    # JetBrains models
    "jetbrains-mellum-4b": {
        "url": None,
        "size": "4GB",
        "type": "huggingface",
        "repo_id": "JetBrains/Mellum-4b-base",
        "task": "code-generation",
        "priority": "medium",
        "description": "JetBrains model specialized for code generation"
    },

    # Microsoft Phi models
    "phi-4-reasoning": {
        "url": None,
        "size": "4GB",
        "type": "huggingface",
        "repo_id": "microsoft/Phi-4-reasoning",
        "task": "reasoning",
        "priority": "high",
        "description": "Microsoft's reasoning-focused Phi-4 model"
    },
    "phi-4-reasoning-plus": {
        "url": None,
        "size": "4.5GB",
        "type": "huggingface",
        "repo_id": "microsoft/Phi-4-reasoning-plus",
        "task": "reasoning",
        "priority": "high",
        "description": "Enhanced version of Microsoft's reasoning-focused Phi-4 model"
    },
    "phi-4-mini-reasoning": {
        "url": None,
        "size": "2GB",
        "type": "huggingface",
        "repo_id": "microsoft/Phi-4-mini-reasoning",
        "task": "reasoning",
        "priority": "high",
        "description": "Lightweight version of Microsoft's reasoning-focused Phi-4 model"
    },
    "microsoft-bitnet": {
        "url": None,
        "size": "2GB",
        "type": "huggingface",
        "repo_id": "microsoft/bitnet-b1.58-2B-4T",
        "task": "text-generation",
        "priority": "medium",
        "description": "Microsoft's efficient 1-bit neural network model"
    },

    # Qwen models
    "qwen3-235b": {
        "url": None,
        "size": "235GB",
        "type": "huggingface",
        "repo_id": "Qwen/Qwen3-235B-A22B",
        "task": "text-generation",
        "priority": "low",  # Very large model, low priority
        "description": "Alibaba's largest general-purpose language model"
    },
    "qwen3-30b": {
        "url": None,
        "size": "30GB",
        "type": "huggingface",
        "repo_id": "Qwen/Qwen3-30B-A3B",
        "task": "text-generation",
        "priority": "medium",
        "description": "Alibaba's mid-sized general-purpose language model"
    },
    "qwen3-8b": {
        "url": None,
        "size": "8GB",
        "type": "huggingface",
        "repo_id": "Qwen/Qwen3-8B",
        "task": "text-generation",
        "priority": "high",
        "description": "Alibaba's compact general-purpose language model"
    },
    "qwen3-0-6b": {
        "url": None,
        "size": "0.6GB",
        "type": "huggingface",
        "repo_id": "Qwen/Qwen3-0.6B",
        "task": "text-generation",
        "priority": "high",
        "description": "Alibaba's smallest general-purpose language model"
    },
    "qwen2-omni-3b": {
        "url": None,
        "size": "3GB",
        "type": "huggingface",
        "repo_id": "Qwen/Qwen2.5-Omni-3B",
        "task": "multimodal",
        "priority": "high",
        "description": "Alibaba's multimodal model for text, image, and more"
    },
    "qwen-unsloth-30b": {
        "url": None,
        "size": "15GB",  # GGUF format is smaller
        "type": "huggingface",
        "repo_id": "unsloth/Qwen3-30B-A3B-GGUF",
        "task": "text-generation",
        "priority": "high",
        "format": "gguf",
        "description": "Optimized GGUF version of Qwen3-30B for efficient inference"
    },

    # Xiaomi models
    "xiaomi-mimo-7b-base": {
        "url": None,
        "size": "7GB",
        "type": "huggingface",
        "repo_id": "XiaomiMiMo/MiMo-7B-Base",
        "task": "text-generation",
        "priority": "medium",
        "description": "Xiaomi's base language model"
    },
    "xiaomi-mimo-7b-rl": {
        "url": None,
        "size": "7GB",
        "type": "huggingface",
        "repo_id": "XiaomiMiMo/MiMo-7B-RL",
        "task": "text-generation",
        "priority": "medium",
        "description": "Xiaomi's reinforcement learning optimized language model"
    },

    # IBM Granite model
    "ibm-granite-4-tiny": {
        "url": None,
        "size": "4GB",
        "type": "huggingface",
        "repo_id": "ibm-granite/granite-4.0-tiny-preview",
        "task": "text-generation",
        "priority": "medium",
        "description": "IBM's lightweight Granite model"
    },

    # Specialized models
    "foundation-sec-8b": {
        "url": None,
        "size": "8GB",
        "type": "huggingface",
        "repo_id": "fdtn-ai/Foundation-Sec-8B",
        "task": "security",
        "priority": "medium",
        "description": "Security-focused language model"
    },
    "lighton-gte-v1": {
        "url": None,
        "size": "0.5GB",
        "type": "huggingface",
        "repo_id": "lightonai/GTE-ModernColBERT-v1",
        "task": "embeddings",
        "priority": "high",
        "description": "Advanced sentence similarity and embedding model"
    },
    "cognition-kevin-32b": {
        "url": None,
        "size": "32GB",
        "type": "huggingface",
        "repo_id": "cognition-ai/Kevin-32B",
        "task": "text-generation",
        "priority": "low",
        "description": "Cognition AI's Kevin large language model"
    },
    
    # Multimodal models
    "lmms-aero-audio": {
        "url": None,
        "size": "1GB",
        "type": "huggingface",
        "repo_id": "lmms-lab/Aero-1-Audio",
        "task": "audio-understanding",
        "priority": "medium",
        "description": "Advanced audio understanding model"
    },
    "black-forest-flux": {
        "url": None,
        "size": "4GB",
        "type": "huggingface",
        "repo_id": "black-forest-labs/FLUX.1-dev",
        "task": "text-to-image",
        "priority": "medium",
        "description": "Advanced text-to-image model"
    },
    "freepik-f-lite": {
        "url": None,
        "size": "2GB",
        "type": "huggingface",
        "repo_id": "Freepik/F-Lite",
        "task": "text-to-image",
        "priority": "medium",
        "description": "Freepik's text-to-image model"
    },
    "lightricks-ltx-video": {
        "url": None,
        "size": "4GB",
        "type": "huggingface",
        "repo_id": "Lightricks/LTX-Video",
        "task": "text-to-video",
        "priority": "low",
        "description": "Text-to-video generation model"
    },
    "ace-step-v1": {
        "url": None,
        "size": "3.5GB",
        "type": "huggingface",
        "repo_id": "ACE-Step/ACE-Step-v1-3.5B",
        "task": "text-to-audio",
        "priority": "medium",
        "description": "Text-to-audio generation model"
    },
    "sanaka87-icedit": {
        "url": None,
        "size": "2GB",
        "type": "huggingface",
        "repo_id": "sanaka87/ICEdit-MoE-LoRA",
        "task": "image-to-image",
        "priority": "medium",
        "description": "Image editing model"
    },
    "tesslate-uigen": {
        "url": None,
        "size": "7GB",
        "type": "huggingface",
        "repo_id": "Tesslate/UIGEN-T2-7B-Q8_0-GGUF",
        "task": "ui-generation",
        "priority": "high",
        "format": "gguf",
        "description": "UI generation model"
    },
    "lodestones-chroma": {
        "url": None,
        "size": "2GB",
        "type": "huggingface",
        "repo_id": "lodestones/Chroma",
        "task": "text-to-image",
        "priority": "medium",
        "description": "Text-to-image model with vibrant color output"
    }
}

# NVIDIA tools to download and install
NVIDIA_TOOLS = {
    "tensorrt": {
        "url": "https://developer.nvidia.com/downloads/compute/machine-learning/tensorrt/secure/8.6.1/tars/TensorRT-*******.Linux.x86_64-gnu.cuda-12.0.tar.gz",
        "description": "NVIDIA TensorRT is an SDK for high-performance deep learning inference",
        "size": "700MB"
    },
    "nemo": {
        "url": "https://github.com/NVIDIA/NeMo",
        "description": "NVIDIA NeMo is a toolkit for conversational AI",
        "github": True,
        "size": "300MB"
    },
    "triton": {
        "url": "https://github.com/triton-inference-server/server",
        "description": "NVIDIA Triton Inference Server provides a cloud and edge inferencing solution",
        "github": True,
        "size": "250MB"
    },
    "riva": {
        "url": "https://developer.nvidia.com/nvidia-riva-asr",
        "description": "NVIDIA Riva is a GPU-accelerated SDK for building speech AI applications",
        "registration_required": True,
        "size": "500MB"
    },
    "nvcr-containers": {
        "description": "NVIDIA NGC containers for AI and HPC applications",
        "container": True,
        "size": "varies"
    },
    "cuda-python": {
        "pip": "cuda-python",
        "description": "Python bindings for CUDA Driver API",
        "size": "50MB"
    },
    "cupy": {
        "pip": "cupy-cuda12x",
        "description": "NumPy-like API accelerated with CUDA",
        "size": "120MB"
    }
}

def parse_args():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(description="Enhanced Local Model Installation")
    parser.add_argument("--all", action="store_true", help="Install all models")
    parser.add_argument("--small-only", action="store_true", help="Install only small models (<5GB)")
    parser.add_argument("--medium-only", action="store_true", help="Install only medium-sized models (5-15GB)")
    parser.add_argument("--nvidia-tools", action="store_true", help="Install NVIDIA tools")
    parser.add_argument("--model", action="append", help="Specific model(s) to install")
    parser.add_argument("--task", choices=[
        "text-generation", "reasoning", "code-generation", "speech-recognition",
        "text-to-speech", "text-to-image", "text-to-video", "multimodal", 
        "embeddings", "security", "ui-generation", "image-to-image", "audio-understanding"
    ], help="Install models for specific task")
    parser.add_argument("--skip-registration", action="store_true", help="Skip MCP registration")
    parser.add_argument("--force", action="store_true", help="Force reinstall even if model exists")
    parser.add_argument("--check", action="store_true", help="Check for model updates")
    parser.add_argument("--list", action="store_true", help="List available models")
    parser.add_argument("--fix-mcp", action="store_true", help="Fix MCP server configuration")
    
    return parser.parse_args()

def check_dependencies():
    """Check if required dependencies are installed."""
    missing_deps = []
    
    # Check for PyTorch
    try:
        import torch
        logger.info(f"PyTorch {torch.__version__} found")
        
        # Check for GPU support
        if torch.cuda.is_available():
            logger.info(f"CUDA is available: {torch.cuda.get_device_name(0)}")
        elif torch.backends.mps.is_available():
            logger.info("Apple Metal Performance Shaders (MPS) backend is available")
        else:
            logger.warning("No GPU acceleration found, models will run on CPU only")
            
    except ImportError:
        missing_deps.append("torch")
    
    # Check for HuggingFace Transformers
    try:
        import transformers
        logger.info(f"Transformers {transformers.__version__} found")
    except ImportError:
        missing_deps.append("transformers")
    
    # Check for HuggingFace Hub
    try:
        import huggingface_hub
        logger.info(f"Hugging Face Hub {huggingface_hub.__version__} found")
    except ImportError:
        missing_deps.append("huggingface_hub")
    
    # Check for Accelerate
    try:
        import accelerate
        logger.info(f"Accelerate {accelerate.__version__} found")
    except ImportError:
        missing_deps.append("accelerate")
        
    # Check for bitsandbytes (for quantization)
    try:
        import bitsandbytes
        logger.info(f"BitsAndBytes {bitsandbytes.__version__} found")
    except ImportError:
        missing_deps.append("bitsandbytes")
    
    # Check for optimum (for model optimization)
    try:
        import optimum
        logger.info(f"Optimum {optimum.__version__} found")
    except ImportError:
        missing_deps.append("optimum")
    
    # Check for other useful libraries
    other_deps = [
        "aiohttp", "safetensors", "onnx", "onnxruntime", 
        "protobuf", "sentencepiece", "tokenizers"
    ]
    
    for dep in other_deps:
        try:
            module = importlib.import_module(dep)
            if hasattr(module, '__version__'):
                logger.info(f"{dep} {module.__version__} found")
            else:
                logger.info(f"{dep} found")
        except ImportError:
            missing_deps.append(dep)
    
    if missing_deps:
        logger.warning(f"Missing dependencies: {', '.join(missing_deps)}")
        install = input("Would you like to install missing dependencies? (y/n): ").lower()
        if install == 'y':
            logger.info("Installing missing dependencies...")
            # Use pip directly for better control
            cmd = [sys.executable, "-m", "pip", "install"] + missing_deps
            subprocess.check_call(cmd)
            logger.info("Dependencies installed successfully")
            return True
        else:
            logger.warning("Dependencies required for some models are missing")
            return False
    
    return True

def create_directories():
    """Create required directories if they don't exist."""
    try:
        # Create base model directories
        MODELS_DIR.mkdir(parents=True, exist_ok=True)
        CONFIG_DIR.mkdir(parents=True, exist_ok=True)
        NVIDIA_TOOLS_DIR.mkdir(parents=True, exist_ok=True)
        
        # Create type-specific model directories
        (MODELS_DIR / "huggingface").mkdir(exist_ok=True)
        (MODELS_DIR / "nvidia").mkdir(exist_ok=True)
        (MODELS_DIR / "gguf").mkdir(exist_ok=True)
        
        # Create task-specific model directories
        tasks = [
            "text-generation", "reasoning", "code-generation", 
            "text-to-speech", "speech-recognition", "text-to-image", 
            "multimodal", "embeddings"
        ]
        
        for task in tasks:
            (MODELS_DIR / task).mkdir(exist_ok=True)
            
        logger.info(f"Created model directory: {MODELS_DIR}")
        logger.info(f"Created config directory: {CONFIG_DIR}")
        logger.info(f"Created NVIDIA tools directory: {NVIDIA_TOOLS_DIR}")
        
        return True
    except Exception as e:
        logger.error(f"Error creating directories: {e}")
        return False

def download_hf_model(model_name: str, model_info: Dict) -> Tuple[bool, Optional[str]]:
    """Download a model from HuggingFace using the HF Hub."""
    try:
        from huggingface_hub import snapshot_download, scan_cache_dir
        import huggingface_hub
        
        repo_id = model_info.get("repo_id") or model_name
        logger.info(f"Downloading {model_name} from HuggingFace ({model_info['size']})...")
        
        # Determine model directory based on model type and task
        task = model_info.get("task", "text-generation")
        if model_info.get("format") == "gguf":
            model_dir = MODELS_DIR / "gguf" / model_name
        elif "qwen" in model_name.lower():
            model_dir = MODELS_DIR / "huggingface" / "qwen" / model_name
        elif "phi" in model_name.lower():
            model_dir = MODELS_DIR / "huggingface" / "microsoft" / model_name
        else:
            model_dir = MODELS_DIR / task / model_name
        
        # Check if already downloaded
        cache_info = scan_cache_dir()
        for repo in cache_info.repos:
            if repo.repo_id.lower() == repo_id.lower():
                logger.info(f"Model {model_name} already in HF cache, using cached version")
        
        # Download with snapshot_download to get all files
        local_dir = snapshot_download(
            repo_id=repo_id,
            local_dir=str(model_dir),
            local_dir_use_symlinks=False,
            ignore_patterns=["*.safetensors.index.json", "*.msgpack", "*.h5"] if model_info.get("format") != "gguf" else None
        )
        
        logger.info(f"Successfully downloaded {model_name} to {local_dir}")
        return True, local_dir
        
    except Exception as e:
        logger.error(f"Error downloading model from HuggingFace: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return False, None

def create_model_config(model_name: str, model_path: Optional[str] = None) -> bool:
    """Create configuration file for the model."""
    model_info = MODEL_URLS.get(model_name)
    if not model_info:
        logger.error(f"Model {model_name} not found in configuration")
        return False
    
    # Determine model path if not provided
    if model_path is None:
        task = model_info.get("task", "text-generation")
        if model_info.get("format") == "gguf":
            model_path = str(MODELS_DIR / "gguf" / model_name)
        elif "qwen" in model_name.lower():
            model_path = str(MODELS_DIR / "huggingface" / "qwen" / model_name)
        elif "phi" in model_name.lower():
            model_path = str(MODELS_DIR / "huggingface" / "microsoft" / model_name)
        else:
            model_path = str(MODELS_DIR / task / model_name)
    
    # Base configuration
    config = {
        "model_name": model_name,
        "model_path": model_path,
        "model_type": model_info.get("type", "huggingface"),
        "hf_repo_id": model_info.get("repo_id", ""),
        "task": model_info.get("task", "text-generation"),
        "format": model_info.get("format", "pytorch"),
        "description": model_info.get("description", ""),
        "created_at": time.strftime("%Y-%m-%dT%H:%M:%S.%fZ")
    }
    
    # Add task-specific parameters
    if model_info.get("task") == "text-generation":
        config["parameters"] = {
            "max_length": 4096,
            "temperature": 0.7,
            "top_p": 0.9,
            "repetition_penalty": 1.1
        }
    elif model_info.get("task") == "reasoning":
        config["parameters"] = {
            "max_length": 8192,
            "temperature": 0.2,
            "top_p": 0.95,
            "repetition_penalty": 1.05
        }
    elif model_info.get("task") == "text-to-speech":
        config["parameters"] = {
            "voice": "default",
            "speed": 1.0,
            "sample_rate": 24000
        }
    elif model_info.get("task") == "speech-recognition":
        config["parameters"] = {
            "language": "en",
            "chunk_length_s": 30,
            "batch_size": 16
        }
    elif model_info.get("task") in ["text-to-image", "image-to-image"]:
        config["parameters"] = {
            "width": 1024,
            "height": 1024,
            "guidance_scale": 7.5,
            "num_inference_steps": 50
        }
    
    # Add model-specific adjustments
    if "qwen3-235b" in model_name.lower():
        config["parameters"]["max_length"] = 32768
    if "phi-4" in model_name.lower():
        config["parameters"]["max_length"] = 4096
    
    # For GGUF models
    if model_info.get("format") == "gguf":
        config["parameters"]["n_ctx"] = 8192
        config["parameters"]["n_batch"] = 512
        config["loader"] = "llama.cpp"
    
    config_path = CONFIG_DIR / f"{model_name}.json"
    try:
        with open(config_path, 'w') as f:
            json.dump(config, f, indent=2)
        logger.info(f"Created config file: {config_path}")
        return True
    except Exception as e:
        logger.error(f"Error creating config: {e}")
        return False

def update_mcp_config(model_name: str, port: int = None) -> bool:
    """Update MCP configuration to include the model."""
    try:
        # Determine port if not specified
        if port is None:
            # Generate a port number between 8100-8499 based on model name hash
            port = 8100 + (hash(model_name) % 400)
        
        model_config = json.load(open(CONFIG_DIR / f"{model_name}.json"))
        
        # Load existing MCP config or create new one
        if MCP_CONFIG_FILE.exists():
            with open(MCP_CONFIG_FILE) as f:
                mcp_config = json.load(f)
        else:
            mcp_config = {"servers": {}, "registered_servers": []}
        
        # Generate server ID and create entry
        server_id = f"local-{model_name.replace('.', '-').lower()}"
        server_name = f"Local {model_name}"
        server_url = f"http://localhost:{port}"
        
        # Add to MCP config
        mcp_config["servers"][server_id] = {
            "id": server_id,
            "name": server_name,
            "url": server_url,
            "model_path": model_config["model_path"],
            "model_type": model_config["model_type"],
            "task": model_config.get("task", "text-generation"),
            "registered_at": time.strftime("%Y-%m-%dT%H:%M:%S.%fZ")
        }
        
        if server_id not in mcp_config.get("registered_servers", []):
            mcp_config["registered_servers"] = mcp_config.get("registered_servers", []) + [server_id]
        
        # Save updated config
        with open(MCP_CONFIG_FILE, 'w') as f:
            json.dump(mcp_config, f, indent=2)
            
        logger.info(f"Updated MCP configuration for {model_name} on port {port}")
        return True
        
    except Exception as e:
        logger.error(f"Error updating MCP config: {e}")
        return False

def update_mcp_registry(model_name: str) -> bool:
    """Update MCP registry with model information."""
    try:
        # Skip if registry doesn't exist yet
        if not MCP_REGISTRY_FILE.exists():
            logger.info(f"MCP registry not found. Will be created when MCP server starts.")
            return True
            
        # Load registry
        with open(MCP_REGISTRY_FILE) as f:
            registry = json.load(f)
            
        # Get model configuration
        model_config = json.load(open(CONFIG_DIR / f"{model_name}.json"))
        
        # Determine capabilities based on model type and task
        capabilities = ["text-generation"]
        task = model_config.get("task", "text-generation")
        
        if task == "reasoning":
            capabilities.extend(["reasoning", "tool-use"])
        elif task == "code-generation":
            capabilities.extend(["code-completion", "code-generation"])
        elif task == "text-to-speech":
            capabilities = ["text-to-speech", "audio-generation"]
        elif task == "speech-recognition":
            capabilities = ["speech-recognition", "audio-understanding"]
        elif task == "multimodal":
            capabilities = ["text-generation", "image-understanding", "multimodal"]
        elif task == "text-to-image":
            capabilities = ["image-generation", "text-to-image"]
        elif task == "embeddings":
            capabilities = ["embeddings", "sentence-similarity"]
            
        # Generate server ID
        server_id = f"local-{model_name.replace('.', '-').lower()}"
        
        # Add to registry capabilities
        if "capabilities" not in registry:
            registry["capabilities"] = {}
        
        registry["capabilities"][server_id] = capabilities
        
        # Add to active servers if not already there
        if "active_servers" not in registry:
            registry["active_servers"] = []
            
        if server_id not in registry["active_servers"]:
            registry["active_servers"].append(server_id)
        
        # Save updated registry
        with open(MCP_REGISTRY_FILE, 'w') as f:
            json.dump(registry, f, indent=2)
            
        logger.info(f"Updated MCP registry with capabilities for {model_name}")
        return True
        
    except Exception as e:
        logger.error(f"Error updating MCP registry: {e}")
        return False

def update_agent_config():
    """Update agent system configuration to include local models."""
    agent_config_path = Path.home() / ".config" / "agent_system" / "models.json"
    try:
        if not agent_config_path.parent.exists():
            agent_config_path.parent.mkdir(parents=True, exist_ok=True)
            
        if not agent_config_path.exists():
            config = {"local_models": [], "model_capabilities": {}}
        else:
            with open(agent_config_path) as f:
                config = json.load(f)

        # Add all installed models
        installed_models = []
        model_tasks = {}
        
        # Look through CONFIG_DIR for model configs
        for config_file in CONFIG_DIR.glob("*.json"):
            try:
                with open(config_file) as f:
                    model_config = json.load(f)
                    model_name = model_config.get("model_name", config_file.stem)
                    model_task = model_config.get("task", "text-generation")
                    model_format = model_config.get("format", "pytorch")
                    
                    installed_models.append(model_name)
                    
                    # Track model capabilities
                    if model_name not in model_tasks:
                        model_tasks[model_name] = {
                            "task": model_task,
                            "format": model_format,
                            "path": model_config.get("model_path", "")
                        }
            except Exception as e:
                logger.warning(f"Error reading model config {config_file}: {e}")

        config["local_models"] = list(set(installed_models))
        config["model_capabilities"] = model_tasks
        
        with open(agent_config_path, 'w') as f:
            json.dump(config, f, indent=2)
        
        logger.info(f"Updated agent system configuration at {agent_config_path}")
        return True
    except Exception as e:
        logger.error(f"Error updating agent config: {e}")
        return False

def download_nvidia_tool(tool_name: str, tool_info: Dict) -> bool:
    """Download and install an NVIDIA tool."""
    logger.info(f"Installing NVIDIA tool: {tool_name} ({tool_info.get('description')})")
    
    tool_dir = NVIDIA_TOOLS_DIR / tool_name
    tool_dir.mkdir(exist_ok=True)
    
    try:
        if tool_info.get("pip"):
            # Use pip to install
            package = tool_info["pip"]
            logger.info(f"Installing {package} via pip...")
            subprocess.check_call([sys.executable, "-m", "pip", "install", package])
            logger.info(f"Successfully installed {package}")
            return True
            
        elif tool_info.get("github"):
            # Clone GitHub repository
            repo_url = tool_info["url"]
            logger.info(f"Cloning repository from {repo_url}...")
            subprocess.check_call(["git", "clone", repo_url, str(tool_dir)])
            
            # Check for setup.py and install if present
            if (tool_dir / "setup.py").exists():
                logger.info(f"Installing {tool_name} package...")
                subprocess.check_call([sys.executable, "-m", "pip", "install", "-e", str(tool_dir)])
                
            logger.info(f"Successfully installed {tool_name}")
            return True
            
        elif tool_info.get("container"):
            # Pull Docker container
            logger.info(f"To use {tool_name}, pull the container with:")
            logger.info(f"docker pull nvcr.io/nvidia/{tool_name}")
            return True
            
        elif tool_info.get("registration_required"):
            logger.info(f"Tool {tool_name} requires NVIDIA Developer registration")
            logger.info(f"Please download manually from: {tool_info.get('url', 'NVIDIA Developer website')}")
            return True
            
        else:
            # Direct download
            url = tool_info["url"]
            logger.info(f"Downloading from {url}...")
            
            response = requests.get(url, stream=True)
            response.raise_for_status()
            
            tool_archive = tool_dir / f"{tool_name}.tar.gz"
            with open(tool_archive, 'wb') as f:
                for chunk in response.iter_content(chunk_size=8192):
                    if chunk:
                        f.write(chunk)
                        
            logger.info(f"Extracting {tool_name}...")
            shutil.unpack_archive(tool_archive, tool_dir)
            logger.info(f"Successfully installed {tool_name}")
            return True
            
    except Exception as e:
        logger.error(f"Error installing NVIDIA tool {tool_name}: {e}")
        return False

def list_available_models():
    """List all available models with details."""
    print("\n=== Available Models ===")
    print(f"{'Model Name':<30} {'Size':<10} {'Task':<20} {'Priority':<10}")
    print("-" * 70)
    
    for model_name, info in sorted(MODEL_URLS.items(), key=lambda x: (x[1].get("priority", "medium"), x[0])):
        print(f"{model_name:<30} {info.get('size', 'N/A'):<10} {info.get('task', 'text-generation'):<20} {info.get('priority', 'medium'):<10}")

def list_installed_models():
    """List all installed models."""
    print("\n=== Installed Models ===")
    
    installed_models = []
    for config_file in CONFIG_DIR.glob("*.json"):
        try:
            with open(config_file) as f:
                model_config = json.load(f)
                model_name = model_config.get("model_name", config_file.stem)
                model_task = model_config.get("task", "text-generation")
                model_path = model_config.get("model_path", "")
                
                installed_models.append((model_name, model_task, model_path))
        except Exception as e:
            logger.warning(f"Error reading model config {config_file}: {e}")
    
    if not installed_models:
        print("No models installed.")
        return
        
    print(f"{'Model Name':<30} {'Task':<20} {'Path':<50}")
    print("-" * 100)
    
    for model_name, task, path in sorted(installed_models):
        print(f"{model_name:<30} {task:<20} {path:<50}")

def check_model_updates():
    """Check for updates to installed models."""
    try:
        from huggingface_hub import list_repo_refs, HfApi
        
        print("\n=== Checking for Model Updates ===")
        
        for config_file in CONFIG_DIR.glob("*.json"):
            try:
                with open(config_file) as f:
                    model_config = json.load(f)
                    
                model_name = model_config.get("model_name", config_file.stem)
                repo_id = model_config.get("hf_repo_id", "")
                
                if not repo_id:
                    continue
                    
                # Get latest commit hash
                api = HfApi()
                refs = list_repo_refs(repo_id)
                latest_sha = refs.branches.get("main", refs.branches.get("master", None))
                
                if latest_sha:
                    print(f"{model_name}: Latest commit is {latest_sha.commit_hash[:7]}")
                    
                    # Check if we should update
                    if model_config.get("commit_hash") != latest_sha.commit_hash:
                        print(f"  ⚠️ Update available for {model_name}")
                        
            except Exception as e:
                print(f"Error checking updates for {config_file.stem}: {e}")
                
    except ImportError:
        print("huggingface_hub not installed. Cannot check for updates.")

def fix_mcp_servers():
    """Fix MCP server configuration."""
    logger.info("Fixing MCP server configuration...")
    
    # Ensure MCP config exists
    if not MCP_CONFIG_FILE.exists():
        logger.info("Creating new MCP configuration...")
        mcp_config = {
            "servers": {},
            "registered_servers": []
        }
        with open(MCP_CONFIG_FILE, 'w') as f:
            json.dump(mcp_config, f, indent=2)
    
    # Create registry file if it doesn't exist
    if not MCP_REGISTRY_FILE.exists():
        logger.info("Creating new MCP registry...")
        mcp_registry = {
            "servers": {},
            "capabilities": {},
            "active_servers": []
        }
        with open(MCP_REGISTRY_FILE, 'w') as f:
            json.dump(mcp_registry, f, indent=2)
    
    # Add all installed models to MCP configuration
    for config_file in CONFIG_DIR.glob("*.json"):
        try:
            with open(config_file) as f:
                model_config = json.load(f)
                
            model_name = model_config.get("model_name", config_file.stem)
            update_mcp_config(model_name)
            update_mcp_registry(model_name)
            
        except Exception as e:
            logger.warning(f"Error processing model config {config_file}: {e}")
    
    logger.info("MCP configuration fixed. Please restart MCP servers.")
    return True

def main():
    """Main entry point"""
    print("\n=== Enhanced Local LLM Model Installation ===")
    
    args = parse_args()
    
    # Handle list command
    if args.list:
        list_available_models()
        list_installed_models()
        return
        
    # Handle check for updates
    if args.check:
        check_model_updates()
        return
    
    # Handle fix MCP servers
    if args.fix_mcp:
        fix_mcp_servers()
        return
    
    # Check dependencies first
    if not check_dependencies():
        print("Please install required dependencies and try again")
        return
        
    create_directories()

    # Handle NVIDIA tools installation
    if args.nvidia_tools:
        print("\nInstalling NVIDIA tools...")
        for tool_name, tool_info in NVIDIA_TOOLS.items():
            download_nvidia_tool(tool_name, tool_info)
    
    # Determine models to install
    models_to_install = []
    
    if args.all:
        models_to_install = list(MODEL_URLS.keys())
    elif args.small_only:
        models_to_install = [
            model_name for model_name, info in MODEL_URLS.items() 
            if "GB" in info.get("size", "") and float(info["size"].replace("GB", "")) < 5
        ]
    elif args.medium_only:
        models_to_install = [
            model_name for model_name, info in MODEL_URLS.items() 
            if "GB" in info.get("size", "") and 5 <= float(info["size"].replace("GB", "")) <= 15
        ]
    elif args.task:
        models_to_install = [
            model_name for model_name, info in MODEL_URLS.items() 
            if info.get("task", "text-generation") == args.task
        ]
    elif args.model:
        models_to_install = args.model
    else:
        # Interactive selection
        print("\nAvailable models:")
        model_options = {}
        for i, (model_name, info) in enumerate(sorted(MODEL_URLS.items(), key=lambda x: (x[1].get("priority", "medium"), x[0])), 1):
            task = info.get("task", "text-generation")
            priority = info.get("priority", "medium")
            print(f"{i}. {model_name} ({info['size']}, {task}, Priority: {priority})")
            model_options[i] = model_name
        
        print("\nQuick selection:")
        print("a) Install all high-priority models")
        print("b) Install all small models (<5GB)")
        print("c) Install all reasoning models")
        print("d) Install NVIDIA models")
        print("e) Install Qwen models")
        print("f) Install multimodal models")
        
        choice = input("\nEnter model numbers to install (comma-separated), or letter for quick selection: ")
        
        if choice.lower() == "a":
            models_to_install = [
                model_name for model_name, info in MODEL_URLS.items() 
                if info.get("priority", "medium") == "high"
            ]
        elif choice.lower() == "b":
            models_to_install = [
                model_name for model_name, info in MODEL_URLS.items() 
                if "GB" in info.get("size", "") and float(info["size"].replace("GB", "")) < 5
            ]
        elif choice.lower() == "c":
            models_to_install = [
                model_name for model_name, info in MODEL_URLS.items() 
                if info.get("task", "") == "reasoning"
            ]
        elif choice.lower() == "d":
            models_to_install = [
                model_name for model_name in MODEL_URLS.keys()
                if "nvidia" in model_name.lower()
            ]
        elif choice.lower() == "e":
            models_to_install = [
                model_name for model_name in MODEL_URLS.keys()
                if "qwen" in model_name.lower()
            ]
        elif choice.lower() == "f":
            models_to_install = [
                model_name for model_name, info in MODEL_URLS.items() 
                if info.get("task", "") in ["multimodal", "text-to-image", "text-to-speech", "text-to-video"]
            ]
        else:
            try:
                indices = [int(idx) for idx in choice.split(",")]
                models_to_install = [model_options[i] for i in indices if i in model_options]
            except ValueError:
                print("Invalid input, installing nothing")
                return
    
    # Install models
    installed_models = []
    for model_name in models_to_install:
        if model_name not in MODEL_URLS:
            logger.warning(f"Model {model_name} not found in available models, skipping")
            continue
            
        logger.info(f"\nInstalling {model_name}...")
        success, path = download_hf_model(model_name, MODEL_URLS[model_name])
        
        if success:
            create_model_config(model_name)
            
            if not args.skip_registration:
                update_mcp_config(model_name)
                update_mcp_registry(model_name)
                
            installed_models.append(model_name)
        else:
            logger.error(f"Failed to install {model_name}")

    # Update agent system configuration
    if installed_models:
        update_agent_config()

    logger.info("\nInstallation complete!")
    logger.info(f"Models are available at: {MODELS_DIR}")
    
    logger.info("\nInstalled models:")
    for model in installed_models:
        logger.info(f"- {model}")
    
    # Remind to restart MCP servers
    if installed_models and not args.skip_registration:
        logger.info("\nPlease restart MCP servers to use the new models:")
        logger.info("python start_mcp_servers.py")

if __name__ == "__main__":
    main()
