"""
Send <PERSON> Communications

This script demonstrates how to send actual communications (call, voicemail, text, email)
to <PERSON> using Twilio and Eleven Labs.

NOTE: This is a demonstration script. To use it, you would need to:
1. Add your Twilio account credentials
2. Add your Eleven Labs API key
3. Add your email service credentials
4. Run the script with proper authorization
"""

import os
import requests
import json
import time
from twilio.rest import Client
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
import smtplib
import ssl

# Import <PERSON> communication templates
from paul_edwards_communications import <PERSON><PERSON><PERSON><PERSON>sCom<PERSON><PERSON>

# <PERSON> contact information
PAUL_EDWARDS = {
    "first_name": "<PERSON>",
    "last_name": "<PERSON>",
    "email": "<EMAIL>",
    "phone": "+***********",  # <PERSON>'s other contact number
}

# ======================== CONFIGURATION ========================
# Twilio credentials
TWILIO_ACCOUNT_SID = "**********************************"
TWILIO_AUTH_TOKEN = "0458bc22d41d6756fc8e62d3e2938382"
TWILIO_API_KEY = "**********************************"
TWILIO_API_SECRET = "CqpVewwter1BEMdFIFHrN2XmUyt22wBP"
TWILIO_PHONE_NUMBER = "+***********"  # Using the verified number

# Eleven Labs credentials
ELEVEN_LABS_API_KEY = "sk_531ff39d6099a4191b57e4935654c909d6196b59d9339015"
ELEVEN_LABS_VOICE_ID = "21m00Tcm4TlvDq8ikWAM"  # Rachel voice ID

# Email configuration - you'll need to add these if you want to send emails
EMAIL_SENDER = "<EMAIL>"
EMAIL_PASSWORD = "your_email_password"
SMTP_SERVER = "smtp.gmail.com"  # Change as needed
SMTP_PORT = 587  # Change as needed

# Create directories for audio files
os.makedirs("audio", exist_ok=True)

# ======================== ELEVEN LABS TEXT-TO-SPEECH ========================
def generate_speech(text, voice_id=ELEVEN_LABS_VOICE_ID, output_path="audio/output.mp3"):
    """Generate speech from text using Eleven Labs API"""
    url = f"https://api.elevenlabs.io/v1/text-to-speech/{voice_id}"

    headers = {
        "Accept": "audio/mpeg",
        "Content-Type": "application/json",
        "xi-api-key": ELEVEN_LABS_API_KEY
    }

    data = {
        "text": text,
        "model_id": "eleven_monolingual_v1",
        "voice_settings": {
            "stability": 0.5,
            "similarity_boost": 0.5
        }
    }

    response = requests.post(url, json=data, headers=headers)

    if response.status_code == 200:
        with open(output_path, "wb") as f:
            f.write(response.content)
        print(f"Audio file saved to {output_path}")
        return output_path
    else:
        print(f"Error generating speech: {response.text}")
        return None

# ======================== TWILIO FUNCTIONS ========================
def initialize_twilio_client():
    """Initialize Twilio client"""
    return Client(TWILIO_ACCOUNT_SID, TWILIO_AUTH_TOKEN)

def make_phone_call(to_number, audio_url=None, twiml=None):
    """Make a phone call using Twilio"""
    client = initialize_twilio_client()

    # Format phone number for Twilio
    if not to_number.startswith("+"):
        to_number = "+1" + to_number

    try:
        # If we have a TwiML, use that
        if twiml:
            call = client.calls.create(
                twiml=twiml,
                to=to_number,
                from_=TWILIO_PHONE_NUMBER
            )
        # If we have an audio URL, use that
        elif audio_url:
            call = client.calls.create(
                url=audio_url,
                to=to_number,
                from_=TWILIO_PHONE_NUMBER
            )
        else:
            print("Error: Either audio_url or twiml must be provided")
            return None

        print(f"Call initiated with SID: {call.sid}")
        return call.sid
    except Exception as e:
        print(f"Error making call: {str(e)}")
        return None

def send_voicemail(to_number, audio_url=None, twiml=None):
    """Send a voicemail using Twilio (calls and hangs up after voicemail)"""
    client = initialize_twilio_client()

    # Format phone number for Twilio
    if not to_number.startswith("+"):
        to_number = "+1" + to_number

    try:
        # Create TwiML to go straight to voicemail
        if twiml:
            voicemail_twiml = f"""
            <Response>
                <Pause length="2"/>
                {twiml}
                <Hangup/>
            </Response>
            """
        elif audio_url:
            voicemail_twiml = f"""
            <Response>
                <Pause length="2"/>
                <Play>{audio_url}</Play>
                <Hangup/>
            </Response>
            """
        else:
            print("Error: Either audio_url or twiml must be provided")
            return None

        # Make the call with the voicemail TwiML
        call = client.calls.create(
            twiml=voicemail_twiml,
            to=to_number,
            from_=TWILIO_PHONE_NUMBER
        )

        print(f"Voicemail initiated with SID: {call.sid}")
        return call.sid
    except Exception as e:
        print(f"Error sending voicemail: {str(e)}")
        return None

def send_text_message(to_number, message):
    """Send a text message using Twilio"""
    client = initialize_twilio_client()

    # Format phone number for Twilio
    if not to_number.startswith("+"):
        to_number = "+1" + to_number

    try:
        message = client.messages.create(
            body=message,
            from_=TWILIO_PHONE_NUMBER,
            to=to_number
        )

        print(f"Text message sent with SID: {message.sid}")
        return message.sid
    except Exception as e:
        print(f"Error sending text message: {str(e)}")
        return None

# ======================== EMAIL FUNCTION ========================
def send_email(to_email, subject, body):
    """Send an email using SMTP"""
    try:
        message = MIMEMultipart()
        message["From"] = EMAIL_SENDER
        message["To"] = to_email
        message["Subject"] = subject

        # Attach body
        message.attach(MIMEText(body, "plain"))

        # Create secure connection and send email
        context = ssl.create_default_context()
        with smtplib.SMTP(SMTP_SERVER, SMTP_PORT) as server:
            server.starttls(context=context)
            server.login(EMAIL_SENDER, EMAIL_PASSWORD)
            server.sendmail(EMAIL_SENDER, to_email, message.as_string())

        print(f"Email sent to {to_email}")
        return True
    except Exception as e:
        print(f"Error sending email: {str(e)}")
        return False

# ======================== MAIN FUNCTION ========================
def send_all_communications():
    """Send all communications to Paul Edwards"""
    print("=" * 80)
    print("SENDING COMMUNICATIONS TO PAUL EDWARDS")
    print("=" * 80)

    # Get communication templates
    call_script = PaulEdwardsCommunications.get_call_script()
    voicemail_script = PaulEdwardsCommunications.get_voicemail_script()
    text_message = PaulEdwardsCommunications.get_text_message()
    email = PaulEdwardsCommunications.get_email()

    # Clean up scripts for text-to-speech
    call_script_clean = call_script.replace("CALL SCRIPT FOR PAUL EDWARDS", "").strip()
    call_script_clean = call_script_clean.split("Introduction:")[1].split("Key Talking Points:")[0].strip()

    voicemail_script_clean = voicemail_script.replace("VOICEMAIL SCRIPT FOR PAUL EDWARDS", "").strip()

    text_message_clean = text_message.replace("TEXT MESSAGE FOR PAUL EDWARDS", "").strip()

    # 1. Generate speech for call and voicemail
    print("\n1. GENERATING SPEECH WITH ELEVEN LABS")
    print("-" * 80)

    call_audio_path = generate_speech(call_script_clean, output_path="audio/paul_edwards_call.mp3")
    voicemail_audio_path = generate_speech(voicemail_script_clean, output_path="audio/paul_edwards_voicemail.mp3")

    # 2. Make phone call
    print("\n2. MAKING PHONE CALL WITH TWILIO")
    print("-" * 80)

    # Since we can't host the audio file at a public URL in this environment,
    # we'll use Twilio's TTS capabilities instead
    call_twiml = f"""
    <Response>
        <Say voice="woman">
            Hello, this is a test call for Paul Edwards. I'm calling from an AI assistant to demonstrate the system's capabilities.
            {call_script_clean}
        </Say>
        <Pause length="2"/>
        <Say voice="woman">
            This is the end of the test call. Thank you.
        </Say>
    </Response>
    """

    call_sid = make_phone_call(PAUL_EDWARDS["phone"], twiml=call_twiml)

    # Wait a bit before sending the voicemail to avoid conflicts
    print("Waiting 30 seconds before sending voicemail...")
    time.sleep(30)

    # 3. Send voicemail
    print("\n3. SENDING VOICEMAIL WITH TWILIO")
    print("-" * 80)

    voicemail_twiml = f"""
    <Response>
        <Say voice="woman">
            {voicemail_script_clean}
        </Say>
    </Response>
    """

    voicemail_sid = send_voicemail(PAUL_EDWARDS["phone"], twiml=voicemail_twiml)

    # 4. Send text message
    print("\n4. SENDING TEXT MESSAGE WITH TWILIO")
    print("-" * 80)

    text_sid = send_text_message(PAUL_EDWARDS["phone"], text_message_clean)

    # 5. Send email
    print("\n5. SENDING EMAIL")
    print("-" * 80)

    email_success = send_email(
        PAUL_EDWARDS["email"],
        email["subject"],
        email["body"]
    )

    # Summary
    print("\n" + "=" * 80)
    print("COMMUNICATION SUMMARY")
    print("=" * 80)

    print(f"Call SID: {call_sid}")
    print(f"Voicemail SID: {voicemail_sid}")
    print(f"Text Message SID: {text_sid}")
    print(f"Email Sent: {'Yes' if email_success else 'No'}")

    print("\nAll communications have been sent to Paul Edwards.")
    print("=" * 80)

if __name__ == "__main__":
    print("This script will send actual communications to Paul Edwards.")
    print("Make sure you have proper authorization before proceeding.")
    print("You will need valid Twilio and Eleven Labs credentials.")

    proceed = input("Do you want to proceed? (yes/no): ")

    if proceed.lower() == "yes":
        send_all_communications()
    else:
        print("Operation cancelled.")
