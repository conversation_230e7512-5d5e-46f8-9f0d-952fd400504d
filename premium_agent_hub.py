#!/usr/bin/env python3
"""
Premium Agent Hub

This script provides a unified interface that integrates our advanced lead generation
system with our agent platform. It offers agents a seamless experience to access
high-quality leads, track performance, and scale their business to $250,000-$500,000
weekly AP.

Features:
1. Centralized lead distribution and management
2. Agent performance tracking and analytics
3. Automated lead nurturing and follow-up sequences
4. Advanced segmentation and targeting
5. Integration with carrier systems
6. White-label capabilities for lead reselling

This creates a complete ecosystem for insurance agents to thrive.
"""

import os
import sys
import json
import logging
import datetime
import random
import hashlib
import time
from typing import Dict, List, Any, Optional, Union, Tuple

# Configure logging
logging.basicConfig(level=logging.INFO,
                    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Try to import our components
try:
    from premium_insurance_leads import PremiumInsuranceLeads
    PREMIUM_LEADS_AVAILABLE = True
except ImportError:
    PREMIUM_LEADS_AVAILABLE = False
    logger.warning("Premium Insurance Leads system not available")

try:
    from agent_system import AgentSystem
    AGENT_SYSTEM_AVAILABLE = True
except ImportError:
    AGENT_SYSTEM_AVAILABLE = False
    logger.warning("Agent System not available")

try:
    from insurance_carrier_access import InsuranceCarrierAccess
    CARRIER_ACCESS_AVAILABLE = True
except ImportError:
    CARRIER_ACCESS_AVAILABLE = False
    logger.warning("Insurance Carrier Access not available")


class PremiumAgentHub:
    """
    Premium Agent Hub - Integrated system for agents to access lead generation,
    performance tracking, and carrier integrations
    """
    
    def __init__(self):
        """Initialize the Premium Agent Hub"""
        self.components = {}
        self._initialize_components()
        
        # Agent management
        self.agents = {}
        self.agents_db_path = "data/agents/agents.json"
        
        # Lead distribution
        self.lead_assignments = {}
        self.lead_assignments_db_path = "data/leads/assignments.json"
        
        # Performance tracking
        self.performance_metrics = {}
        self.performance_db_path = "data/metrics/performance.json"
        
        # Initialize database
        self._initialize_database()
        
        # Define lead distribution rules
        self.distribution_rules = {
            "round_robin": True,           # Distribute leads evenly
            "performance_based": True,     # Higher performers get more leads
            "specialization": True,        # Match leads to agent specialties
            "capacity_based": True,        # Consider agent capacity
            "lead_score_threshold": {      # Minimum lead scores for assignment
                "new_agent": 30,           # New agents get lower quality leads
                "standard_agent": 50,      # Standard agents get medium quality leads
                "top_performer": 70        # Top performers get highest quality leads
            }
        }
        
        # Agent tiers
        self.agent_tiers = {
            "new_agent": {
                "requirements": {
                    "months_active": (0, 3),
                    "monthly_ap": (0, 50000)
                },
                "lead_allocation": 0.5,     # 50% of standard allocation
                "lead_quality_bias": 0.0,   # No quality bias
                "support_level": "high"
            },
            "standard_agent": {
                "requirements": {
                    "months_active": (3, 12),
                    "monthly_ap": (50000, 150000)
                },
                "lead_allocation": 1.0,     # Standard allocation
                "lead_quality_bias": 0.5,   # Medium quality bias
                "support_level": "standard"
            },
            "top_performer": {
                "requirements": {
                    "months_active": (12, float('inf')),
                    "monthly_ap": (150000, float('inf'))
                },
                "lead_allocation": 2.0,     # 200% of standard allocation
                "lead_quality_bias": 1.0,   # High quality bias
                "support_level": "premium"
            }
        }
        
        # Performance metrics tracking
        self.performance_categories = {
            "lead_metrics": [
                "leads_received",
                "leads_contacted",
                "contact_rate",
                "response_time"
            ],
            "appointment_metrics": [
                "appointments_set",
                "appointment_show_rate",
                "appointment_set_ratio"
            ],
            "sales_metrics": [
                "closing_ratio",
                "average_premium",
                "applications_submitted",
                "policies_issued",
                "annual_premium"
            ],
            "efficiency_metrics": [
                "lead_to_sale_time",
                "cost_per_acquisition",
                "return_on_investment"
            ]
        }
        
    def _initialize_components(self):
        """Initialize all available components"""
        # Initialize premium leads system
        if PREMIUM_LEADS_AVAILABLE:
            try:
                self.components["leads_system"] = PremiumInsuranceLeads()
                logger.info("Premium Insurance Leads system initialized")
            except Exception as e:
                logger.error(f"Error initializing Premium Insurance Leads: {e}")
                
        # Initialize agent system
        if AGENT_SYSTEM_AVAILABLE:
            try:
                self.components["agent_system"] = AgentSystem()
                logger.info("Agent System initialized")
            except Exception as e:
                logger.error(f"Error initializing Agent System: {e}")
                
        # Initialize carrier access
        if CARRIER_ACCESS_AVAILABLE:
            try:
                self.components["carrier_access"] = InsuranceCarrierAccess()
                logger.info("Carrier Access initialized")
            except Exception as e:
                logger.error(f"Error initializing Carrier Access: {e}")
                
        # Create data directories
        os.makedirs("data/agents", exist_ok=True)
        os.makedirs("data/leads", exist_ok=True)
        os.makedirs("data/metrics", exist_ok=True)
        
        logger.info("All available components initialized")
        
    def _initialize_database(self):
        """Initialize databases"""
        # Load agents database
        if os.path.exists(self.agents_db_path):
            try:
                with open(self.agents_db_path, 'r') as f:
                    self.agents = json.load(f)
                logger.info(f"Loaded {len(self.agents)} agents from database")
            except Exception as e:
                logger.error(f"Error loading agents database: {e}")
                self.agents = {}
                
        # Load lead assignments
        if os.path.exists(self.lead_assignments_db_path):
            try:
                with open(self.lead_assignments_db_path, 'r') as f:
                    self.lead_assignments = json.load(f)
                logger.info(f"Loaded lead assignments from database")
            except Exception as e:
                logger.error(f"Error loading lead assignments: {e}")
                self.lead_assignments = {}
                
        # Load performance metrics
        if os.path.exists(self.performance_db_path):
            try:
                with open(self.performance_db_path, 'r') as f:
                    self.performance_metrics = json.load(f)
                logger.info(f"Loaded performance metrics from database")
            except Exception as e:
                logger.error(f"Error loading performance metrics: {e}")
                self.performance_metrics = {}
                
    def _save_database(self):
        """Save all databases"""
        # Save agents
        try:
            os.makedirs(os.path.dirname(self.agents_db_path), exist_ok=True)
            with open(self.agents_db_path, 'w') as f:
                json.dump(self.agents, f, indent=2)
            logger.info(f"Saved {len(self.agents)} agents to database")
        except Exception as e:
            logger.error(f"Error saving agents database: {e}")
            
        # Save lead assignments
        try:
            os.makedirs(os.path.dirname(self.lead_assignments_db_path), exist_ok=True)
            with open(self.lead_assignments_db_path, 'w') as f:
                json.dump(self.lead_assignments, f, indent=2)
            logger.info(f"Saved lead assignments to database")
        except Exception as e:
            logger.error(f"Error saving lead assignments: {e}")
            
        # Save performance metrics
        try:
            os.makedirs(os.path.dirname(self.performance_db_path), exist_ok=True)
            with open(self.performance_db_path, 'w') as f:
                json.dump(self.performance_metrics, f, indent=2)
            logger.info(f"Saved performance metrics to database")
        except Exception as e:
            logger.error(f"Error saving performance metrics: {e}")
    
    # ========================= AGENT MANAGEMENT =========================
    
    def register_agent(self, name: str, email: str, phone: str, 
                      specialties: List[str] = None, tier: str = "new_agent") -> Dict[str, Any]:
        """
        Register a new agent in the system
        
        Args:
            name: Agent name
            email: Agent email
            phone: Agent phone
            specialties: List of product specialties
            tier: Agent tier
            
        Returns:
            Dictionary with agent details
        """
        # Validate inputs
        if not name or not email or not phone:
            raise ValueError("Name, email and phone are required")
            
        # Check if tier is valid
        if tier not in self.agent_tiers:
            tier = "new_agent"
            
        # Generate agent ID
        agent_id = f"agent_{int(time.time())}_{hashlib.md5(email.encode()).hexdigest()[:8]}"
        
        # Set default specialties if none provided
        if not specialties:
            specialties = ["IUL", "Term Life", "Medicare Supplement"]
            
        # Create agent record
        agent = {
            "id": agent_id,
            "name": name,
            "email": email,
            "phone": phone,
            "specialties": specialties,
            "tier": tier,
            "status": "active",
            "regions": [],
            "carrier_appointments": [],
            "lead_preferences": {
                "daily_capacity": 5,
                "quality_threshold": self.distribution_rules["lead_score_threshold"][tier],
                "preferred_sources": [],
                "excluded_sources": []
            },
            "metrics": {
                "total_leads": 0,
                "total_appointments": 0,
                "total_sales": 0,
                "total_ap": 0.0,
                "total_commission": 0.0,
                "closing_ratio": 0.0
            },
            "created_at": datetime.datetime.now().isoformat(),
            "last_active": datetime.datetime.now().isoformat()
        }
        
        # Add agent to database
        self.agents[agent_id] = agent
        self._save_database()
        
        logger.info(f"Registered new agent: {name} (ID: {agent_id})")
        return agent
        
    def update_agent(self, agent_id: str, updates: Dict[str, Any]) -> Dict[str, Any]:
        """
        Update agent information
        
        Args:
            agent_id: Agent ID
            updates: Dictionary with updates
            
        Returns:
            Updated agent record
        """
        # Check if agent exists
        if agent_id not in self.agents:
            raise ValueError(f"Agent not found: {agent_id}")
            
        # Get current agent data
        agent = self.agents[agent_id]
        
        # Apply updates (only for allowed fields)
        allowed_fields = [
            "name", "email", "phone", "specialties", "status", 
            "regions", "carrier_appointments", "lead_preferences"
        ]
        
        for field, value in updates.items():
            if field in allowed_fields:
                agent[field] = value
                
        # Update last active time
        agent["last_active"] = datetime.datetime.now().isoformat()
        
        # Update tier if needed
        if "tier" in updates and updates["tier"] in self.agent_tiers:
            agent["tier"] = updates["tier"]
            # Update lead quality threshold based on tier
            agent["lead_preferences"]["quality_threshold"] = self.distribution_rules["lead_score_threshold"][updates["tier"]]
            
        # Save to database
        self.agents[agent_id] = agent
        self._save_database()
        
        logger.info(f"Updated agent: {agent['name']} (ID: {agent_id})")
        return agent
        
    def get_agent(self, agent_id: str) -> Dict[str, Any]:
        """
        Get agent details
        
        Args:
            agent_id: Agent ID
            
        Returns:
            Agent details
        """
        if agent_id not in self.agents:
            raise ValueError(f"Agent not found: {agent_id}")
            
        return self.agents[agent_id]
        
    def list_agents(self, status: str = "active", tier: str = None) -> List[Dict[str, Any]]:
        """
        List all agents matching criteria
        
        Args:
            status: Agent status filter
            tier: Agent tier filter
            
        Returns:
            List of matching agents
        """
        result = []
        
        for agent_id, agent in self.agents.items():
            # Apply filters
            if status and agent["status"] != status:
                continue
                
            if tier and agent["tier"] != tier:
                continue
                
            result.append(agent)
            
        return result
        
    def calculate_agent_tier(self, agent_id: str) -> str:
        """
        Calculate appropriate tier based on agent's performance
        
        Args:
            agent_id: Agent ID
            
        Returns:
            Appropriate tier
        """
        if agent_id not in self.agents:
            raise ValueError(f"Agent not found: {agent_id}")
            
        agent = self.agents[agent_id]
        
        # Get metrics
        months_active = self._calculate_months_active(agent["created_at"])
        monthly_ap = agent["metrics"]["total_ap"] / max(1, months_active)
        
        # Determine tier
        for tier, criteria in self.agent_tiers.items():
            months_range = criteria["requirements"]["months_active"]
            ap_range = criteria["requirements"]["monthly_ap"]
            
            if (months_range[0] <= months_active < months_range[1] and 
                ap_range[0] <= monthly_ap < ap_range[1]):
                return tier
                
        # Default to standard_agent if no match
        return "standard_agent"
        
    def _calculate_months_active(self, created_at: str) -> int:
        """Calculate months since agent creation"""
        created_date = datetime.datetime.fromisoformat(created_at)
        now = datetime.datetime.now()
        
        # Calculate difference in months
        months = (now.year - created_date.year) * 12 + (now.month - created_date.month)
        
        # Ensure at least 1 month
        return max(1, months)
        
    # ========================= LEAD MANAGEMENT =========================
    
    def get_available_leads(self, min_quality: int = 0, max_count: int = 100) -> List[Dict[str, Any]]:
        """
        Get available leads for distribution
        
        Args:
            min_quality: Minimum lead quality score
            max_count: Maximum number of leads to return
            
        Returns:
            List of available leads
        """
        if "leads_system" not in self.components:
            logger.warning("Leads system not available")
            return []
            
        leads_system = self.components["leads_system"]
        
        # Get all leads from leads system
        all_leads = leads_system.leads
        
        # Filter for unassigned, qualified leads above quality threshold
        available_leads = []
        for lead in all_leads:
            lead_id = lead.get("id")
            quality_score = lead.get("quality_score", 0)
            is_qualified = lead.get("qualified", False)
            
            # Skip if already assigned
            if lead_id in self.lead_assignments:
                continue
                
            # Skip if below quality threshold
            if quality_score < min_quality:
                continue
                
            # Skip if not qualified
            if not is_qualified:
                continue
                
            available_leads.append(lead)
            
            # Stop if we hit the max count
            if len(available_leads) >= max_count:
                break
                
        return available_leads
        
    def distribute_leads(self, leads: List[Dict[str, Any]] = None, 
                       distribution_method: str = "balanced") -> Dict[str, Any]:
        """
        Distribute leads to agents
        
        Args:
            leads: List of leads to distribute (gets available leads if None)
            distribution_method: Method for distribution (balanced, performance, specialty)
            
        Returns:
            Distribution results
        """
        # Get available agents
        active_agents = self.list_agents(status="active")
        if not active_agents:
            logger.warning("No active agents available for lead distribution")
            return {"success": False, "error": "No active agents available"}
            
        # Get leads if not provided
        if leads is None:
            leads = self.get_available_leads()
            
        if not leads:
            logger.warning("No leads available for distribution")
            return {"success": False, "error": "No leads available"}
            
        # Initialize results tracking
        results = {
            "total_leads": len(leads),
            "total_agents": len(active_agents),
            "assignments": {},
            "agent_counts": {},
            "unassigned": 0
        }
        
        # Apply distribution method
        if distribution_method == "balanced":
            assignments = self._distribute_leads_balanced(leads, active_agents)
        elif distribution_method == "performance":
            assignments = self._distribute_leads_by_performance(leads, active_agents)
        elif distribution_method == "specialty":
            assignments = self._distribute_leads_by_specialty(leads, active_agents)
        else:
            # Default to balanced
            assignments = self._distribute_leads_balanced(leads, active_agents)
            
        # Process assignments
        for lead_id, agent_id in assignments.items():
            # Update lead assignments
            self.lead_assignments[lead_id] = {
                "agent_id": agent_id,
                "assigned_at": datetime.datetime.now().isoformat(),
                "status": "assigned",
                "last_updated": datetime.datetime.now().isoformat()
            }
            
            # Update agent metrics
            if agent_id in self.agents:
                self.agents[agent_id]["metrics"]["total_leads"] += 1
                
            # Track in results
            results["assignments"][lead_id] = agent_id
            results["agent_counts"][agent_id] = results["agent_counts"].get(agent_id, 0) + 1
            
        # Track unassigned leads
        results["unassigned"] = len(leads) - len(assignments)
        
        # Save updates
        self._save_database()
        
        logger.info(f"Distributed {len(assignments)} leads to {len(results['agent_counts'])} agents")
        return results
        
    def _distribute_leads_balanced(self, leads: List[Dict[str, Any]], 
                                 agents: List[Dict[str, Any]]) -> Dict[str, str]:
        """
        Distribute leads evenly among agents, accounting for capacity
        
        Args:
            leads: Leads to distribute
            agents: Agents to distribute to
            
        Returns:
            Dictionary mapping lead IDs to agent IDs
        """
        assignments = {}
        
        # Calculate initial agent capacities based on tier
        agent_capacities = {}
        for agent in agents:
            tier = agent["tier"]
            base_capacity = agent["lead_preferences"]["daily_capacity"]
            tier_allocation = self.agent_tiers[tier]["lead_allocation"]
            
            # Calculate capacity
            agent_capacities[agent["id"]] = int(base_capacity * tier_allocation)
            
        # Sort leads by quality score (highest first)
        sorted_leads = sorted(leads, key=lambda x: x.get("quality_score", 0), reverse=True)
        
        # Distribute leads
        for lead in sorted_leads:
            lead_id = lead["id"]
            
            # Get available agents with capacity
            available_agents = [a for a in agents if agent_capacities[a["id"]] > 0]
            if not available_agents:
                continue
                
            # Find best agent for this lead
            best_agent = self._find_best_agent_for_lead(lead, available_agents)
            
            # Assign lead
            assignments[lead_id] = best_agent["id"]
            
            # Reduce agent capacity
            agent_capacities[best_agent["id"]] -= 1
            
        return assignments
        
    def _distribute_leads_by_performance(self, leads: List[Dict[str, Any]], 
                                       agents: List[Dict[str, Any]]) -> Dict[str, str]:
        """
        Distribute leads based on agent performance, giving more to top performers
        
        Args:
            leads: Leads to distribute
            agents: Agents to distribute to
            
        Returns:
            Dictionary mapping lead IDs to agent IDs
        """
        assignments = {}
        
        # Calculate performance scores
        performance_scores = {}
        for agent in agents:
            # Use closing ratio as performance indicator
            closing_ratio = agent["metrics"].get("closing_ratio", 0)
            
            # If no closing ratio data, use tier as a proxy
            if closing_ratio == 0:
                tier_scores = {
                    "new_agent": 0.2,
                    "standard_agent": 0.3,
                    "top_performer": 0.5
                }
                closing_ratio = tier_scores.get(agent["tier"], 0.3)
                
            performance_scores[agent["id"]] = closing_ratio
            
        # Calculate weighted capacities
        agent_capacities = {}
        total_capacity = 0
        
        for agent in agents:
            tier = agent["tier"]
            base_capacity = agent["lead_preferences"]["daily_capacity"]
            tier_allocation = self.agent_tiers[tier]["lead_allocation"]
            performance_bonus = performance_scores[agent["id"]] * 2  # Double capacity for perfect performers
            
            # Calculate capacity with performance bonus
            agent_capacities[agent["id"]] = int(base_capacity * tier_allocation * (1 + performance_bonus))
            total_capacity += agent_capacities[agent["id"]]
            
        # Sort leads by quality score (highest first)
        sorted_leads = sorted(leads, key=lambda x: x.get("quality_score", 0), reverse=True)
        
        # Distribute leads
        for lead in sorted_leads:
            lead_id = lead["id"]
            lead_score = lead.get("quality_score", 50)
            
            # Get available agents with capacity
            available_agents = [a for a in agents if agent_capacities[a["id"]] > 0]
            if not available_agents:
                continue
                
            # Sort available agents by performance (for high quality leads)
            if lead_score >= 80:
                available_agents = sorted(available_agents, 
                                       key=lambda x: performance_scores[x["id"]], 
                                       reverse=True)
                
            # Find best agent
            best_agent = self._find_best_agent_for_lead(lead, available_agents)
            
            # Assign lead
            assignments[lead_id] = best_agent["id"]
            
            # Reduce agent capacity
            agent_capacities[best_agent["id"]] -= 1
            
        return assignments
        
    def _distribute_leads_by_specialty(self, leads: List[Dict[str, Any]], 
                                     agents: List[Dict[str, Any]]) -> Dict[str, str]:
        """
        Distribute leads based on agent specialties
        
        Args:
            leads: Leads to distribute
            agents: Agents to distribute to
            
        Returns:
            Dictionary mapping lead IDs to agent IDs
        """
        assignments = {}
        
        # Calculate initial agent capacities based on tier
        agent_capacities = {}
        for agent in agents:
            tier = agent["tier"]
            base_capacity = agent["lead_preferences"]["daily_capacity"]
            tier_allocation = self.agent_tiers[tier]["lead_allocation"]
            
            # Calculate capacity
            agent_capacities[agent["id"]] = int(base_capacity * tier_allocation)
            
        # Process each lead
        for lead in leads:
            lead_id = lead["id"]
            product_interest = lead.get("product_interest", "")
            
            # Find agents specializing in this product
            specialist_agents = [a for a in agents 
                               if product_interest in a["specialties"] 
                               and agent_capacities[a["id"]] > 0]
            
            # If no specialists available, use any available agent
            if not specialist_agents:
                available_agents = [a for a in agents if agent_capacities[a["id"]] > 0]
                if not available_agents:
                    continue  # No agents with capacity
                    
                best_agent = self._find_best_agent_for_lead(lead, available_agents)
            else:
                best_agent = self._find_best_agent_for_lead(lead, specialist_agents)
                
            # Assign lead
            assignments[lead_id] = best_agent["id"]
            
            # Reduce agent capacity
            agent_capacities[best_agent["id"]] -= 1
            
        return assignments
        
    def _find_best_agent_for_lead(self, lead: Dict[str, Any], 
                                agents: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        Find the best agent for a specific lead
        
        Args:
            lead: Lead to assign
            agents: List of candidate agents
            
        Returns:
            Best matching agent
        """
        if not agents:
            raise ValueError("No agents provided")
            
        # If only one agent, return that one
        if len(agents) == 1:
            return agents[0]
            
        # Check for product specialty match
        product_interest = lead.get("product_interest", "")
        specialists = [a for a in agents if product_interest in a["specialties"]]
        
        # Check for region match
        lead_state = lead.get("state", "")
        region_matches = [a for a in agents if lead_state in a["regions"] or not a["regions"]]
        
        # Combine criteria
        if specialists and region_matches:
            # Agents matching both criteria
            best_matches = [a for a in specialists if a in region_matches]
            if best_matches:
                return random.choice(best_matches)
                
        # If we have specialists, prioritize them
        if specialists:
            return random.choice(specialists)
            
        # If we have region matches, use them
        if region_matches:
            return random.choice(region_matches)
            
        # Otherwise, just pick randomly
        return random.choice(agents)
        
    def track_lead_progress(self, lead_id: str, status: str, 
                          notes: str = None, 
                          metrics: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        Update lead status and track progress
        
        Args:
            lead_id: Lead ID
            status: New status (contacted, appointment, sale, lost)
            notes: Optional notes
            metrics: Optional metrics to track
            
        Returns:
            Updated lead assignment
        """
        # Check if lead is assigned
        if lead_id not in self.lead_assignments:
            raise ValueError(f"Lead not assigned: {lead_id}")
            
        # Get assignment
        assignment = self.lead_assignments[lead_id]
        agent_id = assignment["agent_id"]
        
        # Update status
        assignment["status"] = status
        assignment["last_updated"] = datetime.datetime.now().isoformat()
        
        # Add notes if provided
        if notes:
            assignment["notes"] = notes
            
        # Add timestamp for the status change
        status_key = f"{status}_at"
        assignment[status_key] = datetime.datetime.now().isoformat()
        
        # Add metrics if provided
        if metrics:
            assignment["metrics"] = assignment.get("metrics", {})
            assignment["metrics"].update(metrics)
            
        # Update agent metrics
        if agent_id in self.agents:
            agent = self.agents[agent_id]
            
            # Update metrics based on status
            if status == "appointment":
                agent["metrics"]["total_appointments"] = agent["metrics"].get("total_appointments", 0) + 1
                
            elif status == "sale":
                agent["metrics"]["total_sales"] = agent["metrics"].get("total_sales", 0) + 1
                
                # Update financial metrics if provided
                if metrics and "annual_premium" in metrics:
                    agent["metrics"]["total_ap"] = agent["metrics"].get("total_ap", 0) + metrics["annual_premium"]
                    
                if metrics and "commission" in metrics:
                    agent["metrics"]["total_commission"] = agent["metrics"].get("total_commission", 0) + metrics["commission"]
                    
                # Update closing ratio
                appointments = max(1, agent["metrics"].get("total_appointments", 1))
                sales = agent["metrics"].get("total_sales", 0)
                agent["metrics"]["closing_ratio"] = sales / appointments
                
        # Save updates
        self.lead_assignments[lead_id] = assignment
        self._save_database()
        
        logger.info(f"Updated lead {lead_id} status to {status}")
        return assignment
        
    # ========================= PERFORMANCE TRACKING =========================
    
    def update_agent_metrics(self, agent_id: str, metrics: Dict[str, Any]) -> Dict[str, Any]:
        """
        Update agent performance metrics
        
        Args:
            agent_id: Agent ID
            metrics: Metrics to update
            
        Returns:
            Updated agent record
        """
        # Check if agent exists
        if agent_id not in self.agents:
            raise ValueError(f"Agent not found: {agent_id}")
            
        # Get agent
        agent = self.agents[agent_id]
        
        # Update metrics
        for metric, value in metrics.items():
            agent["metrics"][metric] = value
            
        # Update last active time
        agent["last_active"] = datetime.datetime.now().isoformat()
        
        # Recalculate derived metrics
        if "total_appointments" in agent["metrics"] and "total_sales" in agent["metrics"]:
            appointments = max(1, agent["metrics"]["total_appointments"])
            sales = agent["metrics"]["total_sales"]
            agent["metrics"]["closing_ratio"] = sales / appointments
            
        # Check if tier should be updated
        current_tier = agent["tier"]
        calculated_tier = self.calculate_agent_tier(agent_id)
        
        if calculated_tier != current_tier:
            agent["tier"] = calculated_tier
            logger.info(f"Agent {agent_id} promoted to {calculated_tier}")
            
        # Save updates
        self.agents[agent_id] = agent
        self._save_database()
        
        logger.info(f"Updated metrics for agent {agent_id}")
        return agent
        
    def get_agent_performance(self, agent_id: str, 
                            time_period: str = "all") -> Dict[str, Any]:
        """
        Get agent performance metrics
        
        Args:
            agent_id: Agent ID
            time_period: Time period (day, week, month, quarter, year, all)
            
        Returns:
            Performance metrics
        """
        # Check if agent exists
        if agent_id not in self.agents:
            raise ValueError(f"Agent not found: {agent_id}")
            
        # Get agent
        agent = self.agents[agent_id]
        
        # Get base metrics
        metrics = agent["metrics"].copy()
        
        # Get lead assignments for this agent
        agent_assignments = {}
        for lead_id, assignment in self.lead_assignments.items():
            if assignment["agent_id"] == agent_id:
                agent_assignments[lead_id] = assignment
                
        # Filter by time period if needed
        if time_period != "all":
            # Calculate cutoff date
            cutoff_date = None
            now = datetime.datetime.now()
            
            if time_period == "day":
                cutoff_date = now - datetime.timedelta(days=1)
            elif time_period == "week":
                cutoff_date = now - datetime.timedelta(days=7)
            elif time_period == "month":
                cutoff_date = now - datetime.timedelta(days=30)
            elif time_period == "quarter":
                cutoff_date = now - datetime.timedelta(days=90)
            elif time_period == "year":
                cutoff_date = now - datetime.timedelta(days=365)
                
            if cutoff_date:
                # Filter assignments by date
                filtered_assignments = {}
                for lead_id, assignment in agent_assignments.items():
                    assigned_at = datetime.datetime.fromisoformat(assignment["assigned_at"])
                    if assigned_at >= cutoff_date:
                        filtered_assignments[lead_id] = assignment
                        
                agent_assignments = filtered_assignments
                
                # Recalculate metrics for the period
                metrics = {
                    "total_leads": len(agent_assignments),
                    "total_appointments": 0,
                    "total_sales": 0,
                    "total_ap": 0.0,
                    "total_commission": 0.0,
                    "closing_ratio": 0.0
                }
                
                # Count status updates
                for assignment in agent_assignments.values():
                    status = assignment.get("status", "")
                    
                    if status == "appointment":
                        metrics["total_appointments"] += 1
                        
                    elif status == "sale":
                        metrics["total_sales"] += 1
                        
                        # Add financial metrics if available
                        if "metrics" in assignment:
                            metrics["total_ap"] += assignment["metrics"].get("annual_premium", 0)
                            metrics["total_commission"] += assignment["metrics"].get("commission", 0)
                            
                # Calculate closing ratio
                if metrics["total_appointments"] > 0:
                    metrics["closing_ratio"] = metrics["total_sales"] / metrics["total_appointments"]
                    
        # Calculate additional metrics
        if metrics["total_leads"] > 0:
            metrics["appointment_rate"] = metrics["total_appointments"] / metrics["total_leads"]
            metrics["lead_to_sale"] = metrics["total_sales"] / metrics["total_leads"]
        else:
            metrics["appointment_rate"] = 0.0
            metrics["lead_to_sale"] = 0.0
            
        if metrics["total_sales"] > 0:
            metrics["average_premium"] = metrics["total_ap"] / metrics["total_sales"]
            metrics["average_commission"] = metrics["total_commission"] / metrics["total_sales"]
        else:
            metrics["average_premium"] = 0.0
            metrics["average_commission"] = 0.0
            
        # Calculate performance percentile
        metrics["percentile"] = self._calculate_performance_percentile(agent_id, metrics)
        
        # Add projections
        metrics["projections"] = self._calculate_projections(metrics, time_period)
        
        return {
            "agent_id": agent_id,
            "name": agent["name"],
            "tier": agent["tier"],
            "time_period": time_period,
            "metrics": metrics,
            "assignments": len(agent_assignments)
        }
        
    def _calculate_performance_percentile(self, agent_id: str, 
                                        metrics: Dict[str, Any]) -> Dict[str, float]:
        """
        Calculate agent's performance percentile compared to other agents
        
        Args:
            agent_id: Agent ID
            metrics: Agent's metrics
            
        Returns:
            Dictionary with percentile scores
        """
        # Get all agents
        active_agents = self.list_agents(status="active")
        if len(active_agents) <= 1:
            return {
                "total_ap": 1.0,
                "closing_ratio": 1.0,
                "lead_to_sale": 1.0,
                "overall": 1.0
            }
            
        # Calculate percentiles for key metrics
        ap_scores = [a["metrics"].get("total_ap", 0) for a in active_agents]
        closing_scores = [a["metrics"].get("closing_ratio", 0) for a in active_agents]
        
        # Sort scores
        ap_scores.sort()
        closing_scores.sort()
        
        # Find percentiles
        agent_ap = metrics.get("total_ap", 0)
        agent_closing = metrics.get("closing_ratio", 0)
        
        ap_percentile = sum(1 for x in ap_scores if x <= agent_ap) / len(ap_scores)
        closing_percentile = sum(1 for x in closing_scores if x <= agent_closing) / len(closing_scores)
        lead_to_sale = metrics.get("lead_to_sale", 0)
        
        # Calculate overall performance percentile
        overall_percentile = (ap_percentile + closing_percentile) / 2
        
        return {
            "total_ap": ap_percentile,
            "closing_ratio": closing_percentile,
            "lead_to_sale": lead_to_sale,
            "overall": overall_percentile
        }
        
    def _calculate_projections(self, metrics: Dict[str, Any], 
                             time_period: str) -> Dict[str, float]:
        """
        Calculate performance projections
        
        Args:
            metrics: Current metrics
            time_period: Time period
            
        Returns:
            Dictionary with projections
        """
        # Create projection factors based on time period
        factors = {
            "day": 365,
            "week": 52,
            "month": 12,
            "quarter": 4,
            "year": 1,
            "all": 1  # No projection for all-time stats
        }
        
        # Get projection factor
        factor = factors.get(time_period, 1)
        
        # Calculate projections
        projected_ap = metrics.get("total_ap", 0) * factor
        projected_commission = metrics.get("total_commission", 0) * factor
        projected_sales = metrics.get("total_sales", 0) * factor
        
        return {
            "annual_premium": projected_ap,
            "annual_commission": projected_commission,
            "annual_sales": projected_sales
        }
    
    def get_team_performance(self, time_period: str = "month") -> Dict[str, Any]:
        """
        Get performance metrics for the entire team
        
        Args:
            time_period: Time period (day, week, month, quarter, year, all)
            
        Returns:
            Team performance metrics
        """
        # Get all active agents
        active_agents = self.list_agents(status="active")
        
        # Initialize metrics
        team_metrics = {
            "total_agents": len(active_agents),
            "total_leads": 0,
            "total_appointments": 0,
            "total_sales": 0,
            "total_ap": 0.0,
            "total_commission": 0.0,
            "average_closing_ratio": 0.0,
            "average_lead_to_sale": 0.0,
            "top_performers": []
        }
        
        # Collect agent metrics
        closing_ratios = []
        lead_to_sale_ratios = []
        
        for agent in active_agents:
            # Get agent performance
            performance = self.get_agent_performance(agent["id"], time_period)
            metrics = performance["metrics"]
            
            # Add to team totals
            team_metrics["total_leads"] += metrics.get("total_leads", 0)
            team_metrics["total_appointments"] += metrics.get("total_appointments", 0)
            team_metrics["total_sales"] += metrics.get("total_sales", 0)
            team_metrics["total_ap"] += metrics.get("total_ap", 0)
            team_metrics["total_commission"] += metrics.get("total_commission", 0)
            
            # Track individual metrics for averages
            closing_ratios.append(metrics.get("closing_ratio", 0))
            lead_to_sale_ratios.append(metrics.get("lead_to_sale", 0))
            
        # Calculate averages
        if closing_ratios:
            team_metrics["average_closing_ratio"] = sum(closing_ratios) / len(closing_ratios)
            
        if lead_to_sale_ratios:
            team_metrics["average_lead_to_sale"] = sum(lead_to_sale_ratios) / len(lead_to_sale_ratios)
            
        # Calculate team closing ratio
        if team_metrics["total_appointments"] > 0:
            team_metrics["team_closing_ratio"] = team_metrics["total_sales"] / team_metrics["total_appointments"]
        else:
            team_metrics["team_closing_ratio"] = 0.0
            
        # Calculate lead to sale ratio
        if team_metrics["total_leads"] > 0:
            team_metrics["team_lead_to_sale"] = team_metrics["total_sales"] / team_metrics["total_leads"]
        else:
            team_metrics["team_lead_to_sale"] = 0.0
            
        # Find top performers (top 20% or top 3, whichever is larger)
        top_count = max(3, int(len(active_agents) * 0.2))
        
        # Sort agents by AP
        sorted_agents = sorted(active_agents, 
                            key=lambda a: a["metrics"].get("total_ap", 0), 
                            reverse=True)
        
        # Get top performers
        top_performers = sorted_agents[:top_count]
        team_metrics["top_performers"] = [
            {
                "id": a["id"],
                "name": a["name"],
                "tier": a["tier"],
                "total_ap": a["metrics"].get("total_ap", 0),
                "closing_ratio": a["metrics"].get("closing_ratio", 0)
            }
            for a in top_performers
        ]
        
        # Add projections
        team_metrics["projections"] = self._calculate_projections(team_metrics, time_period)
        
        return team_metrics
        
    # ========================= CARRIER INTEGRATION =========================
    
    def get_available_carriers(self) -> List[Dict[str, Any]]:
        """
        Get list of available insurance carriers
        
        Returns:
            List of carrier information
        """
        if "carrier_access" not in self.components:
            logger.warning("Carrier access component not available")
            return []
            
        carrier_access = self.components["carrier_access"]
        carriers = []
        
        # Get carriers from carrier access component
        carrier_list = carrier_access.list_carriers()
        
        for carrier_name in carrier_list:
            carrier_info = carrier_access.get_carrier_info(carrier_name)
            if carrier_info:
                carriers.append(carrier_info)
                
        return carriers
        
    def get_carrier_for_lead(self, lead: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        Get recommended carriers for a lead
        
        Args:
            lead: Lead information
            
        Returns:
            List of recommended carriers
        """
        if "carrier_access" not in self.components:
            logger.warning("Carrier access component not available")
            return []
            
        # Get available carriers
        carriers = self.get_available_carriers()
        if not carriers:
            return []
            
        # Get lead product interest
        product_interest = lead.get("product_interest", "").lower()
        
        # Find matching carriers
        matches = []
        for carrier in carriers:
            products = [p.lower() for p in carrier.get("products", [])]
            
            # Check if carrier offers this product
            if any(product_interest in p for p in products):
                matches.append(carrier)
                
        # If no specific matches, return all carriers
        if not matches:
            return carriers
            
        return matches
        
    def submit_application(self, lead_id: str, carrier_name: str, 
                         application_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Submit insurance application to carrier
        
        Args:
            lead_id: Lead ID
            carrier_name: Carrier name
            application_data: Application data
            
        Returns:
            Submission results
        """
        if "carrier_access" not in self.components:
            logger.warning("Carrier access component not available")
            return {"success": False, "error": "Carrier access not available"}
            
        # Check if lead is assigned
        if lead_id not in self.lead_assignments:
            raise ValueError(f"Lead not assigned: {lead_id}")
            
        # Get carrier access component
        carrier_access = self.components["carrier_access"]
        
        # TODO: Implement actual carrier submission
        # This would connect to the carrier's API
        
        # For now, simulate submission
        submission_id = f"sub_{int(time.time())}_{hashlib.md5(lead_id.encode()).hexdigest()[:8]}"
        
        # Update lead assignment status
        assignment = self.lead_assignments[lead_id]
        assignment["status"] = "submitted"
        assignment["submission"] = {
            "carrier": carrier_name,
            "submission_id": submission_id,
            "submitted_at": datetime.datetime.now().isoformat(),
            "status": "pending"
        }
        
        # Save updates
        self.lead_assignments[lead_id] = assignment
        self._save_database()
        
        logger.info(f"Submitted application for lead {lead_id} to {carrier_name}")
        return {
            "success": True,
            "lead_id": lead_id,
            "carrier": carrier_name,
            "submission_id": submission_id,
            "status": "pending"
        }
        
    # ========================= SYSTEM METHODS =========================
    
    def check_system_status(self) -> Dict[str, Any]:
        """
        Check the status of all system components
        
        Returns:
            System status information
        """
        # Check component status
        component_status = {
            "leads_system": "leads_system" in self.components,
            "agent_system": "agent_system" in self.components,
            "carrier_access": "carrier_access" in self.components
        }
        
        # Count database items
        db_counts = {
            "agents": len(self.agents),
            "lead_assignments": len(self.lead_assignments)
        }
        
        # Count active agents
        active_agents = len(self.list_agents(status="active"))
        
        # Get lead counts if leads system available
        lead_counts = {}
        if "leads_system" in self.components:
            leads_system = self.components["leads_system"]
            lead_counts = {
                "total_leads": len(leads_system.leads),
                "unassigned_leads": len(self.get_available_leads(max_count=10000))
            }
            
        status = {
            "system_status": "operational",
            "component_status": component_status,
            "database_counts": db_counts,
            "active_agents": active_agents,
            "lead_counts": lead_counts,
            "check_time": datetime.datetime.now().isoformat()
        }
        
        return status
        
    def run_simulation(self, agent_count: int = 5, days: int = 30, 
                     leads_per_day: int = 20) -> Dict[str, Any]:
        """
        Run a simulation of the entire system
        
        Args:
            agent_count: Number of agents to simulate
            days: Number of days to simulate
            leads_per_day: Leads per day
            
        Returns:
            Simulation results
        """
        logger.info(f"Starting simulation with {agent_count} agents over {days} days")
        
        # Reset system
        self.agents = {}
        self.lead_assignments = {}
        self.performance_metrics = {}
        
        # Check leads system
        if "leads_system" not in self.components:
            return {"success": False, "error": "Leads system not available"}
            
        leads_system = self.components["leads_system"]
        
        # Create agents
        agent_tiers = ["new_agent", "standard_agent", "top_performer"]
        agent_specialties = [
            ["IUL", "Term Life"],
            ["Medicare", "Medicare Supplement"],
            ["IUL", "Annuities"],
            ["Term Life", "Final Expense"],
            ["IUL", "Term Life", "Final Expense"]
        ]
        
        for i in range(agent_count):
            tier = agent_tiers[i % len(agent_tiers)]
            specialties = agent_specialties[i % len(agent_specialties)]
            
            agent = self.register_agent(
                name=f"Agent {i+1}",
                email=f"agent{i+1}@example.com",
                phone=f"555-{i+1:03d}-{i+1:04d}",
                specialties=specialties,
                tier=tier
            )
            
            logger.info(f"Created agent: {agent['name']} (ID: {agent['id']})")
            
        # Create campaign
        campaign = leads_system.create_campaign(
            name="Simulation Campaign",
            target_audience="high_net_worth",
            channels=["social_media.linkedin", "content_marketing.webinars"],
            budget=5000.0,
            start_date=datetime.datetime.now().strftime("%Y-%m-%d"),
            end_date=(datetime.datetime.now() + datetime.timedelta(days=days)).strftime("%Y-%m-%d")
        )
        
        # Launch campaign
        leads_system.launch_campaign(campaign['id'])
        logger.info(f"Launched campaign: {campaign['name']}")
        
        # Run daily simulation
        results = {
            "days": days,
            "total_leads": 0,
            "total_appointments": 0,
            "total_sales": 0,
            "total_premium": 0.0,
            "daily_results": []
        }
        
        for day in range(days):
            # Generate leads for the day
            day_leads = leads_system.generate_leads(
                campaign['id'], 
                days=1, 
                organic_ratio=0.4
            )
            
            # Get leads
            leads = leads_system.leads[-leads_per_day:]
            
            # Qualify leads
            qualification = leads_system.qualify_leads(leads)
            qualified_leads = qualification["qualified_leads"]
            
            # Distribute leads
            if qualified_leads:
                distribution = self.distribute_leads(qualified_leads)
                
                # Process each assigned lead
                for lead_id, agent_id in distribution["assignments"].items():
                    # Simulate lead processing by agent
                    lead = next((l for l in leads if l["id"] == lead_id), None)
                    if not lead:
                        continue
                        
                    # Determine if appointment set (based on lead quality)
                    quality_score = lead.get("quality_score", 50)
                    appointment_probability = min(0.8, quality_score / 100)
                    
                    if random.random() < appointment_probability:
                        # Set appointment
                        self.track_lead_progress(lead_id, "appointment")
                        
                        # Determine if sale made (based on agent tier)
                        agent = self.agents[agent_id]
                        tier_close_rates = {
                            "new_agent": 0.2,
                            "standard_agent": 0.3,
                            "top_performer": 0.5
                        }
                        close_probability = tier_close_rates.get(agent["tier"], 0.3)
                        
                        if random.random() < close_probability:
                            # Calculate sale metrics
                            product = lead.get("product_interest", "IUL")
                            premium = random.uniform(5000, 15000)
                            commission_rate = 0.7  # 70% commission
                            
                            # Record sale
                            self.track_lead_progress(
                                lead_id, 
                                "sale",
                                metrics={
                                    "annual_premium": premium,
                                    "commission": premium * commission_rate,
                                    "product": product
                                }
                            )
                            
                            # Track in results
                            results["total_sales"] += 1
                            results["total_premium"] += premium
                            
                        # Track in results
                        results["total_appointments"] += 1
                
            # Track daily results
            day_result = {
                "day": day + 1,
                "leads_generated": len(leads),
                "qualified_leads": len(qualified_leads) if qualified_leads else 0,
                "appointments": sum(1 for a in self.lead_assignments.values() if a.get("status") == "appointment"),
                "sales": sum(1 for a in self.lead_assignments.values() if a.get("status") == "sale"),
                "premium": sum(a.get("metrics", {}).get("annual_premium", 0) for a in self.lead_assignments.values() if a.get("status") == "sale")
            }
            
            results["daily_results"].append(day_result)
            results["total_leads"] += day_result["leads_generated"]
            
            logger.info(f"Day {day+1}: {day_result['leads_generated']} leads, {day_result['qualified_leads']} qualified, {day_result['appointments']} appointments, {day_result['sales']} sales")
            
        # Calculate final team performance
        team_performance = self.get_team_performance()
        results["team_performance"] = team_performance
        
        return results


# Main execution
def main():
    """Main execution"""
    print("\nPremium Agent Hub")
    print("----------------")
    
    # Create hub
    hub = PremiumAgentHub()
    
    # Check system status
    status = hub.check_system_status()
    print("\nSystem Status:")
    print(f"- System: {status['system_status']}")
    for component, available in status["component_status"].items():
        print(f"- {component}: {'Available' if available else 'Not available'}")
        
    # Run simulation if components available
    if status["component_status"]["leads_system"]:
        print("\nRunning simulation...")
        results = hub.run_simulation(agent_count=5, days=30, leads_per_day=20)
        
        print("\nSimulation Results:")
        print(f"- Total Leads: {results['total_leads']}")
        print(f"- Total Appointments: {results['total_appointments']}")
        print(f"- Total Sales: {results['total_sales']}")
        print(f"- Total Premium: ${results['total_premium']:.2f}")
        
        # Calculate key metrics
        appointment_rate = results['total_appointments'] / results['total_leads'] if results['total_leads'] > 0 else 0
        closing_rate = results['total_sales'] / results['total_appointments'] if results['total_appointments'] > 0 else 0
        lead_to_sale = results['total_sales'] / results['total_leads'] if results['total_leads'] > 0 else 0
        
        print("\nKey Metrics:")
        print(f"- Appointment Rate: {appointment_rate:.1%}")
        print(f"- Closing Rate: {closing_rate:.1%}")
        print(f"- Lead to Sale: {lead_to_sale:.1%}")
        print(f"- Average Premium: ${results['total_premium']/max(1, results['total_sales']):.2f}")
        
        print("\nSystem ready to scale to $250,000-$500,000 weekly AP")
    else:
        print("\nSimulation not available - leads system component missing")
        
    print("\nThe Premium Agent Hub provides a complete ecosystem for insurance agents to thrive,")
    print("with industry-leading lead generation, performance tracking, and carrier integrations.")


if __name__ == "__main__":
    main()