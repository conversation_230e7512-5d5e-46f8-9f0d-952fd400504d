version: '3.8'

services:
  # Agent System
  agent-system:
    build:
      context: .
      dockerfile: Dockerfile
    ports:
      - "8000:8000"
    environment:
      - POSTGRES_HOST=postgres
      - POSTGRES_PORT=5432
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - MESSAGE_BROKER_HOST=rabbitmq
      - MESSAGE_BROKER_PORT=5672
    volumes:
      - ./config:/opt/agent-system/config
      - ./data:/opt/agent-system/data
      - ./plugins:/opt/agent-system/plugins
      - ./logs:/var/log
    depends_on:
      - postgres
      - redis
      - rabbitmq
    networks:
      - agent-network

  # Database
  postgres:
    image: postgres:14-alpine
    ports:
      - "5432:5432"
    environment:
      POSTGRES_DB: agent_system
      POSTGRES_USER: agent_system
      POSTGRES_PASSWORD: ${DB_PASSWORD:-changeme}
    volumes:
      - postgres_data:/var/lib/postgresql/data
    networks:
      - agent-network

  # NVIDIA NIM Server
  nvidia-nim-server:
    build:
      context: .
      dockerfile: Dockerfile
    ports:
      - "8006:8006" # Port for the NIM MCP server
    environment:
      - NVIDIA_VISIBLE_DEVICES=all # Expose all GPUs to the container
      - NVIDIA_DRIVER_CAPABILITIES=all # Expose all driver capabilities
    volumes:
      - ./mcp_servers:/app/mcp_servers # Mount the server code
      - ./data:/app/data # Mount data for models if needed
    command: ["python", "mcp_servers/nvidia_nim_mcp_server.py"]
    networks:
      - agent-network
    depends_on:
      - agent-system # Depends on agent-system if it needs to interact with it on startup
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U agent_system"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Cache
  redis:
    image: redis:6-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - agent-network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Message Broker
  rabbitmq:
    image: rabbitmq:3.9-management-alpine
    ports:
      - "5672:5672"
      - "15672:15672"
    environment:
      - RABBITMQ_DEFAULT_USER=agent_system
      - RABBITMQ_DEFAULT_PASS=${RABBITMQ_PASSWORD:-changeme}
    volumes:
      - rabbitmq_data:/var/lib/rabbitmq
    networks:
      - agent-network
    healthcheck:
      test: ["CMD", "rabbitmq-diagnostics", "check_port_connectivity"]
      interval: 30s
      timeout: 10s
      retries: 5

  # Monitoring Stack
  prometheus:
    image: prom/prometheus:latest
    ports:
      - "9090:9090"
    volumes:
      - ./config/prometheus:/etc/prometheus
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/usr/share/prometheus/console_libraries'
      - '--web.console.templates=/usr/share/prometheus/consoles'
    networks:
      - agent-network
    depends_on:
      - agent-system

  grafana:
    image: grafana/grafana:latest
    ports:
      - "3000:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=${GRAFANA_PASSWORD:-admin}
      - GF_USERS_ALLOW_SIGN_UP=false
    volumes:
      - ./config/grafana:/etc/grafana
      - grafana_data:/var/lib/grafana
    networks:
      - agent-network
    depends_on:
      - prometheus
      - loki
      - tempo

  loki:
    image: grafana/loki:latest
    ports:
      - "3100:3100"
    volumes:
      - loki_data:/loki
    command: -config.file=/etc/loki/local-config.yaml
    networks:
      - agent-network

  tempo:
    image: grafana/tempo:latest
    ports:
      - "3200:3200"
      - "4317:4317"
    volumes:
      - ./config/tempo:/etc/tempo
      - tempo_data:/tmp/tempo
    command: -config.file=/etc/tempo/tempo.yaml
    networks:
      - agent-network

  promtail:
    image: grafana/promtail:latest
    volumes:
      - ./logs:/var/log
      - ./config/promtail:/etc/promtail
    command: -config.file=/etc/promtail/config.yml
    networks:
      - agent-network
    depends_on:
      - loki

  node-exporter:
    image: prom/node-exporter:latest
    volumes:
      - /proc:/host/proc:ro
      - /sys:/host/sys:ro
      - /:/rootfs:ro
    command:
      - '--path.procfs=/host/proc'
      - '--path.sysfs=/host/sys'
      - '--collector.filesystem.ignored-mount-points=^/(sys|proc|dev|host|etc)($$|/)'
    networks:
      - agent-network

networks:
  agent-network:
    driver: bridge

volumes:
  postgres_data:
  redis_data:
  rabbitmq_data:
  prometheus_data:
  grafana_data:
  loki_data:
  tempo_data: