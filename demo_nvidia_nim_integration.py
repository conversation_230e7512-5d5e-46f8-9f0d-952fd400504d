import asyncio
import logging
from mcp_client import get_client_for_server_id
from mcp_server_registry import registry

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def demo_nvidia_nim_integration():
    """
    Demonstrates interaction with the NVIDIA NIM MCP server.
    """
    logger.info("Starting NVIDIA NIM integration demo...")

    # Ensure the MCP server registry is initialized
    if not registry.initialized:
        await registry.initialize()
        logger.info("MCP Server Registry initialized.")

    # Get the NVIDIA NIM client
    nim_client = await get_client_for_server_id("nvidia-nim")

    if not nim_client:
        logger.error("NVIDIA NIM MCP server client not found. Is the server running and registered?")
        return

    logger.info(f"Connected to NVIDIA NIM server at {nim_client.url}")

    try:
        # 1. Load a model
        logger.info("Attempting to load an example NIM model...")
        load_result = await nim_client.execute_tool(
            "load_model",
            {"model_name": "my_awesome_llm", "model_path": "/models/llm/my_awesome_llm"}
        )
        logger.info(f"Load model result: {load_result}")

        if load_result.get("status") != "success" and load_result.get("status") != "already_loaded":
            logger.error("Failed to load model. Aborting demo.")
            return

        # 2. Perform inference
        logger.info("Attempting to perform inference with the loaded model...")
        inference_data = {"text": "What is the capital of France?", "max_tokens": 50}
        inference_result = await nim_client.execute_tool(
            "infer",
            {"model_name": "my_awesome_llm", "data": inference_data}
        )
        logger.info(f"Inference result: {inference_result}")

        # 3. Get list of loaded models
        logger.info("Getting list of loaded models...")
        models_list = await nim_client.execute_tool("get_models", {})
        logger.info(f"Loaded models: {models_list}")

        # 4. Unload the model
        logger.info("Attempting to unload the example NIM model...")
        unload_result = await nim_client.execute_tool(
            "unload_model",
            {"model_name": "my_awesome_llm"}
        )
        logger.info(f"Unload model result: {unload_result}")

    except Exception as e:
        logger.error(f"An error occurred during NIM integration demo: {e}")

if __name__ == "__main__":
    asyncio.run(demo_nvidia_nim_integration())