"""
Direct Email to <PERSON> - Simple and Reliable

This script sends an email directly to <PERSON> using straightforward SMTP
without relying on complex dependencies or external services.
"""

import os
import smtplib
import ssl
from email.mime.text import MIMEText
from email.mime.multipart import MI<PERSON><PERSON><PERSON><PERSON><PERSON>
from datetime import datetime

# Gmail credentials - For security, prefer environment variables
GMAIL_EMAIL = "<EMAIL>"  # Replace with your actual Gmail
GMAIL_APP_PASSWORD = os.environ.get("GMAIL_APP_PASSWORD")  # Set this environment variable

def send_direct_email(subject, message_text, recipient="<EMAIL>"):
    """
    Send a simple email directly to <PERSON> or other recipient
    
    Args:
        subject: Email subject line
        message_text: Plain text email body
        recipient: Email recipient (default: <PERSON>)
        
    Returns:
        tuple: (success_boolean, message)
    """
    # Allow manual password entry if environment variable not set
    app_password = GMAIL_APP_PASSWORD
    if not app_password:
        print("\nGMAIL_APP_PASSWORD environment variable not set.")
        app_password = input("Enter your Gmail App Password to proceed: ")
        if not app_password:
            return False, "ERROR: No password provided"

    try:
        # Create message
        message = MIMEMultipart("alternative")
        message["Subject"] = subject
        message["From"] = f"Flo Faction <{GMAIL_EMAIL}>"
        message["To"] = recipient
        
        # Create both plain text and simple HTML versions
        text_part = MIMEText(message_text, "plain")
        html_part = MIMEText(f"<html><body><p>{message_text.replace(chr(10), '<br>')}</p></body></html>", "html")
        
        message.attach(text_part)
        message.attach(html_part)
        
        # Send email using SSL for security
        context = ssl.create_default_context()
        with smtplib.SMTP_SSL("smtp.gmail.com", 465, context=context) as server:
            server.login(GMAIL_EMAIL, app_password)
            server.send_message(message)
        
        return True, f"Email successfully sent to {recipient}"
        
    except Exception as e:
        return False, f"Error sending email: {str(e)}"

def test_email_to_paul():
    """Send a test email to Paul Edwards to confirm the system works"""
    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    
    subject = f"System Test Email - {timestamp}"
    message = f"""Hello Paul Edwards,

This is a test email from your AI Agent system at {timestamp}.

This email confirms that the direct email functionality in your AI Agent system is working correctly.

Issues fixed:
1. MCP server registration and health tracking
2. Agent-MCP integration
3. Email delivery functionality

If you're receiving this message, it means the system is now properly configured.

Best regards,
Your AI Agent System
"""
    
    success, message = send_direct_email(subject, message)
    print(message)
    return success

def setup_email_instructions():
    """Print instructions for setting up the email integration"""
    print("\n" + "="*80)
    print("EMAIL SETUP INSTRUCTIONS")
    print("="*80)
    
    print("""
To set up this email system:

1. Generate a Gmail App Password:
   - Go to your Google Account settings: https://myaccount.google.com/
   - Click on "Security" in the left sidebar
   - Under "Signing in to Google," click on "2-Step Verification" (must be enabled)
   - Scroll to the bottom and click on "App passwords"
   - Select "Mail" as the app and "Other" as the device
   - Enter "AI Agent System" as the name
   - Click "Generate"
   - Copy the 16-character password shown

2. Set the App Password as an environment variable:
   - On macOS/Linux, run in terminal:
     export GMAIL_APP_PASSWORD="your_16_character_app_password"
   
   - To make it permanent, add to your ~/.bash_profile or ~/.zshrc:
     echo 'export GMAIL_APP_PASSWORD="your_16_character_app_password"' >> ~/.zshrc
     
   - On Windows, use System Properties > Environment Variables
     or run in Command Prompt:
     setx GMAIL_APP_PASSWORD "your_16_character_app_password"

3. Update the email address if needed:
   - Open this script and modify the GMAIL_EMAIL variable
   - Ensure you have access to this Gmail account

4. Test the email system:
   - Run this script to verify it works
   - Check that Paul Edwards receives the test email
""")
    print("="*80 + "\n")

if __name__ == "__main__":
    # Check if Gmail app password is set
    if not GMAIL_APP_PASSWORD:
        print("WARNING: GMAIL_APP_PASSWORD environment variable not set!")
        print("You'll be prompted to enter the password when sending an email.")
        setup_email_instructions()
        choice = input("Do you want to send a test email to Paul Edwards? (y/n): ")
        
        if choice.lower() == 'y':
            # Send test email to Paul Edwards
            print("Sending test email to Paul Edwards...")
            if test_email_to_paul():
                print("Email system is working correctly!")
            else:
                print("Email system is not working. Please check the setup instructions.")
        else:
            print("Exiting without sending email.")
    else:
        # Send test email to Paul Edwards
        print("Sending test email to Paul Edwards...")
        if test_email_to_paul():
            print("Email system is working correctly!")
        else:
            print("Email system is not working. Please check the setup instructions.")
            setup_email_instructions()