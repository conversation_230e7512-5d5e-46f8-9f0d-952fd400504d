# <PERSON> OpenAI Agent

This system uses the OpenAI Agents SDK to create an agent that can communicate with <PERSON> through email with audio attachments. This approach bypasses the need for Twilio authentication by using OpenAI's voice capabilities directly.

## Features

- Generate audio using OpenAI's text-to-speech capabilities
- Send emails with audio attachments
- Provide personalized insurance information to <PERSON>
- No Twilio authentication required

## Installation

1. Install the required dependencies:
   ```
   pip install -r openai_agent_requirements.txt
   ```

2. Set up your environment variables in the `.env` file:
   ```
   # OpenAI API key
   OPENAI_API_KEY=your_openai_api_key

   # Email credentials
   EMAIL_SENDER=<EMAIL>
   EMAIL_PASSWORD=your_app_password_here

   # <PERSON> contact information
   PAUL_FIRST_NAME=Paul
   PAUL_LAST_NAME=Edwards
   PAUL_EMAIL=<EMAIL>
   PAUL_PRIMARY_PHONE=+17722089646
   PAUL_SECONDARY_PHONE=+17725395908

   # Agent information
   AGENT_NAME=<PERSON>
   AGENT_AGENCY=Flo Faction Insurance
   AGENT_EMAIL=<EMAIL>
   AGENT_WEBSITE=https://www.flofaction.com/insurance
   ```

## Usage

Run the OpenAI Agent script:

```
python paul_edwards_openai_agent.py
```

The agent will:
1. Create an insurance agent with tools for getting client information, insurance product details, generating quotes, and sending emails
2. Generate an audio message using OpenAI's text-to-speech capabilities
3. Send an email to Paul Edwards with the audio attachment

## How It Works

### Agent Tools

The agent has several tools at its disposal:

1. `get_client_info()`: Retrieves information about Paul Edwards
2. `get_insurance_product_info(product_type)`: Gets details about specific insurance products
3. `generate_personalized_quote()`: Creates a personalized insurance quote for Paul Edwards
4. `send_email_with_audio(subject, message, audio_text)`: Sends an email with an audio attachment

### Voice Generation

The system uses OpenAI's text-to-speech capabilities through the Agents SDK to generate natural-sounding audio messages. This is done using the `VoicePipeline` and `SingleAgentVoiceWorkflow` classes.

### Email Sending

The system sends emails with audio attachments using SMTP. It automatically detects the appropriate SMTP server based on the sender's email domain.

## Gmail App Password Setup

If you're using Gmail, you'll need to create an App Password:

1. Go to your Google Account settings
2. Navigate to Security > 2-Step Verification
3. At the bottom, click on "App passwords"
4. Select "Mail" and "Other (Custom name)"
5. Enter a name like "Flo Faction Email System"
6. Click "Generate"
7. Use the generated 16-character password as your `EMAIL_PASSWORD`

## Troubleshooting

### Audio Generation Issues

- Check that your OpenAI API key is valid
- Ensure you have internet connectivity
- Check the console output for specific error messages

### Email Sending Issues

- If SMTP fails, check your email credentials
- For Gmail, make sure you're using an App Password, not your regular password
- Check the console output for specific error messages

## Notes

- The audio files are saved in the `audio/` directory
- The script automatically creates this directory if it doesn't exist
- This approach is more reliable than using Twilio as it doesn't require phone number verification
