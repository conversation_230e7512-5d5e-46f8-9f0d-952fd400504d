#!/usr/bin/env python3
"""
Improved script to move large files and applications to your external G Drive,
handling permission issues by using AppleScript and rsync.
"""

import os
import sys
import subprocess
import time
import shutil
from pathlib import Path

# External drive path
EXTERNAL_DRIVE = Path("/Volumes/G Drive")

def check_external_drive():
    """Check if the external drive is connected."""
    if not EXTERNAL_DRIVE.exists():
        print(f"ERROR: External drive not found at {EXTERNAL_DRIVE}")
        print("Please make sure your G Drive is connected and try again.")
        return False
    print(f"External drive found at {EXTERNAL_DRIVE}")
    return True

def open_in_finder(path):
    """Open a path in Finder."""
    try:
        print(f"Opening {path} in Finder...")
        subprocess.run(["open", path])
        return True
    except Exception as e:
        print(f"Error opening {path}: {e}")
        return False

def create_folder_on_external(folder_name):
    """Create a folder on the external drive."""
    folder_path = EXTERNAL_DRIVE / folder_name
    
    if folder_path.exists():
        print(f"Folder already exists: {folder_path}")
        return True
    
    try:
        print(f"Creating folder: {folder_path}")
        os.makedirs(folder_path, exist_ok=True)
        return True
    except Exception as e:
        print(f"Error creating folder with mkdir: {e}")
        
        # Try with AppleScript as fallback
        try:
            script = f'''
            tell application "Finder"
                make new folder at disk "G Drive" with properties {{name:"{folder_name}"}}
            end tell
            '''
            subprocess.run(["osascript", "-e", script], capture_output=True)
            
            if (EXTERNAL_DRIVE / folder_name).exists():
                print(f"Created folder using AppleScript: {folder_name}")
                return True
        except Exception as e:
            print(f"Error creating folder with AppleScript: {e}")
        
        print("Could not create folder automatically.")
        print(f"Please create folder '{folder_name}' on your G Drive manually.")
        
        # Ask user to create the folder manually
        open_in_finder(EXTERNAL_DRIVE)
        return False

def get_folder_size(path):
    """Get the size of a folder."""
    try:
        result = subprocess.run(["du", "-sh", path], capture_output=True, text=True)
        if result.returncode == 0:
            return result.stdout.split()[0]
        return "unknown size"
    except:
        return "unknown size"

def copy_with_rsync(src, dest):
    """Copy files using rsync, which preserves permissions and handles errors better."""
    print(f"Copying {src} to {dest}...")
    
    try:
        # Run rsync with archive mode, verbose output, and human-readable sizes
        cmd = ["rsync", "-avh", str(src) + "/", str(dest) + "/"]
        process = subprocess.Popen(
            cmd, 
            stdout=subprocess.PIPE, 
            stderr=subprocess.PIPE,
            universal_newlines=True
        )
        
        # Monitor progress
        for line in process.stdout:
            print(line, end='')
        
        # Wait for completion
        process.wait()
        
        if process.returncode != 0:
            print("rsync encountered errors:")
            for line in process.stderr:
                print(line, end='')
            return False
        
        print(f"Successfully copied {src} to {dest}")
        return True
    except Exception as e:
        print(f"Error copying files: {e}")
        return False

def copy_with_ditto(src, dest):
    """Copy files using ditto, which works better with macOS file attributes."""
    print(f"Copying {src} to {dest} using ditto...")
    
    try:
        # Run ditto to copy files
        cmd = ["ditto", "-v", str(src), str(dest)]
        process = subprocess.Popen(
            cmd, 
            stdout=subprocess.PIPE, 
            stderr=subprocess.PIPE,
            universal_newlines=True
        )
        
        # Monitor progress
        for line in process.stdout:
            print(line, end='')
        
        # Wait for completion
        process.wait()
        
        if process.returncode != 0:
            print("ditto encountered errors:")
            for line in process.stderr:
                print(line, end='')
            return False
        
        print(f"Successfully copied {src} to {dest}")
        return True
    except Exception as e:
        print(f"Error copying files with ditto: {e}")
        return False

def move_large_app(app_path, app_name=None):
    """Copy a large application to external drive using multiple methods."""
    path = Path(app_path)
    if not path.exists():
        print(f"Application not found: {app_path}")
        return False
    
    if app_name is None:
        app_name = path.name
    
    # Create Applications folder on external drive
    dest_folder = EXTERNAL_DRIVE / "Applications"
    if not create_folder_on_external("Applications"):
        print("Could not create Applications folder on external drive.")
        return False
    
    app_dest = dest_folder / app_name
    
    # Check if app already exists on external drive
    if app_dest.exists():
        print(f"{app_name} already exists on external drive.")
        return True
    
    print(f"Moving {app_name} to external drive...")
    print(f"This will take some time. Please be patient.")
    
    # Try first with ditto (works well for macOS apps)
    success = copy_with_ditto(app_path, app_dest)
    
    # If ditto fails, try with rsync 
    if not success:
        print("Trying alternative copy method...")
        success = copy_with_rsync(app_path, app_dest)
    
    # If automated methods fail, help user do it manually
    if not success:
        print("\nAutomated copy failed. Let's try manual copy:")
        print("1. Two Finder windows will open.")
        print("2. Drag the application from the source window to the destination.")
        
        # Open both locations in Finder
        open_in_finder(app_path)
        open_in_finder(dest_folder)
        
        # Wait for manual copy
        print("\nPress Enter when manual copy is complete...")
        input()
        
        # Check if the app now exists on external drive
        if app_dest.exists():
            print(f"Confirmed: {app_name} now exists on external drive.")
            success = True
        else:
            print(f"Could not verify {app_name} was copied to external drive.")
            success = False
    
    return success

def setup_model_folders():
    """Set up folders for model files on external drive."""
    # Create Models folder on external drive
    if not create_folder_on_external("Models"):
        return False
    
    # Create subdirectories for different model types
    for subdir in ["phi-2", "llama", "mixtral"]:
        create_folder_on_external(f"Models/{subdir}")
    
    print("\nCreated 'Models' folder structure on your external drive.")
    print("You can now store your language models in these folders.")
    
    # Open the Models folder in Finder
    open_in_finder(EXTERNAL_DRIVE / "Models")
    
    return True

def setup_local_config_for_external_models():
    """Set up local configuration to use models from external drive."""
    config_dir = Path.home() / ".config" / "llm_models"
    
    # Create config directory if it doesn't exist
    os.makedirs(config_dir, exist_ok=True)
    
    # Create basic configuration pointing to external models
    config = {
        "models_dir": str(EXTERNAL_DRIVE / "Models"),
        "phi-2": {
            "path": str(EXTERNAL_DRIVE / "Models/phi-2"),
            "type": "phi"
        },
        "mixtral": {
            "path": str(EXTERNAL_DRIVE / "Models/mixtral"),
            "type": "mixtral" 
        },
        "llama": {
            "path": str(EXTERNAL_DRIVE / "Models/llama"),
            "type": "llama"
        }
    }
    
    # Write config file
    config_file = config_dir / "models_config.json"
    with open(config_file, "w") as f:
        import json
        json.dump(config, f, indent=2)
    
    print(f"Created configuration file at {config_file}")
    print("This will allow your applications to find models on the external drive.")
    
    return True

def move_large_downloads():
    """Move large files from Downloads folder to external drive."""
    downloads = Path.home() / "Downloads"
    if not downloads.exists():
        print("Downloads folder not found.")
        return False
    
    # Create Downloads folder on external drive
    if not create_folder_on_external("Downloads"):
        return False
    
    ext_downloads = EXTERNAL_DRIVE / "Downloads"
    
    # Find large files in Downloads (>100MB)
    print("Finding large files in Downloads folder...")
    try:
        result = subprocess.run(
            ["find", str(downloads), "-type", "f", "-size", "+100M"],
            capture_output=True, text=True
        )
        
        if result.returncode != 0:
            print("Error finding large files.")
            return False
        
        large_files = result.stdout.strip().split('\n')
        large_files = [f for f in large_files if f]  # Remove empty strings
        
        if not large_files:
            print("No large files found in Downloads folder.")
            return True
        
        print(f"Found {len(large_files)} large files in Downloads folder.")
        
        # Copy each large file to external drive
        for file_path in large_files:
            file_name = os.path.basename(file_path)
            dest_path = ext_downloads / file_name
            
            if dest_path.exists():
                print(f"File already exists on external drive: {file_name}")
                continue
            
            print(f"Copying {file_name} to external drive...")
            try:
                shutil.copy2(file_path, dest_path)
                print(f"Successfully copied {file_name}")
            except Exception as e:
                print(f"Error copying {file_name}: {e}")
        
        print("Finished processing large files in Downloads folder.")
        return True
    
    except Exception as e:
        print(f"Error processing Downloads folder: {e}")
        return False

def run_all_operations():
    """Run all operations in sequence."""
    print("\n=== RUNNING ALL OPERATIONS ===")
    
    operations = [
        ("Setting up Models folder for LLM models", setup_model_folders),
        ("Setting up configuration for external models", setup_local_config_for_external_models),
        ("Moving AIR Music Technology to external drive", lambda: move_large_app("/Applications/AIR Music Technology")),
        ("Moving Avid/ProTools to external drive", lambda: move_large_app("/Applications/Avid")),
        ("Moving Docker.app to external drive", lambda: move_large_app("/Applications/Docker.app")),
        ("Moving large files from Downloads folder", move_large_downloads),
        ("Opening external drive in Finder", lambda: open_in_finder(EXTERNAL_DRIVE))
    ]
    
    for description, operation in operations:
        print(f"\n=== {description} ===")
        success = operation()
        if success:
            print(f"✅ {description} completed successfully.")
        else:
            print(f"⚠️ {description} encountered issues.")
        
        # Brief pause between operations
        time.sleep(1)
    
    print("\n=== ALL OPERATIONS COMPLETED ===")
    return True

def main():
    """Main function."""
    print("=== EXTERNAL DRIVE FILE MOVER ===")
    
    # Check if external drive is connected
    if not check_external_drive():
        return False
    
    # Main menu
    while True:
        print("\nWhat would you like to do?")
        print("1. Move ProTools/Avid to external drive")
        print("2. Move AIR Music Technology to external drive")
        print("3. Move Docker.app to external drive")
        print("4. Set up Models folder for LLM models")
        print("5. Move large files from Downloads folder")
        print("6. Open external drive in Finder")
        print("7. RUN ALL OPERATIONS (1-6)")
        print("8. Exit")
        
        try:
            choice = input("\nEnter your choice (1-8): ")
            
            if choice == "1":
                move_large_app("/Applications/Avid")
            elif choice == "2":
                move_large_app("/Applications/AIR Music Technology")
            elif choice == "3":
                move_large_app("/Applications/Docker.app")
            elif choice == "4":
                setup_model_folders()
                setup_local_config_for_external_models()
            elif choice == "5":
                move_large_downloads()
            elif choice == "6":
                open_in_finder(EXTERNAL_DRIVE)
            elif choice == "7":
                run_all_operations()
            elif choice == "8":
                print("Exiting...")
                break
            else:
                print("Invalid choice. Please try again.")
        
        except KeyboardInterrupt:
            print("\nExiting...")
            break
    
    return True

if __name__ == "__main__":
    # Run the script with option 7 (run all operations) automatically
    print("=== EXTERNAL DRIVE FILE MOVER ===")
    if check_external_drive():
        print("\nAutomatically running all operations as requested...")
        run_all_operations()
        print("\nAll operations have been attempted. Check the output above for results.")
    else:
        print("External drive not found. Please connect your G Drive and try again.")