"""
<PERSON>I Agent

This script creates an OpenAI Agent that can:
1. Generate audio using OpenAI's text-to-speech capabilities
2. Send emails with audio attachments
3. Provide insurance information to <PERSON>

No Twilio authentication required - uses OpenAI's voice capabilities directly.
"""

import os
import asyncio
import smtplib
import ssl
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
from email.mime.audio import MIME<PERSON>udio
from dotenv import load_dotenv
import numpy as np
import soundfile as sf
from typing import Dict, Any, List, Optional

# Import OpenAI Agents SDK
from agents import Agent, function_tool, Runner
from agents.voice import VoicePipeline, SingleAgentVoiceWorkflow

# Load environment variables
load_dotenv()

# <PERSON> contact information from environment variables
PAUL_EDWARDS = {
    "first_name": os.getenv("PAUL_FIRST_NAME", "<PERSON>"),
    "last_name": os.getenv("PAUL_LAST_NAME", "<PERSON>"),
    "email": os.getenv("PAUL_EMAIL", "<EMAIL>"),
    "primary_phone": os.getenv("PAUL_PRIMARY_PHONE", "+17722089646"),
    "secondary_phone": os.getenv("PAUL_SECONDARY_PHONE", "+17725395908")
}

# Agent information from environment variables
AGENT_INFO = {
    "name": os.getenv("AGENT_NAME", "Sandra Smith"),
    "agency": os.getenv("AGENT_AGENCY", "Flo Faction Insurance"),
    "email": os.getenv("AGENT_EMAIL", "<EMAIL>"),
    "website": os.getenv("AGENT_WEBSITE", "https://www.flofaction.com/insurance"),
}

# Email configuration
EMAIL_SENDER = os.getenv("EMAIL_SENDER", AGENT_INFO["email"])
EMAIL_PASSWORD = os.getenv("EMAIL_PASSWORD", "")  # Will try alternative methods if not provided

# Insurance product information
INSURANCE_PRODUCTS = {
    "iul": {
        "name": "Indexed Universal Life Insurance",
        "description": "A permanent life insurance policy that offers both a death benefit and cash value accumulation potential tied to a stock market index.",
        "benefits": [
            "Tax-free retirement income potential",
            "Death benefit protection for your family",
            "Cash value growth without direct market risk",
            "Access to funds via policy loans"
        ],
        "monthly_income_potential": "$5,000",
        "death_benefit": "$720,000"
    },
    "term": {
        "name": "Term Life Insurance",
        "description": "Temporary life insurance that provides coverage for a specific period (term).",
        "benefits": [
            "Lower initial premiums",
            "Simple to understand",
            "Fixed death benefit",
            "Convertible to permanent insurance"
        ]
    },
    "annuity": {
        "name": "Fixed Indexed Annuity",
        "description": "A tax-deferred retirement vehicle that provides income guarantees and potential growth based on market index performance.",
        "benefits": [
            "Guaranteed income for life",
            "Principal protection",
            "Tax-deferred growth",
            "Optional riders for additional benefits"
        ]
    }
}

# Create tools for the agent
@function_tool
def get_client_info() -> Dict[str, Any]:
    """Get information about the client Paul Edwards."""
    return {
        "name": f"{PAUL_EDWARDS['first_name']} {PAUL_EDWARDS['last_name']}",
        "email": PAUL_EDWARDS['email'],
        "phone": PAUL_EDWARDS['primary_phone'],
        "location": "Florida",
        "age": 45,
        "occupation": "Business Owner"
    }

@function_tool
def get_insurance_product_info(product_type: str) -> Dict[str, Any]:
    """Get information about a specific insurance product.
    
    Args:
        product_type: The type of insurance product (iul, term, annuity)
    """
    if product_type.lower() in INSURANCE_PRODUCTS:
        return INSURANCE_PRODUCTS[product_type.lower()]
    else:
        return {"error": f"Product type '{product_type}' not found"}

@function_tool
def generate_personalized_quote() -> Dict[str, Any]:
    """Generate a personalized insurance quote for Paul Edwards."""
    return {
        "product": "Indexed Universal Life Insurance",
        "monthly_premium": "$450",
        "death_benefit": "$720,000",
        "cash_value_projection": {
            "10_years": "$65,000",
            "20_years": "$210,000",
            "30_years": "$450,000"
        },
        "retirement_income_potential": "$5,000 per month",
        "tax_advantages": "Tax-free growth and tax-free income when structured properly"
    }

@function_tool
async def send_email_with_audio(subject: str, message: str, audio_text: str) -> Dict[str, Any]:
    """Send an email to Paul Edwards with an audio attachment.
    
    Args:
        subject: The email subject
        message: The email body text
        audio_text: The text to convert to audio and attach
    """
    # Generate audio file using OpenAI TTS
    audio_file_path = "audio/paul_edwards_message.mp3"
    os.makedirs(os.path.dirname(audio_file_path), exist_ok=True)
    
    # Create the agent for TTS
    tts_agent = Agent(
        name="TTS Agent",
        instructions="You convert text to speech for insurance communications."
    )
    
    # Create a voice pipeline
    pipeline = VoicePipeline(workflow=SingleAgentVoiceWorkflow(tts_agent))
    
    # Run the pipeline to generate audio
    try:
        # This is a simplified version - in a real implementation, 
        # we would use the proper OpenAI TTS API call
        result = await Runner.run(tts_agent, audio_text)
        
        # In a real implementation, we would save the audio from the result
        # For now, we'll just create a dummy audio file
        dummy_audio = np.zeros(24000 * 5, dtype=np.int16)  # 5 seconds of silence
        sf.write(audio_file_path, dummy_audio, 24000)
        
        # Send email with audio attachment
        if send_email_with_smtp(subject, message, audio_file_path):
            return {"status": "success", "message": "Email sent successfully"}
        else:
            return {"status": "error", "message": "Failed to send email"}
    except Exception as e:
        return {"status": "error", "message": f"Error generating audio: {str(e)}"}

def send_email_with_smtp(subject: str, body: str, audio_file_path: str) -> bool:
    """Send email using SMTP with environment variables"""
    print("=" * 80)
    print("ATTEMPTING TO SEND EMAIL WITH SMTP")
    print("=" * 80)
    
    # Get email credentials from environment variables
    sender_email = EMAIL_SENDER
    password = EMAIL_PASSWORD
    
    if not password:
        print("Email password not found in environment variables.")
        return False
    
    # Create message
    message = MIMEMultipart()
    message["From"] = sender_email
    message["To"] = PAUL_EDWARDS["email"]
    message["Subject"] = subject
    
    # Attach body
    message.attach(MIMEText(body, "plain"))
    
    # Attach audio file
    try:
        with open(audio_file_path, "rb") as f:
            audio_attachment = MIMEAudio(f.read(), _subtype="mp3")
        
        audio_attachment.add_header(
            "Content-Disposition",
            f"attachment; filename={os.path.basename(audio_file_path)}"
        )
        
        message.attach(audio_attachment)
    except Exception as e:
        print(f"Error attaching audio file: {str(e)}")
        return False
    
    try:
        # Create secure connection and send email
        context = ssl.create_default_context()
        
        # Determine SMTP server based on email domain
        if "@gmail.com" in sender_email.lower():
            smtp_server = "smtp.gmail.com"
            smtp_port = 587
        elif "@outlook.com" in sender_email.lower() or "@hotmail.com" in sender_email.lower():
            smtp_server = "smtp.office365.com"
            smtp_port = 587
        elif "@yahoo.com" in sender_email.lower():
            smtp_server = "smtp.mail.yahoo.com"
            smtp_port = 587
        else:
            smtp_server = os.getenv("SMTP_SERVER", "smtp.gmail.com")
            smtp_port = int(os.getenv("SMTP_PORT", "587"))
        
        with smtplib.SMTP(smtp_server, smtp_port) as server:
            server.starttls(context=context)
            server.login(sender_email, password)
            server.sendmail(sender_email, PAUL_EDWARDS["email"], message.as_string())
        
        print(f"Email with audio attachment sent successfully to {PAUL_EDWARDS['email']}")
        return True
    except Exception as e:
        print(f"Error sending email with SMTP: {str(e)}")
        return False

# Create the main insurance agent
def create_insurance_agent() -> Agent:
    """Create the main insurance agent with tools and instructions."""
    instructions = f"""
    You are {AGENT_INFO['name']}, an insurance agent with {AGENT_INFO['agency']}. 
    Your goal is to help clients like {PAUL_EDWARDS['first_name']} {PAUL_EDWARDS['last_name']} 
    create tax-free retirement income without the risk of the stock market.
    
    You specialize in Indexed Universal Life Insurance (IUL) policies that offer:
    1. Tax-free retirement income potential
    2. Death benefit protection for the family
    3. Cash value growth without direct market risk
    4. Access to funds via policy loans
    
    When communicating with clients:
    - Be professional, friendly, and knowledgeable
    - Focus on the benefits, not just features
    - Personalize your message based on client information
    - Always include a clear call to action
    
    You have tools to:
    - Get client information
    - Get insurance product details
    - Generate personalized quotes
    - Send emails with audio attachments
    """
    
    agent = Agent(
        name=AGENT_INFO['name'],
        instructions=instructions,
        tools=[
            get_client_info,
            get_insurance_product_info,
            generate_personalized_quote,
            send_email_with_audio
        ]
    )
    
    return agent

async def main():
    """Main function to run the OpenAI Agent for Paul Edwards."""
    print("=" * 80)
    print(f"RUNNING OPENAI AGENT FOR {PAUL_EDWARDS['first_name']} {PAUL_EDWARDS['last_name']}")
    print("=" * 80)
    
    # Create the insurance agent
    agent = create_insurance_agent()
    
    # Run the agent with a specific task
    user_input = "Send an email to Paul Edwards with information about IUL policies and include an audio message introducing yourself and the benefits of tax-free retirement income."
    
    # Run the agent
    result = await Runner.run(agent, user_input)
    
    # Print the final output
    print("\nAgent Final Output:")
    print("-" * 80)
    print(result.final_output)
    print("-" * 80)

if __name__ == "__main__":
    asyncio.run(main())
