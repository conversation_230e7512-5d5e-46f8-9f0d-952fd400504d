import asyncio
import logging
import json
from datetime import datetime
from insurance_carriers import CarrierManager, FFLTridentPortal, MutualOfOmahaPortal
from client_template import ClientManager
from secure_credentials import SecureCredentialsManager
from carrier_urls import get_carrier_urls

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def test_quote_platform(carrier_name: str, client_data: dict):
    """Test specific carrier quote platform"""
    
    try:
        logger.info(f"\n=== Testing {carrier_name.upper()} Quote Platform ===")
        
        # Get carrier URLs
        urls = get_carrier_urls(carrier_name)
        logger.info(f"Quote URL: {urls['quote_url']}")
        
        # Initialize managers
        creds_manager = SecureCredentialsManager()
        client_manager = ClientManager()
        
        # Create client record
        client = client_manager.create_client(client_data)
        
        # Initialize carrier portal
        if carrier_name == "ffl_trident":
            portal = FFLTridentPortal(creds_manager)
        else:
            portal = MutualOfOmahaPortal(creds_manager)
            
        # Test connection
        logger.info("\nTesting connection...")
        connected = await portal.test_connection()
        logger.info(f"Connection status: {'Connected' if connected else 'Failed'}")
        
        if not connected:
            return None
            
        # Login with credentials
        logger.info("\nAttempting login...")
        logged_in = await portal.login("sandra")
        
        if not logged_in:
            logger.error("Failed to log in")
            return None
            
        logger.info("Login successful")
        
        # Get quote parameters
        logger.info("\nPreparing quote parameters...")
        quote_params = await portal._get_quote_parameters(client_data)
        logger.info("Quote parameters prepared:")
        logger.info(json.dumps(quote_params, indent=2))
        
        # Get quote
        logger.info("\nRequesting quote...")
        quote = await portal.get_quote(quote_params)
        
        if quote:
            logger.info("\nQuote received successfully:")
            logger.info(json.dumps(quote, indent=2))
            return quote
        else:
            logger.error("Failed to get quote")
            return None
            
    except Exception as e:
        logger.error(f"Error testing {carrier_name}: {str(e)}")
        return None

async def main():
    """Test both quote platforms"""
    
    # Test client data
    test_client = {
        "name": "John Medicare Test",
        "dob": "1955-06-15",
        "height": "5'10\"",
        "weight": "175",
        "gender": "M",
        "phone": "************",
        "email": "<EMAIL>",
        "address": "123 Senior Way, Port St. Lucie, FL 34952",
        "ssn": "***********",
        "drivers_license": "FL*********",
        
        "medications": [
            {
                "drug_name": "Metformin",
                "dosage": "500mg",
                "frequency": "twice daily"
            }
        ],
        
        "tobacco_use": False,
        "marijuana_use": False,
        
        "bank_info": {
            "bank_name": "Wells Fargo",
            "routing_number": "*********",
            "account_number": "*********"
        },
        
        "beneficiaries": [
            {
                "name": "Mary Test",
                "dob": "1957-08-20",
                "relationship": "spouse"
            }
        ],
        
        "budget_range": "$200-$250",
        "family_health_history": "No significant issues",
        "reason_notes": "Looking for Medicare Supplement plan",
        "start_date": datetime.now().strftime("%Y-%m-%d"),
        
        # Additional fields for different products
        "product_type": "Medicare Supplement",  # or "Term Life" for FFL
        "desired_coverage": "250000"  # for life insurance
    }
    
    # Test FFL Trident
    ffl_result = await test_quote_platform("ffl_trident", test_client)
    
    # Update product type for MOO
    test_client["product_type"] = "Medicare Supplement"
    moo_result = await test_quote_platform("mutual_of_omaha", test_client)
    
    # Compare results
    logger.info("\n=== Quote Comparison ===")
    
    if ffl_result:
        logger.info("\nFFL Trident Life Quote:")
        logger.info(f"Premium: ${ffl_result.get('premium')}")
        logger.info(f"Coverage: ${ffl_result.get('coverage')}")
        if 'details' in ffl_result:
            logger.info("Details:")
            for key, value in ffl_result['details'].items():
                logger.info(f"  {key}: {value}")
                
    if moo_result:
        logger.info("\nMutual of Omaha Quote:")
        logger.info(f"Premium: ${moo_result.get('premium')}")
        logger.info(f"Coverage: ${moo_result.get('coverage')}")
        if 'details' in moo_result:
            logger.info("Details:")
            for key, value in moo_result['details'].items():
                logger.info(f"  {key}: {value}")

if __name__ == "__main__":
    asyncio.run(main())