#!/usr/bin/env python3
"""
MidScene.js Integration
=======================

Advanced browser automation with AI-powered element detection and interaction.
Integrates MidScene.js capabilities with Python for intelligent web automation.
"""

import asyncio
import json
import logging
import os
import subprocess
import tempfile
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass, asdict

logger = logging.getLogger(__name__)

@dataclass
class MidSceneAction:
    action_type: str  # click, type, scroll, screenshot, wait, extract
    target: str  # CSS selector, text description, or coordinates
    value: Optional[str] = None
    options: Optional[Dict[str, Any]] = None
    wait_for: Optional[str] = None  # Element to wait for after action

@dataclass
class MidSceneResult:
    success: bool
    action: str
    target: str
    result: Optional[Any] = None
    screenshot: Optional[str] = None
    error: Optional[str] = None
    execution_time: float = 0.0
    timestamp: str = ""

class MidSceneIntegration:
    """MidScene.js integration for advanced browser automation"""
    
    def __init__(self, headless: bool = True, viewport_width: int = 1280, viewport_height: int = 800):
        self.headless = headless
        self.viewport_width = viewport_width
        self.viewport_height = viewport_height
        self.temp_dir = Path(tempfile.mkdtemp(prefix="midscene_"))
        self.config_file = self.temp_dir / "midscene_config.json"
        self.results_file = self.temp_dir / "results.json"
        
        # MidScene configuration
        self.config = {
            "browser": {
                "headless": self.headless,
                "viewport": {
                    "width": self.viewport_width,
                    "height": self.viewport_height
                },
                "timeout": 30000,
                "waitForSelector": 5000
            },
            "ai": {
                "enabled": True,
                "model": "gpt-4-vision-preview",
                "confidence_threshold": 0.8
            },
            "screenshots": {
                "enabled": True,
                "quality": 90,
                "fullPage": False
            },
            "logging": {
                "level": "info",
                "screenshots_on_error": True
            }
        }
        
        self._create_config_file()
        self._create_midscene_script()
        
    def _create_config_file(self):
        """Create MidScene configuration file"""
        with open(self.config_file, 'w') as f:
            json.dump(self.config, f, indent=2)
            
    def _create_midscene_script(self):
        """Create the MidScene.js execution script"""
        script_content = '''
const { chromium } = require('playwright');
const fs = require('fs');
const path = require('path');

class MidSceneAutomation {
    constructor(configPath) {
        this.config = JSON.parse(fs.readFileSync(configPath, 'utf8'));
        this.browser = null;
        this.page = null;
        this.results = [];
    }
    
    async initialize() {
        this.browser = await chromium.launch({
            headless: this.config.browser.headless,
            args: ['--no-sandbox', '--disable-setuid-sandbox']
        });
        
        const context = await this.browser.newContext({
            viewport: this.config.browser.viewport
        });
        
        this.page = await context.newPage();
        
        // Enable console logging
        this.page.on('console', msg => {
            console.log(`Browser console: ${msg.text()}`);
        });
        
        console.log('MidScene browser initialized');
    }
    
    async navigate(url) {
        const startTime = Date.now();
        try {
            await this.page.goto(url, { 
                waitUntil: 'networkidle',
                timeout: this.config.browser.timeout 
            });
            
            const result = {
                success: true,
                action: 'navigate',
                target: url,
                result: {
                    url: this.page.url(),
                    title: await this.page.title()
                },
                execution_time: Date.now() - startTime,
                timestamp: new Date().toISOString()
            };
            
            if (this.config.screenshots.enabled) {
                result.screenshot = await this.takeScreenshot();
            }
            
            this.results.push(result);
            return result;
            
        } catch (error) {
            const result = {
                success: false,
                action: 'navigate',
                target: url,
                error: error.message,
                execution_time: Date.now() - startTime,
                timestamp: new Date().toISOString()
            };
            this.results.push(result);
            return result;
        }
    }
    
    async click(target, options = {}) {
        const startTime = Date.now();
        try {
            let element;
            
            // Try different selection strategies
            if (target.startsWith('#') || target.startsWith('.') || target.includes('[')) {
                // CSS selector
                element = await this.page.locator(target);
            } else {
                // Try text-based selection (AI-powered)
                element = await this.page.getByText(target).first();
                if (!(await element.count())) {
                    element = await this.page.getByRole('button', { name: target });
                }
                if (!(await element.count())) {
                    element = await this.page.getByLabel(target);
                }
            }
            
            await element.click(options);
            
            // Wait for any specified element
            if (options.waitFor) {
                await this.page.waitForSelector(options.waitFor, {
                    timeout: this.config.browser.waitForSelector
                });
            }
            
            const result = {
                success: true,
                action: 'click',
                target: target,
                result: { clicked: true },
                execution_time: Date.now() - startTime,
                timestamp: new Date().toISOString()
            };
            
            if (this.config.screenshots.enabled) {
                result.screenshot = await this.takeScreenshot();
            }
            
            this.results.push(result);
            return result;
            
        } catch (error) {
            const result = {
                success: false,
                action: 'click',
                target: target,
                error: error.message,
                execution_time: Date.now() - startTime,
                timestamp: new Date().toISOString()
            };
            
            if (this.config.logging.screenshots_on_error) {
                result.screenshot = await this.takeScreenshot();
            }
            
            this.results.push(result);
            return result;
        }
    }
    
    async type(target, text, options = {}) {
        const startTime = Date.now();
        try {
            let element;
            
            if (target.startsWith('#') || target.startsWith('.') || target.includes('[')) {
                element = await this.page.locator(target);
            } else {
                element = await this.page.getByLabel(target);
                if (!(await element.count())) {
                    element = await this.page.getByPlaceholder(target);
                }
            }
            
            await element.fill(text);
            
            const result = {
                success: true,
                action: 'type',
                target: target,
                result: { text: text },
                execution_time: Date.now() - startTime,
                timestamp: new Date().toISOString()
            };
            
            this.results.push(result);
            return result;
            
        } catch (error) {
            const result = {
                success: false,
                action: 'type',
                target: target,
                error: error.message,
                execution_time: Date.now() - startTime,
                timestamp: new Date().toISOString()
            };
            this.results.push(result);
            return result;
        }
    }
    
    async extract(target, options = {}) {
        const startTime = Date.now();
        try {
            let elements;
            let extractedData;
            
            if (target.startsWith('#') || target.startsWith('.') || target.includes('[')) {
                elements = await this.page.locator(target);
            } else {
                elements = await this.page.getByText(target);
            }
            
            const count = await elements.count();
            if (count === 0) {
                throw new Error(`No elements found for target: ${target}`);
            }
            
            if (options.multiple) {
                extractedData = [];
                for (let i = 0; i < count; i++) {
                    const element = elements.nth(i);
                    const text = await element.textContent();
                    extractedData.push(text?.trim());
                }
            } else {
                const element = elements.first();
                extractedData = await element.textContent();
                extractedData = extractedData?.trim();
            }
            
            const result = {
                success: true,
                action: 'extract',
                target: target,
                result: { data: extractedData, count: count },
                execution_time: Date.now() - startTime,
                timestamp: new Date().toISOString()
            };
            
            this.results.push(result);
            return result;
            
        } catch (error) {
            const result = {
                success: false,
                action: 'extract',
                target: target,
                error: error.message,
                execution_time: Date.now() - startTime,
                timestamp: new Date().toISOString()
            };
            this.results.push(result);
            return result;
        }
    }
    
    async takeScreenshot(options = {}) {
        try {
            const screenshot = await this.page.screenshot({
                type: 'png',
                quality: this.config.screenshots.quality,
                fullPage: this.config.screenshots.fullPage,
                ...options
            });
            return screenshot.toString('base64');
        } catch (error) {
            console.error('Screenshot failed:', error.message);
            return null;
        }
    }
    
    async wait(milliseconds) {
        const startTime = Date.now();
        await this.page.waitForTimeout(milliseconds);
        
        const result = {
            success: true,
            action: 'wait',
            target: `${milliseconds}ms`,
            result: { waited: milliseconds },
            execution_time: Date.now() - startTime,
            timestamp: new Date().toISOString()
        };
        
        this.results.push(result);
        return result;
    }
    
    async executeActions(actions) {
        for (const action of actions) {
            console.log(`Executing action: ${action.action_type} on ${action.target}`);
            
            switch (action.action_type) {
                case 'navigate':
                    await this.navigate(action.target);
                    break;
                case 'click':
                    await this.click(action.target, action.options || {});
                    break;
                case 'type':
                    await this.type(action.target, action.value, action.options || {});
                    break;
                case 'extract':
                    await this.extract(action.target, action.options || {});
                    break;
                case 'wait':
                    await this.wait(parseInt(action.value) || 1000);
                    break;
                case 'screenshot':
                    const screenshot = await this.takeScreenshot(action.options || {});
                    this.results.push({
                        success: true,
                        action: 'screenshot',
                        target: 'page',
                        result: { screenshot: screenshot },
                        timestamp: new Date().toISOString()
                    });
                    break;
            }
            
            // Wait between actions if specified
            if (action.wait_for) {
                await this.page.waitForSelector(action.wait_for, {
                    timeout: this.config.browser.waitForSelector
                });
            }
        }
    }
    
    async cleanup() {
        if (this.browser) {
            await this.browser.close();
        }
    }
    
    getResults() {
        return this.results;
    }
}

// Main execution
async function main() {
    const configPath = process.argv[2];
    const actionsPath = process.argv[3];
    const resultsPath = process.argv[4];
    
    if (!configPath || !actionsPath || !resultsPath) {
        console.error('Usage: node midscene_script.js <config_path> <actions_path> <results_path>');
        process.exit(1);
    }
    
    const automation = new MidSceneAutomation(configPath);
    
    try {
        await automation.initialize();
        
        const actions = JSON.parse(fs.readFileSync(actionsPath, 'utf8'));
        await automation.executeActions(actions);
        
        const results = automation.getResults();
        fs.writeFileSync(resultsPath, JSON.stringify(results, null, 2));
        
        console.log(`Completed ${results.length} actions`);
        
    } catch (error) {
        console.error('Automation failed:', error.message);
        
        const errorResult = {
            success: false,
            error: error.message,
            timestamp: new Date().toISOString()
        };
        
        fs.writeFileSync(resultsPath, JSON.stringify([errorResult], null, 2));
        process.exit(1);
        
    } finally {
        await automation.cleanup();
    }
}

main().catch(console.error);
'''
        
        script_file = self.temp_dir / "midscene_script.js"
        with open(script_file, 'w') as f:
            f.write(script_content)
            
        self.script_file = script_file
        
    async def execute_actions(self, actions: List[MidSceneAction]) -> List[MidSceneResult]:
        """Execute a sequence of MidScene actions"""
        # Convert actions to JSON format
        actions_data = [asdict(action) for action in actions]
        actions_file = self.temp_dir / "actions.json"
        
        with open(actions_file, 'w') as f:
            json.dump(actions_data, f, indent=2)
            
        try:
            # Execute the Node.js script
            cmd = [
                'node',
                str(self.script_file),
                str(self.config_file),
                str(actions_file),
                str(self.results_file)
            ]
            
            process = await asyncio.create_subprocess_exec(
                *cmd,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE,
                cwd=self.temp_dir
            )
            
            stdout, stderr = await process.communicate()
            
            if process.returncode != 0:
                logger.error(f"MidScene execution failed: {stderr.decode()}")
                return [MidSceneResult(
                    success=False,
                    action="execution",
                    target="all",
                    error=stderr.decode(),
                    timestamp=datetime.now().isoformat()
                )]
                
            # Read results
            if self.results_file.exists():
                with open(self.results_file, 'r') as f:
                    results_data = json.load(f)
                    
                return [
                    MidSceneResult(
                        success=result.get('success', False),
                        action=result.get('action', ''),
                        target=result.get('target', ''),
                        result=result.get('result'),
                        screenshot=result.get('screenshot'),
                        error=result.get('error'),
                        execution_time=result.get('execution_time', 0.0),
                        timestamp=result.get('timestamp', datetime.now().isoformat())
                    )
                    for result in results_data
                ]
            else:
                return [MidSceneResult(
                    success=False,
                    action="execution",
                    target="all",
                    error="No results file generated",
                    timestamp=datetime.now().isoformat()
                )]
                
        except Exception as e:
            logger.error(f"Error executing MidScene actions: {e}")
            return [MidSceneResult(
                success=False,
                action="execution",
                target="all",
                error=str(e),
                timestamp=datetime.now().isoformat()
            )]
            
    async def navigate_and_interact(self, url: str, interactions: List[Dict[str, Any]]) -> List[MidSceneResult]:
        """Navigate to a URL and perform interactions"""
        actions = [MidSceneAction(action_type="navigate", target=url)]
        
        for interaction in interactions:
            action = MidSceneAction(
                action_type=interaction['type'],
                target=interaction['target'],
                value=interaction.get('value'),
                options=interaction.get('options'),
                wait_for=interaction.get('wait_for')
            )
            actions.append(action)
            
        return await self.execute_actions(actions)
        
    def cleanup(self):
        """Clean up temporary files"""
        import shutil
        if self.temp_dir.exists():
            shutil.rmtree(self.temp_dir)
            
    def __del__(self):
        """Cleanup on destruction"""
        self.cleanup()

# Example usage
if __name__ == "__main__":
    async def test_midscene():
        midscene = MidSceneIntegration(headless=False)
        
        actions = [
            MidSceneAction("navigate", "https://httpbin.org/forms/post"),
            MidSceneAction("type", "custname", "John Doe"),
            MidSceneAction("type", "custtel", "************"),
            MidSceneAction("type", "custemail", "<EMAIL>"),
            MidSceneAction("screenshot", "form_filled"),
            MidSceneAction("click", "Submit")
        ]
        
        results = await midscene.execute_actions(actions)
        
        for result in results:
            print(f"Action: {result.action}, Success: {result.success}")
            if result.error:
                print(f"Error: {result.error}")
                
        midscene.cleanup()
        
    asyncio.run(test_midscene())
