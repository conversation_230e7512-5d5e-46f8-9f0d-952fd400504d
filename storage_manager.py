#!/usr/bin/env python3
"""
Storage Manager - Helps move large files to external drive to free up disk space.
This script will scan for large files and directories, and help you move them to
your external drive.
"""

import os
import sys
import shutil
import subprocess
import time
from pathlib import Path

# Configuration
EXTERNAL_DRIVE = Path("/Volumes/G Drive")
TARGET_DIR = EXTERNAL_DRIVE / "Moved_Files"
MIN_FILE_SIZE_MB = 100  # Minimum file size to consider for moving
LARGE_APPS = [
    "/Applications/AIR Music Technology",
    "/Applications/Avid",
    "/Applications/Docker.app"
]

def check_disk_space(path):
    """Check available disk space at the given path."""
    try:
        stat = os.statvfs(path)
        free_space = stat.f_frsize * stat.f_bavail
        free_space_mb = free_space / (1024 * 1024)
        print(f"Available space at {path}: {free_space_mb:.2f} MB")
        return free_space_mb
    except Exception as e:
        print(f"Error checking disk space: {e}")
        return 0

def get_directory_size(path):
    """Get the size of a directory in MB."""
    try:
        result = subprocess.run(["du", "-sm", path], capture_output=True, text=True)
        if result.returncode == 0:
            size = result.stdout.split()[0]
            return int(size)
        return 0
    except Exception as e:
        print(f"Error getting directory size: {e}")
        return 0

def find_large_files(directory, min_size_mb=MIN_FILE_SIZE_MB):
    """Find large files in the given directory."""
    large_files = []
    
    print(f"\nScanning {directory} for files larger than {min_size_mb}MB...")
    
    try:
        cmd = ["find", directory, "-type", "f", "-size", f"+{min_size_mb}M", "-exec", "du", "-h", "{}", ";"]
        result = subprocess.run(cmd, capture_output=True, text=True)
        if result.returncode == 0:
            lines = result.stdout.strip().split('\n')
            for line in lines:
                if line:
                    parts = line.split('\t')
                    if len(parts) == 2:
                        size, path = parts
                        large_files.append((path, size))
        
        # Sort by size (largest first)
        large_files.sort(key=lambda x: x[1], reverse=True)
        return large_files
    except Exception as e:
        print(f"Error finding large files: {e}")
        return []

def ensure_dir_exists(dir_path):
    """Create directory on external drive using applescript to bypass permission issues."""
    if dir_path.exists():
        print(f"Directory already exists: {dir_path}")
        return True
    
    try:
        # Try to create directory directly
        os.makedirs(dir_path, exist_ok=True)
        print(f"Created directory: {dir_path}")
        return True
    except Exception as e:
        print(f"Error creating directory: {e}")
        
        # Use AppleScript as an alternative
        try:
            script = f'''
            tell application "Finder"
                make new folder at folder "{dir_path.parent}" with properties {{name:"{dir_path.name}"}}
            end tell
            '''
            subprocess.run(["osascript", "-e", script], capture_output=True)
            
            # Check if directory was created
            if dir_path.exists():
                print(f"Created directory using AppleScript: {dir_path}")
                return True
            else:
                print(f"Failed to create directory: {dir_path}")
                return False
        except Exception as e:
            print(f"Error creating directory with AppleScript: {e}")
            return False

def move_with_finder(src_path, dest_folder):
    """Use AppleScript to move files with Finder to bypass permission issues."""
    try:
        # Ensure destination exists
        if not ensure_dir_exists(dest_folder):
            print(f"Could not create destination folder: {dest_folder}")
            return False
        
        src_path = Path(src_path).resolve()
        dest_path = dest_folder / Path(src_path).name
        
        # Check if destination already exists
        if dest_path.exists():
            print(f"Destination already exists: {dest_path}")
            return False
        
        print(f"Moving {src_path} to {dest_folder}...")
        
        # Use AppleScript to move the file/folder
        script = f'''
        tell application "Finder"
            move POSIX file "{src_path}" to POSIX file "{dest_folder}" with replacing
        end tell
        '''
        
        result = subprocess.run(["osascript", "-e", script], capture_output=True, text=True)
        if result.returncode != 0:
            print(f"Error moving file: {result.stderr}")
            return False
        
        print(f"Successfully moved to {dest_folder}")
        return True
    except Exception as e:
        print(f"Error moving with Finder: {e}")
        return False

def open_in_finder(path):
    """Open a path in Finder."""
    try:
        subprocess.run(["open", "-R", path], check=True)
        return True
    except Exception as e:
        print(f"Error opening in Finder: {e}")
        return False

def create_symlink(src, dest):
    """Create a symbolic link from src to dest."""
    try:
        # If dest already exists, remove it
        if os.path.exists(dest):
            if os.path.islink(dest):
                os.unlink(dest)
            else:
                print(f"Destination exists and is not a symlink: {dest}")
                return False
        
        # Create the symlink
        os.symlink(src, dest)
        print(f"Created symlink: {dest} -> {src}")
        return True
    except Exception as e:
        print(f"Error creating symlink: {e}")
        return False

def move_large_applications():
    """Move large applications to external drive."""
    print("\n=== MOVING LARGE APPLICATIONS ===")
    
    moved_apps = []
    
    # Create apps directory on external drive
    apps_dir = TARGET_DIR / "Applications"
    if not ensure_dir_exists(apps_dir):
        print("Could not create Applications directory on external drive.")
        return
    
    for app in LARGE_APPS:
        app_path = Path(app)
        if app_path.exists():
            size_mb = get_directory_size(app)
            print(f"\nFound large application: {app} ({size_mb} MB)")
            
            # Confirm move
            choice = input(f"Move {app_path.name} to external drive? (y/n): ")
            if choice.lower() == 'y':
                # Move the application
                if move_with_finder(app, apps_dir):
                    moved_apps.append((app, size_mb))
                    
                    # Create a symlink to the moved app
                    ext_app_path = apps_dir / app_path.name
                    create_symlink(ext_app_path, app_path)
    
    # Summarize moved applications
    if moved_apps:
        total_mb = sum(size for _, size in moved_apps)
        print(f"\nMoved {len(moved_apps)} applications, freeing approximately {total_mb} MB")
    else:
        print("\nNo applications were moved.")

def scan_downloads_folder():
    """Scan downloads folder for large files."""
    downloads = Path.home() / "Downloads"
    if downloads.exists():
        print("\n=== SCANNING DOWNLOADS FOLDER ===")
        large_files = find_large_files(downloads)
        
        if large_files:
            downloads_dir = TARGET_DIR / "Downloads"
            if not ensure_dir_exists(downloads_dir):
                print("Could not create Downloads directory on external drive.")
                return
            
            for file_path, size in large_files[:10]:  # Limit to top 10 largest files
                print(f"\nFound large file: {file_path} ({size})")
                choice = input(f"Move to external drive? (y/n): ")
                if choice.lower() == 'y':
                    move_with_finder(file_path, downloads_dir)
        else:
            print("No large files found in Downloads folder.")

def check_external_drive():
    """Check if external drive is available."""
    if not EXTERNAL_DRIVE.exists():
        print(f"External drive not found at {EXTERNAL_DRIVE}")
        print("Please connect your external drive and try again.")
        return False
    
    print(f"External drive found at {EXTERNAL_DRIVE}")
    
    # Check available space
    space_mb = check_disk_space(EXTERNAL_DRIVE)
    print(f"Available space on external drive: {space_mb:.2f} MB")
    
    return True

def setup_external_drive():
    """Set up directory structure on external drive."""
    if not check_external_drive():
        return False
    
    # Create main target directory
    if not ensure_dir_exists(TARGET_DIR):
        print(f"Could not create target directory: {TARGET_DIR}")
        return False
    
    # Create subdirectories
    for subdir in ["Applications", "Downloads", "Models", "Documents"]:
        if not ensure_dir_exists(TARGET_DIR / subdir):
            print(f"Could not create subdirectory: {subdir}")
            continue
    
    return True

def empty_trash():
    """Empty the trash."""
    try:
        print("\nEmptying trash...")
        subprocess.run(["osascript", "-e", 'tell application "Finder" to empty trash'])
        print("Trash emptied successfully.")
        return True
    except Exception as e:
        print(f"Error emptying trash: {e}")
        return False

def show_menu():
    """Show the main menu."""
    print("\n=== STORAGE MANAGER MENU ===")
    print("1. Move large applications")
    print("2. Scan Downloads folder")
    print("3. Empty trash")
    print("4. Show disk usage")
    print("5. Setup external drive directories")
    print("6. Open external drive in Finder")
    print("7. Exit")
    
    try:
        choice = int(input("\nEnter your choice (1-7): "))
        return choice
    except ValueError:
        print("Invalid input. Please enter a number.")
        return 0

def main():
    """Main function."""
    print("=== STORAGE MANAGER ===")
    print("This tool helps you move large files to your external drive.")
    
    if not check_external_drive():
        return False
    
    while True:
        choice = show_menu()
        
        if choice == 1:
            move_large_applications()
        elif choice == 2:
            scan_downloads_folder()
        elif choice == 3:
            empty_trash()
        elif choice == 4:
            check_disk_space(Path.home())
            check_disk_space(EXTERNAL_DRIVE)
        elif choice == 5:
            setup_external_drive()
        elif choice == 6:
            open_in_finder(EXTERNAL_DRIVE)
        elif choice == 7:
            print("Exiting...")
            break
        else:
            print("Invalid choice. Please try again.")
    
    return True

if __name__ == "__main__":
    main()