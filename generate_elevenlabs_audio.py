"""
Generate Eleven Labs Audio for <PERSON>

This script generates audio using Eleven Labs API with a female voice.
The audio can be used for phone calls and voicemails to <PERSON>.
"""

import os
import requests
import json
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Eleven Labs credentials
ELEVEN_LABS_API_KEY = "sk_531ff39d6099a4191b57e4935654c909d6196b59d9339015"
ELEVEN_LABS_VOICE_ID = "21m00Tcm4TlvDq8ikWAM"  # Rachel voice ID (female voice)

# <PERSON> contact information
PAUL_EDWARDS = {
    "first_name": "<PERSON>",
    "last_name": "<PERSON>",
    "email": "<EMAIL>",
    "primary_phone": "+17722089646",
    "secondary_phone": "+17725395908"
}

# Agent information
AGENT_INFO = {
    "name": "<PERSON>",
    "agency": "Flo Faction Insurance",
    "email": "<EMAIL>",
    "website": "https://www.flofaction.com/insurance",
}

def generate_call_script():
    """Generate a call script for <PERSON>"""
    script = f"""
Hello {PAUL_EDWARDS['first_name']}, this is {AGENT_INFO['name']} with {AGENT_INFO['agency']}. How are you doing today?

I'm reaching out because I help people like you create tax-free retirement income without the risk of the stock market. Based on your profile, I've found that you might qualify for a strategy that could provide around $5,000 per month in tax-free retirement income.

I'd love to share a personalized analysis I've prepared for you. Please give me a call back when you have a moment, or visit our website at {AGENT_INFO['website']} for more information.

Thank you and have a great day!
    """
    return script.strip()

def generate_voicemail_script():
    """Generate a voicemail script for Paul Edwards"""
    script = f"""
Hi {PAUL_EDWARDS['first_name']}, this is {AGENT_INFO['name']} with {AGENT_INFO['agency']}. I'm calling because I help people like you create tax-free retirement income without the risk of the stock market.

Based on your profile, I've found that you might qualify for a strategy that could provide significant tax-free retirement income while also protecting your family with a death benefit.

I'd love to share a personalized analysis I've prepared for you. Please give me a call back when you have a moment, or visit our website at {AGENT_INFO['website']} for more information.

Thank you and have a great day!
    """
    return script.strip()

def generate_audio(text, output_file):
    """Generate audio using Eleven Labs API"""
    print("=" * 80)
    print("GENERATING AUDIO WITH ELEVEN LABS")
    print("=" * 80)
    
    url = f"https://api.elevenlabs.io/v1/text-to-speech/{ELEVEN_LABS_VOICE_ID}"
    
    headers = {
        "Accept": "audio/mpeg",
        "Content-Type": "application/json",
        "xi-api-key": ELEVEN_LABS_API_KEY
    }
    
    data = {
        "text": text,
        "model_id": "eleven_monolingual_v1",
        "voice_settings": {
            "stability": 0.5,
            "similarity_boost": 0.5
        }
    }
    
    try:
        print(f"Sending request to Eleven Labs API...")
        response = requests.post(url, json=data, headers=headers)
        
        if response.status_code == 200:
            # Save the audio file
            with open(output_file, "wb") as f:
                f.write(response.content)
            
            print(f"Audio generated successfully and saved as {output_file}")
            return True
        else:
            print(f"Error generating audio: {response.status_code} - {response.text}")
            return False
    except Exception as e:
        print(f"Exception generating audio: {str(e)}")
        return False

def list_available_voices():
    """List available voices from Eleven Labs"""
    print("=" * 80)
    print("LISTING AVAILABLE VOICES FROM ELEVEN LABS")
    print("=" * 80)
    
    url = "https://api.elevenlabs.io/v1/voices"
    
    headers = {
        "Accept": "application/json",
        "xi-api-key": ELEVEN_LABS_API_KEY
    }
    
    try:
        response = requests.get(url, headers=headers)
        
        if response.status_code == 200:
            voices = response.json().get("voices", [])
            
            print(f"Found {len(voices)} voices:")
            for voice in voices:
                print(f"- {voice.get('name')} (ID: {voice.get('voice_id')})")
            
            return voices
        else:
            print(f"Error listing voices: {response.status_code} - {response.text}")
            return []
    except Exception as e:
        print(f"Exception listing voices: {str(e)}")
        return []

def main():
    """Main function to generate audio"""
    print("This script generates audio using Eleven Labs API with a female voice.")
    print("The audio can be used for phone calls and voicemails to Paul Edwards.")
    
    print("\nWhat would you like to do?")
    print("1. Generate call audio")
    print("2. Generate voicemail audio")
    print("3. List available voices")
    print("4. Generate audio from custom text")
    
    choice = input("\nEnter your choice (1-4): ")
    
    if choice == "1":
        script = generate_call_script()
        print("\nCall Script:")
        print("-" * 80)
        print(script)
        print("-" * 80)
        
        proceed = input("\nDo you want to generate audio for this script? (yes/no): ")
        
        if proceed.lower() == "yes":
            generate_audio(script, "paul_edwards_call.mp3")
    elif choice == "2":
        script = generate_voicemail_script()
        print("\nVoicemail Script:")
        print("-" * 80)
        print(script)
        print("-" * 80)
        
        proceed = input("\nDo you want to generate audio for this script? (yes/no): ")
        
        if proceed.lower() == "yes":
            generate_audio(script, "paul_edwards_voicemail.mp3")
    elif choice == "3":
        list_available_voices()
    elif choice == "4":
        print("\nEnter your custom text:")
        text = input("> ")
        
        output_file = input("\nEnter output file name (e.g., custom_message.mp3): ")
        
        generate_audio(text, output_file)
    else:
        print("Invalid choice. Please run the script again.")

if __name__ == "__main__":
    main()
