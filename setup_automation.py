import subprocess
import sys
import logging
import os
import requests
from zipfile import ZipFile
from io import BytesIO

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def install_requirements():
    """Install required Python packages"""
    requirements = [
        'selenium==4.18.1',
        'webdriver_manager==4.0.1',
        'requests==2.31.0'
    ]
    
    logger.info("Installing Python requirements...")
    for req in requirements:
        try:
            subprocess.check_call([sys.executable, '-m', 'pip', 'install', req])
            logger.info(f"Installed {req}")
        except subprocess.CalledProcessError as e:
            logger.error(f"Failed to install {req}: {str(e)}")
            return False
    return True

def setup_chrome_driver():
    """Setup ChromeDriver using webdriver_manager"""
    try:
        from webdriver_manager.chrome import ChromeDriverManager
        from selenium import webdriver
        
        logger.info("Setting up ChromeDriver...")
        driver_path = ChromeDriverManager().install()
        logger.info(f"ChromeDriver installed at: {driver_path}")
        
        # Test driver
        options = webdriver.ChromeOptions()
        options.add_argument('--headless')  # Run in headless mode for test
        driver = webdriver.Chrome(options=options)
        driver.quit()
        
        logger.info("ChromeDriver setup successful!")
        return True
        
    except Exception as e:
        logger.error(f"Failed to setup ChromeDriver: {str(e)}")
        return False

def main():
    """Run setup process"""
    logger.info("Starting automation setup...")
    
    # Install Python packages
    if not install_requirements():
        logger.error("Failed to install requirements")
        return False
        
    # Setup ChromeDriver
    if not setup_chrome_driver():
        logger.error("Failed to setup ChromeDriver")
        return False
        
    logger.info("\nSetup completed successfully!")
    logger.info("You can now run the form automation tests with:")
    logger.info("python test_form_automation.py")
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)