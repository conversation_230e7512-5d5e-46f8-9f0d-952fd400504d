import logging
import time
from typing import Dict, Optional
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.common.keys import Keys
from selenium.common.exceptions import TimeoutException, NoSuchElementException
from carrier_urls import CARRIER_URLS

logger = logging.getLogger(__name__)

class QuoteFormHandler:
    """Handles quote form interactions with better error handling and dynamic element detection"""
    
    def __init__(self, headless: bool = False):
        self.driver = None
        self.headless = headless
        
    def _initialize_driver(self):
        """Initialize Chrome WebDriver with appropriate options"""
        options = webdriver.ChromeOptions()
        if self.headless:
            options.add_argument('--headless')
        options.add_argument('--start-maximized')
        options.add_argument('--disable-extensions')
        options.add_argument('--disable-popup-blocking')
        options.add_experimental_option('excludeSwitches', ['enable-automation'])
        options.add_experimental_option('useAutomationExtension', False)
        
        self.driver = webdriver.Chrome(options=options)
        
    def close(self):
        """Close browser"""
        if self.driver:
            self.driver.quit()
            
    def fill_moo_quote_form(self, client_data: Dict) -> bool:
        """Fill Mutual of Omaha quote form with better element handling"""
        try:
            if not self.driver:
                self._initialize_driver()
                
            # Navigate to MOO quote page
            url = CARRIER_URLS['mutual_of_omaha']['quote_url']
            logger.info(f"Navigating to: {url}")
            self.driver.get(url)
            
            # Wait for page load and handle any initial popups
            time.sleep(3)  # Allow for dynamic content to load
            self._handle_popups()
            
            # Fill Medicare Supplement form
            self._wait_and_click("//button[contains(text(), 'Medicare Supplement')]")
            
            # Fill ZIP code first (usually required to proceed)
            zip_code = client_data["address"].split()[-1]
            self._fill_field_by_name("zipCode", zip_code)
            self._press_enter("zipCode")
            time.sleep(1)
            
            # Fill personal information
            self._fill_field_by_name("firstName", client_data["name"].split()[0])
            self._fill_field_by_name("lastName", " ".join(client_data["name"].split()[1:]))
            
            # Handle DOB with proper format
            dob = client_data["dob"]
            self._fill_field_by_name("dateOfBirth", dob)
            
            # Handle additional fields if they appear
            self._try_fill_field("height", client_data.get("height"))
            self._try_fill_field("weight", client_data.get("weight"))
            
            # Handle radio buttons and checkboxes
            self._handle_radio_button("gender", "male" if client_data.get("gender") == "M" else "female")
            self._handle_radio_button("tobaccoUse", "yes" if client_data.get("tobacco_use") else "no")
            
            logger.info("Successfully filled MOO quote form")
            return True
            
        except Exception as e:
            logger.error(f"Error filling MOO form: {str(e)}")
            return False
            
    def fill_ffl_quote_form(self, client_data: Dict) -> bool:
        """Fill FFL Trident quote form with better element handling"""
        try:
            if not self.driver:
                self._initialize_driver()
                
            # Navigate to FFL quote page
            url = CARRIER_URLS['ffl_trident']['quote_url']
            logger.info(f"Navigating to: {url}")
            self.driver.get(url)
            
            # Wait for page load and handle any initial popups
            time.sleep(3)
            self._handle_popups()
            
            # Select Term Life product
            self._wait_and_click("//button[contains(text(), 'Term Life')]")
            
            # Fill basic information
            zip_code = client_data["address"].split()[-1]
            self._fill_field_by_name("zipCode", zip_code)
            self._press_enter("zipCode")
            time.sleep(1)
            
            self._fill_field_by_name("firstName", client_data["name"].split()[0])
            self._fill_field_by_name("lastName", " ".join(client_data["name"].split()[1:]))
            self._fill_field_by_name("dateOfBirth", client_data["dob"])
            
            # Select coverage amount
            coverage = client_data.get("desired_coverage", "250000")
            self._select_coverage_amount(coverage)
            
            # Handle health class and tobacco use
            self._handle_radio_button("tobaccoUse", "yes" if client_data.get("tobacco_use") else "no")
            health_class = "Preferred" if not client_data.get("tobacco_use") else "Standard"
            self._select_health_class(health_class)
            
            logger.info("Successfully filled FFL quote form")
            return True
            
        except Exception as e:
            logger.error(f"Error filling FFL form: {str(e)}")
            return False
            
    def _wait_and_click(self, xpath: str, timeout: int = 10):
        """Wait for element and click when available"""
        element = WebDriverWait(self.driver, timeout).until(
            EC.element_to_be_clickable((By.XPATH, xpath))
        )
        element.click()
        
    def _fill_field_by_name(self, name: str, value: str):
        """Fill form field by name with proper waiting"""
        try:
            field = WebDriverWait(self.driver, 5).until(
                EC.presence_of_element_located((By.NAME, name))
            )
            field.clear()
            field.send_keys(value)
        except TimeoutException:
            logger.warning(f"Field not found by name: {name}, trying by ID")
            self._try_fill_field(name, value)
            
    def _try_fill_field(self, field_id: str, value: str):
        """Attempt to fill field by ID with fallbacks"""
        try:
            # Try different locator strategies
            for strategy in [(By.ID, field_id), (By.NAME, field_id), 
                           (By.XPATH, f"//input[@placeholder='{field_id}']")]:
                try:
                    field = WebDriverWait(self.driver, 2).until(
                        EC.presence_of_element_located(strategy)
                    )
                    field.clear()
                    field.send_keys(value)
                    return
                except TimeoutException:
                    continue
            logger.warning(f"Could not find field: {field_id}")
        except Exception as e:
            logger.warning(f"Error filling field {field_id}: {str(e)}")
            
    def _press_enter(self, field_name: str):
        """Press enter in a field"""
        try:
            field = self.driver.find_element(By.NAME, field_name)
            field.send_keys(Keys.RETURN)
        except Exception:
            pass
            
    def _handle_popups(self):
        """Handle common popup scenarios"""
        try:
            # Look for common popup close buttons or overlays
            close_buttons = self.driver.find_elements(By.XPATH, 
                "//button[contains(@class, 'close') or contains(@class, 'popup-close')]")
            for button in close_buttons:
                try:
                    button.click()
                except:
                    pass
        except Exception:
            pass
            
    def _select_coverage_amount(self, amount: str):
        """Select coverage amount with better handling"""
        try:
            # Try different selection methods
            selectors = [
                f"//select[@name='coverage']//option[contains(text(), '{amount}')]",
                f"//button[contains(text(), '{amount}')]",
                "//input[@type='range']"  # For slider inputs
            ]
            
            for selector in selectors:
                try:
                    element = WebDriverWait(self.driver, 2).until(
                        EC.presence_of_element_located((By.XPATH, selector))
                    )
                    element.click()
                    return
                except:
                    continue
                    
            logger.warning("Could not set coverage amount through standard methods")
        except Exception as e:
            logger.warning(f"Error setting coverage amount: {str(e)}")
            
    def _select_health_class(self, health_class: str):
        """Select health class with better handling"""
        try:
            # Try different selection methods
            selectors = [
                f"//select[@name='healthClass']//option[contains(text(), '{health_class}')]",
                f"//button[contains(text(), '{health_class}')]",
                f"//label[contains(text(), '{health_class}')]"
            ]
            
            for selector in selectors:
                try:
                    element = WebDriverWait(self.driver, 2).until(
                        EC.presence_of_element_located((By.XPATH, selector))
                    )
                    element.click()
                    return
                except:
                    continue
                    
            logger.warning("Could not set health class through standard methods")
        except Exception as e:
            logger.warning(f"Error setting health class: {str(e)}")

def main():
    """Test form handling"""
    logging.basicConfig(level=logging.INFO)
    
    # Test client data
    test_client = {
        "name": "John Test Smith",
        "dob": "1955-06-15",
        "height": "5'10\"",
        "weight": "175",
        "gender": "M",
        "address": "123 Test St, Port St. Lucie, FL 34952",
        "tobacco_use": False,
        "desired_coverage": "250000"
    }
    
    handler = QuoteFormHandler(headless=False)  # Set to True for headless mode
    
    try:
        # Test MOO form
        logger.info("\nTesting MOO quote form...")
        success = handler.fill_moo_quote_form(test_client)
        if success:
            input("Check MOO form and press Enter to continue...")
            
        # Test FFL form
        logger.info("\nTesting FFL quote form...")
        success = handler.fill_ffl_quote_form(test_client)
        if success:
            input("Check FFL form and press Enter to continue...")
            
    finally:
        handler.close()

if __name__ == "__main__":
    main()