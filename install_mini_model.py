#!/usr/bin/env python3
"""
Minimal script to install a small, efficient model.
This script downloads a very small model to conserve disk space.
"""

import os
import sys
from pathlib import Path
import shutil
import json
import subprocess
import tempfile

# Configuration - Use local directory first, then manually move to external
LOCAL_MODELS_DIR = Path.home() / ".local" / "models"
CONFIG_DIR = Path.home() / ".config" / "llm_models"
MCP_CONFIG_FILE = CONFIG_DIR / "mcp_config.json"
MCP_REGISTRY_FILE = CONFIG_DIR / "mcp_registry.json"

# External drive information
EXTERNAL_DRIVE = Path("/Volumes/G Drive")
EXTERNAL_MODELS_DIR = EXTERNAL_DRIVE / "Models"

# Model information - Phi-2 is very small (only ~300MB)
MODEL_NAME = "phi-2"
MODEL_TYPE = "phi"
MODEL_FILES = [
    ("https://huggingface.co/microsoft/phi-2/resolve/main/model-00001-of-00002.safetensors", "model-00001-of-00002.safetensors"),
    ("https://huggingface.co/microsoft/phi-2/resolve/main/model-00002-of-00002.safetensors", "model-00002-of-00002.safetensors"),
    ("https://huggingface.co/microsoft/phi-2/resolve/main/config.json", "config.json"),
    ("https://huggingface.co/microsoft/phi-2/resolve/main/tokenizer.json", "tokenizer.json"),
    ("https://huggingface.co/microsoft/phi-2/resolve/main/tokenizer_config.json", "tokenizer_config.json"),
    ("https://huggingface.co/microsoft/phi-2/resolve/main/special_tokens_map.json", "special_tokens_map.json"),
]

def ensure_dir_exists(dir_path):
    """Create directory if it doesn't exist."""
    try:
        os.makedirs(dir_path, exist_ok=True)
        print(f"Ensured directory exists: {dir_path}")
        return True
    except Exception as e:
        print(f"Error creating directory {dir_path}: {e}")
        return False

def download_file(url, dest_path):
    """Download a file from URL to destination path using curl command."""
    print(f"Downloading {url} to {dest_path}")
    try:
        # Use curl command to download directly to destination
        result = subprocess.run(["curl", "-L", url, "-o", str(dest_path)], capture_output=True, text=True)
        if result.returncode != 0:
            print(f"Error downloading: {result.stderr}")
            return False
        
        if os.path.exists(dest_path) and os.path.getsize(dest_path) > 0:
            print(f"Successfully downloaded to {dest_path}")
            return True
        else:
            print(f"Download appears to have failed. Check file at {dest_path}")
            return False
    except Exception as e:
        print(f"Error downloading {url}: {e}")
        return False

def check_disk_space(path):
    """Check available disk space at the given path."""
    try:
        stat = os.statvfs(path)
        free_space = stat.f_frsize * stat.f_bavail
        free_space_mb = free_space / (1024 * 1024)
        print(f"Available space at {path}: {free_space_mb:.2f} MB")
        return free_space_mb
    except Exception as e:
        print(f"Error checking disk space: {e}")
        return 0

def update_mcp_config():
    """Update MCP configuration to use the model."""
    ensure_dir_exists(CONFIG_DIR)
    
    # Create or update MCP config - initially point to local path
    config = {
        "models": {
            MODEL_NAME: {
                "path": str(LOCAL_MODELS_DIR / MODEL_NAME),
                "type": MODEL_TYPE,
                "parameters": {
                    "max_tokens": 2048,
                    "temperature": 0.7
                }
            }
        }
    }
    
    # Write config file
    with open(MCP_CONFIG_FILE, "w") as f:
        json.dump(config, f, indent=4)
    
    print(f"Updated MCP config at {MCP_CONFIG_FILE}")
    
    # Create or update MCP registry
    registry = {
        "models": {
            MODEL_NAME: {
                "description": "Phi-2 - Microsoft's 2.7B compact and efficient model",
                "config": {
                    "model_path": str(LOCAL_MODELS_DIR / MODEL_NAME),
                    "model_type": MODEL_TYPE
                }
            }
        }
    }
    
    # Write registry file
    with open(MCP_REGISTRY_FILE, "w") as f:
        json.dump(registry, f, indent=4)
    
    print(f"Updated MCP registry at {MCP_REGISTRY_FILE}")

def move_files_to_external_drive():
    """Provide instructions for moving files to external drive."""
    print("\n=== INSTRUCTIONS FOR MOVING MODEL TO EXTERNAL DRIVE ===")
    print(f"1. Open Finder and navigate to: {LOCAL_MODELS_DIR}")
    print(f"2. Open another Finder window and navigate to your G Drive")
    print(f"3. Create a 'Models' folder on your G Drive if it doesn't exist")
    print(f"4. Copy the entire '{MODEL_NAME}' folder from {LOCAL_MODELS_DIR} to the Models folder on your G Drive")
    print(f"5. After copying, update the config files to point to the new location:")
    print(f"   - Edit {MCP_CONFIG_FILE}")
    print(f"   - Edit {MCP_REGISTRY_FILE}")
    print(f"   - Change all instances of '{LOCAL_MODELS_DIR / MODEL_NAME}' to '{EXTERNAL_MODELS_DIR / MODEL_NAME}'")
    print("=====================================================\n")

def clean_disk_space():
    """Provide instructions for cleaning disk space."""
    print("\n=== RECOMMENDATIONS FOR FREEING UP DISK SPACE ===")
    print("1. Remove ProTools and other unused applications:")
    print("   - Open Finder and navigate to Applications")
    print("   - Move unused large applications to trash or to your external drive")
    
    print("\n2. Clean up large files:")
    print("   - Run the following command to find large files on your system:")
    print("     sudo find / -type f -size +100M | grep -v '/System/|/Library/' | sort -nk 5")
    
    print("\n3. Remove old downloads and temporary files:")
    print("   - Clean your Downloads folder")
    print("   - Empty trash")
    
    print("\n4. Clear application caches:")
    print("   - Navigate to ~/Library/Caches and delete contents of large folders")
    
    print("\n5. Consider using the macOS Storage Management tool:")
    print("   - Apple menu > About This Mac > Storage > Manage...")
    print("================================================\n")

def main():
    """Main function to install the model."""
    print("Starting minimal model installation...")
    
    # Check available disk space
    home_space_mb = check_disk_space(Path.home())
    if home_space_mb < 500:  # Need at least 500MB for the model
        print("WARNING: Not enough disk space available on your system!")
        print("Please free up some space before continuing.")
        clean_disk_space()
        return False
    
    # Create local models directory
    ensure_dir_exists(LOCAL_MODELS_DIR)
    model_dir = LOCAL_MODELS_DIR / MODEL_NAME
    ensure_dir_exists(model_dir)
    
    # Download model files locally
    for url, filename in MODEL_FILES:
        dest_path = model_dir / filename
        if not download_file(url, dest_path):
            print(f"Failed to download {url}")
            return False
    
    # Update MCP configuration (pointing to local path initially)
    update_mcp_config()
    
    # Check external drive and provide instructions for moving
    if EXTERNAL_DRIVE.exists():
        print(f"\nExternal drive found at {EXTERNAL_DRIVE}")
        move_files_to_external_drive()
    else:
        print(f"\nExternal drive not found at {EXTERNAL_DRIVE}")
        print("Please connect your external drive, and then follow these steps:")
        move_files_to_external_drive()
    
    # Provide instructions for freeing disk space
    clean_disk_space()
    
    print("\nModel installation completed successfully!")
    print(f"Model installed to: {model_dir}")
    print("\nYou can now use this model with the MCP server.")
    print("To start the MCP server, run: python local_models_mcp_server.py")
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)