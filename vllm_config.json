{"model_settings": {"default": {"model_path": "local", "tensor_parallel_size": 1, "max_model_len": 8192, "trust_remote_code": true, "gpu_memory_utilization": 0.9, "quantization": "awq"}}, "midscene_settings": {"enabled": true, "chrome_path": "/Applications/Google Chrome.app/Contents/MacOS/Google Chrome", "headless": false, "timeout_ms": 60000, "viewport": {"width": 1280, "height": 800}, "connection_retries": 3, "connection_retry_delay": 2000}, "server": {"host": "localhost", "port": 8086, "cors_origin": "*"}, "logging": {"level": "debug"}}