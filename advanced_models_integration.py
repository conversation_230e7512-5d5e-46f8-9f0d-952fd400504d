# Advanced Models Integration
try:
    from advanced_models.unified_interface import UnifiedModelInterface, ResponseStrategy, QueryType
    from enhanced_agent_interface import EnhancedAgentInterface
    ADVANCED_MODELS_AVAILABLE = True
    print("✅ Advanced models loaded successfully")
except ImportError as e:
    ADVANCED_MODELS_AVAILABLE = False
    print(f"⚠️ Advanced models not available: {e}")

class AdvancedModelIntegration:
    """Integration class for advanced models"""
    
    def __init__(self):
        self.interface = None
        self.enhanced_interface = None
        self.initialized = False
    
    async def initialize(self):
        """Initialize advanced models"""
        if not ADVANCED_MODELS_AVAILABLE:
            return False
        
        try:
            self.interface = UnifiedModelInterface()
            await self.interface.initialize()
            
            self.enhanced_interface = EnhancedAgentInterface()
            await self.enhanced_interface.initialize()
            
            self.initialized = True
            return True
        except Exception as e:
            print(f"❌ Failed to initialize advanced models: {e}")
            return False
    
    async def process_query(self, query, context=None, image_data=None, strategy=None):
        """Process query with advanced models"""
        if not self.initialized:
            await self.initialize()
        
        if not self.initialized:
            return {
                'response': f"Advanced models not available. Query: {query}",
                'confidence': 0.0,
                'models_used': [],
                'error': 'Advanced models not initialized'
            }
        
        try:
            # Use enhanced interface for best results
            result = await self.enhanced_interface.process_query(
                query=query,
                context=context,
                image_data=image_data,
                strategy=strategy
            )
            
            return {
                'response': result.primary_response,
                'confidence': result.confidence,
                'processing_time': result.processing_time,
                'models_used': result.models_used,
                'strategy_used': result.strategy_used,
                'alternative_responses': result.alternative_responses,
                'metadata': result.metadata
            }
            
        except Exception as e:
            return {
                'response': f"Error processing query: {str(e)}",
                'confidence': 0.0,
                'models_used': [],
                'error': str(e)
            }
    
    def get_available_models(self):
        """Get list of available models"""
        if not ADVANCED_MODELS_AVAILABLE:
            return []
        
        return [
            "MANUS (OpenManus) - Autonomous Reasoning",
            "MiMo-VL-7B - Vision-Language Model", 
            "Detail Flow - ByteDance Flow Processing",
            "Giga Agent - Abacus.ai Autonomous Agent",
            "Honest AI - Google Research Agent"
        ]
    
    def get_available_strategies(self):
        """Get list of available strategies"""
        if not ADVANCED_MODELS_AVAILABLE:
            return []
        
        return [
            "best_single - Select best performing model",
            "consensus - Aggregate multiple model responses",
            "parallel_all - Query all models simultaneously", 
            "sequential - Query models in priority order",
            "specialized - Route to most appropriate model"
        ]

# Global instance
advanced_models = AdvancedModelIntegration()

# Helper functions for easy integration
async def query_advanced_models(query, context=None, image_data=None, strategy=None):
    """Helper function to query advanced models"""
    return await advanced_models.process_query(query, context, image_data, strategy)

def get_advanced_models_status():
    """Get status of advanced models"""
    return {
        'available': ADVANCED_MODELS_AVAILABLE,
        'initialized': advanced_models.initialized,
        'models': advanced_models.get_available_models(),
        'strategies': advanced_models.get_available_strategies()
    }
