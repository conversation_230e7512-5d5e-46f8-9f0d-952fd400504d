import logging
import os
import time
import datetime
import json
import uuid
import smtplib
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
from email.mime.application import MIMEApplication
from typing import Dict, List, Optional, Tuple
import pandas as pd
from pydantic import BaseModel, Field

# Import from other modules
from enhanced_lead_system import LeadRepository, Lead
from medicare_quote_engine import QuoteGenerator as MedicareQuoteGenerator, QuoteRepository as MedicareQuoteRepository
# Import IUL related modules
from insurance_products import get_iul_products
from insurance_carriers import get_iul_carriers
from quote_helper import generate_iul_quote, format_iul_proposal

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger("LeadFollowUpAutomation")

class EmailTemplate(BaseModel):
    """Model for email templates"""
    id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    name: str
    subject: str
    body: str
    created_at: str = Field(default_factory=lambda: datetime.datetime.now().isoformat())
    updated_at: str = Field(default_factory=lambda: datetime.datetime.now().isoformat())
    tags: List[str] = Field(default_factory=list)

class SMSTemplate(BaseModel):
    """Model for SMS templates"""
    id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    name: str
    body: str
    created_at: str = Field(default_factory=lambda: datetime.datetime.now().isoformat())
    updated_at: str = Field(default_factory=lambda: datetime.datetime.now().isoformat())
    tags: List[str] = Field(default_factory=list)

class FollowUpAction(BaseModel):
    """Model for follow-up actions"""
    id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    lead_id: str
    action_type: str  # "email", "sms", "call", "note"
    scheduled_time: str  # ISO format
    completed: bool = False
    completed_time: Optional[str] = None
    template_id: Optional[str] = None
    custom_message: Optional[str] = None
    result: Optional[str] = None  # Result of the action (e.g. "delivered", "opened", "clicked", "failed")
    notes: List[str] = Field(default_factory=list)

class EmailTemplateRepository:
    """Repository for managing email templates"""
    
    def __init__(self, storage_path: str = "email_templates.json"):
        self.storage_path = storage_path
        self.templates: Dict[str, EmailTemplate] = {}
        self._load_templates()
        
    def _load_templates(self) -> None:
        """Load templates from storage"""
        if os.path.exists(self.storage_path):
            try:
                with open(self.storage_path, "r") as f:
                    templates_data = json.load(f)
                    for template_id, template_dict in templates_data.items():
                        template = EmailTemplate(
                            id=template_id,
                            name=template_dict.get("name", ""),
                            subject=template_dict.get("subject", ""),
                            body=template_dict.get("body", ""),
                            created_at=template_dict.get("created_at", datetime.datetime.now().isoformat()),
                            updated_at=template_dict.get("updated_at", datetime.datetime.now().isoformat()),
                            tags=template_dict.get("tags", [])
                        )
                        self.templates[template_id] = template
                logger.info(f"Loaded {len(self.templates)} email templates from {self.storage_path}")
            except Exception as e:
                logger.error(f"Error loading email templates: {e}")
                # If no templates exist, initialize with defaults
                self._initialize_default_templates()
        else:
            # If the file doesn't exist, initialize with defaults
            self._initialize_default_templates()
    
    def _initialize_default_templates(self) -> None:
        """Initialize default email templates if none exist"""
        default_templates = [
            EmailTemplate(
                name="Quote Intro",
                subject="Your Personalized Medicare Insurance Quote",
                body="""Dear {first_name},

Thank you for your interest in Medicare insurance coverage. I'm excited to share your personalized quote with you.

Based on the information you provided, I've found several excellent options that match your needs and budget.

Your Recommended Plan:
{recommended_plan_name} from {recommended_plan_carrier}
Monthly Premium: ${recommended_plan_premium:.2f}
Estimated Annual Cost: ${recommended_plan_annual_cost:.2f}
Plan Type: {recommended_plan_type}

This plan stands out because {recommendation_reason}

I've attached a detailed summary of all the plans I've reviewed for you. I'd love to walk you through these options and answer any questions you might have.

Would you have time for a quick 15-minute call tomorrow? I'm available at 9 AM, 11 AM, or 2 PM.

Looking forward to helping you find the perfect coverage.

Best regards,
Your Insurance Agent
Phone: (*************
Email: <EMAIL>
""",
                tags=["quote", "intro", "medicare"]
            ),
            
            EmailTemplate(
                name="Follow-Up",
                subject="Following Up on Your Medicare Insurance Quote",
                body="""Dear {first_name},

I hope this email finds you well.

I wanted to follow up on the Medicare insurance quote I sent you recently. Have you had a chance to review the options?

I'd be happy to address any questions or concerns you might have about the plans, coverage details, or enrollment process.

Your Recommended Plan:
{recommended_plan_name} from {recommended_plan_carrier}
Monthly Premium: ${recommended_plan_premium:.2f}

Would you prefer to discuss this over the phone or via email? I'm here to help make this decision as smooth as possible for you.

Best regards,
Your Insurance Agent
Phone: (*************
Email: <EMAIL>
""",
                tags=["follow-up", "medicare"]
            ),
            
            EmailTemplate(
                name="Medicare Advantage Benefits",
                subject="Key Benefits of Your Medicare Advantage Plan",
                body="""Dear {first_name},

I wanted to highlight some of the key benefits of the Medicare Advantage plan I recommended for you:

1. Comprehensive healthcare coverage
2. Low monthly premium of ${recommended_plan_premium:.2f}
3. Prescription drug coverage
4. Additional benefits not covered by Original Medicare

Many of my clients particularly appreciate the {key_benefit} feature of this plan.

Would you like to schedule a call to discuss these benefits in more detail?

Best regards,
Your Insurance Agent
Phone: (*************
Email: <EMAIL>
""",
                tags=["medicare", "advantage", "benefits"]
            ),
            
            EmailTemplate(
                name="Medicare Supplement Benefits",
                subject="Key Benefits of Your Medicare Supplement Plan",
                body="""Dear {first_name},

I wanted to highlight some of the key benefits of the Medicare Supplement plan I recommended for you:

1. Freedom to see any doctor that accepts Medicare
2. No referrals needed to see specialists
3. Predictable out-of-pocket costs
4. Coverage when traveling within the US

Many of my clients particularly appreciate the {key_benefit} feature of this plan.

Would you like to schedule a call to discuss these benefits in more detail?

Best regards,
Your Insurance Agent
Phone: (*************
Email: <EMAIL>
""",
                tags=["medicare", "supplement", "benefits"]
            ),
            
            EmailTemplate(
                name="Thank You",
                subject="Thank You for Your Time",
                body="""Dear {first_name},

Thank you for taking the time to speak with me about your Medicare insurance options. I appreciate your questions and enjoyed our conversation.

As promised, I've attached the additional information we discussed. Please review it at your convenience.

If you have any further questions or are ready to move forward with enrollment, please don't hesitate to reach out.

Best regards,
Your Insurance Agent
Phone: (*************
Email: <EMAIL>
""",
                tags=["thank you", "follow-up"]
            )
        ]
        
        for template in default_templates:
            self.templates[template.id] = template
            
        self._save_templates()
        logger.info(f"Initialized {len(self.templates)} default email templates")
    
    def _save_templates(self) -> None:
        """Save templates to storage"""
        try:
            templates_dict = {template_id: template.dict() for template_id, template in self.templates.items()}
            with open(self.storage_path, "w") as f:
                json.dump(templates_dict, f, indent=2)
            logger.info(f"Saved {len(self.templates)} email templates to {self.storage_path}")
        except Exception as e:
            logger.error(f"Error saving email templates: {e}")
    
    def add_template(self, template: EmailTemplate) -> str:
        """Add a new template"""
        self.templates[template.id] = template
        self._save_templates()
        logger.info(f"Added email template {template.id}: {template.name}")
        return template.id
    
    def get_template(self, template_id: str) -> Optional[EmailTemplate]:
        """Get a template by ID"""
        return self.templates.get(template_id)
    
    def get_all_templates(self) -> List[EmailTemplate]:
        """Get all templates"""
        return list(self.templates.values())
    
    def get_templates_by_tag(self, tag: str) -> List[EmailTemplate]:
        """Get templates by tag"""
        return [t for t in self.templates.values() if tag in t.tags]
    
    def update_template(self, template_id: str, updated_data: Dict) -> Optional[EmailTemplate]:
        """Update a template"""
        if template_id not in self.templates:
            logger.warning(f"Template {template_id} not found for update")
            return None
        
        template = self.templates[template_id]
        # Update attributes
        for key, value in updated_data.items():
            if key in ["name", "subject", "body"]:
                setattr(template, key, value)
            elif key == "tags" and isinstance(value, list):
                template.tags = value
                
        # Update the updated_at timestamp
        template.updated_at = datetime.datetime.now().isoformat()
                
        self._save_templates()
        logger.info(f"Updated email template {template_id}")
        return template
    
    def delete_template(self, template_id: str) -> bool:
        """Delete a template"""
        if template_id in self.templates:
            del self.templates[template_id]
            self._save_templates()
            logger.info(f"Deleted email template {template_id}")
            return True
        logger.warning(f"Template {template_id} not found for deletion")
        return False

class SMSTemplateRepository:
    """Repository for managing SMS templates"""
    
    def __init__(self, storage_path: str = "sms_templates.json"):
        self.storage_path = storage_path
        self.templates: Dict[str, SMSTemplate] = {}
        self._load_templates()
        
    def _load_templates(self) -> None:
        """Load templates from storage"""
        if os.path.exists(self.storage_path):
            try:
                with open(self.storage_path, "r") as f:
                    templates_data = json.load(f)
                    for template_id, template_dict in templates_data.items():
                        template = SMSTemplate(
                            id=template_id,
                            name=template_dict.get("name", ""),
                            body=template_dict.get("body", ""),
                            created_at=template_dict.get("created_at", datetime.datetime.now().isoformat()),
                            updated_at=template_dict.get("updated_at", datetime.datetime.now().isoformat()),
                            tags=template_dict.get("tags", [])
                        )
                        self.templates[template_id] = template
                logger.info(f"Loaded {len(self.templates)} SMS templates from {self.storage_path}")
            except Exception as e:
                logger.error(f"Error loading SMS templates: {e}")
                # If no templates exist, initialize with defaults
                self._initialize_default_templates()
        else:
            # If the file doesn't exist, initialize with defaults
            self._initialize_default_templates()
    
    def _initialize_default_templates(self) -> None:
        """Initialize default SMS templates if none exist"""
        default_templates = [
            SMSTemplate(
                name="Quote Ready",
                body="""Hi {first_name}, your Medicare insurance quote is ready! I found a great plan for ${recommended_plan_premium:.2f}/month. When would be a good time to discuss? Reply with your preferred time or call me at (*************.""",
                tags=["quote", "intro", "medicare"]
            ),
            
            SMSTemplate(
                name="Follow-Up",
                body="""Hi {first_name}, just following up on your Medicare insurance quote. Have you had a chance to review it? I'm available to answer any questions you might have.""",
                tags=["follow-up", "medicare"]
            ),
            
            SMSTemplate(
                name="Appointment Reminder",
                body="""Hi {first_name}, this is a friendly reminder about our call tomorrow at {appointment_time} to discuss your Medicare insurance options. Looking forward to speaking with you!""",
                tags=["appointment", "reminder"]
            ),
            
            SMSTemplate(
                name="Thank You",
                body="""Hi {first_name}, thank you for your time today discussing your Medicare insurance options. If you have any more questions, feel free to call or text me anytime.""",
                tags=["thank you", "follow-up"]
            ),
            
            SMSTemplate(
                name="Enrollment Deadline",
                body="""Hi {first_name}, just a reminder that the Medicare enrollment deadline is approaching on {enrollment_deadline}. Would you like to finalize your application soon?""",
                tags=["deadline", "enrollment"]
            )
        ]
        
        for template in default_templates:
            self.templates[template.id] = template
            
        self._save_templates()
        logger.info(f"Initialized {len(self.templates)} default SMS templates")
    
    def _save_templates(self) -> None:
        """Save templates to storage"""
        try:
            templates_dict = {template_id: template.dict() for template_id, template in self.templates.items()}
            with open(self.storage_path, "w") as f:
                json.dump(templates_dict, f, indent=2)
            logger.info(f"Saved {len(self.templates)} SMS templates to {self.storage_path}")
        except Exception as e:
            logger.error(f"Error saving SMS templates: {e}")
    
    def add_template(self, template: SMSTemplate) -> str:
        """Add a new template"""
        self.templates[template.id] = template
        self._save_templates()
        logger.info(f"Added SMS template {template.id}: {template.name}")
        return template.id
    
    def get_template(self, template_id: str) -> Optional[SMSTemplate]:
        """Get a template by ID"""
        return self.templates.get(template_id)
    
    def get_all_templates(self) -> List[SMSTemplate]:
        """Get all templates"""
        return list(self.templates.values())
    
    def get_templates_by_tag(self, tag: str) -> List[SMSTemplate]:
        """Get templates by tag"""
        return [t for t in self.templates.values() if tag in t.tags]
    
    def update_template(self, template_id: str, updated_data: Dict) -> Optional[SMSTemplate]:
        """Update a template"""
        if template_id not in self.templates:
            logger.warning(f"Template {template_id} not found for update")
            return None
        
        template = self.templates[template_id]
        # Update attributes
        for key, value in updated_data.items():
            if key in ["name", "body"]:
                setattr(template, key, value)
            elif key == "tags" and isinstance(value, list):
                template.tags = value
                
        # Update the updated_at timestamp
        template.updated_at = datetime.datetime.now().isoformat()
                
        self._save_templates()
        logger.info(f"Updated SMS template {template_id}")
        return template
    
    def delete_template(self, template_id: str) -> bool:
        """Delete a template"""
        if template_id in self.templates:
            del self.templates[template_id]
            self._save_templates()
            logger.info(f"Deleted SMS template {template_id}")
            return True
        logger.warning(f"Template {template_id} not found for deletion")
        return False

class FollowUpActionRepository:
    """Repository for managing follow-up actions"""
    
    def __init__(self, storage_path: str = "follow_up_actions.json"):
        self.storage_path = storage_path
        self.actions: Dict[str, FollowUpAction] = {}
        self._load_actions()
        
    def _load_actions(self) -> None:
        """Load actions from storage"""
        if os.path.exists(self.storage_path):
            try:
                with open(self.storage_path, "r") as f:
                    actions_data = json.load(f)
                    for action_id, action_dict in actions_data.items():
                        action = FollowUpAction(
                            id=action_id,
                            lead_id=action_dict.get("lead_id", ""),
                            action_type=action_dict.get("action_type", ""),
                            scheduled_time=action_dict.get("scheduled_time", ""),
                            completed=action_dict.get("completed", False),
                            completed_time=action_dict.get("completed_time"),
                            template_id=action_dict.get("template_id"),
                            custom_message=action_dict.get("custom_message"),
                            result=action_dict.get("result"),
                            notes=action_dict.get("notes", [])
                        )
                        self.actions[action_id] = action
                logger.info(f"Loaded {len(self.actions)} follow-up actions from {self.storage_path}")
            except Exception as e:
                logger.error(f"Error loading follow-up actions: {e}")
                self.actions = {}
    
    def _save_actions(self) -> None:
        """Save actions to storage"""
        try:
            actions_dict = {action_id: action.dict() for action_id, action in self.actions.items()}
            with open(self.storage_path, "w") as f:
                json.dump(actions_dict, f, indent=2)
            logger.info(f"Saved {len(self.actions)} follow-up actions to {self.storage_path}")
        except Exception as e:
            logger.error(f"Error saving follow-up actions: {e}")
    
    def add_action(self, action: FollowUpAction) -> str:
        """Add a new action"""
        self.actions[action.id] = action
        self._save_actions()
        logger.info(f"Added follow-up action {action.id} for lead {action.lead_id}")
        return action.id
    
    def get_action(self, action_id: str) -> Optional[FollowUpAction]:
        """Get an action by ID"""
        return self.actions.get(action_id)
    
    def get_actions_for_lead(self, lead_id: str) -> List[FollowUpAction]:
        """Get all actions for a specific lead"""
        return [a for a in self.actions.values() if a.lead_id == lead_id]
    
    def get_pending_actions(self) -> List[FollowUpAction]:
        """Get all pending actions"""
        return [a for a in self.actions.values() if not a.completed]
    
    def get_due_actions(self) -> List[FollowUpAction]:
        """Get actions that are due but not completed"""
        now = datetime.datetime.now().isoformat()
        return [a for a in self.actions.values() if not a.completed and a.scheduled_time <= now]
    
    def update_action(self, action_id: str, updated_data: Dict) -> Optional[FollowUpAction]:
        """Update an action"""
        if action_id not in self.actions:
            logger.warning(f"Action {action_id} not found for update")
            return None
        
        action = self.actions[action_id]
        # Update attributes
        for key, value in updated_data.items():
            if key in ["lead_id", "action_type", "scheduled_time", "completed", "completed_time", 
                      "template_id", "custom_message", "result"]:
                setattr(action, key, value)
            elif key == "notes" and isinstance(value, list):
                action.notes = value
                
        self._save_actions()
        logger.info(f"Updated follow-up action {action_id}")
        return action
    
    def delete_action(self, action_id: str) -> bool:
        """Delete an action"""
        if action_id in self.actions:
            del self.actions[action_id]
            self._save_actions()
            logger.info(f"Deleted follow-up action {action_id}")
            return True
        logger.warning(f"Action {action_id} not found for deletion")
        return False
    
    def mark_as_completed(self, action_id: str, result: str = "completed", note: Optional[str] = None) -> bool:
        """Mark an action as completed"""
        if action_id not in self.actions:
            logger.warning(f"Action {action_id} not found for completion")
            return False
        
        action = self.actions[action_id]
        action.completed = True
        action.completed_time = datetime.datetime.now().isoformat()
        action.result = result
        
        if note:
            action.notes.append(note)
            
        self._save_actions()
        logger.info(f"Marked follow-up action {action_id} as completed with result: {result}")
        return True

class EmailSender:
    """Service for sending emails"""
    
    def __init__(self, smtp_server: str = "smtp.gmail.com", smtp_port: int = 587, 
                 username: str = None, password: str = None):
        self.smtp_server = smtp_server
        self.smtp_port = smtp_port
        self.username = username or os.environ.get("EMAIL_USERNAME")
        self.password = password or os.environ.get("EMAIL_PASSWORD")
        
        if not self.username or not self.password:
            logger.warning("Email username or password not provided. Email sending will not work.")
            
    def send_email(self, to_email: str, subject: str, body: str, 
                  attachment_path: Optional[str] = None) -> bool:
        """Send an email"""
        if not self.username or not self.password:
            logger.error("Email username or password not provided. Cannot send email.")
            return False
            
        try:
            # Create the email message
            msg = MIMEMultipart()
            msg["From"] = self.username
            msg["To"] = to_email
            msg["Subject"] = subject
            
            # Attach the body of the email
            msg.attach(MIMEText(body, "plain"))
            
            # Attach a file if provided
            if attachment_path and os.path.exists(attachment_path):
                with open(attachment_path, "rb") as f:
                    attachment = MIMEApplication(f.read(), _subtype="pdf")
                    attachment.add_header("Content-Disposition", f"attachment; filename={os.path.basename(attachment_path)}")
                    msg.attach(attachment)
            
            # Connect to the SMTP server and send the email
            with smtplib.SMTP(self.smtp_server, self.smtp_port) as server:
                server.starttls()
                server.login(self.username, self.password)
                server.send_message(msg)
                
            logger.info(f"Email sent successfully to {to_email}")
            return True
            
        except Exception as e:
            logger.error(f"Error sending email to {to_email}: {e}")
            return False

class SMSSender:
    """Service for sending SMS messages (mock implementation)"""
    
    def __init__(self, api_key: str = None):
        self.api_key = api_key or os.environ.get("SMS_API_KEY")
        
        if not self.api_key:
            logger.warning("SMS API key not provided. SMS sending will be mocked.")
            
    def send_sms(self, to_phone: str, message: str) -> bool:
        """Send an SMS message (mock implementation)"""
        try:
            # In a real implementation, this would use an SMS service API
            # Here we'll just log the message
            logger.info(f"[MOCK SMS] To: {to_phone}, Message: {message}")
            
            # Simulate API call delay
            time.sleep(0.5)
            
            logger.info(f"SMS sent successfully to {to_phone}")
            return True
            
        except Exception as e:
            logger.error(f"Error sending SMS to {to_phone}: {e}")
            return False

class LeadFollowUpService:
    """Service for automating lead follow-up"""
    
    def __init__(self):
        self.lead_repo = LeadRepository()
        self.action_repo = FollowUpActionRepository()
        self.email_template_repo = EmailTemplateRepository()
        self.sms_template_repo = SMSTemplateRepository()
        self.email_sender = EmailSender()
        self.sms_sender = SMSSender()
        self.quote_repo = MedicareQuoteRepository()
        
    def create_follow_up_plan(self, lead_id: str, quote_id: Optional[str] = None) -> List[str]:
        """Create a follow-up plan for a lead"""
        # Get the lead
        lead = self.lead_repo.get_lead(lead_id)
        if not lead:
            logger.warning(f"Lead {lead_id} not found for follow-up plan creation")
            return []
            
        # Get the quote if provided
        quote_summary = None
        if quote_id:
            quote_generator = MedicareQuoteGenerator()
            quote_summary = quote_generator.get_quote_summary(quote_id)
            
        # Create a series of follow-up actions
        action_ids = []
        
        # Current time
        now = datetime.datetime.now()
        
        # 1. Initial email with quote (immediate)
        if quote_summary and "error" not in quote_summary and lead.contact.email:
            email_template = self._find_best_email_template("quote")
            if email_template:
                # Format email content with lead and quote info
                subject, body = self._format_email_template(email_template, lead, quote_summary)
                
                # Create the action
                action = FollowUpAction(
                    lead_id=lead_id,
                    action_type="email",
                    scheduled_time=now.isoformat(),
                    template_id=email_template.id,
                    custom_message=f"Subject: {subject}\n\n{body}"
                )
                action_id = self.action_repo.add_action(action)
                action_ids.append(action_id)
        
        # 2. Initial SMS (immediate)
        if lead.contact.phone:
            sms_template = self._find_best_sms_template("quote")
            if sms_template:
                # Format SMS content with lead and quote info
                message = self._format_sms_template(sms_template, lead, quote_summary)
                
                # Create the action
                action = FollowUpAction(
                    lead_id=lead_id,
                    action_type="sms",
                    scheduled_time=now.isoformat(),
                    template_id=sms_template.id,
                    custom_message=message
                )
                action_id = self.action_repo.add_action(action)
                action_ids.append(action_id)
        
        # 3. Follow-up email (1 day later)
        if lead.contact.email:
            follow_up_time = now + datetime.timedelta(days=1)
            email_template = self._find_best_email_template("follow-up")
            if email_template:
                # Format email content with lead and quote info
                subject, body = self._format_email_template(email_template, lead, quote_summary)
                
                # Create the action
                action = FollowUpAction(
                    lead_id=lead_id,
                    action_type="email",
                    scheduled_time=follow_up_time.isoformat(),
                    template_id=email_template.id,
                    custom_message=f"Subject: {subject}\n\n{body}"
                )
                action_id = self.action_repo.add_action(action)
                action_ids.append(action_id)
        
        # 4. Follow-up SMS (2 days later)
        if lead.contact.phone:
            follow_up_time = now + datetime.timedelta(days=2)
            sms_template = self._find_best_sms_template("follow-up")
            if sms_template:
                # Format SMS content with lead and quote info
                message = self._format_sms_template(sms_template, lead, quote_summary)
                
                # Create the action
                action = FollowUpAction(
                    lead_id=lead_id,
                    action_type="sms",
                    scheduled_time=follow_up_time.isoformat(),
                    template_id=sms_template.id,
                    custom_message=message
                )
                action_id = self.action_repo.add_action(action)
                action_ids.append(action_id)
        
        # 5. Schedule a follow-up call (3 days later)
        call_time = now + datetime.timedelta(days=3)
        action = FollowUpAction(
            lead_id=lead_id,
            action_type="call",
            scheduled_time=call_time.isoformat(),
            custom_message=f"Follow-up call to discuss Medicare insurance quote"
        )
        action_id = self.action_repo.add_action(action)
        action_ids.append(action_id)
        
        # 6. Final follow-up email (5 days later)
        if lead.contact.email:
            final_follow_up_time = now + datetime.timedelta(days=5)
            email_template = self._find_best_email_template("follow-up")
            if email_template:
                # Format email content with lead and quote info
                subject, body = self._format_email_template(email_template, lead, quote_summary)
                
                # Create the action
                action = FollowUpAction(
                    lead_id=lead_id,
                    action_type="email",
                    scheduled_time=final_follow_up_time.isoformat(),
                    template_id=email_template.id,
                    custom_message=f"Subject: {subject}\n\n{body}"
                )
                action_id = self.action_repo.add_action(action)
                action_ids.append(action_id)
        
        # Update the lead's follow-up date
        self.lead_repo.update_lead(lead_id, {
            "follow_up_date": call_time.isoformat(),
            "status": "follow-up"
        })
        
        return action_ids
    
    def _find_best_email_template(self, tag: str) -> Optional[EmailTemplate]:
        """Find the best email template for a given tag"""
        templates = self.email_template_repo.get_templates_by_tag(tag)
        if templates:
            # For now, just return the first template with the tag
            return templates[0]
        return None
    
    def _find_best_sms_template(self, tag: str) -> Optional[SMSTemplate]:
        """Find the best SMS template for a given tag"""
        templates = self.sms_template_repo.get_templates_by_tag(tag)
        if templates:
            # For now, just return the first template with the tag
            return templates[0]
        return None
    
    def _format_email_template(self, template: EmailTemplate, lead: Lead, quote_summary: Optional[Dict]) -> Tuple[str, str]:
        """Format an email template with lead and quote information"""
        subject = template.subject
        body = template.body
        
        # Basic lead info replacements
        replacements = {
            "{first_name}": lead.first_name,
            "{last_name}": lead.last_name,
            "{full_name}": f"{lead.first_name} {lead.last_name}",
            "{email}": lead.contact.email or "",
            "{phone}": lead.contact.phone or "",
            "{address}": lead.contact.address or "",
            "{city}": lead.contact.city or "",
            "{state}": lead.contact.state or "",
            "{zip_code}": lead.contact.zip_code or "",
        }
        
        # Add quote info if available
        if quote_summary and "error" not in quote_summary:
            rec_plan = quote_summary.get("recommended_plan", {})
            recommendation_reason = "it provides excellent coverage at a competitive price."
            key_benefit = "comprehensive coverage"
            
            if quote_summary.get("notes"):
                for note in quote_summary.get("notes", []):
                    if "recommended because" in note:
                        recommendation_reason = note.split("because it is ")[1]
                        break
            
            benefits = []
            for plan in quote_summary.get("plans", []):
                if plan.get("is_recommended") and plan.get("key_benefits"):
                    benefits = plan.get("key_benefits", [])
                    if benefits:
                        key_benefit = benefits[0]
                    break
            
            quote_replacements = {
                "{recommended_plan_name}": rec_plan.get("name", ""),
                "{recommended_plan_carrier}": rec_plan.get("carrier", ""),
                "{recommended_plan_premium}": rec_plan.get("monthly_premium", 0.0),
                "{recommended_plan_annual_cost}": rec_plan.get("annual_cost", 0.0),
                "{recommended_plan_type}": rec_plan.get("type", ""),
                "{recommendation_reason}": recommendation_reason,
                "{key_benefit}": key_benefit,
                "{enrollment_deadline}": (datetime.datetime.now() + datetime.timedelta(days=30)).strftime("%B %d, %Y")
            }
            
            replacements.update(quote_replacements)
        
        # Apply all replacements
        for placeholder, value in replacements.items():
            subject = subject.replace(placeholder, str(value))
            body = body.replace(placeholder, str(value))
            
        return subject, body
    
    def _format_sms_template(self, template: SMSTemplate, lead: Lead, quote_summary: Optional[Dict]) -> str:
        """Format an SMS template with lead and quote information"""
        message = template.body
        
        # Basic lead info replacements
        replacements = {
            "{first_name}": lead.first_name,
            "{last_name}": lead.last_name,
            "{full_name}": f"{lead.first_name} {lead.last_name}",
            "{phone}": lead.contact.phone or "",
        }
        
        # Add quote info if available
        if quote_summary and "error" not in quote_summary:
            rec_plan = quote_summary.get("recommended_plan", {})
            
            quote_replacements = {
                "{recommended_plan_name}": rec_plan.get("name", ""),
                "{recommended_plan_carrier}": rec_plan.get("carrier", ""),
                "{recommended_plan_premium}": rec_plan.get("monthly_premium", 0.0),
                "{recommended_plan_type}": rec_plan.get("type", ""),
                "{appointment_time}": "10:00 AM", # Default time
                "{enrollment_deadline}": (datetime.datetime.now() + datetime.timedelta(days=30)).strftime("%B %d, %Y")
            }
            
            replacements.update(quote_replacements)
        
        # Apply all replacements
        for placeholder, value in replacements.items():
            message = message.replace(placeholder, str(value))
            
        return message
    
    def execute_due_actions(self) -> List[Dict]:
        """Execute all due follow-up actions"""
        # Get actions that are due
        due_actions = self.action_repo.get_due_actions()
        
        results = []
        
        for action in due_actions:
            result = {
                "action_id": action.id,
                "lead_id": action.lead_id,
                "action_type": action.action_type,
                "success": False,
                "message": ""
            }
            
            lead = self.lead_repo.get_lead(action.lead_id)
            if not lead:
                result["message"] = f"Lead {action.lead_id} not found"
                results.append(result)
                continue
                
            try:
                if action.action_type == "email":
                    # Parse the custom message for subject and body
                    message_parts = action.custom_message.split("\n\n", 1)
                    subject = message_parts[0].replace("Subject: ", "")
                    body = message_parts[1] if len(message_parts) > 1 else ""
                    
                    # Send the email
                    success = False
                    if lead.contact.email:
                        success = self.email_sender.send_email(
                            to_email=lead.contact.email,
                            subject=subject,
                            body=body
                        )
                    
                    if success:
                        self.action_repo.mark_as_completed(action.id, "delivered", f"Email sent to {lead.contact.email}")
                        result["success"] = True
                        result["message"] = f"Email sent to {lead.contact.email}"
                    else:
                        self.action_repo.mark_as_completed(action.id, "failed", f"Failed to send email to {lead.contact.email}")
                        result["message"] = f"Failed to send email to {lead.contact.email}"
                
                elif action.action_type == "sms":
                    # Send the SMS
                    success = False
                    if lead.contact.phone:
                        success = self.sms_sender.send_sms(
                            to_phone=lead.contact.phone,
                            message=action.custom_message
                        )
                    
                    if success:
                        self.action_repo.mark_as_completed(action.id, "delivered", f"SMS sent to {lead.contact.phone}")
                        result["success"] = True
                        result["message"] = f"SMS sent to {lead.contact.phone}"
                    else:
                        self.action_repo.mark_as_completed(action.id, "failed", f"Failed to send SMS to {lead.contact.phone}")
                        result["message"] = f"Failed to send SMS to {lead.contact.phone}"
                
                elif action.action_type == "call":
                    # For call actions, just mark as needing attention
                    # In a real system, this might integrate with a call center API or create a task
                    # in a CRM system
                    self.action_repo.mark_as_completed(action.id, "needs_attention", 
                                                      f"Call action for {lead.first_name} {lead.last_name} ({lead.contact.phone}) needs manual follow-up")
                    result["success"] = True
                    result["message"] = f"Call action for {lead.first_name} {lead.last_name} marked for manual follow-up"
                
                elif action.action_type == "note":
                    # For note actions, just mark as completed
                    self.action_repo.mark_as_completed(action.id, "completed", f"Note action for {lead.first_name} {lead.last_name} completed")
                    result["success"] = True
                    result["message"] = f"Note action for {lead.first_name} {lead.last_name} completed"
                
            except Exception as e:
                logger.error(f"Error executing action {action.id}: {e}")
                self.action_repo.mark_as_completed(action.id, "error", f"Error: {str(e)}")
                result["message"] = f"Error: {str(e)}"
                
            results.append(result)
            
        return results

class IULQuoteGenerator:
    """Generator for IUL quotes"""
    
    def __init__(self):
        self.iul_products = get_iul_products()
        self.iul_carriers = get_iul_carriers()
    
    def generate_quote_for_young_adult(self, lead_data):
        """Generate an IUL quote for a young adult"""
        try:
            # Extract client data
            age = lead_data.get("age", 32)
            gender = lead_data.get("gender", "Male")
            health_class = "Preferred" if not lead_data.get("medications") and not lead_data.get("smoker") else "Standard"
            income = lead_data.get("income", 85000)
            budget = lead_data.get("budget", 200)
            
            # For IUL, we typically want to base the death benefit on income multiple
            # Common rule of thumb: 10-15x annual income
            target_coverage = min(income * 12, 1000000)  # Cap at $1M for simplicity
            
            # Adjust based on budget
            monthly_premium = budget
            
            # Find suitable products from carriers
            suitable_products = []
            for carrier, products in self.iul_carriers.items():
                for product in products:
                    if product in self.iul_products:
                        product_info = self.iul_products[product]
                        if product_info.get("min_age", 18) <= age <= product_info.get("max_age", 70):
                            suitable_products.append({
                                "carrier": carrier,
                                "product": product,
                                "details": product_info
                            })
            
            if not suitable_products:
                return {
                    "error": "No suitable IUL products found",
                    "quote_id": None
                }
            
            # Sort by product rating or other criteria
            suitable_products.sort(key=lambda p: p["details"].get("rating", 0), reverse=True)
            
            # Calculate death benefit based on age, gender, and premium
            # This is a simplified calculation - real IUL would use actuarial tables
            base_multiple = 250 if age < 35 else 200
            gender_factor = 1.1 if gender == "Female" else 1.0
            health_factor = 1.2 if health_class == "Preferred" else 1.0
            
            death_benefit = monthly_premium * 12 * base_multiple * gender_factor * health_factor
            
            # Cap at target coverage
            if death_benefit > target_coverage:
                death_benefit = target_coverage
                # Recalculate premium
                monthly_premium = (death_benefit / (base_multiple * gender_factor * health_factor)) / 12
            
            # Calculate cash value growth projections (simplified)
            projected_cash_values = {}
            for year in range(1, 31):  # 30-year projection
                if year <= 5:
                    projected_cash_values[year] = monthly_premium * 12 * year * 0.7  # First 5 years lower due to fees
                else:
                    # After year 5, assume better growth
                    projected_cash_values[year] = projected_cash_values[5] + (monthly_premium * 12 * (year - 5) * 0.9)
            
            # Calculate projected retirement income
            # Assume start at age 65 with 15 years of distributions
            years_to_retirement = max(65 - age, 20)
            retirement_cash_value = projected_cash_values[min(years_to_retirement, 30)]
            annual_retirement_income = retirement_cash_value * 0.05  # 5% annual distribution
            total_retirement_income = annual_retirement_income * 15
            
            # Generate quote ID
            import uuid
            quote_id = str(uuid.uuid4())
            
            # Build the quote summary
            quote_summary = {
                "quote_id": quote_id,
                "client": {
                    "age": age,
                    "gender": gender,
                    "health_class": health_class,
                    "income": income,
                    "budget": budget
                },
                "product_type": "IUL",
                "recommended_plan": {
                    "carrier": suitable_products[0]["carrier"],
                    "name": suitable_products[0]["product"],
                    "death_benefit": round(death_benefit, 2),
                    "monthly_premium": round(monthly_premium, 2),
                    "annual_premium": round(monthly_premium * 12, 2),
                    "cash_value_projection": {
                        "year_10": round(projected_cash_values.get(10, 0), 2),
                        "year_20": round(projected_cash_values.get(20, 0), 2),
                        "at_retirement": round(retirement_cash_value, 2)
                    },
                    "retirement_income": {
                        "annual_amount": round(annual_retirement_income, 2),
                        "years": 15,
                        "total_amount": round(total_retirement_income, 2)
                    }
                },
                "alternative_plans": [
                    {
                        "carrier": p["carrier"],
                        "name": p["product"],
                        "monthly_premium": round(monthly_premium * (1 + (i * 0.05)), 2),
                        "death_benefit": round(death_benefit * (1 + (i * 0.03)), 2)
                    } for i, p in enumerate(suitable_products[1:4])  # Next 3 products as alternatives
                ] if len(suitable_products) > 1 else [],
                "notes": [
                    f"This IUL plan is recommended because it provides strong cash value growth potential with a competitive death benefit.",
                    f"The plan is designed to stay within your budget of ${budget}/month while providing a ${round(death_benefit):,} death benefit.",
                    f"Projected to provide ${round(annual_retirement_income):,} of annual tax-free retirement income for 15 years, totaling ${round(total_retirement_income):,}."
                ]
            }
            
            return quote_summary
            
        except Exception as e:
            import traceback
            return {
                "error": f"Error generating IUL quote: {str(e)}",
                "traceback": traceback.format_exc(),
                "quote_id": None
            }
    
    def get_quote_summary(self, quote_id):
        """Retrieve a quote summary by ID"""
        # In a real implementation, this would retrieve from a database
        # Here we'll regenerate using a dummy lead
        if not quote_id:
            return {"error": "Quote ID not provided"}
            
        dummy_lead = {
            "age": 32,
            "gender": "Male",
            "income": 85000,
            "budget": 200,
            "medications": [],
            "smoker": False
        }
        
        return self.generate_quote_for_young_adult(dummy_lead)

def generate_iul_quote_for_paul_edwards():
    """Generate an IUL quote for Paul Edwards"""
    
    # Create quote generator
    quote_generator = IULQuoteGenerator()
    
    # Paul Edwards data
    paul_data = {
        "first_name": "Paul",
        "last_name": "Edwards",
        "email": "<EMAIL>",
        "phone": "7725395908",
        "age": 32,
        "gender": "Male",
        "dob": "02/22/1993",
        "address": "2426 SE Whitehorse Street",
        "city": "Port Saint Lucie",
        "state": "FL",
        "zip": "34983",
        "ssn": "590312356",
        "income": 85000,
        "budget": 200,
        "medications": [],
        "smoker": False
    }
    
    # Generate the quote
    quote_summary = quote_generator.generate_quote_for_young_adult(paul_data)
    
    # Save the quote summary
    import json
    import os
    os.makedirs("data/quotes", exist_ok=True)
    
    with open("data/quotes/paul_edwards_iul_quote.json", "w") as f:
        json.dump(quote_summary, f, indent=2)
    
    return quote_summary

def create_follow_up_plan_for_paul_edwards():
    """Create a follow-up plan for Paul Edwards"""
    from enhanced_lead_system import create_test_lead_paul_edwards, LeadRepository
    from medicare_quote_engine import generate_quote_for_paul_edwards
    
    # Ensure the test lead exists
    lead_repo = LeadRepository()
    existing_pauls = lead_repo.search_leads(name="Paul Edwards")
    
    if existing_pauls:
        lead = existing_pauls[0]
        lead_id = lead.id
    else:
        lead = create_test_lead_paul_edwards()
        lead_id = lead_repo.add_lead(lead)
    
    # Generate a quote if needed
    quote_summary = generate_quote_for_paul_edwards()
    quote_id = quote_summary.get("quote_id") if "error" not in quote_summary else None
    
    # Create follow-up plan
    follow_up_service = LeadFollowUpService()
    action_ids = follow_up_service.create_follow_up_plan(lead_id, quote_id)
    
    # Execute immediate actions
    results = follow_up_service.execute_due_actions()
    
    return {
        "lead_id": lead_id,
        "quote_id": quote_id,
        "action_ids": action_ids,
        "execution_results": results
    }

def main():
    """Main function for testing"""
    print("Creating follow-up plan for Paul Edwards...")
    result = create_follow_up_plan_for_paul_edwards()
    
    print(f"Lead ID: {result['lead_id']}")
    print(f"Quote ID: {result['quote_id']}")
    print(f"Created {len(result['action_ids'])} follow-up actions")
    
    print("\nExecution Results:")
    for i, res in enumerate(result['execution_results']):
        print(f"{i+1}. {res['action_type']} - {'Success' if res['success'] else 'Failed'}: {res['message']}")
    
    # Export the result to JSON
    import json
    with open("paul_edwards_follow_up.json", "w") as f:
        json.dump(result, f, indent=2)
    print("\nFollow-up plan details exported to paul_edwards_follow_up.json")
    
if __name__ == "__main__":
    main()