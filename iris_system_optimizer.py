#!/usr/bin/env python3
"""
IRIS System Optimizer
====================

Optimizes the IRIS system by:
- Cleaning up unnecessary files and duplicates
- Optimizing disk space usage
- Verifying system integration
- Preparing the system for production use
"""

import os
import shutil
import logging
from pathlib import Path
from typing import List, Dict, Any
import json

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class IRISSystemOptimizer:
    """Optimizes and cleans up the IRIS system"""
    
    def __init__(self, project_root: str = "."):
        self.project_root = Path(project_root)
        self.cleanup_stats = {
            'files_removed': 0,
            'directories_cleaned': 0,
            'space_saved': 0,
            'duplicates_found': 0
        }
        
    def optimize_system(self):
        """Run complete system optimization"""
        print("🔧 IRIS SYSTEM OPTIMIZATION")
        print("=" * 50)
        
        try:
            # Clean up temporary files
            self._cleanup_temporary_files()
            
            # Remove duplicate files
            self._remove_duplicates()
            
            # Optimize Python cache
            self._cleanup_python_cache()
            
            # Clean up logs
            self._cleanup_old_logs()
            
            # Verify core files
            self._verify_core_files()
            
            # Generate optimization report
            self._generate_optimization_report()
            
            print("\n✅ System optimization completed successfully!")
            
        except Exception as e:
            logger.error(f"System optimization failed: {e}")
            print(f"❌ System optimization failed: {e}")
    
    def _cleanup_temporary_files(self):
        """Clean up temporary and cache files"""
        print("\n🧹 Cleaning temporary files...")
        
        temp_patterns = [
            "*.tmp",
            "*.temp",
            "*.cache",
            "*.pyc",
            "*.pyo",
            "*.pyd",
            "__pycache__",
            ".pytest_cache",
            ".coverage",
            "*.log.old",
            "*.bak",
            "*.swp",
            ".DS_Store",
            "Thumbs.db"
        ]
        
        for pattern in temp_patterns:
            self._remove_files_by_pattern(pattern)
    
    def _remove_duplicates(self):
        """Remove duplicate files"""
        print("\n🔍 Checking for duplicate files...")
        
        # Known duplicate patterns in the project
        potential_duplicates = [
            ("agent_system.py", "rag_agent_system.py"),
            ("unified_agent_dashboard.py", "iris_dashboard.py"),
            ("advanced_models_dashboard.py", "iris_dashboard.py")
        ]
        
        for file1, file2 in potential_duplicates:
            path1 = self.project_root / file1
            path2 = self.project_root / file2
            
            if path1.exists() and path2.exists():
                # Check if files are similar (basic check)
                if self._files_are_similar(path1, path2):
                    print(f"  📄 Found potential duplicate: {file1} and {file2}")
                    # Keep the newer/better version
                    if "iris" in file2.lower():
                        self._safe_remove_file(path1)
                    else:
                        self._safe_remove_file(path2)
    
    def _cleanup_python_cache(self):
        """Clean up Python cache directories"""
        print("\n🐍 Cleaning Python cache...")
        
        cache_dirs = []
        for root, dirs, files in os.walk(self.project_root):
            for dir_name in dirs:
                if dir_name == "__pycache__":
                    cache_dirs.append(Path(root) / dir_name)
        
        for cache_dir in cache_dirs:
            try:
                shutil.rmtree(cache_dir)
                self.cleanup_stats['directories_cleaned'] += 1
                print(f"  🗑️ Removed cache directory: {cache_dir}")
            except Exception as e:
                logger.warning(f"Failed to remove cache directory {cache_dir}: {e}")
    
    def _cleanup_old_logs(self):
        """Clean up old log files"""
        print("\n📝 Cleaning old log files...")
        
        log_files = list(self.project_root.glob("*.log"))
        log_files.extend(list(self.project_root.glob("*.log.*")))
        
        for log_file in log_files:
            try:
                # Keep recent log files, remove old ones
                if log_file.stat().st_size > 10 * 1024 * 1024:  # > 10MB
                    self._safe_remove_file(log_file)
                    print(f"  🗑️ Removed large log file: {log_file}")
            except Exception as e:
                logger.warning(f"Failed to process log file {log_file}: {e}")
    
    def _verify_core_files(self):
        """Verify that core IRIS files are present and valid"""
        print("\n✅ Verifying core system files...")
        
        core_files = [
            "iris_dashboard.py",
            "iris_cli.py",
            "enhanced_agent_interface.py",
            "IRIS_USER_GUIDE.md",
            "advanced_models/__init__.py",
            "advanced_models/unified_interface.py",
            "advanced_models/mimo_vl_agent.py",
            "advanced_models/web_vision_agent.py",
            "advanced_models/visual_task_executor.py"
        ]
        
        missing_files = []
        for file_path in core_files:
            full_path = self.project_root / file_path
            if not full_path.exists():
                missing_files.append(file_path)
            else:
                print(f"  ✅ {file_path}")
        
        if missing_files:
            print(f"\n⚠️ Missing core files:")
            for missing in missing_files:
                print(f"  ❌ {missing}")
        else:
            print(f"\n✅ All core files present")
    
    def _remove_files_by_pattern(self, pattern: str):
        """Remove files matching a pattern"""
        if pattern == "__pycache__":
            return  # Handled separately
        
        files_to_remove = list(self.project_root.rglob(pattern))
        
        for file_path in files_to_remove:
            self._safe_remove_file(file_path)
    
    def _safe_remove_file(self, file_path: Path):
        """Safely remove a file with error handling"""
        try:
            if file_path.is_file():
                size = file_path.stat().st_size
                file_path.unlink()
                self.cleanup_stats['files_removed'] += 1
                self.cleanup_stats['space_saved'] += size
                print(f"  🗑️ Removed: {file_path}")
            elif file_path.is_dir():
                shutil.rmtree(file_path)
                self.cleanup_stats['directories_cleaned'] += 1
                print(f"  🗑️ Removed directory: {file_path}")
        except Exception as e:
            logger.warning(f"Failed to remove {file_path}: {e}")
    
    def _files_are_similar(self, file1: Path, file2: Path) -> bool:
        """Check if two files are similar (basic implementation)"""
        try:
            # Simple size comparison
            size1 = file1.stat().st_size
            size2 = file2.stat().st_size
            
            # If sizes are very different, they're not duplicates
            if abs(size1 - size2) > min(size1, size2) * 0.1:
                return False
            
            # Read first few lines for comparison
            with open(file1, 'r', encoding='utf-8', errors='ignore') as f1:
                lines1 = f1.readlines()[:10]
            
            with open(file2, 'r', encoding='utf-8', errors='ignore') as f2:
                lines2 = f2.readlines()[:10]
            
            # Check if first few lines are similar
            similar_lines = sum(1 for l1, l2 in zip(lines1, lines2) if l1.strip() == l2.strip())
            return similar_lines >= min(len(lines1), len(lines2)) * 0.7
            
        except Exception:
            return False
    
    def _generate_optimization_report(self):
        """Generate optimization report"""
        print("\n📊 OPTIMIZATION REPORT")
        print("-" * 30)
        print(f"Files removed: {self.cleanup_stats['files_removed']}")
        print(f"Directories cleaned: {self.cleanup_stats['directories_cleaned']}")
        print(f"Space saved: {self.cleanup_stats['space_saved'] / 1024 / 1024:.2f} MB")
        print(f"Duplicates found: {self.cleanup_stats['duplicates_found']}")
        
        # Save report
        report = {
            'optimization_stats': self.cleanup_stats,
            'timestamp': str(Path().cwd()),
            'project_root': str(self.project_root)
        }
        
        with open('iris_optimization_report.json', 'w') as f:
            json.dump(report, f, indent=2)
        
        print(f"\n💾 Optimization report saved to: iris_optimization_report.json")

def create_iris_launcher():
    """Create a simple launcher script for IRIS"""
    launcher_content = '''#!/usr/bin/env python3
"""
IRIS System Launcher
===================

Quick launcher for the IRIS system.
"""

import sys
import asyncio
from pathlib import Path

# Add current directory to path
sys.path.append(str(Path(__file__).parent))

def main():
    """Main launcher function"""
    print("🚀 IRIS System Launcher")
    print("=" * 30)
    print("1. Interactive CLI")
    print("2. Dashboard Mode")
    print("3. System Test")
    print("4. Help")
    print("5. Exit")
    
    while True:
        try:
            choice = input("\\nSelect option (1-5): ").strip()
            
            if choice == "1":
                print("Starting IRIS Interactive CLI...")
                import iris_cli
                asyncio.run(iris_cli.main())
                break
            elif choice == "2":
                print("Starting IRIS Dashboard...")
                import iris_dashboard
                asyncio.run(iris_dashboard.main())
                break
            elif choice == "3":
                print("Running IRIS System Test...")
                import iris_system_test
                asyncio.run(iris_system_test.main())
                break
            elif choice == "4":
                print("\\n📖 IRIS Help:")
                print("• Option 1: Interactive CLI for conversational interface")
                print("• Option 2: Dashboard mode for programmatic access")
                print("• Option 3: System test to verify all components")
                print("• See IRIS_USER_GUIDE.md for detailed instructions")
            elif choice == "5":
                print("👋 Goodbye!")
                break
            else:
                print("❌ Invalid option. Please select 1-5.")
                
        except KeyboardInterrupt:
            print("\\n👋 Goodbye!")
            break
        except Exception as e:
            print(f"❌ Error: {e}")

if __name__ == "__main__":
    main()
'''
    
    with open('iris_launcher.py', 'w') as f:
        f.write(launcher_content)
    
    # Make it executable
    os.chmod('iris_launcher.py', 0o755)
    
    print("✅ Created IRIS launcher: iris_launcher.py")

def create_quick_start_script():
    """Create a quick start script"""
    quick_start_content = '''#!/bin/bash
# IRIS Quick Start Script

echo "🚀 IRIS System Quick Start"
echo "=========================="

# Check Python version
python_version=$(python3 --version 2>&1)
echo "Python: $python_version"

# Check if virtual environment is active
if [[ "$VIRTUAL_ENV" != "" ]]; then
    echo "✅ Virtual environment: $VIRTUAL_ENV"
else
    echo "⚠️ No virtual environment detected"
    echo "Consider activating a virtual environment first"
fi

# Install dependencies if needed
echo "📦 Checking dependencies..."
pip install -q -r requirements.txt 2>/dev/null || echo "⚠️ Some dependencies may be missing"

# Start IRIS
echo "🚀 Starting IRIS..."
python3 iris_launcher.py
'''
    
    with open('start_iris.sh', 'w') as f:
        f.write(quick_start_content)
    
    # Make it executable
    os.chmod('start_iris.sh', 0o755)
    
    print("✅ Created quick start script: start_iris.sh")

def main():
    """Main optimization function"""
    print("🔧 IRIS SYSTEM OPTIMIZATION AND SETUP")
    print("=" * 50)
    
    # Run optimization
    optimizer = IRISSystemOptimizer()
    optimizer.optimize_system()
    
    # Create launcher scripts
    print("\n📝 Creating launcher scripts...")
    create_iris_launcher()
    create_quick_start_script()
    
    # Final instructions
    print("\n" + "=" * 50)
    print("🎉 IRIS SYSTEM OPTIMIZATION COMPLETE!")
    print("=" * 50)
    print("\n🚀 To start IRIS, use any of these methods:")
    print("1. ./start_iris.sh                    # Quick start script")
    print("2. python iris_launcher.py            # Interactive launcher")
    print("3. python iris_cli.py                 # Direct CLI access")
    print("4. python iris_dashboard.py           # Direct dashboard access")
    print("\n📖 For detailed instructions, see: IRIS_USER_GUIDE.md")
    print("\n🧪 To test the system: python iris_system_test.py")
    print("\n✅ IRIS is ready for use!")

if __name__ == "__main__":
    main()
