#!/bin/bash
# 
# Setup and Run Script
#
# This script provides a command-line interface to set up and run the entire 
# integrated system with unrestricted capabilities. It handles:
# - ProtonVPN setup and configuration
# - Security tools initialization with NO RESTRICTIONS
# - Advanced credential recovery for any account or system
# - Agent system configuration and enhancement
# - UI Tars integration
# - Agent security integration
#
# SECURITY NOTE: This script provides unrestricted access to powerful tools.
# This is designed for a controlled environment where full access is authorized.
#

# Set up colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Banner
display_banner() {
    echo -e "${BLUE}"
    echo "╔══════════════════════════════════════════════════════════════╗"
    echo "║                                                              ║"
    echo "║  FULL INTEGRATION SYSTEM - UNRESTRICTED ACCESS               ║"
    echo "║                                                              ║"
    echo "║  • Security Tools - NO RESTRICTIONS                          ║"
    echo "║  • Advanced Credential Recovery                              ║"
    echo "║  • ProtonVPN Integration                                     ║"
    echo "║  • Email & Portal Access                                     ║"
    echo "║  • UI Tars Integration                                       ║"
    echo "║  • Agent Security Integration                                ║"
    echo "║                                                              ║"
    echo "║  CONTROLLED ENVIRONMENT - AUTHORIZED ACCESS ONLY             ║"
    echo "║                                                              ║"
    echo "╚══════════════════════════════════════════════════════════════╝"
    echo -e "${NC}"
}

# Check Python availability
check_python() {
    if ! command -v python3 &> /dev/null; then
        echo -e "${RED}Error: Python 3 is not installed or not in PATH${NC}"
        echo "Please install Python 3 and try again"
        exit 1
    fi
    
    echo -e "${GREEN}Python 3 found${NC}"
}

# Check script dependencies
check_dependencies() {
    local missing_files=()
    
    # List of required files
    required_files=(
        "unrestricted_security_tools.py"
        "protonvpn_setup.py"
        "full_agent_integration.py"
        "advanced_credential_recovery.py"
        "agent_security_integration.py"
    )
    
    for file in "${required_files[@]}"; do
        if [[ ! -f "$file" ]]; then
            missing_files+=("$file")
        fi
    done
    
    if [[ ${#missing_files[@]} -gt 0 ]]; then
        echo -e "${RED}Error: The following required files are missing:${NC}"
        for file in "${missing_files[@]}"; do
            echo "  - $file"
        done
        exit 1
    fi
    
    echo -e "${GREEN}All required files found${NC}"
}

# Install required packages
install_required_packages() {
    echo -e "${YELLOW}Installing required Python packages...${NC}"
    
    # Define required packages
    packages=(
        "requests"
        "selenium"
        "cryptography"
        "keyring"
        "paramiko"          # For SSH operations
        "pyopenssl"         # For SSL/TLS operations
        "pycryptodome"      # Advanced cryptography
        "pillow"            # For image processing
        "scapy"             # For network packet manipulation
    )
    
    python3 -m pip install --upgrade pip
    
    for package in "${packages[@]}"; do
        echo -e "${BLUE}Installing $package...${NC}"
        python3 -m pip install $package
    done
    
    echo -e "${GREEN}All required packages installed${NC}"
}

# Set up ProtonVPN
setup_protonvpn() {
    echo -e "${YELLOW}Setting up ProtonVPN...${NC}"
    
    if [[ "$1" == "--auto" ]]; then
        # Automatic mode
        if [[ -n "$2" && -n "$3" ]]; then
            python3 protonvpn_setup.py --auto "$2" "$3"
        else
            python3 protonvpn_setup.py --auto
        fi
    else
        # Interactive mode
        python3 protonvpn_setup.py
    fi
    
    if [[ $? -eq 0 ]]; then
        echo -e "${GREEN}ProtonVPN setup completed successfully${NC}"
    else
        echo -e "${RED}ProtonVPN setup failed${NC}"
    fi
}

# Run full integration
run_full_integration() {
    echo -e "${YELLOW}Running full system integration...${NC}"
    
    if [[ "$1" == "--setup" ]]; then
        python3 full_agent_integration.py --setup
    elif [[ "$1" == "--security" ]]; then
        python3 full_agent_integration.py --security
    elif [[ "$1" == "--vpn" ]]; then
        python3 full_agent_integration.py --vpn
    elif [[ "$1" == "--tars" ]]; then
        python3 full_agent_integration.py --tars
    elif [[ "$1" == "--agent" ]]; then
        python3 full_agent_integration.py --agent
    else:
        python3 full_agent_integration.py
    fi
    
    if [[ $? -eq 0 ]]; then
        echo -e "${GREEN}Full system integration completed successfully${NC}"
    else
        echo -e "${RED}Full system integration failed${NC}"
    fi
}

# List security tools
list_security_tools() {
    echo -e "${YELLOW}Listing available security tools...${NC}"
    python3 -c "from unrestricted_security_tools import security_tools; print('\n'.join([f'- {tool}' for tool in security_tools.get_installed_tools()]))"
}

# Test credential recovery
test_credential_recovery() {
    echo -e "${YELLOW}Testing credential recovery capabilities...${NC}"
    python3 advanced_credential_recovery.py
}

# Run agent security integration
run_security_integration() {
    echo -e "${YELLOW}Running agent security integration...${NC}"
    python3 agent_security_integration.py --integrate
}

# Display help
display_help() {
    echo "Usage: $0 [OPTION]"
    echo
    echo "Options:"
    echo "  --help          Display this help message"
    echo "  --setup         Run full system setup"
    echo "  --vpn           Set up ProtonVPN only"
    echo "  --vpn-auto      Set up ProtonVPN automatically (no interaction)"
    echo "  --vpn-auto USER PASS    Set up ProtonVPN with credentials"
    echo "  --security      List available security tools"
    echo "  --tars          Configure and launch UI Tars"
    echo "  --agent         Launch Agent Access System"
    echo "  --recovery      Test credential recovery capabilities"
    echo "  --integrate     Run agent security integration"
    echo "  --all           Install dependencies and run full setup"
    echo
    echo "Examples:"
    echo "  $0 --all                # Install dependencies and run full setup"
    echo "  $0 --vpn                # Set up ProtonVPN interactively"
    echo "  $0 --vpn-auto user pass # Set up ProtonVPN with credentials"
    echo "  $0 --security           # List available security tools"
    echo "  $0 --recovery           # Test credential recovery capabilities"
}

# Main function
main() {
    # Display banner
    display_banner
    
    # Process command line arguments
    if [[ $# -eq 0 ]]; then
        display_help
        exit 0
    fi
    
    case "$1" in
        --help)
            display_help
            ;;
        --setup)
            check_python
            check_dependencies
            run_full_integration "--setup"
            ;;
        --vpn)
            check_python
            check_dependencies
            setup_protonvpn
            ;;
        --vpn-auto)
            check_python
            check_dependencies
            if [[ -n "$2" && -n "$3" ]]; then
                setup_protonvpn "--auto" "$2" "$3"
            else
                setup_protonvpn "--auto"
            fi
            ;;
        --security)
            check_python
            check_dependencies
            list_security_tools
            ;;
        --recovery)
            check_python
            check_dependencies
            test_credential_recovery
            ;;
        --integrate)
            check_python
            check_dependencies
            run_security_integration
            ;;
        --tars)
            check_python
            check_dependencies
            run_full_integration "--tars"
            ;;
        --agent)
            check_python
            check_dependencies
            run_full_integration "--agent"
            ;;
        --all)
            check_python
            check_dependencies
            install_required_packages
            setup_protonvpn
            run_security_integration
            run_full_integration "--setup"
            ;;
        *)
            echo -e "${RED}Unknown option: $1${NC}"
            display_help
            exit 1
            ;;
    esac
}

# Run main function
main "$@"