# Gmail Setup Guide for Flo Faction Insurance

This guide will help you set up the Flo Faction Gmail account (<EMAIL>) to send automated emails to clients like <PERSON>.

## Step 1: Generate an App Password for Gmail

Since we're using a script to send emails, we need to generate an App Password instead of using the regular Gmail password:

1. Go to your Google Account settings: https://myaccount.google.com/
2. Click on "Security" in the left sidebar
3. Under "Signing in to Google," click on "2-Step Verification" (you must have this enabled)
4. Scroll to the bottom and click on "App passwords"
5. Select "Mail" as the app and "Other" as the device
6. Enter "Flo Faction Insurance Script" as the name
7. Click "Generate"
8. Google will display a 16-character password - **copy this password**
9. Use this password in the script instead of your regular Gmail password

## Step 2: Update the Email Configuration in the Script

Open the `send_paul_edwards_communications_fixed.py` script and update the email configuration:

```python
# Email configuration - using Flo Faction email
EMAIL_SENDER = "<EMAIL>"
EMAIL_PASSWORD = "your_16_character_app_password_here"  # Paste the app password you generated
SMTP_SERVER = "smtp.gmail.com"
SMTP_PORT = 587
```

## Step 3: Create Email Templates in Gmail

To make future emails easier to send, create templates in Gmail:

1. Log in to <NAME_EMAIL>
2. Click "Compose" to create a new email
3. Write your template email (you can use the template from our script)
4. Click the three dots at the bottom of the compose window
5. Select "Templates" > "Save draft as template" > "Save as new template"
6. Name your template (e.g., "IUL Introduction - Paul Edwards")
7. Repeat for other email templates you want to save

## Step 4: Set Up Email Signature with Flo Faction Branding

Create a professional email signature with Flo Faction branding:

1. In Gmail, click the gear icon in the top right
2. Select "See all settings"
3. Scroll down to the "Signature" section
4. Create a new signature with:
   - Your name and title
   - Flo Faction Insurance logo
   - Contact information
   - Website URL
   - Social media links
5. Save the signature and set it as default for new emails

## Step 5: Set Up Email Filters for Client Responses

Create filters to organize client responses:

1. In Gmail, click the gear icon > "See all settings"
2. Click on the "Filters and Blocked Addresses" tab
3. Click "Create a new filter"
4. In the "From" field, enter "<EMAIL>"
5. Click "Create filter"
6. Check "Apply the label" and select "New label"
7. Create a label called "Paul Edwards"
8. Check "Never send to Spam"
9. Click "Create filter"

## Step 6: Set Up Email Tracking (Optional)

To track email opens and clicks:

1. Install a Gmail extension like Mailtrack or Streak
2. Follow the extension's setup instructions
3. Enable tracking for emails sent to clients

## Step 7: Integrate with Wix Website

To integrate your Gmail with the Flo Faction Wix website:

1. In your Wix dashboard, go to "Settings" > "Email Settings"
2. Select "Connect a custom email provider"
3. Choose "Other" as the provider
4. Enter the following SMTP settings:
   - SMTP Server: smtp.gmail.com
   - SMTP Port: 587
   - Username: <EMAIL>
   - Password: [Your app password]
   - Encryption: TLS
5. Click "Connect" to test the connection
6. Set up email notifications for form submissions

## Step 8: Create Automated Email Sequences

Set up automated email sequences for follow-up:

1. Install Boomerang for Gmail or a similar tool
2. Create a sequence of follow-up emails
3. Schedule them to be sent at appropriate intervals
4. Track responses and adjust timing as needed

## Step 9: Test the Email Integration

Before sending emails to real clients:

1. Send a test email to yourself
2. Check that all links work correctly
3. Verify that the formatting looks professional
4. Confirm that tracking is working (if used)
5. Test on different devices and email clients

## Step 10: Troubleshooting Common Issues

If you encounter issues:

- **Emails not sending**: Check that the app password is correct and that "Less secure app access" is enabled
- **Emails going to spam**: Ask clients to add your email to their contacts
- **Formatting issues**: Use simple HTML formatting and test across different email clients
- **Rate limits**: Gmail limits you to 500 emails per day; use a service like SendGrid for higher volumes

## Next Steps

After setting up Gmail:

1. Create a contact group for all your insurance clients
2. Set up a regular newsletter using Gmail templates
3. Create a system for tracking email responses
4. Integrate with your CRM system for better lead management

For any questions or assistance, contact your IT support team.
