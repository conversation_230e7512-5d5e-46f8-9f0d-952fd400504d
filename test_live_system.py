import asyncio
import logging
from datetime import datetime
import json
from client_template import ClientManager
from insurance_carriers import CarrierManager
from secure_credentials import SecureCredentialsManager
from agent_coordinator import AgentCoordinator
from knowledge_management import KnowledgeManager

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def test_medicare_quote():
    """Test Medicare insurance quote workflow"""
    
    # Initialize all system components
    coordinator = AgentCoordinator()
    client_manager = ClientManager()
    carrier_manager = CarrierManager()
    knowledge_manager = KnowledgeManager()
    
    # Test client data for Medicare
    medicare_client = {
        "name": "Test Medicare Client",
        "dob": "1955-06-15",  # Age 68
        "height": "5'6\"",
        "weight": "160",
        "phone": "************",  # Using provided phone
        "email": "<EMAIL>",
        "address": "789 Medicare Ave, Port St. Lucie, FL 34952",
        "ssn": "***********",
        "drivers_license": "FL*********",
        
        "medications": [
            {
                "drug_name": "Metformin",
                "dosage": "500mg",
                "frequency": "twice daily"
            }
        ],
        
        "tobacco_use": False,
        "marijuana_use": False,
        
        "bank_info": {
            "bank_name": "Wells Fargo",
            "routing_number": "*********",
            "account_number": "*********"
        },
        
        "beneficiaries": [
            {
                "name": "Spouse Test",
                "dob": "1957-08-20",
                "relationship": "spouse"
            }
        ],
        
        "budget_range": "$200-$250",
        "family_health_history": "No significant issues",
        "reason_notes": "Seeking Medicare Supplement coverage",
        "start_date": "2025-06-01"
    }
    
    try:
        # 1. Create client record
        logger.info("Creating client record...")
        client = client_manager.create_client(medicare_client)
        
        # 2. Test Aetna quote
        logger.info("\nTesting Aetna quote generation...")
        aetna_task = {
            "type": "insurance_quote",
            "carrier": "aetna",
            "agent_id": "sandra",
            "client": client,
            "product": "medicare_supplement"
        }
        aetna_result = await coordinator.process_task(aetna_task)
        
        # 3. Test UHC quote
        logger.info("\nTesting UHC quote generation...")
        uhc_task = {
            "type": "insurance_quote",
            "carrier": "uhc",
            "agent_id": "sandra",
            "client": client,
            "product": "medicare_advantage"
        }
        uhc_result = await coordinator.process_task(uhc_task)
        
        # 4. Compare and analyze quotes
        quotes = {
            "aetna": aetna_result,
            "uhc": uhc_result
        }
        
        logger.info("\nQuote Comparison:")
        for carrier, quote in quotes.items():
            if quote.get("status") == "success":
                logger.info(f"\n{carrier.upper()}:")
                logger.info(f"Plan: {quote.get('plan_type')}")
                logger.info(f"Premium: ${quote.get('premium')}")
                logger.info(f"Benefits: {quote.get('benefits')}")
            else:
                logger.error(f"{carrier.upper()} quote failed: {quote.get('error')}")
        
        return quotes
        
    except Exception as e:
        logger.error(f"Error in Medicare quote test: {str(e)}")
        return None

async def test_life_insurance_quote():
    """Test life insurance quote workflow"""
    
    coordinator = AgentCoordinator()
    client_manager = ClientManager()
    
    # Test client data for life insurance
    life_client = {
        "name": "Test Life Client",
        "dob": "1980-08-20",
        "height": "5'10\"",
        "weight": "175",
        "phone": "************",
        "email": "<EMAIL>",
        "address": "456 Insurance Blvd, Port St. Lucie, FL 34952",
        "ssn": "***********",
        "drivers_license": "FL*********",
        
        "medications": [],
        "tobacco_use": False,
        "marijuana_use": False,
        
        "bank_info": {
            "bank_name": "Chase",
            "routing_number": "*********",
            "account_number": "*********"
        },
        
        "beneficiaries": [
            {
                "name": "Spouse Test Life",
                "dob": "1982-03-15",
                "relationship": "spouse"
            },
            {
                "name": "Child Test Life",
                "dob": "2010-05-10",
                "relationship": "child"
            }
        ],
        
        "budget_range": "$100-$150",
        "family_health_history": "No significant issues",
        "reason_notes": "Seeking term life insurance for family protection",
        "start_date": "2025-06-01"
    }
    
    try:
        # 1. Create client record
        logger.info("Creating life insurance client record...")
        client = client_manager.create_client(life_client)
        
        # 2. Test Mutual of Omaha quote
        logger.info("\nTesting Mutual of Omaha quote generation...")
        moo_task = {
            "type": "insurance_quote",
            "carrier": "mutual_of_omaha",
            "agent_id": "sandra",
            "client": client,
            "product": "term_life"
        }
        moo_result = await coordinator.process_task(moo_task)
        
        # 3. Test Americo quote
        logger.info("\nTesting Americo quote generation...")
        americo_task = {
            "type": "insurance_quote",
            "carrier": "americo",
            "agent_id": "sandra",
            "client": client,
            "product": "term_life"
        }
        americo_result = await coordinator.process_task(americo_task)
        
        quotes = {
            "mutual_of_omaha": moo_result,
            "americo": americo_result
        }
        
        logger.info("\nLife Insurance Quote Comparison:")
        for carrier, quote in quotes.items():
            if quote.get("status") == "success":
                logger.info(f"\n{carrier.upper()}:")
                logger.info(f"Product: {quote.get('product_type')}")
                logger.info(f"Coverage: ${quote.get('coverage_amount')}")
                logger.info(f"Premium: ${quote.get('premium')}/month")
                logger.info(f"Term: {quote.get('term')} years")
            else:
                logger.error(f"{carrier.upper()} quote failed: {quote.get('error')}")
        
        return quotes
        
    except Exception as e:
        logger.error(f"Error in life insurance quote test: {str(e)}")
        return None

async def main():
    """Run all tests"""
    try:
        # Test Medicare quotes
        logger.info("=== Testing Medicare Insurance Workflow ===")
        medicare_results = await test_medicare_quote()
        
        # Test life insurance quotes
        logger.info("\n=== Testing Life Insurance Workflow ===")
        life_results = await test_life_insurance_quote()
        
        # Save results
        results = {
            "timestamp": datetime.now().isoformat(),
            "medicare_quotes": medicare_results,
            "life_insurance_quotes": life_results
        }
        
        with open("test_results.json", "w") as f:
            json.dump(results, f, indent=2)
            
        logger.info("\nAll tests completed! Results saved to test_results.json")
        
    except Exception as e:
        logger.error(f"Error during testing: {str(e)}")
        raise

if __name__ == "__main__":
    asyncio.run(main())