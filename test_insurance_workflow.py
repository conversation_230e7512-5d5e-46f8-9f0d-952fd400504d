import asyncio
import logging
from datetime import datetime
import json
from client_template import <PERSON>lientManager, ClientInformation
from insurance_carriers import CarrierManager
from secure_credentials import SecureCredentialsManager

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def test_complete_workflow():
    """Test complete insurance quote workflow"""
    
    # Initialize managers
    client_manager = ClientManager()
    carrier_manager = CarrierManager()
    
    # Example client information
    client_info = {
        "name": "<PERSON>",
        "dob": "1965-05-15",
        "height": "5'10\"",
        "weight": "185",
        "phone": "************",
        "email": "<EMAIL>",
        "address": "123 Medicare Ave, Tampa, FL 33601",
        "ssn": "***********",
        "drivers_license": "FL*********",
        
        "medications": [
            {
                "drug_name": "Metformin",
                "dosage": "500mg",
                "frequency": "twice daily"
            },
            {
                "drug_name": "Lisinopril",
                "dosage": "10mg",
                "frequency": "once daily"
            }
        ],
        
        "tobacco_use": False,
        "marijuana_use": False,
        
        "bank_info": {
            "bank_name": "Chase Bank",
            "routing_number": "*********",
            "account_number": "*********"
        },
        
        "beneficiaries": [
            {
                "name": "Mary Smith",
                "dob": "1968-08-20",
                "relationship": "spouse"
            },
            {
                "name": "<PERSON> <PERSON>",
                "dob": "1990-03-10",
                "relationship": "son"
            }
        ],
        
        "budget_range": "$250-$300",
        "family_health_history": "Father had type 2 diabetes, Mother had high blood pressure",
        "reason_notes": "Looking for Medicare Supplement coverage",
        "start_date": "2025-06-01"
    }
    
    try:
        logger.info("Starting insurance quote workflow...")
        
        # 1. Validate and create client record
        logger.info("Validating client information...")
        if client_manager.validate_client_info(client_info):
            client = client_manager.create_client(client_info)
            logger.info("Client record created successfully")
        else:
            raise ValueError("Invalid client information")
        
        # 2. Get quotes from all carriers
        logger.info("\nGetting quotes from carriers...")
        quotes = await carrier_manager.get_quotes(client, agent_id="sandra")  # Using Sandra's credentials
        
        # 3. Process and display quotes
        logger.info("\nQuote Results:")
        for carrier, quote in quotes.items():
            logger.info(f"\n{carrier.upper()}:")
            logger.info(f"Monthly Premium: ${quote.get('premium', 'N/A')}")
            logger.info(f"Coverage Amount: ${quote.get('coverage', 'N/A')}")
            
            # Display carrier-specific details
            details = quote.get('details', {})
            if details:
                logger.info("Additional Benefits:")
                for benefit, value in details.items():
                    logger.info(f"- {benefit}: {value}")
        
        # 4. Format client info for best quote carrier
        best_quote = min(quotes.items(), key=lambda x: float(x[1]['premium']))
        best_carrier = best_quote[0]
        
        logger.info(f"\nFormatting client information for {best_carrier.upper()}...")
        formatted_data = carrier_manager._format_for_carrier(client, best_carrier)
        
        # 5. Display formatted submission data
        logger.info("\nFormatted Submission Data:")
        logger.info(json.dumps(formatted_data, indent=2))
        
        return {
            "status": "success",
            "client_id": client.name,  # In production, use actual client ID
            "quotes": quotes,
            "best_quote": {
                "carrier": best_carrier,
                "details": best_quote[1]
            },
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        logger.error(f"Error in workflow: {str(e)}")
        return {
            "status": "error",
            "error": str(e),
            "timestamp": datetime.now().isoformat()
        }

async def test_specific_carrier_quote(carrier_name: str):
    """Test quote generation for specific carrier"""
    
    client_manager = ClientManager()
    carrier_manager = CarrierManager()
    
    # Create test client with Medicare-specific info
    medicare_client_info = {
        "name": "Patricia Johnson",
        "dob": "1958-03-15",  # 65+ for Medicare
        "height": "5'4\"",
        "weight": "145",
        "phone": "************",
        "email": "<EMAIL>",
        "address": "456 Senior Living Dr, Miami, FL 33125",
        "ssn": "***********",
        "drivers_license": "FL*********",
        
        "medications": [
            {
                "drug_name": "Atorvastatin",
                "dosage": "20mg",
                "frequency": "once daily"
            }
        ],
        
        "tobacco_use": False,
        "marijuana_use": False,
        
        "bank_info": {
            "bank_name": "Wells Fargo",
            "routing_number": "*********",
            "account_number": "*********"
        },
        
        "budget_range": "$200-$250",
        "reason_notes": "Looking for Medicare Advantage plan",
        "start_date": "2025-07-01"
    }
    
    try:
        client = client_manager.create_client(medicare_client_info)
        
        logger.info(f"\nTesting quote generation for {carrier_name.upper()}...")
        quotes = await carrier_manager.get_quotes(client, agent_id="sandra")
        
        if carrier_name.lower() in quotes:
            quote = quotes[carrier_name.lower()]
            logger.info(f"\nQuote Details for {carrier_name.upper()}:")
            logger.info(json.dumps(quote, indent=2))
            return quote
        else:
            logger.error(f"No quote available from {carrier_name}")
            return None
            
    except Exception as e:
        logger.error(f"Error getting quote from {carrier_name}: {str(e)}")
        return None

async def main():
    """Run all tests"""
    logger.info("Starting Insurance Workflow Tests\n")
    
    try:
        # Test complete workflow
        logger.info("=== Testing Complete Workflow ===")
        result = await test_complete_workflow()
        logger.info(f"\nWorkflow result: {json.dumps(result, indent=2)}")
        
        # Test specific carriers (only those with credentials in sandra's profile)
        carriers_to_test = ['aetna', 'united_healthcare', 'mutual_of_omaha']
        
        for carrier in carriers_to_test:
            logger.info(f"\n=== Testing {carrier.upper()} Quotes ===")
            quote = await test_specific_carrier_quote(carrier)
            if quote:
                logger.info(f"Successfully generated quote for {carrier.upper()}")
            else:
                logger.error(f"Failed to generate quote for {carrier.upper()}")
        
        logger.info("\nAll tests completed!")
        
    except Exception as e:
        logger.error(f"Error during testing: {str(e)}")
        raise

if __name__ == "__main__":
    asyncio.run(main())