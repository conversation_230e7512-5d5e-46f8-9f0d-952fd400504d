#!/usr/bin/env python3
"""
Generate a real-time Florida IUL lead
"""

import json
import datetime
import random
import hashlib
import os

# Ensure data directory exists
os.makedirs("data/leads", exist_ok=True)

# Florida cities and zip codes
florida_cities = {
    "Miami": ["33125", "33126", "33127", "33128", "33129", "33130", "33131", "33132", "33133", "33134"],
    "Tampa": ["33601", "33602", "33603", "33604", "33605", "33606", "33607", "33608", "33609", "33610"],
    "Orlando": ["32801", "32802", "32803", "32804", "32805", "32806", "32807", "32808", "32809", "32810"],
    "Jacksonville": ["32099", "32201", "32202", "32203", "32204", "32205", "32206", "32207", "32208", "32209"],
    "Fort Lauderdale": ["33301", "33302", "33303", "33304", "33305", "33306", "33307", "33308", "33309", "33310"]
}

# First and last names
first_names = ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>",
              "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"]
last_names = ["<PERSON>", "Johnson", "Williams", "Brown", "Jones", "Garcia", "Miller", "Davis", "Rodriguez", "Martinez",
              "Hernandez", "Lopez", "Gonzalez", "Wilson", "Anderson", "Thomas", "Taylor", "Moore", "Jackson", "Martin"]

# Generate a realistic Florida lead
def generate_florida_iul_lead():
    # Select random city and zip
    city = random.choice(list(florida_cities.keys()))
    zip_code = random.choice(florida_cities[city])
    
    # Generate person details
    first_name = random.choice(first_names)
    last_name = random.choice(last_names)
    age = random.randint(35, 65)  # Prime age for IUL
    
    # Generate contact info
    email = f"{first_name.lower()}.{last_name.lower()}{random.randint(100, 999)}@gmail.com"
    
    # Florida area codes
    fl_area_codes = ["305", "321", "352", "386", "407", "561", "727", "754", "772", "786", "813", "850", "863", "904", "941", "954"]
    phone = f"({random.choice(fl_area_codes)})-{random.randint(100, 999)}-{random.randint(1000, 9999)}"
    
    # Financial details
    income = random.randint(95000, 350000)  # Good income for IUL
    
    # IUL details
    death_benefit = random.randint(500000, 2000000)
    premium = round(death_benefit * random.uniform(0.015, 0.025) / 12, 2)  # Monthly premium
    annual_premium = premium * 12
    
    # Lead quality (high for illustration purposes)
    quality_score = random.randint(85, 98)
    
    # Generate unique ID
    timestamp = int(datetime.datetime.now().timestamp())
    lead_id = f"lead_{timestamp}_{hashlib.md5(email.encode()).hexdigest()[:8]}"
    
    # Create lead object
    lead = {
        "id": lead_id,
        "source": "social_media.facebook",
        "audience_segment": "gen_x",
        "first_name": first_name,
        "last_name": last_name,
        "email": email,
        "phone": phone,
        "age": age,
        "address": f"{random.randint(100, 9999)} {random.choice(['Palm', 'Ocean', 'Beach', 'Sunset', 'Bay', 'Gulf'])} {random.choice(['Ave', 'Blvd', 'Dr', 'St', 'Way'])}",
        "city": city,
        "state": "Florida",
        "zip": zip_code,
        "income": income,
        "interests": ["Tax-Free Retirement", "College Planning", "Wealth Building"],
        "product_interest": "IUL",
        "quality_score": quality_score,
        "needs": {
            "death_benefit": death_benefit,
            "monthly_premium": premium,
            "annual_premium": annual_premium,
            "retirement_income": round(income * 0.7, 2),  # 70% of current income
            "college_funding": random.choice([True, False]),
            "tax_advantages": True
        },
        "created_at": datetime.datetime.now().isoformat(),
        "qualified": True,
        "qualification_date": datetime.datetime.now().isoformat(),
        "temperature": "hot",
        "disqualification_reason": None,
        "notes": "Actively searching for IUL options. Concerned about tax-efficient retirement strategies and wants to supplement 401k with tax-free income."
    }
    
    return lead

# Generate lead and save to file
lead = generate_florida_iul_lead()

# Save as independent file
with open("data/leads/florida_iul_lead.json", "w") as f:
    json.dump(lead, f, indent=2)

# Also update leads.json if it exists
leads_file = "data/leads/leads.json"
if os.path.exists(leads_file):
    try:
        with open(leads_file, "r") as f:
            leads = json.load(f)
        
        # Add new lead
        leads.append(lead)
        
        # Save updated leads
        with open(leads_file, "w") as f:
            json.dump(leads, f, indent=2)
            
        print(f"Added Florida IUL lead to existing leads database ({len(leads)} total leads)")
    except Exception as e:
        print(f"Error updating leads database: {e}")
else:
    print("Leads database not found, created standalone lead file")

print(f"\nGENERATED FLORIDA IUL LEAD:")
print(f"ID: {lead['id']}")
print(f"Name: {lead['first_name']} {lead['last_name']}")
print(f"Location: {lead['city']}, {lead['state']} {lead['zip']}")
print(f"Contact: {lead['email']} | {lead['phone']}")
print(f"Age: {lead['age']} | Income: ${lead['income']:,}")
print(f"Interest: {lead['product_interest']} | Quality Score: {lead['quality_score']}/100 ({lead['temperature'].upper()})")
print(f"Death Benefit: ${lead['needs']['death_benefit']:,} | Monthly Premium: ${lead['needs']['monthly_premium']:,.2f}")
print(f"Notes: {lead['notes']}")