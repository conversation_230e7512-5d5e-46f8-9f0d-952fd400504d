["test_web_automation_system.py::TestGoogleCloudDeployer::test_deployer_initialization", "test_web_automation_system.py::TestGoogleCloudDeployer::test_dockerfile_creation", "test_web_automation_system.py::TestGoogleCloudDeployer::test_package_json_creation", "test_web_automation_system.py::TestGoogleCloudDeployer::test_prerequisites_check", "test_web_automation_system.py::TestIntegration::test_component_integration", "test_web_automation_system.py::TestIntegration::test_end_to_end_workflow", "test_web_automation_system.py::TestMidSceneIntegration::test_action_creation", "test_web_automation_system.py::TestMidSceneIntegration::test_action_execution_mock", "test_web_automation_system.py::TestMidSceneIntegration::test_config_file_creation", "test_web_automation_system.py::TestMidSceneIntegration::test_script_file_creation", "test_web_automation_system.py::TestUnifiedDashboard::test_get_all_tasks_endpoint", "test_web_automation_system.py::TestUnifiedDashboard::test_system_status_endpoint", "test_web_automation_system.py::TestUnifiedDashboard::test_task_creation_endpoint", "test_web_automation_system.py::TestWebRoverComponent::test_link_prioritization", "test_web_automation_system.py::TestWebRoverComponent::test_page_navigation", "test_web_automation_system.py::TestWebRoverComponent::test_url_validation", "test_web_automation_system.py::TestWebUIComponent::test_task_creation", "test_web_automation_system.py::TestWebUIComponent::test_task_execution", "test_web_automation_system.py::TestWebUIComponent::test_task_serialization"]