#!/usr/bin/env python3
"""
Video Comment Lead Finder

This tool monitors social media platforms (YouTube, TikTok, Instagram) for comments
on videos that indicate interest in insurance products. It identifies potential leads
who have explicitly shown interest by:

1. Using specific trigger keywords in comments (e.g., "interested", "need quote")
2. Asking questions about insurance products featured in videos
3. Directly requesting contact or more information

The system ethically engages with these potential leads by:
1. Only processing comments that express explicit interest
2. Responding with a helpful reply that includes a link to a lead form
3. Tracking which users have been contacted to prevent duplicate outreach
4. Respecting platform terms of service and privacy policies

This approach ensures leads have given consent through their public comments.
"""

import os
import json
import logging
import time
import re
import datetime
import random
import uuid
from typing import Dict, List, Any, Optional, Tuple
from pathlib import Path

# Configure logging
logging.basicConfig(level=logging.INFO,
                    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Try to import platform-specific API modules
try:
    # These would be replaced with actual API imports in production
    # import googleapiclient.discovery  # For YouTube API
    # import TikTokApi  # For TikTok API
    # import InstagramAPI  # For Instagram API
    PLATFORM_APIS_AVAILABLE = True
except ImportError:
    PLATFORM_APIS_AVAILABLE = False
    logger.warning("Social media platform APIs not available - using mock data")


class VideoCommentLeadFinder:
    """
    Monitors video comments across platforms to identify and capture leads who
    have expressed explicit interest in insurance products
    """
    
    def __init__(self, data_dir="data"):
        """Initialize the video comment lead finder system"""
        self.data_dir = data_dir
        self.leads_dir = f"{data_dir}/leads/video_comments"
        self.config_dir = f"{data_dir}/config"
        
        # Ensure directories exist
        for directory in [self.data_dir, self.leads_dir, self.config_dir]:
            os.makedirs(directory, exist_ok=True)
            
        # Load configuration
        self.config = self._load_config()
        
        # Configure platform APIs
        self._setup_platform_apis()
        
        # Initialize storage for leads and tracked comments
        self.leads = self._load_leads()
        self.processed_comments = self._load_processed_comments()
        
        # Define trigger keywords by product type
        self.trigger_keywords = {
            "general": [
                "interested", "want quote", "need quote", "get quote", 
                "more info", "more information", "contact me", "reach out",
                "call me", "email me", "dm me", "message me", "send info",
                "how much", "how do i get", "how to get", "how can i get",
                "looking for", "want to know", "want to learn", "need help with"
            ],
            "life_insurance": [
                "life insurance", "term life", "whole life", "iul", "universal life",
                "indexed universal", "life policy", "death benefit", "cash value",
                "life coverage", "cover family", "protect family", "beneficiary"
            ],
            "medicare": [
                "medicare", "supplement", "advantage", "part b", "part d",
                "medigap", "senior", "turning 65", "retirement", "retired",
                "health insurance", "medical coverage", "medical insurance"
            ],
            "annuities": [
                "annuity", "annuities", "retirement income", "guaranteed income",
                "lifetime income", "fixed indexed", "fixed annuity", "retirement plan",
                "retirement strategy", "tax-deferred", "surrender period"
            ],
            "final_expense": [
                "final expense", "burial insurance", "funeral", "burial coverage",
                "cremation", "funeral costs", "last expenses", "funeral expenses",
                "burial costs", "funeral insurance"
            ]
        }
        
        # Response templates for different platforms
        self.response_templates = {
            "youtube": {
                "general": "Thanks for your interest! I'd be happy to provide more information. You can get a personalized quote by filling out our quick form here: {{LEAD_FORM_URL}} or call us at {{PHONE_NUMBER}}.",
                "specific": "Thanks for asking about {{PRODUCT}}! I'd be happy to help you with that. For a personalized quote, please visit: {{LEAD_FORM_URL}} or call us at {{PHONE_NUMBER}}."
            },
            "tiktok": {
                "general": "Thanks for your interest! Get a quick quote at {{LEAD_FORM_URL}} or call {{PHONE_NUMBER}} 📱",
                "specific": "Thanks for asking about {{PRODUCT}}! Get your quote at {{LEAD_FORM_URL}} or call {{PHONE_NUMBER}} 📱"
            },
            "instagram": {
                "general": "Thanks for your interest! 👍 Get a personalized quote at the link in bio or call us at {{PHONE_NUMBER}}",
                "specific": "Thanks for asking about {{PRODUCT}}! 👍 Get your personalized quote through the link in bio or call {{PHONE_NUMBER}}"
            },
            "facebook": {
                "general": "Thanks for your interest! I'd be happy to help. You can get a quote here: {{LEAD_FORM_URL}} or call us at {{PHONE_NUMBER}}.",
                "specific": "Thanks for asking about {{PRODUCT}}! For a personalized quote, please visit: {{LEAD_FORM_URL}} or call us at {{PHONE_NUMBER}}."
            }
        }
        
        logger.info("Video Comment Lead Finder initialized")
        
    def _load_config(self) -> Dict[str, Any]:
        """Load configuration settings"""
        config_file = f"{self.config_dir}/video_comment_finder_config.json"
        default_config = {
            "platforms": {
                "youtube": {
                    "enabled": True,
                    "check_frequency_minutes": 60,
                    "channel_ids": ["FlofactionInsurance"],  # Updated with Flo Faction Insurance YouTube channel
                    "channel_names": ["Flo Faction Insurance"],  # Added channel name for readability
                    "max_videos_to_check": 10,  # Most recent videos to check
                    "days_to_look_back": 30,  # Only check comments from last 30 days
                    "api_key": ""  # YouTube API key
                },
                "tiktok": {
                    "enabled": True,
                    "check_frequency_minutes": 60,
                    "account_names": ["@flofaction.insurance"],  # Updated with correct TikTok account
                    "max_videos_to_check": 20,
                    "days_to_look_back": 14,
                    "ms_token": ""  # TikTok API token
                },
                "instagram": {
                    "enabled": True,
                    "check_frequency_minutes": 60,
                    "account_names": ["@_flofaction.insurance"],  # Updated with correct Instagram account
                    "max_posts_to_check": 20,
                    "days_to_look_back": 14,
                    "access_token": ""  # Instagram API token
                },
                "facebook": {
                    "enabled": True,
                    "check_frequency_minutes": 60,
                    "page_ids": [],  # Facebook page IDs will be looked up by name
                    "page_names": ["Flo Faction Insurance"],  # Updated with Facebook page name
                    "max_posts_to_check": 20,
                    "days_to_look_back": 30,
                    "access_token": ""  # Facebook API token
                }
            },
            "lead_forms": {
                "default_url": "https://flofaction.insurance/quote",
                "youtube_url": "https://flofaction.insurance/youtube-quote",
                "tiktok_url": "https://flofaction.insurance/tiktok-quote",
                "instagram_url": "https://flofaction.insurance/instagram-quote",
                "facebook_url": "https://flofaction.insurance/facebook-quote"
            },
            "contact_info": {
                "phone": "(*************",  # Update with actual phone number if different
                "email": "<EMAIL>",
                "agency_name": "Flo Faction Insurance"
            },
            "auto_respond": True,  # Automatically respond to comments with template
            "min_confidence_score": 0.7,  # Minimum confidence score to consider a comment as a lead
            "default_product": "insurance"  # Default product to assume if none detected
        }
        
        if os.path.exists(config_file):
            try:
                with open(config_file, 'r') as f:
                    return json.load(f)
            except Exception as e:
                logger.error(f"Error loading configuration: {e}, using defaults")
                return default_config
        else:
            # Save default config
            try:
                with open(config_file, 'w') as f:
                    json.dump(default_config, f, indent=2)
            except Exception as e:
                logger.error(f"Error saving default configuration: {e}")
                
            return default_config
            
    def _save_config(self):
        """Save configuration settings"""
        config_file = f"{self.config_dir}/video_comment_finder_config.json"
        try:
            with open(config_file, 'w') as f:
                json.dump(self.config, f, indent=2)
        except Exception as e:
            logger.error(f"Error saving configuration: {e}")
            
    def _setup_platform_apis(self):
        """Set up APIs for each platform"""
        self.platform_apis = {}
        
        if not PLATFORM_APIS_AVAILABLE:
            logger.warning("Platform APIs not available, using mock implementations")
            return
            
        # Initialize YouTube API
        if self.config["platforms"]["youtube"]["enabled"]:
            try:
                # In production, use the actual YouTube API:
                # youtube = googleapiclient.discovery.build(
                #     "youtube", "v3",
                #     developerKey=self.config["platforms"]["youtube"]["api_key"]
                # )
                # self.platform_apis["youtube"] = youtube
                logger.info("YouTube API initialized")
            except Exception as e:
                logger.error(f"Error initializing YouTube API: {e}")

        # Initialize TikTok API
        if self.config["platforms"]["tiktok"]["enabled"]:
            try:
                # In production, use the actual TikTok API:
                # tiktok = TikTokApi.get_instance()
                # self.platform_apis["tiktok"] = tiktok
                logger.info("TikTok API initialized")
            except Exception as e:
                logger.error(f"Error initializing TikTok API: {e}")

        # Initialize Instagram API
        if self.config["platforms"]["instagram"]["enabled"]:
            try:
                # In production, use the actual Instagram API:
                # instagram = InstagramAPI.Client(
                #     access_token=self.config["platforms"]["instagram"]["access_token"]
                # )
                # self.platform_apis["instagram"] = instagram
                logger.info("Instagram API initialized")
            except Exception as e:
                logger.error(f"Error initializing Instagram API: {e}")

        # Initialize Facebook API
        if self.config["platforms"]["facebook"]["enabled"]:
            try:
                # In production, use the actual Facebook API:
                # facebook = facebook.GraphAPI(
                #     access_token=self.config["platforms"]["facebook"]["access_token"]
                # )
                # self.platform_apis["facebook"] = facebook
                logger.info("Facebook API initialized")
            except Exception as e:
                logger.error(f"Error initializing Facebook API: {e}")

    def _load_leads(self) -> List[Dict[str, Any]]:
        """Load previously identified leads"""
        leads_file = f"{self.leads_dir}/video_comment_leads.json"
        if os.path.exists(leads_file):
            try:
                with open(leads_file, 'r') as f:
                    return json.load(f)
            except Exception as e:
                logger.error(f"Error loading leads: {e}")
                return []
        return []
        
    def _save_leads(self):
        """Save leads to file"""
        leads_file = f"{self.leads_dir}/video_comment_leads.json"
        try:
            with open(leads_file, 'w') as f:
                json.dump(self.leads, f, indent=2)
        except Exception as e:
            logger.error(f"Error saving leads: {e}")
            
    def _load_processed_comments(self) -> Dict[str, List[str]]:
        """Load list of already processed comments to avoid duplicates"""
        processed_file = f"{self.leads_dir}/processed_comments.json"
        if os.path.exists(processed_file):
            try:
                with open(processed_file, 'r') as f:
                    return json.load(f)
            except Exception as e:
                logger.error(f"Error loading processed comments: {e}")
                return {"youtube": [], "tiktok": [], "instagram": [], "facebook": []}
        return {"youtube": [], "tiktok": [], "instagram": [], "facebook": []}
        
    def _save_processed_comments(self):
        """Save processed comments to file"""
        processed_file = f"{self.leads_dir}/processed_comments.json"
        try:
            with open(processed_file, 'w') as f:
                json.dump(self.processed_comments, f, indent=2)
        except Exception as e:
            logger.error(f"Error saving processed comments: {e}")
    
    def find_leads_on_all_platforms(self) -> int:
        """Find leads on all enabled platforms
        
        Returns:
            Number of new leads found
        """
        total_new_leads = 0
        
        # Check each enabled platform
        if self.config["platforms"]["youtube"]["enabled"]:
            youtube_leads = self.find_leads_on_youtube()
            total_new_leads += youtube_leads
            logger.info(f"Found {youtube_leads} new leads on YouTube")
            
        if self.config["platforms"]["tiktok"]["enabled"]:
            tiktok_leads = self.find_leads_on_tiktok()
            total_new_leads += tiktok_leads
            logger.info(f"Found {tiktok_leads} new leads on TikTok")
            
        if self.config["platforms"]["instagram"]["enabled"]:
            instagram_leads = self.find_leads_on_instagram()
            total_new_leads += instagram_leads
            logger.info(f"Found {instagram_leads} new leads on Instagram")
            
        if self.config["platforms"]["facebook"]["enabled"]:
            facebook_leads = self.find_leads_on_facebook()
            total_new_leads += facebook_leads
            logger.info(f"Found {facebook_leads} new leads on Facebook")
            
        logger.info(f"Found {total_new_leads} total new leads across all platforms")
        return total_new_leads
        
    def find_leads_on_youtube(self) -> int:
        """Find leads from YouTube comments
        
        Returns:
            Number of new leads found
        """
        logger.info("Searching for leads in YouTube comments")
        
        if not PLATFORM_APIS_AVAILABLE:
            # For testing/demo without API access, use mock data
            return self._find_leads_on_youtube_mock()
            
        platform_config = self.config["platforms"]["youtube"]
        new_leads = 0
        
        # For each channel to monitor
        for channel_id in platform_config["channel_ids"]:
            try:
                # Get recent videos
                # videos = self.platform_apis["youtube"].search().list(
                #     part="id,snippet",
                #     channelId=channel_id,
                #     maxResults=platform_config["max_videos_to_check"],
                #     order="date",
                #     type="video"
                # ).execute()
                
                # Process each video
                # for video in videos.get("items", []):
                #     video_id = video["id"]["videoId"]
                #     video_title = video["snippet"]["title"]
                #     
                #     # Get comments for this video
                #     comments = self.platform_apis["youtube"].commentThreads().list(
                #         part="id,snippet",
                #         videoId=video_id,
                #         maxResults=100
                #     ).execute()
                #     
                #     # Process comments
                #     for comment in comments.get("items", []):
                #         comment_id = comment["id"]
                #         if comment_id in self.processed_comments["youtube"]:
                #             continue
                #             
                #         comment_text = comment["snippet"]["topLevelComment"]["snippet"]["textDisplay"]
                #         author = comment["snippet"]["topLevelComment"]["snippet"]["authorDisplayName"]
                #         published_at = comment["snippet"]["topLevelComment"]["snippet"]["publishedAt"]
                #         
                #         # Check if comment indicates interest
                #         interest_result = self._check_comment_for_interest(comment_text)
                #         if interest_result["is_interested"]:
                #             # Create lead record
                #             lead = self._create_lead_from_youtube_comment(
                #                 channel_id=channel_id,
                #                 video_id=video_id,
                #                 video_title=video_title,
                #                 comment_id=comment_id,
                #                 author=author,
                #                 comment=comment_text,
                #                 published_at=published_at,
                #                 interest_info=interest_result
                #             )
                #             
                #             # Add to leads
                #             self.leads.append(lead)
                #             new_leads += 1
                #             
                #             # Respond to comment if enabled
                #             if self.config["auto_respond"]:
                #                 self._respond_to_youtube_comment(
                #                     comment_id=comment_id,
                #                     interest_info=interest_result
                #                 )
                #         
                #         # Mark as processed
                #         self.processed_comments["youtube"].append(comment_id)
                    
                # Save after each channel is processed
                self._save_leads()
                self._save_processed_comments()
                
            except Exception as e:
                logger.error(f"Error processing YouTube channel {channel_id}: {e}")
                
        return new_leads
        
    def _find_leads_on_youtube_mock(self) -> int:
        """Mock implementation for demo purposes"""
        logger.info("Using mock data for YouTube leads")
        
        # Mock finding 2-5 new leads
        new_leads_count = random.randint(2, 5)
        
        # Create sample leads
        for i in range(new_leads_count):
            lead_id = f"youtube_{int(time.time())}_{uuid.uuid4().hex[:8]}"
            
            # Mock comment indicating interest
            comments = [
                "I'm interested in life insurance. How do I get more info?",
                "This IUL product sounds interesting. Can you tell me more about it?",
                "I've been looking for a good retirement strategy. How can I get a quote?",
                "My parents are turning 65 soon. How does Medicare work? Can you contact me?",
                "Need info on final expense coverage. How much would it cost for a 75-year-old?"
            ]
            
            # Mock interest detection
            comment = random.choice(comments)
            interest_result = self._check_comment_for_interest(comment)
            
            # Create lead
            lead = {
                "id": lead_id,
                "platform": "youtube",
                "source": "video_comment",
                "video_id": f"mock_video_{random.randint(1000, 9999)}",
                "video_title": "How to Use IUL for Tax-Free Retirement",
                "comment_id": f"mock_comment_{random.randint(10000, 99999)}",
                "author_name": f"User_{random.randint(100, 999)}",
                "author_channel": f"channel_{random.randint(100, 999)}",
                "comment": comment,
                "detected_interest": interest_result,
                "published_at": datetime.datetime.now().isoformat(),
                "processed_at": datetime.datetime.now().isoformat(),
                "responded": self.config["auto_respond"],
                "response_template_used": "specific" if "specific_product" in interest_result else "general",
                "status": "new"
            }
            
            # Add to leads
            self.leads.append(lead)
            
            # Track processed comment
            comment_id = lead["comment_id"]
            if comment_id not in self.processed_comments["youtube"]:
                self.processed_comments["youtube"].append(comment_id)
                
        # Save changes
        self._save_leads()
        self._save_processed_comments()
        
        return new_leads_count
        
    def find_leads_on_tiktok(self) -> int:
        """Find leads from TikTok comments
        
        Returns:
            Number of new leads found
        """
        logger.info("Searching for leads in TikTok comments")
        
        if not PLATFORM_APIS_AVAILABLE:
            # For testing/demo without API access, use mock data
            return self._find_leads_on_tiktok_mock()
            
        # Actual implementation would use TikTok API
        # Similar structure to YouTube implementation
        
        return 0
        
    def _find_leads_on_tiktok_mock(self) -> int:
        """Mock implementation for demo purposes"""
        logger.info("Using mock data for TikTok leads")
        
        # Mock finding 1-3 new leads
        new_leads_count = random.randint(1, 3)
        
        # Create sample leads
        for i in range(new_leads_count):
            lead_id = f"tiktok_{int(time.time())}_{uuid.uuid4().hex[:8]}"
            
            # Mock comment indicating interest
            comments = [
                "How much would term life insurance cost for a 35 year old?",
                "Interested in IUL! How do I reach you?",
                "Need help with Medicare options. Can you DM me?",
                "Looking for life insurance for my family. Can I get a quote?",
                "How does the cash value part work? Can you explain more?"
            ]
            
            # Mock interest detection
            comment = random.choice(comments)
            interest_result = self._check_comment_for_interest(comment)
            
            # Create lead
            lead = {
                "id": lead_id,
                "platform": "tiktok",
                "source": "video_comment",
                "video_id": f"mock_tiktok_{random.randint(1000, 9999)}",
                "comment_id": f"mock_comment_{random.randint(10000, 99999)}",
                "author_name": f"tiktok_user_{random.randint(100, 999)}",
                "comment": comment,
                "detected_interest": interest_result,
                "published_at": datetime.datetime.now().isoformat(),
                "processed_at": datetime.datetime.now().isoformat(),
                "responded": self.config["auto_respond"],
                "response_template_used": "specific" if "specific_product" in interest_result else "general",
                "status": "new"
            }
            
            # Add to leads
            self.leads.append(lead)
            
            # Track processed comment
            comment_id = lead["comment_id"]
            if comment_id not in self.processed_comments["tiktok"]:
                self.processed_comments["tiktok"].append(comment_id)
                
        # Save changes
        self._save_leads()
        self._save_processed_comments()
        
        return new_leads_count
        
    def find_leads_on_instagram(self) -> int:
        """Find leads from Instagram comments
        
        Returns:
            Number of new leads found
        """
        logger.info("Searching for leads in Instagram comments")
        
        if not PLATFORM_APIS_AVAILABLE:
            # For testing/demo without API access, use mock data
            return self._find_leads_on_instagram_mock()
            
        # Actual implementation would use Instagram API
        # Similar structure to YouTube implementation
        
        return 0
        
    def _find_leads_on_instagram_mock(self) -> int:
        """Mock implementation for demo purposes"""
        logger.info("Using mock data for Instagram leads")
        
        # Mock finding 1-4 new leads
        new_leads_count = random.randint(1, 4)
        
        # Create sample leads
        for i in range(new_leads_count):
            lead_id = f"instagram_{int(time.time())}_{uuid.uuid4().hex[:8]}"
            
            # Mock comment indicating interest
            comments = [
                "Very interesting! How do I sign up for a quote?",
                "Need life insurance for my family. What's your contact info?",
                "How does this compare to other retirement options? Can you help?",
                "I'm turning 65 soon, need help with Medicare options.",
                "What's the minimum investment for this IUL strategy?"
            ]
            
            # Mock interest detection
            comment = random.choice(comments)
            interest_result = self._check_comment_for_interest(comment)
            
            # Create lead
            lead = {
                "id": lead_id,
                "platform": "instagram",
                "source": "post_comment",
                "post_id": f"mock_post_{random.randint(1000, 9999)}",
                "comment_id": f"mock_comment_{random.randint(10000, 99999)}",
                "author_name": f"ig_user_{random.randint(100, 999)}",
                "comment": comment,
                "detected_interest": interest_result,
                "published_at": datetime.datetime.now().isoformat(),
                "processed_at": datetime.datetime.now().isoformat(),
                "responded": self.config["auto_respond"],
                "response_template_used": "specific" if "specific_product" in interest_result else "general",
                "status": "new"
            }
            
            # Add to leads
            self.leads.append(lead)
            
            # Track processed comment
            comment_id = lead["comment_id"]
            if comment_id not in self.processed_comments["instagram"]:
                self.processed_comments["instagram"].append(comment_id)
                
        # Save changes
        self._save_leads()
        self._save_processed_comments()
        
        return new_leads_count
    
    def find_leads_on_facebook(self) -> int:
        """Find leads from Facebook comments
        
        Returns:
            Number of new leads found
        """
        logger.info("Searching for leads in Facebook comments")
        
        if not PLATFORM_APIS_AVAILABLE:
            # For testing/demo without API access, use mock data
            return self._find_leads_on_facebook_mock()
            
        # Actual implementation would use Facebook API
        # Similar structure to YouTube implementation
        
        return 0
        
    def _find_leads_on_facebook_mock(self) -> int:
        """Mock implementation for demo purposes"""
        logger.info("Using mock data for Facebook leads")
        
        # Mock finding 2-6 new leads
        new_leads_count = random.randint(2, 6)
        
        # Create sample leads
        for i in range(new_leads_count):
            lead_id = f"facebook_{int(time.time())}_{uuid.uuid4().hex[:8]}"
            
            # Mock comment indicating interest
            comments = [
                "I need to set up life insurance for my family. How do I get started?",
                "This IUL strategy sounds perfect for my retirement. Can you contact me?",
                "Just turned 65 and confused about Medicare options. Can you help?",
                "How much would this cost for a 42-year-old non-smoker?",
                "I've been looking for a financial strategy like this. How do I learn more?",
                "My parents need final expense insurance. What's the process?"
            ]
            
            # Mock interest detection
            comment = random.choice(comments)
            interest_result = self._check_comment_for_interest(comment)
            
            # Create lead
            lead = {
                "id": lead_id,
                "platform": "facebook",
                "source": "post_comment",
                "post_id": f"mock_post_{random.randint(1000, 9999)}",
                "comment_id": f"mock_comment_{random.randint(10000, 99999)}",
                "author_name": f"fb_user_{random.randint(100, 999)}",
                "comment": comment,
                "detected_interest": interest_result,
                "published_at": datetime.datetime.now().isoformat(),
                "processed_at": datetime.datetime.now().isoformat(),
                "responded": self.config["auto_respond"],
                "response_template_used": "specific" if "specific_product" in interest_result else "general",
                "status": "new"
            }
            
            # Add to leads
            self.leads.append(lead)
            
            # Track processed comment
            comment_id = lead["comment_id"]
            if comment_id not in self.processed_comments["facebook"]:
                self.processed_comments["facebook"].append(comment_id)
                
        # Save changes
        self._save_leads()
        self._save_processed_comments()
        
        return new_leads_count
    
    def _check_comment_for_interest(self, comment: str) -> Dict[str, Any]:
        """
        Check if a comment indicates interest in insurance products
        
        Args:
            comment: The comment text to analyze
            
        Returns:
            Dictionary with interest analysis results
        """
        # Convert to lowercase for easier matching
        comment_lower = comment.lower()
        
        # Check for general interest keywords
        general_interest = False
        for keyword in self.trigger_keywords["general"]:
            if keyword.lower() in comment_lower:
                general_interest = True
                break
                
        # Check for specific product interest
        specific_product = None
        specific_keywords = []
        confidence_score = 0.0
        
        # Check each product type
        for product, keywords in self.trigger_keywords.items():
            if product == "general":
                continue
                
            # Count how many keywords match for this product
            matches = 0
            matched_keywords = []
            for keyword in keywords:
                if keyword.lower() in comment_lower:
                    matches += 1
                    matched_keywords.append(keyword)
                    
            # Calculate confidence based on number of matches
            product_confidence = min(1.0, matches * 0.2)
            
            # Update if this product has higher confidence
            if product_confidence > confidence_score:
                confidence_score = product_confidence
                specific_product = product
                specific_keywords = matched_keywords
                
        # Combine general interest with specific product confidence
        if general_interest:
            confidence_score = max(confidence_score, 0.6)
            
        # Final determination
        is_interested = confidence_score >= self.config["min_confidence_score"]
        
        return {
            "is_interested": is_interested,
            "confidence_score": confidence_score,
            "general_interest": general_interest,
            "specific_product": specific_product,
            "matched_keywords": specific_keywords
        }
    
    def _respond_to_comment(self, platform: str, comment_id: str, interest_info: Dict[str, Any]) -> bool:
        """
        Respond to a comment with a template response
        
        Args:
            platform: The platform the comment is on
            comment_id: The ID of the comment to respond to
            interest_info: Interest analysis information
            
        Returns:
            Boolean indicating success
        """
        if not self.config["auto_respond"]:
            return False
            
        try:
            # Get appropriate template
            template_key = "specific" if interest_info.get("specific_product") else "general"
            template = self.response_templates.get(platform, {}).get(template_key, "")
            
            if not template:
                logger.warning(f"No template found for {platform}/{template_key}")
                return False
                
            # Get lead form URL
            lead_form_url = self.config["lead_forms"].get(f"{platform}_url")
            if not lead_form_url:
                lead_form_url = self.config["lead_forms"].get("default_url")
                
            # Get contact info
            phone_number = self.config["contact_info"].get("phone")
            
            # Fill in template placeholders
            response = template.replace("{{LEAD_FORM_URL}}", lead_form_url)
            response = response.replace("{{PHONE_NUMBER}}", phone_number)
            
            # Add product name if specific template
            if template_key == "specific" and interest_info.get("specific_product"):
                product_name = interest_info["specific_product"].replace("_", " ").title()
                response = response.replace("{{PRODUCT}}", product_name)
                
            # In a real implementation, call the appropriate API to post the response
            # For now, just log it
            logger.info(f"Would respond to {platform} comment {comment_id} with: {response}")
            
            return True
            
        except Exception as e:
            logger.error(f"Error responding to {platform} comment {comment_id}: {e}")
            return False
    
    def export_leads_to_crm(self) -> int:
        """
        Export new leads to CRM or lead follow-up system
        
        Returns:
            Number of leads exported
        """
        # Filter for leads that haven't been exported
        new_leads = [lead for lead in self.leads if lead.get("status") == "new"]
        
        if not new_leads:
            logger.info("No new leads to export")
            return 0
            
        logger.info(f"Exporting {len(new_leads)} new leads to follow-up system")
        
        # In a real implementation, integrate with your CRM or lead follow-up system
        # For example, you might call a method from your lead follow-up automation system
        
        # try:
        #     from lead_follow_up_automation import LeadFollowupAutomation
        #     follow_up_system = LeadFollowupAutomation()
        #     
        #     for lead in new_leads:
        #         # Convert comment lead to standard lead format
        #         standard_lead = self._convert_to_standard_lead_format(lead)
        #         
        #         # Add to follow-up system
        #         follow_up_system.load_lead(lead_data=standard_lead)
        #         
        #         # Schedule initial follow-up
        #         follow_up_system.schedule_follow_up(
        #             lead=standard_lead,
        #             channel="email",
        #             template_key="initial",
        #             delay_days=0
        #         )
        #         
        #         # Update status
        #         lead["status"] = "exported"
        # except Exception as e:
        #     logger.error(f"Error exporting leads to follow-up system: {e}")
        #     return 0
            
        # For demo, just mark as exported
        for lead in new_leads:
            lead["status"] = "exported"
            lead["exported_at"] = datetime.datetime.now().isoformat()
            
        # Save updated leads
        self._save_leads()
        
        return len(new_leads)
    
    def _convert_to_standard_lead_format(self, comment_lead: Dict[str, Any]) -> Dict[str, Any]:
        """
        Convert a comment lead to standard lead format for the follow-up system
        
        Args:
            comment_lead: Lead from comment
            
        Returns:
            Lead in standard format
        """
        # In a real system, we'd extract more information and perhaps 
        # enhance with additional data lookup, but for demo we create
        # a minimal viable lead
        
        # Determine product interest
        product_interest = self.config["default_product"]
        if comment_lead.get("detected_interest", {}).get("specific_product"):
            product_map = {
                "life_insurance": "term_life",
                "annuities": "iul",
                "medicare": "medicare_supplement",
                "final_expense": "final_expense"
            }
            specific_product = comment_lead["detected_interest"]["specific_product"]
            product_interest = product_map.get(specific_product, product_interest)
        
        # Generate a standard lead format
        standard_lead = {
            "id": comment_lead["id"],
            "source": f"{comment_lead['platform']}_comment",
            "source_details": {
                "platform": comment_lead["platform"],
                "comment_id": comment_lead["comment_id"],
                "comment": comment_lead["comment"]
            },
            "product_interest": product_interest,
            "first_name": comment_lead["author_name"],  # Best we can do with social comment
            "last_name": "",
            "created_at": datetime.datetime.now().isoformat()
        }
        
        return standard_lead
    
    def start_monitoring(self, check_interval_minutes=None):
        """
        Start continuous monitoring of comments for leads
        
        Args:
            check_interval_minutes: Override the default check interval
        """
        if check_interval_minutes is None:
            # Use the lowest interval from platform configs
            intervals = [
                self.config["platforms"][p]["check_frequency_minutes"]
                for p in self.config["platforms"]
                if self.config["platforms"][p]["enabled"]
            ]
            
            check_interval_minutes = min(intervals) if intervals else 60
            
        logger.info(f"Starting comment monitoring with {check_interval_minutes} minute interval")
        
        try:
            while True:
                # Find new leads
                new_leads = self.find_leads_on_all_platforms()
                
                # Export to follow-up system
                if new_leads > 0:
                    exported = self.export_leads_to_crm()
                    logger.info(f"Exported {exported} leads to follow-up system")
                    
                # Wait for next check
                logger.info(f"Waiting {check_interval_minutes} minutes until next check")
                time.sleep(check_interval_minutes * 60)
                
        except KeyboardInterrupt:
            logger.info("Monitoring stopped by user")
        except Exception as e:
            logger.error(f"Error in monitoring loop: {e}")
            
    def get_leads_by_status(self, status=None) -> List[Dict[str, Any]]:
        """
        Get leads filtered by status
        
        Args:
            status: Status to filter by (or None for all)
            
        Returns:
            Filtered leads
        """
        if status is None:
            return self.leads
            
        return [lead for lead in self.leads if lead.get("status") == status]
    
    def get_leads_by_platform(self, platform) -> List[Dict[str, Any]]:
        """
        Get leads filtered by platform
        
        Args:
            platform: Platform to filter by
            
        Returns:
            Filtered leads
        """
        return [lead for lead in self.leads if lead.get("platform") == platform]
    
    def get_leads_by_product(self, product) -> List[Dict[str, Any]]:
        """
        Get leads filtered by product interest
        
        Args:
            product: Product interest to filter by
            
        Returns:
            Filtered leads
        """
        return [
            lead for lead in self.leads 
            if lead.get("detected_interest", {}).get("specific_product") == product
        ]
    
    def get_stats(self) -> Dict[str, Any]:
        """
        Get statistics about leads found
        
        Returns:
            Dictionary with statistics
        """
        stats = {
            "total_leads": len(self.leads),
            "by_status": {},
            "by_platform": {},
            "by_product": {},
            "response_rate": 0
        }
        
        # Count by status
        status_counts = {}
        for lead in self.leads:
            status = lead.get("status", "unknown")
            status_counts[status] = status_counts.get(status, 0) + 1
        stats["by_status"] = status_counts
        
        # Count by platform
        platform_counts = {}
        for lead in self.leads:
            platform = lead.get("platform", "unknown")
            platform_counts[platform] = platform_counts.get(platform, 0) + 1
        stats["by_platform"] = platform_counts
        
        # Count by product interest
        product_counts = {}
        for lead in self.leads:
            product = lead.get("detected_interest", {}).get("specific_product", "general")
            product_counts[product] = product_counts.get(product, 0) + 1
        stats["by_product"] = product_counts
        
        # Calculate response rate
        responded = sum(1 for lead in self.leads if lead.get("responded", False))
        if len(self.leads) > 0:
            stats["response_rate"] = responded / len(self.leads)
            
        return stats


# Example usage when run directly
if __name__ == "__main__":
    # Create lead finder
    lead_finder = VideoCommentLeadFinder()
    
    # Run one-time scan for leads
    new_leads = lead_finder.find_leads_on_all_platforms()
    print(f"Found {new_leads} new leads")
    
    # Export leads to follow-up system
    exported = lead_finder.export_leads_to_crm()
    print(f"Exported {exported} leads to follow-up system")
    
    # Get statistics
    stats = lead_finder.get_stats()
    print(f"Lead statistics: {json.dumps(stats, indent=2)}")
    
    # Uncomment to start continuous monitoring
    # lead_finder.start_monitoring()
