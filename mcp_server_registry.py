#!/usr/bin/env python3
"""MCP Server Registry - Manages all MCP server connections"""

import asyncio
import logging
from typing import Dict, List, Any
from dataclasses import dataclass

logger = logging.getLogger(__name__)

@dataclass
class MCPServer:
    name: str
    url: str
    capabilities: List[str]
    status: str = "connected"

class MCPServerRegistry:
    def __init__(self):
        self.servers = {}
        self.initialized = False
    
    async def initialize(self):
        """Initialize MCP server registry"""
        # Add default servers
        self.servers = {
            "email": MCPServer("email", "http://localhost:8001", ["send_email", "read_email"]),
            "web": MCPServer("web", "http://localhost:8002", ["search", "scrape", "navigate"]),
            "file": MCPServer("file", "http://localhost:8003", ["read", "write", "process"]),
            "audio": MCPServer("audio", "http://localhost:8004", ["generate", "transcribe"]),
            "vision": MCPServer("vision", "http://localhost:8005", ["analyze", "ocr", "detect"]),
            "nvidia-nim": MCPServer("nvidia-nim", "http://localhost:8006", ["load_model", "unload_model", "infer", "get_models"])
        }
        self.initialized = True
        logger.info("✅ MCP Server Registry initialized")
    
    def get_active_servers(self):
        return list(self.servers.values())
    
    def get_server_by_capability(self, capability: str):
        for server in self.servers.values():
            if capability in server.capabilities:
                return server
        return None

# Global registry instance
registry = MCPServerRegistry()
