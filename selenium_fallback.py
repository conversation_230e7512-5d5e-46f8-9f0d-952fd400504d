#!/usr/bin/env python3
"""Selenium Fallback - Basic web automation"""

class WebDriverFallback:
    def __init__(self):
        self.current_url = "http://localhost"
    
    def get(self, url):
        self.current_url = url
        print(f"🌐 Navigating to: {url}")
    
    def find_element(self, by, value):
        return WebElementFallback(value)
    
    def quit(self):
        print("🔚 Browser closed")

class WebElementFallback:
    def __init__(self, value):
        self.value = value
    
    def click(self):
        print(f"🖱️ Clicking element: {self.value}")
    
    def send_keys(self, text):
        print(f"⌨️ Typing: {text}")
    
    @property
    def text(self):
        return f"Text from {self.value}"

class ByFallback:
    ID = "id"
    CLASS_NAME = "class name"
    TAG_NAME = "tag name"
    XPATH = "xpath"

# Mock selenium modules
import sys
sys.modules['selenium'] = type('selenium', (), {})()
sys.modules['selenium.webdriver'] = type('webdriver', (), {'Chrome': WebDriverFallback})()
sys.modules['selenium.webdriver.common.by'] = type('by', (), {'By': ByFallback})()
