import os
from google.oauth2 import service_account
from googleapiclient.discovery import build
from msal import ConfidentialClientApplication
from dotenv import load_dotenv
import requests
from googleapiclient.discovery import build
from google_auth_oauthlib.flow import InstalledAppFlow
from google.auth.transport.requests import Request
import pickle
import base64
import asyncio
from autogen_agentchat.agents import AssistantAgent
from autogen_ext.models.openai import OpenAIChatCompletionClient
from autogen_ext.tools.mcp import McpWorkbench, StdioServerParams
import openai
import time
from datetime import datetime
import json
import logging
from github import Github
from langchain.chains import RetrievalQA
from langchain.vectorstores import FAISS
from langchain.embeddings.openai import OpenAIEmbeddings

# Load environment variables from the .env file
load_dotenv()

# Example: Access the API key
api_key = os.getenv("API_KEY")
if api_key:
    print("API key loaded successfully.")
else:
    print("API key not found. Please check your .env file.")

# We will add other imports as we integrate more tools and agents

# --- Shared Knowledge Base ---
class KnowledgeBase:
    """Centralized knowledge base for all agents."""
    def __init__(self):
        self.local_data = {}
        self.remote_data = {}
        self.online_data = {}

    def load_local_data(self, path):
        """Load data from local files."""
        print(f"[KnowledgeBase]: Loading local data from {path}...")
        # Placeholder for file reading logic
        self.local_data[path] = "Sample local data"

    def query_remote_database(self, query):
        """Query a remote database."""
        print(f"[KnowledgeBase]: Querying remote database with: {query}...")
        # Placeholder for database query logic
        self.remote_data[query] = "Sample remote data"

    def search_online(self, query):
        """Search the internet-like knowledge using OpenAI API."""
        print(f"[KnowledgeBase]: Searching online for: {query}...")
        try:
            response = openai.Completion.create(
                engine="text-davinci-003",
                prompt=f"You are an expert assistant. Provide detailed information about: {query}",
                max_tokens=200
            )
            return response.choices[0].text.strip()
        except Exception as e:
            print(f"[KnowledgeBase Error]: Failed to search online using OpenAI. Error: {e}")
            return "I'm sorry, I couldn't retrieve information at this time."

# Initialize the shared knowledge base
knowledge_base = KnowledgeBase()

# Example usage of the knowledge base
knowledge_base.load_local_data("/path/to/local/file")
knowledge_base.query_remote_database("SELECT * FROM clients")
knowledge_base.search_online("latest barbering trends")

# --- Model Context Protocol (MCP) ---
# This dictionary will hold the conversation history and other relevant state.
# As the system grows, this might become a more complex class or structure,
# potentially stored in a database for persistence.
context = {
    "conversation_history": [],
    "user_name": "Paul Edwards", # We know this!
    "current_business_focus": None, # e.g., "insurance", "emergency_management"
    "relevant_entities": {}, # e.g., {"property_address": "...", "client_name": "..."}
    # Add other context elements as needed (e.g., API keys will be loaded here or env)
}

# Create a logger
logger = logging.getLogger(__name__)
logger.setLevel(logging.INFO)

# Create a file handler and a stream handler
file_handler = logging.FileHandler('orchestrator.log')
stream_handler = logging.StreamHandler()

# Create a formatter and set it for the handlers
formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
file_handler.setFormatter(formatter)
stream_handler.setFormatter(formatter)

# Add the handlers to the logger
logger.addHandler(file_handler)
logger.addHandler(stream_handler)

# Replace print statements with logger.info
# Example:
# print("Orchestrator AI Agent started. How can I assist you today, Paul?")
logger.info("Orchestrator AI Agent started. How can I assist you today, Paul?")

# Replace other print statements similarly

# In the KnowledgeBase class
class KnowledgeBase:
    """Centralized knowledge base for all agents."""
    def __init__(self):
        self.local_data = {}
        self.remote_data = {}
        self.online_data = {}

    def load_local_data(self, path):
        """Load data from local files."""
        logger.info(f"[KnowledgeBase]: Loading local data from {path}...")
        # Placeholder for file reading logic
        self.local_data[path] = "Sample local data"

    def query_remote_database(self, query):
        """Query a remote database."""
        logger.info(f"[KnowledgeBase]: Querying remote database with: {query}...")
        # Placeholder for database query logic
        self.remote_data[query] = "Sample remote data"

    def search_online(self, query):
        """Search the internet-like knowledge using OpenAI API."""
        logger.info(f"[KnowledgeBase]: Searching online for: {query}...")
        try:
            response = openai.Completion.create(
                engine="text-davinci-003",
                prompt=f"You are an expert assistant. Provide detailed information about: {query}",
                max_tokens=200
            )
            return response.choices[0].text.strip()
        except Exception as e:
            logger.error(f"[KnowledgeBase Error]: Failed to search online using OpenAI. Error: {e}")
            return "I'm sorry, I couldn't retrieve information at this time."

# In the MasterControlProgram class
class MasterControlProgram:
    """Oversees and coordinates the activities of all agents and sub-agents."""
    def __init__(self):
        self.agents = {}

    def register_agent(self, agent_name, agent_instance):
        """Register a new agent or sub-agent."""
        self.agents[agent_name] = agent_instance
        logger.info(f"[MCP]: Registered agent '{agent_name}'.")

    def delegate_task(self, task, involved_agents):
        """Delegate a task to multiple agents for collaboration."""
        logger.info(f"[MCP]: Delegating task '{task}' to agents: {involved_agents}")
        results = []
        for agent_name in involved_agents:
            if agent_name in self.agents:
                result = self.agents[agent_name].execute_task(task)
                results.append((agent_name, result))
            else:
                logger.error(f"[MCP]: Agent '{agent_name}' not found.")
        return results

# Updated BaseAgent class with multiple MCPs and tools support
class BaseAgent:
    """Base class for all agents with support for multiple MCPs and tools."""
    def __init__(self, name):
        self.name = name
        self.tools = []  # List of MCP server tools assigned to this agent
        self.mcps = []   # List of MCP Workbench instances

    async def initialize_mcps(self, mcp_configs):
        """Initialize multiple MCP Workbenches for autonomous web search and actions."""
        for config in mcp_configs:
            server_params = StdioServerParams(
                command=config.get("command", "docker"),
                args=config.get("args", [
                    "run",
                    "-i",
                    "--rm",
                    "-e",
                    "GITHUB_PERSONAL_ACCESS_TOKEN",
                    "ghcr.io/github/github-mcp-server",
                ]),
                env=config.get("env", {
                    "GITHUB_PERSONAL_ACCESS_TOKEN": "ghp_XXXXXXXXXXXXXXXXXXXXXXXXXXXXXX",
                })
            )
            mcp_instance = await McpWorkbench(server_params).__aenter__()
            self.mcps.append(mcp_instance)

    def execute_task(self, task):
        """Execute a given task using all MCP Protocols and tools if available."""
        logger.info(f"[{self.name}]: Executing task '{task}'...")
        results = []
        if self.mcps:
            for mcp in self.mcps:
                logger.info(f"[{self.name}]: Using MCP Protocol instance for task '{task}'")
                # Placeholder for actual MCP usage per instance
                results.append(f"Result from MCP instance for task '{task}'")
        if self.tools:
            for tool in self.tools:
                logger.info(f"[{self.name}]: Using tool '{tool}' for task '{task}'")
                results.append(f"Result from {tool} for task '{task}'")
        if results:
            return f"Task '{task}' completed by {self.name} with results: {results}"
        else:
            return f"Task '{task}' completed by {self.name}."

# Updated SubAgent class with multiple MCPs and tools support
class SubAgent(BaseAgent):
    """Sub-agent with specialized capabilities and support for multiple MCPs and tools."""
    def __init__(self, name):
        super().__init__(name)

    def execute_task(self, task):
        logger.info(f"[{self.name}]: Collaborating on task '{task}'...")
        results = []
        if self.mcps:
            for mcp in self.mcps:
                logger.info(f"[{self.name}]: Using MCP Protocol instance for sub-task '{task}'")
                # Placeholder for actual MCP usage per instance
                results.append(f"Result from MCP instance for sub-task '{task}'")
        if self.tools:
            for tool in self.tools:
                logger.info(f"[{self.name}]: Using tool '{tool}' for sub-task '{task}'")
                results.append(f"Result from {tool} for sub-task '{task}'")
        if results:
            return f"Sub-task '{task}' handled by {self.name} with results: {results}"
        else:
            return f"Sub-task '{task}' handled by {self.name}."

class MCPServerRegistry:
    """Registry for managing MCP servers and their capabilities."""
    def __init__(self):
        self.servers = {
            'code': {
                'code-assistant': {'status': 'ready', 'type': 'code_explorer'},
                'code-executor': {'status': 'ready', 'type': 'python_executor'},
                'code-sandbox': {'status': 'ready', 'type': 'secure_sandbox'},
                'vscode-mcp': {'status': 'ready', 'type': 'diagnostics'}
            },
            'ai_models': {
                'openai-mcp': {'status': 'ready', 'type': 'model_bridge'},
                'claude-code': {'status': 'ready', 'type': 'code_generation'},
                'multi-model': {'status': 'ready', 'type': 'model_aggregator'}
            },
            'search': {
                'chroma': {'status': 'ready', 'type': 'vector_db'},
                'needle': {'status': 'ready', 'type': 'document_manager'},
                'perplexity': {'status': 'ready', 'type': 'web_search'}
            },
            'data': {
                'xiyan': {'status': 'ready', 'type': 'db_query'},
                'sql-analyzer': {'status': 'ready', 'type': 'sql_tools'},
                'openapi': {'status': 'ready', 'type': 'api_integration'}
            }
        }
        self.active_connections = {}

    def connect_server(self, category, server_name):
        """Connect to a specific MCP server."""
        if category in self.servers and server_name in self.servers[category]:
            logger.info(f"[MCPRegistry]: Connecting to {server_name} server...")
            self.servers[category][server_name]['status'] = 'connected'
            self.active_connections[server_name] = True
            return True
        return False

    def get_available_servers(self, category=None):
        """Get list of available servers, optionally filtered by category."""
        if category:
            return self.servers.get(category, {})
        return self.servers

# Initialize MCP server registry
mcp_registry = MCPServerRegistry()

# Update SecurityMasterAgent to use MCP servers
def enhance_security_master_with_mcp():
    """Enhance SecurityMasterAgent with MCP server capabilities."""
    # Connect to relevant MCP servers
    mcp_registry.connect_server('code', 'code-sandbox')
    mcp_registry.connect_server('search', 'perplexity')
    mcp_registry.connect_server('data', 'openapi')
    
    # Update security tools with MCP capabilities
    security_master.tools.update({
        'code_analysis': mcp_registry.servers['code'],
        'threat_intelligence': mcp_registry.servers['search'],
        'api_security': mcp_registry.servers['data']
    })

# Enhance security master with MCP capabilities
enhance_security_master_with_mcp()

class WebSearchAgent(BaseAgent):
    """Agent specialized in internet searches and web connectivity."""
    def __init__(self, name):
        super().__init__(name)
        self.search_providers = {
            'perplexity': mcp_registry.servers['search']['perplexity'],
            'chroma': mcp_registry.servers['search']['chroma'],
            'needle': mcp_registry.servers['search']['needle']
        }
        self.cache = {}
        
    def execute_task(self, task):
        logger.info(f"[{self.name}]: Executing web search task: {task}")
        
        # Determine which search provider to use
        if "technical" in task.lower() or "code" in task.lower():
            provider = 'perplexity'
        elif "document" in task.lower():
            provider = 'needle'
        else:
            provider = 'chroma'
            
        search_result = self._perform_search(task, provider)
        self.cache[task] = {
            'result': search_result,
            'timestamp': datetime.now().isoformat(),
            'provider': provider
        }
        
        return search_result
        
    def _perform_search(self, query, provider):
        """Perform search using specified provider."""
        logger.info(f"[{self.name}]: Using {provider} for query: {query}")
        
        # Simulate search using MCP server
        if self.search_providers[provider]['status'] == 'connected':
            return f"Search results for '{query}' using {provider}"
        else:
            # Try to connect to the server
            category = next(cat for cat, servers in mcp_registry.servers.items() 
                          if provider in servers)
            mcp_registry.connect_server(category, provider)
            return f"Reconnected and searched for '{query}' using {provider}"

# Initialize WebSearchAgent
web_search_agent = WebSearchAgent("Web Search Agent")
mcp.register_agent("Web Search", web_search_agent)

# Update the security master to use the web search agent
def enhance_security_master_with_websearch():
    """Enhance SecurityMasterAgent with web search capabilities."""
    security_master.tools.update({
        'web_intelligence': web_search_agent
    })

# Enhance security master with web search
enhance_security_master_with_websearch()

class AgentLearningSystem:
    """System for agent learning and adaptation."""
    def __init__(self):
        self.learning_data = {}
        self.performance_metrics = {}
        self.adaptation_rules = {}
        
    def record_interaction(self, agent_name, task, result, feedback=None):
        """Record agent interactions for learning."""
        if agent_name not in self.learning_data:
            self.learning_data[agent_name] = []
            
        interaction = {
            'timestamp': datetime.now().isoformat(),
            'task': task,
            'result': result,
            'feedback': feedback
        }
        
        self.learning_data[agent_name].append(interaction)
        self._update_performance_metrics(agent_name)
        
    def _update_performance_metrics(self, agent_name):
        """Update agent performance metrics."""
        if agent_name not in self.performance_metrics:
            self.performance_metrics[agent_name] = {
                'tasks_completed': 0,
                'success_rate': 0.0,
                'average_response_time': 0.0
            }
            
        agent_interactions = self.learning_data[agent_name]
        self.performance_metrics[agent_name]['tasks_completed'] = len(agent_interactions)
        
    def adapt_agent_behavior(self, agent_name):
        """Adapt agent behavior based on learning data."""
        if agent_name not in self.learning_data:
            return None
            
        recent_interactions = self.learning_data[agent_name][-10:]  # Last 10 interactions
        adaptation_rules = self._generate_adaptation_rules(recent_interactions)
        
        self.adaptation_rules[agent_name] = adaptation_rules
        return adaptation_rules
        
    def _generate_adaptation_rules(self, interactions):
        """Generate adaptation rules based on interaction patterns."""
        rules = {
            'preferred_tools': self._identify_effective_tools(interactions),
            'task_patterns': self._analyze_task_patterns(interactions),
            'optimization_suggestions': self._generate_optimizations(interactions)
        }
        return rules

    def _identify_effective_tools(self, interactions):
        """Identify most effective tools from interactions."""
        tool_effectiveness = {}
        for interaction in interactions:
            if 'tools_used' in interaction:
                for tool in interaction['tools_used']:
                    if tool not in tool_effectiveness:
                        tool_effectiveness[tool] = {'success_count': 0, 'total_uses': 0}
                    tool_effectiveness[tool]['total_uses'] += 1
                    if interaction.get('success', False):
                        tool_effectiveness[tool]['success_count'] += 1
        
        return tool_effectiveness

    def _analyze_task_patterns(self, interactions):
        """Analyze patterns in successful task completions."""
        task_patterns = {}
        for interaction in interactions:
            task_type = interaction.get('task_type', 'unknown')
            if task_type not in task_patterns:
                task_patterns[task_type] = {'success_count': 0, 'total': 0}
            task_patterns[task_type]['total'] += 1
            if interaction.get('success', False):
                task_patterns[task_type]['success_count'] += 1
        
        return task_patterns

    def _generate_optimizations(self, interactions):
        """Generate optimization suggestions based on interaction patterns."""
        return {
            'response_time': self._analyze_response_times(interactions),
            'success_factors': self._analyze_success_factors(interactions),
            'improvement_areas': self._identify_improvement_areas(interactions)
        }

# Initialize learning system
learning_system = AgentLearningSystem()

# Update BaseAgent to use learning system
class BaseAgent:
    """Enhanced base agent with learning capabilities."""
    def __init__(self, name):
        super().__init__(name)
        self.learning_enabled = True
        
    def execute_task(self, task):
        """Execute task with learning integration."""
        start_time = time.time()
        result = super().execute_task(task)
        execution_time = time.time() - start_time
        
        if self.learning_enabled:
            learning_system.record_interaction(
                self.name,
                task,
                result,
                feedback={
                    'execution_time': execution_time,
                    'tools_used': self.tools,
                    'success': True  # You might want to add actual success detection
                }
            )
            
            # Adapt behavior based on learning
            adaptations = learning_system.adapt_agent_behavior(self.name)
            if adaptations:
                self._apply_adaptations(adaptations)
                
        return result
        
    def _apply_adaptations(self, adaptations):
        """Apply learned adaptations to agent behavior."""
        if 'preferred_tools' in adaptations:
            self._optimize_tool_usage(adaptations['preferred_tools'])
        if 'task_patterns' in adaptations:
            self._optimize_task_handling(adaptations['task_patterns'])
