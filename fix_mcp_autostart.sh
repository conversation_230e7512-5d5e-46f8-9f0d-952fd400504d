#!/bin/bash

# Fix MCP Server Autostart Script
# This script installs the launchd service and starts all MCP servers

echo "🔧 MCP Server Autostart Fixer"
echo "============================"
echo "This script will fix the MCP server autostart issue and ensure all 10 servers start automatically."
echo

# Get the current directory
CURRENT_DIR=$(pwd)

# Install the launchd plist file
echo "Installing launchd service..."
cp "${CURRENT_DIR}/com.flofaction.mcpservers.plist" ~/Library/LaunchAgents/
launchctl unload ~/Library/LaunchAgents/com.flofaction.mcpservers.plist 2>/dev/null
launchctl load -w ~/Library/LaunchAgents/com.flofaction.mcpservers.plist

# Check if the service was installed correctly
if launchctl list | grep -q "com.flofaction.mcpservers"; then
    echo "✅ Launchd service installed successfully"
else
    echo "⚠️ Failed to install launchd service"
fi

# Stop any running MCP servers
echo "Stopping any running MCP servers..."
pkill -f "python3 start_mcp_servers.py" 2>/dev/null
pkill -f "local_models_mcp_server.py" 2>/dev/null

# Start the servers using the startup script
echo "Starting all MCP servers..."
"${CURRENT_DIR}/start_all_mcp_servers.sh" &

# Wait a moment for servers to start
sleep 10

# Check if servers are running
echo "Checking MCP server status..."
for port in 8080 8081 8082 8083 8084 8085 8086 8087 8088 8089; do
    if nc -z localhost $port 2>/dev/null; then
        echo "✅ Server on port $port is running"
    else
        echo "⚠️ Server on port $port is not responding"
    fi
done

echo
echo "✅ MCP server autostart setup complete!"
echo "All 10 MCP servers should now start automatically when your system boots."
echo "If you still have issues, check the log files:"
echo "  - ${CURRENT_DIR}/mcp_servers_startup.log"
echo "  - ${CURRENT_DIR}/mcp_servers_output.log"
echo "  - ${CURRENT_DIR}/mcp_servers_error.log"
echo
echo "You can manually start all servers at any time by running:"
echo "  ${CURRENT_DIR}/start_all_mcp_servers.sh"
