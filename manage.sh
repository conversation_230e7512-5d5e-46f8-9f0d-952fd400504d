#!/bin/bash

# Shell script for managing the agent system
# Provides convenient shortcuts and environment management

# Default settings
PYTHON="python3"
VENV_DIR="venv"
CONFIG_DIR="config"
PLUGIN_DIR="plugins"
REQUIREMENTS="requirements.txt"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Helper functions
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

check_python() {
    if ! command -v $PYTHON &> /dev/null; then
        log_error "Python not found. Please install Python 3.8 or higher"
        exit 1
    fi
}

check_venv() {
    if [ ! -d "$VENV_DIR" ]; then
        log_warn "Virtual environment not found"
        return 1
    fi
    return 0
}

create_venv() {
    log_info "Creating virtual environment..."
    $PYTHON -m venv $VENV_DIR
    if [ $? -ne 0 ]; then
        log_error "Failed to create virtual environment"
        exit 1
    fi
}

activate_venv() {
    if [ -f "$VENV_DIR/bin/activate" ]; then
        source "$VENV_DIR/bin/activate"
    else
        source "$VENV_DIR/Scripts/activate"  # Windows
    fi
}

install_requirements() {
    log_info "Installing requirements..."
    if [ ! -f "$REQUIREMENTS" ]; then
        log_error "Requirements file not found"
        exit 1
    fi
    pip install -r "$REQUIREMENTS"
    if [ $? -ne 0 ]; then
        log_error "Failed to install requirements"
        exit 1
    fi
}

# Command functions
cmd_init() {
    check_python
    
    # Create virtual environment if needed
    if ! check_venv; then
        create_venv
    fi
    
    # Activate virtual environment
    activate_venv
    
    # Install requirements
    install_requirements
    
    # Run system setup
    python run.py setup $@
    if [ $? -ne 0 ]; then
        log_error "System setup failed"
        exit 1
    fi
    
    log_info "System initialized successfully"
}

cmd_start() {
    if ! check_venv; then
        log_error "Virtual environment not found. Run 'init' first"
        exit 1
    fi
    
    activate_venv
    python run.py start $@
}

cmd_stop() {
    if ! check_venv; then
        log_error "Virtual environment not found. Run 'init' first"
        exit 1
    fi
    
    activate_venv
    python run.py stop
}

cmd_restart() {
    if ! check_venv; then
        log_error "Virtual environment not found. Run 'init' first"
        exit 1
    fi
    
    activate_venv
    python run.py restart
}

cmd_status() {
    if ! check_venv; then
        log_error "Virtual environment not found. Run 'init' first"
        exit 1
    fi
    
    activate_venv
    python run.py status
}

cmd_logs() {
    if ! check_venv; then
        log_error "Virtual environment not found. Run 'init' first"
        exit 1
    fi
    
    activate_venv
    python run.py logs $@
}

cmd_test() {
    if ! check_venv; then
        log_error "Virtual environment not found. Run 'init' first"
        exit 1
    fi
    
    activate_venv
    python -m pytest test_full_system.py $@
}

cmd_clean() {
    if ! check_venv; then
        log_error "Virtual environment not found. Run 'init' first"
        exit 1
    fi
    
    activate_venv
    python run.py cleanup $@
    
    # Remove virtual environment
    if [ "$1" == "--all" ]; then
        log_info "Removing virtual environment..."
        rm -rf $VENV_DIR
    fi
}

cmd_shell() {
    if ! check_venv; then
        log_error "Virtual environment not found. Run 'init' first"
        exit 1
    fi
    
    activate_venv
    $PYTHON
}

# Command line parsing
usage() {
    echo "Usage: $0 <command> [options]"
    echo
    echo "Commands:"
    echo "  init     Initialize the system"
    echo "  start    Start the system"
    echo "  stop     Stop the system"
    echo "  restart  Restart the system"
    echo "  status   Show system status"
    echo "  logs     Show system logs"
    echo "  test     Run system tests"
    echo "  clean    Clean up system data"
    echo "  shell    Start Python shell with environment"
    echo
    echo "Options:"
    echo "  init:"
    echo "    --force   Force initialization even if already set up"
    echo "  start:"
    echo "    -c FILE   Use specific config file"
    echo "    -p DIR    Use specific plugins directory"
    echo "  logs:"
    echo "    -n NUM    Show NUM lines (default: 100)"
    echo "  clean:"
    echo "    --force   Force cleanup even if system is running"
    echo "    --all     Also remove virtual environment"
    echo "  test:"
    echo "    -v        Verbose output"
    echo "    -k EXPR   Only run tests matching expression"
}

# Main script
if [ $# -eq 0 ]; then
    usage
    exit 1
fi

CMD=$1
shift

case $CMD in
    init)
        cmd_init $@
        ;;
    start)
        cmd_start $@
        ;;
    stop)
        cmd_stop
        ;;
    restart)
        cmd_restart
        ;;
    status)
        cmd_status
        ;;
    logs)
        cmd_logs $@
        ;;
    test)
        cmd_test $@
        ;;
    clean)
        cmd_clean $@
        ;;
    shell)
        cmd_shell
        ;;
    help)
        usage
        ;;
    *)
        log_error "Unknown command: $CMD"
        usage
        exit 1
        ;;
esac

exit 0