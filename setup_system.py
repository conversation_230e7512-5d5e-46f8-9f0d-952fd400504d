"""
System Setup Script for initializing the agent system environment
"""

import asyncio
import os
import sys
from pathlib import Path
import argparse
import yaml
import secrets
import bcrypt
from typing import Dict, Any
import psycopg2
from psycopg2.extensions import ISOLATION_LEVEL_AUTOCOMMIT
import redis
import logging
from core.config_manager import DatabaseConfig, RedisConfig

# Default paths
DEFAULT_CONFIG_DIR = Path("config")
DEFAULT_DATA_DIR = Path("data")
DEFAULT_LOG_DIR = Path("logs")
DEFAULT_PLUGIN_DIR = Path("plugins")

# Database schemas
SCHEMAS = {
    "agents": """
        CREATE TABLE IF NOT EXISTS agents (
            id VARCHAR(64) PRIMARY KEY,
            name VARCHAR(255) NOT NULL,
            type VARCHAR(64) NOT NULL,
            status VARCHAR(32) NOT NULL,
            config JSON<PERSON>,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        );
    """,
    "workflows": """
        CREATE TABLE IF NOT EXISTS workflows (
            id VARCHAR(64) PRIMARY KEY,
            name VARCHAR(255) NOT NULL,
            version VARCHAR(32) NOT NULL,
            definition JSONB NOT NULL,
            status VARCHAR(32) NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        );
    """,
    "events": """
        CREATE TABLE IF NOT EXISTS events (
            id VARCHAR(64) PRIMARY KEY,
            type VARCHAR(255) NOT NULL,
            source VARCHAR(255) NOT NULL,
            data JSONB,
            timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        );
    """,
    "users": """
        CREATE TABLE IF NOT EXISTS users (
            id VARCHAR(64) PRIMARY KEY,
            username VARCHAR(255) UNIQUE NOT NULL,
            password_hash VARCHAR(255) NOT NULL,
            roles JSONB NOT NULL,
            active BOOLEAN DEFAULT true,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        );
    """
}

class SystemSetup:
    """Handles system setup and initialization"""
    
    def __init__(self, config_path: Path):
        self.config_path = config_path
        self.logger = self._setup_logging()
        
    def _setup_logging(self) -> logging.Logger:
        """Set up logging"""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s [%(levelname)s] %(message)s'
        )
        return logging.getLogger("system_setup")
        
    def create_directory_structure(self):
        """Create required directories"""
        dirs = [
            DEFAULT_CONFIG_DIR,
            DEFAULT_DATA_DIR,
            DEFAULT_LOG_DIR / "agent_system",
            DEFAULT_LOG_DIR / "insurance",
            DEFAULT_LOG_DIR / "trading",
            DEFAULT_LOG_DIR / "content",
            DEFAULT_LOG_DIR / "system",
            DEFAULT_PLUGIN_DIR
        ]
        
        for directory in dirs:
            directory.mkdir(parents=True, exist_ok=True)
            self.logger.info(f"Created directory: {directory}")
            
    def generate_config(self) -> Dict[str, Any]:
        """Generate default configuration"""
        config = {
            "environment": "development",
            "database": {
                "host": "localhost",
                "port": 5432,
                "database": "agent_system",
                "username": "agent_system",
                "password": secrets.token_urlsafe(16),
                "pool_size": 10,
                "ssl_mode": "disable"
            },
            "redis": {
                "host": "localhost",
                "port": 6379,
                "db": 0,
                "password": secrets.token_urlsafe(16)
            },
            "security": {
                "jwt_secret": secrets.token_hex(32),
                "admin": {
                    "username": "admin",
                    "password": "admin"  # Should be changed after setup
                }
            },
            "monitoring": {
                "prometheus_port": 9090,
                "grafana_port": 3000,
                "metrics_interval": 60,
                "health_check_interval": 30,
                "retention_days": 30
            },
            "logging": {
                "level": "INFO",
                "file_path": "logs",
                "max_size_mb": 100,
                "backup_count": 5,
                "json_format": True
            }
        }
        
        return config
        
    def save_config(self, config: Dict[str, Any]):
        """Save configuration to file"""
        with open(self.config_path, 'w') as f:
            yaml.dump(config, f, default_flow_style=False)
        self.logger.info(f"Saved configuration to {self.config_path}")
        
    def setup_database(self, config: DatabaseConfig):
        """Set up PostgreSQL database"""
        # Connect to PostgreSQL server
        conn = psycopg2.connect(
            host=config.host,
            port=config.port,
            user=config.username,
            password=config.password
        )
        conn.set_isolation_level(ISOLATION_LEVEL_AUTOCOMMIT)
        
        try:
            with conn.cursor() as cur:
                # Create database if not exists
                cur.execute(
                    f"SELECT 1 FROM pg_database WHERE datname = '{config.database}'"
                )
                if not cur.fetchone():
                    cur.execute(f"CREATE DATABASE {config.database}")
                    self.logger.info(f"Created database: {config.database}")
                    
            # Connect to the new database
            conn = psycopg2.connect(
                host=config.host,
                port=config.port,
                user=config.username,
                password=config.password,
                database=config.database
            )
            
            # Create schemas
            with conn.cursor() as cur:
                for name, schema in SCHEMAS.items():
                    cur.execute(schema)
                    self.logger.info(f"Created schema: {name}")
                    
            conn.commit()
            
        finally:
            conn.close()
            
    def setup_redis(self, config: RedisConfig):
        """Set up Redis"""
        redis_client = redis.Redis(
            host=config.host,
            port=config.port,
            db=config.db,
            password=config.password,
            decode_responses=True
        )
        
        try:
            redis_client.ping()
            self.logger.info("Redis connection successful")
        finally:
            redis_client.close()
            
    def create_admin_user(self, config: Dict[str, Any]):
        """Create admin user in database"""
        admin = config["security"]["admin"]
        password = admin["password"]
        
        # Hash password
        salt = bcrypt.gensalt()
        password_hash = bcrypt.hashpw(
            password.encode(),
            salt
        ).decode()
        
        # Connect to database
        db_config = DatabaseConfig(**config["database"])
        conn = psycopg2.connect(
            host=db_config.host,
            port=db_config.port,
            user=db_config.username,
            password=db_config.password,
            database=db_config.database
        )
        
        try:
            with conn.cursor() as cur:
                # Check if admin exists
                cur.execute(
                    "SELECT 1 FROM users WHERE username = %s",
                    (admin["username"],)
                )
                if not cur.fetchone():
                    # Create admin user
                    cur.execute(
                        """
                        INSERT INTO users (
                            id, username, password_hash, roles
                        ) VALUES (%s, %s, %s, %s)
                        """,
                        (
                            "admin",
                            admin["username"],
                            password_hash,
                            '["admin"]'
                        )
                    )
                    conn.commit()
                    self.logger.info("Created admin user")
                    
        finally:
            conn.close()

def main():
    """Main entry point"""
    parser = argparse.ArgumentParser(description="System Setup")
    parser.add_argument(
        "-c",
        "--config",
        default="config/config.yml",
        help="Path to configuration file"
    )
    args = parser.parse_args()
    
    config_path = Path(args.config)
    setup = SystemSetup(config_path)
    
    try:
        # Create directories
        setup.create_directory_structure()
        
        # Generate and save config
        config = setup.generate_config()
        setup.save_config(config)
        
        # Set up database
        db_config = DatabaseConfig(**config["database"])
        setup.setup_database(db_config)
        
        # Set up Redis
        redis_config = RedisConfig(**config["redis"])
        setup.setup_redis(redis_config)
        
        # Create admin user
        setup.create_admin_user(config)
        
        setup.logger.info("System setup completed successfully")
        return 0
        
    except Exception as e:
        setup.logger.error(f"Error during setup: {str(e)}")
        return 1

if __name__ == "__main__":
    sys.exit(main())