"""
Full System Test Suite for testing complete agent system functionality
"""

import asyncio
import pytest
from typing import Dict, Any, Optional
from datetime import datetime
from core.coordinator import get_coordinator
from core.base_agent import BaseAgent
from core.config_manager import get_config_manager
from core.state_manager import get_state_manager
from core.event_system import get_event_system
from core.message_bus import get_message_bus
from core.cache_manager import get_cache_manager
from core.security_manager import get_security_manager, SecurityLevel
from core.plugin_manager import get_plugin_manager
from core.workflow_engine import get_workflow_engine
from core.test_framework import test_case, TestEnvironment
from agent_monitor import get_agent_monitor, AgentStatus
from utils.logging_setup import get_logger
from utils.metrics import get_metrics_collector
from utils.tracing import AgentTracer

class TestAgent(BaseAgent):
    """Test agent implementation"""
    
    async def initialize(self):
        self.counter = 0
        self.logger = get_logger("test_agent")
        self.metrics = get_metrics_collector("test_agent")
        
    async def process_message(self, message: Dict) -> Dict:
        self.counter += 1
        return {"result": f"Processed message {self.counter}"}

@pytest.fixture
async def system_setup():
    """Set up complete system for testing"""
    coordinator = get_coordinator()
    await coordinator.start()
    yield coordinator
    await coordinator.shutdown()

@pytest.fixture
async def test_env():
    """Create test environment"""
    env = TestEnvironment()
    yield env
    await env.cleanup()

@test_case(
    "test_system_startup",
    "Verify system startup and component initialization"
)
async def test_system_startup(env: TestEnvironment):
    coordinator = get_coordinator()
    
    # Check all components started
    status = coordinator.get_component_status()
    for component, info in status.items():
        assert info["state"] == "running", f"Component {component} not running"
        
    return "All components started successfully"

@test_case(
    "test_agent_registration",
    "Test agent registration and monitoring"
)
async def test_agent_registration(env: TestEnvironment):
    monitor = get_agent_monitor()
    await monitor.start()
    
    # Create and register test agent
    agent = TestAgent("test_agent_1")
    await monitor.register_agent(agent)
    
    # Verify registration
    status = await monitor.get_agent_status("test_agent_1")
    assert status["status"] == AgentStatus.IDLE.value
    
    # Clean up
    await monitor.unregister_agent("test_agent_1")
    await monitor.stop()
    
    return "Agent registration successful"

@test_case(
    "test_message_flow",
    "Test message passing between components"
)
async def test_message_flow(env: TestEnvironment):
    message_bus = get_message_bus()
    event_system = get_event_system()
    
    # Set up test message handler
    messages_received = []
    
    async def handle_message(message):
        messages_received.append(message)
        
    await message_bus.subscribe("test_topic", handle_message)
    
    # Publish test message
    await message_bus.publish(
        "test_topic",
        {"test": "data"},
        "test_publisher"
    )
    
    # Wait for processing
    await asyncio.sleep(1)
    
    assert len(messages_received) == 1
    assert messages_received[0]["test"] == "data"
    
    return "Message flow working correctly"

@test_case(
    "test_state_management",
    "Test state management and persistence"
)
async def test_state_management(env: TestEnvironment):
    state_manager = get_state_manager()
    cache = get_cache_manager()
    
    # Set test state
    test_key = "test_state"
    test_value = {"data": "test"}
    
    await state_manager.set(test_key, test_value, "test")
    
    # Verify state
    stored_value = await state_manager.get(test_key)
    assert stored_value == test_value
    
    # Verify cache
    cached_value = await cache.get(f"state:{test_key}")
    assert cached_value == test_value
    
    return "State management working correctly"

@test_case(
    "test_security",
    "Test security and access control"
)
async def test_security(env: TestEnvironment):
    security = get_security_manager()
    
    # Register test user
    user_id = await security.register_user(
        "test_user",
        "SecurePass123!",
        ["user"]
    )
    
    # Test authentication
    token = await security.authenticate("test_user", "SecurePass123!")
    user = await security.validate_token(token)
    
    assert user.username == "test_user"
    
    # Test permissions
    has_permission = await security.check_permission(
        user,
        "read",
        SecurityLevel.RESTRICTED
    )
    assert has_permission
    
    return "Security controls working correctly"

@test_case(
    "test_workflow",
    "Test workflow execution"
)
async def test_workflow(env: TestEnvironment):
    workflow = get_workflow_engine()
    
    # Define test workflow steps
    async def step_1(context):
        return "Step 1 complete"
        
    async def step_2(context):
        return "Step 2 complete"
        
    # Create workflow definition
    definition = {
        "id": "test_workflow",
        "name": "Test Workflow",
        "version": "1.0",
        "steps": {
            "step1": {
                "id": "step1",
                "name": "Step 1",
                "type": "task",
                "handler": step_1,
                "next_steps": ["step2"]
            },
            "step2": {
                "id": "step2",
                "name": "Step 2",
                "type": "task",
                "handler": step_2,
                "next_steps": []
            }
        },
        "initial_step": "step1"
    }
    
    workflow.register_workflow(definition)
    
    # Start workflow
    instance_id = await workflow.start_workflow("test_workflow")
    
    # Wait for completion
    await asyncio.sleep(2)
    
    # Check results
    status = workflow.get_workflow_status(instance_id)
    assert status["state"] == "completed"
    
    return "Workflow execution successful"

@test_case(
    "test_plugin_system",
    "Test plugin loading and execution"
)
async def test_plugin_system(env: TestEnvironment):
    plugin_manager = get_plugin_manager()
    
    # Register plugin path
    plugin_manager.register_plugin_path("plugins")
    
    # Discover plugins
    await plugin_manager.discover_plugins()
    
    # Load test plugin if available
    try:
        await plugin_manager.load_plugin("test_plugin")
        await plugin_manager.enable_plugin("test_plugin")
        
        # Test plugin hook
        results = await plugin_manager.call_hook("test_hook")
        assert len(results) > 0
        
        await plugin_manager.disable_plugin("test_plugin")
        await plugin_manager.unload_plugin("test_plugin")
        
    except ValueError:
        # Skip if test plugin not available
        return "Plugin system functional (no test plugin)"
        
    return "Plugin system working correctly"

@test_case(
    "test_metrics_and_monitoring",
    "Test metrics collection and monitoring"
)
async def test_metrics_and_monitoring(env: TestEnvironment):
    monitor = get_agent_monitor()
    metrics = get_metrics_collector("test")
    
    # Record test metrics
    metrics.record_operation("test_op", "success")
    metrics.set_gauge("test_gauge", 50.0)
    
    # Create test agent with metrics
    agent = TestAgent("test_agent_2")
    agent.metrics.set_gauge("cpu_usage", 50.0)
    agent.metrics.set_gauge("memory_usage", 60.0)
    
    await monitor.register_agent(agent)
    
    # Wait for metrics collection
    await asyncio.sleep(2)
    
    # Check agent status
    status = await monitor.get_agent_status("test_agent_2")
    assert "metrics" in status
    
    await monitor.unregister_agent("test_agent_2")
    
    return "Metrics and monitoring working correctly"

@pytest.mark.asyncio
async def test_full_system():
    """Run complete system test suite"""
    # Get test framework
    from core.test_framework import get_test_framework
    framework = get_test_framework()
    
    # Register test suite
    framework.register_suite(
        "full_system",
        [
            "test_system_startup",
            "test_agent_registration",
            "test_message_flow",
            "test_state_management",
            "test_security",
            "test_workflow",
            "test_plugin_system",
            "test_metrics_and_monitoring"
        ]
    )
    
    # Run test suite
    results = await framework.run_suite("full_system")
    
    # Check results
    failed_tests = [
        r.name for r in results
        if r.result != "pass"
    ]
    
    if failed_tests:
        pytest.fail(f"Failed tests: {', '.join(failed_tests)}")

if __name__ == "__main__":
    pytest.main([__file__])