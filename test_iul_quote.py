#!/usr/bin/env python3
"""
Test script for IUL quote generation specifically for young adults like <PERSON>
"""

from insurance_products import get_iul_products, get_iul_carriers, find_suitable_iul_products
from quote_helper import generate_iul_quote, format_iul_proposal
import json
import os

def test_iul_quote_for_paul():
    """Generate and save an IUL quote for <PERSON>"""
    
    # <PERSON> data
    paul_data = {
        'first_name': '<PERSON>',
        'last_name': '<PERSON>',
        'email': '<EMAIL>',
        'phone': '7725395908',
        'age': 32,
        'gender': 'Male',
        'dob': '02/22/1993',
        'address': '2426 SE Whitehorse Street',
        'city': 'Port Saint Lucie',
        'state': 'FL',
        'zip': '34983',
        'ssn': '590312356',
        'income': 85000,
        'budget': 200,
        'medications': [],
        'smoker': False
    }
    
    print("Testing IUL product finder...")
    suitable_products = find_suitable_iul_products(paul_data['age'])
    print(f"Found {len(suitable_products)} suitable IUL products for age {paul_data['age']}")
    for product in suitable_products[:2]:  # Show top 2 products
        print(f"- {product['carrier']} - {product['name']} (Rating: {product['rating']})")
    
    print("\nGenerating IUL quote...")
    quote = generate_iul_quote(paul_data)
    
    # Save the quote summary
    os.makedirs('data/quotes', exist_ok=True)
    with open('data/quotes/paul_edwards_iul_quote.json', 'w') as f:
        json.dump(quote, f, indent=2)
    
    # Format and save the proposal
    proposal = format_iul_proposal(quote, paul_data)
    with open('data/quotes/paul_edwards_iul_proposal.md', 'w') as f:
        f.write(proposal)
    
    # Print key details
    print("\nIUL Quote Summary:")
    print(f"Carrier: {quote['recommended_plan']['carrier']}")
    print(f"Product: {quote['recommended_plan']['name']}")
    print(f"Death Benefit: ${quote['recommended_plan']['death_benefit']:,.2f}")
    print(f"Monthly Premium: ${quote['recommended_plan']['monthly_premium']:,.2f}")
    
    print(f"\nProjected Cash Value (Year 20): ${quote['recommended_plan']['cash_value_projection']['year_20']:,.2f}")
    print(f"Annual Retirement Income: ${quote['recommended_plan']['retirement_income']['annual_amount']:,.2f}")
    print(f"Total Retirement Income: ${quote['recommended_plan']['retirement_income']['total_amount']:,.2f}")
    
    print("\nFiles saved:")
    print(f"- Quote JSON: data/quotes/paul_edwards_iul_quote.json")
    print(f"- Proposal: data/quotes/paul_edwards_iul_proposal.md")
    
    return quote, proposal

if __name__ == "__main__":
    print("=== IUL Quote Test ===")
    test_iul_quote_for_paul()
    print("=== Test Complete ===")