"""
Metrics collection and reporting utilities for the agent system
"""

from typing import Dict, Any, Optional, List
import time
from prometheus_client import (
    Counter, Gauge, Histogram, Summary,
    CollectorRegistry, multiprocess, REGISTRY
)
from prometheus_client.metrics import MetricWrapperBase
import threading
from contextlib import contextmanager
import os

class MetricsCollector:
    """Manages metrics collection and reporting"""
    
    def __init__(self, namespace: str):
        self.namespace = namespace
        self._metrics: Dict[str, MetricWrapperBase] = {}
        self._lock = threading.Lock()
        
        # Initialize metrics registry
        if 'prometheus_multiproc_dir' in os.environ:
            self.registry = CollectorRegistry()
            multiprocess.MultiProcessCollector(self.registry)
        else:
            self.registry = REGISTRY
            
        # Common labels
        self.common_labels = {
            'service': os.getenv('SERVICE_NAME', 'agent-system'),
            'environment': os.getenv('ENVIRONMENT', 'development'),
            'version': os.getenv('VERSION', 'unknown')
        }
        
        # Initialize default metrics
        self._setup_default_metrics()
        
    def _setup_default_metrics(self):
        """Initialize default system metrics"""
        # Operation metrics
        self.counter(
            'operations_total',
            'Total number of operations',
            ['operation', 'status']
        )
        
        self.counter(
            'errors_total',
            'Total number of errors',
            ['type']
        )
        
        # Performance metrics
        self.histogram(
            'operation_duration_seconds',
            'Operation duration in seconds',
            ['operation'],
            buckets=[.01, .05, .1, .5, 1, 5]
        )
        
        self.gauge(
            'memory_usage_bytes',
            'Memory usage in bytes'
        )
        
        self.gauge(
            'cpu_usage_percent',
            'CPU usage percentage'
        )
        
    def _create_metric_name(self, name: str) -> str:
        """Create full metric name with namespace"""
        return f"{self.namespace}_{name}" if self.namespace else name
        
    def counter(
        self,
        name: str,
        description: str,
        labelnames: Optional[List[str]] = None,
        **kwargs
    ) -> Counter:
        """Create or get a Counter metric"""
        metric_name = self._create_metric_name(name)
        with self._lock:
            if metric_name not in self._metrics:
                self._metrics[metric_name] = Counter(
                    metric_name,
                    description,
                    labelnames=labelnames or [],
                    registry=self.registry,
                    **kwargs
                )
        return self._metrics[metric_name]
        
    def gauge(
        self,
        name: str,
        description: str,
        labelnames: Optional[List[str]] = None,
        **kwargs
    ) -> Gauge:
        """Create or get a Gauge metric"""
        metric_name = self._create_metric_name(name)
        with self._lock:
            if metric_name not in self._metrics:
                self._metrics[metric_name] = Gauge(
                    metric_name,
                    description,
                    labelnames=labelnames or [],
                    registry=self.registry,
                    **kwargs
                )
        return self._metrics[metric_name]
        
    def histogram(
        self,
        name: str,
        description: str,
        labelnames: Optional[List[str]] = None,
        **kwargs
    ) -> Histogram:
        """Create or get a Histogram metric"""
        metric_name = self._create_metric_name(name)
        with self._lock:
            if metric_name not in self._metrics:
                self._metrics[metric_name] = Histogram(
                    metric_name,
                    description,
                    labelnames=labelnames or [],
                    registry=self.registry,
                    **kwargs
                )
        return self._metrics[metric_name]
        
    def summary(
        self,
        name: str,
        description: str,
        labelnames: Optional[List[str]] = None,
        **kwargs
    ) -> Summary:
        """Create or get a Summary metric"""
        metric_name = self._create_metric_name(name)
        with self._lock:
            if metric_name not in self._metrics:
                self._metrics[metric_name] = Summary(
                    metric_name,
                    description,
                    labelnames=labelnames or [],
                    registry=self.registry,
                    **kwargs
                )
        return self._metrics[metric_name]
        
    def record_operation(
        self,
        operation: str,
        status: str,
        labels: Optional[Dict[str, str]] = None
    ):
        """Record an operation metric"""
        metric_labels = {**self.common_labels, **(labels or {})}
        self.counter(
            'operations_total',
            'Total operations'
        ).labels(
            operation=operation,
            status=status,
            **metric_labels
        ).inc()
        
    def record_error(
        self,
        error_type: str,
        labels: Optional[Dict[str, str]] = None
    ):
        """Record an error metric"""
        metric_labels = {**self.common_labels, **(labels or {})}
        self.counter(
            'errors_total',
            'Total errors'
        ).labels(
            type=error_type,
            **metric_labels
        ).inc()
        
    def set_gauge(
        self,
        name: str,
        value: float,
        labels: Optional[Dict[str, str]] = None
    ):
        """Set a gauge value"""
        metric_labels = {**self.common_labels, **(labels or {})}
        self.gauge(
            name,
            f'Gauge metric {name}'
        ).labels(**metric_labels).set(value)
        
    @contextmanager
    def operation_duration(
        self,
        operation: str,
        labels: Optional[Dict[str, str]] = None
    ):
        """Context manager to measure operation duration"""
        start_time = time.time()
        try:
            yield
        finally:
            duration = time.time() - start_time
            metric_labels = {**self.common_labels, **(labels or {})}
            self.histogram(
                'operation_duration_seconds',
                'Operation duration'
            ).labels(
                operation=operation,
                **metric_labels
            ).observe(duration)

# Global metrics collectors registry
_collectors: Dict[str, MetricsCollector] = {}
_collectors_lock = threading.Lock()

def get_metrics_collector(namespace: str) -> MetricsCollector:
    """Get or create a metrics collector for a namespace"""
    with _collectors_lock:
        if namespace not in _collectors:
            _collectors[namespace] = MetricsCollector(namespace)
        return _collectors[namespace]

# Example usage
if __name__ == "__main__":
    # Get metrics collector
    metrics = get_metrics_collector("example")
    
    # Record operation with duration
    with metrics.operation_duration("test_operation"):
        time.sleep(0.1)  # Simulate work
        
    # Record success and error
    metrics.record_operation("test", "success")
    metrics.record_error("test_error")
    
    # Set gauge value
    metrics.set_gauge("test_gauge", 42.0)