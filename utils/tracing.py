"""
Distributed tracing utilities for the agent system
"""

from typing import Dict, Any, Optional, Iterator, ContextManager
import contextvars
from contextlib import contextmanager
import threading
from opentelemetry import trace
from opentelemetry.sdk.trace import TracerProvider
from opentelemetry.sdk.trace.export import (
    BatchSpanProcessor,
    ConsoleSpanExporter
)
from opentelemetry.sdk.trace.sampling import ParentBasedTraceIdRatio
from opentelemetry.exporter.otlp.proto.grpc.trace_exporter import OTLPSpanExporter
from opentelemetry.sdk.resources import Resource
from opentelemetry.trace.propagation.tracecontext import TraceContextTextMapPropagator
from opentelemetry.context.context import Context
import os
import time

class AgentTracer:
    """Manages distributed tracing for agent system components"""
    
    def __init__(self, service_name: str):
        self.service_name = service_name
        self._setup_tracer()
        self._context = contextvars.ContextVar('trace_context', default={})
        self._propagator = TraceContextTextMapPropagator()
        
    def _setup_tracer(self):
        """Initialize tracer with configuration"""
        # Create resource
        resource = Resource.create({
            "service.name": self.service_name,
            "service.namespace": "agent-system",
            "environment": os.getenv("ENVIRONMENT", "development"),
            "version": os.getenv("VERSION", "unknown")
        })
        
        # Configure trace provider
        provider = TracerProvider(
            resource=resource,
            sampler=ParentBasedTraceIdRatio(0.1)  # Sample 10% of traces
        )
        
        # Add exporters
        if os.getenv("OTEL_EXPORTER_OTLP_ENDPOINT"):
            # OTLP exporter for production
            otlp_exporter = OTLPSpanExporter(
                endpoint=os.getenv("OTEL_EXPORTER_OTLP_ENDPOINT")
            )
            provider.add_span_processor(
                BatchSpanProcessor(otlp_exporter)
            )
        else:
            # Console exporter for development
            console_exporter = ConsoleSpanExporter()
            provider.add_span_processor(
                BatchSpanProcessor(console_exporter)
            )
            
        # Set global trace provider
        trace.set_tracer_provider(provider)
        
        # Get tracer
        self.tracer = trace.get_tracer(self.service_name)
        
    @contextmanager
    def start_span(
        self,
        name: str,
        attributes: Optional[Dict[str, Any]] = None,
        kind: Optional[trace.SpanKind] = None
    ) -> Iterator[trace.Span]:
        """Start a new span"""
        attributes = attributes or {}
        
        # Add common attributes
        attributes.update({
            "service.name": self.service_name,
            "environment": os.getenv("ENVIRONMENT", "development")
        })
        
        with self.tracer.start_as_current_span(
            name,
            attributes=attributes,
            kind=kind or trace.SpanKind.INTERNAL
        ) as span:
            yield span
            
    @contextmanager
    def operation_span(
        self,
        operation: str,
        attributes: Optional[Dict[str, Any]] = None
    ) -> Iterator[trace.Span]:
        """Create span for operation with timing"""
        start_time = time.time()
        attributes = attributes or {}
        
        with self.start_span(
            f"{self.service_name}.{operation}",
            attributes=attributes
        ) as span:
            try:
                yield span
            finally:
                duration = time.time() - start_time
                span.set_attribute("operation.duration", duration)
                
    def inject_context(self, carrier: Dict[str, str]):
        """Inject trace context into carrier"""
        context = trace.get_current_span().get_span_context()
        self._propagator.inject(carrier)
        
    def extract_context(self, carrier: Dict[str, str]) -> Context:
        """Extract trace context from carrier"""
        return self._propagator.extract(carrier=carrier)
        
    def get_current_trace_id(self) -> Optional[str]:
        """Get current trace ID if available"""
        span_context = trace.get_current_span().get_span_context()
        if span_context and span_context.is_valid:
            return format(span_context.trace_id, '032x')
        return None
        
    def get_current_span_id(self) -> Optional[str]:
        """Get current span ID if available"""
        span_context = trace.get_current_span().get_span_context()
        if span_context and span_context.is_valid:
            return format(span_context.span_id, '016x')
        return None
        
    def record_exception(
        self,
        exception: Exception,
        attributes: Optional[Dict[str, Any]] = None
    ):
        """Record exception in current span"""
        span = trace.get_current_span()
        if span:
            span.record_exception(
                exception,
                attributes=attributes
            )
            
    @contextmanager
    def traced_operation(
        self,
        operation: str,
        attributes: Optional[Dict[str, Any]] = None
    ) -> ContextManager:
        """Context manager for traced operations"""
        with self.operation_span(operation, attributes) as span:
            try:
                yield span
            except Exception as e:
                self.record_exception(e)
                raise

# Global tracer registry
_tracers: Dict[str, AgentTracer] = {}
_tracers_lock = threading.Lock()

def get_tracer(service_name: str) -> AgentTracer:
    """Get or create a tracer for a service"""
    with _tracers_lock:
        if service_name not in _tracers:
            _tracers[service_name] = AgentTracer(service_name)
        return _tracers[service_name]

# Example usage
if __name__ == "__main__":
    # Get tracer
    tracer = get_tracer("example_service")
    
    # Trace an operation
    with tracer.traced_operation(
        "test_operation",
        {"test_attribute": "value"}
    ) as span:
        # Do some work
        time.sleep(0.1)
        
        # Add event
        span.add_event("operation_step", {"step": 1})
        
        try:
            # Simulate error
            raise ValueError("Test error")
        except Exception as e:
            tracer.record_exception(e)
            raise