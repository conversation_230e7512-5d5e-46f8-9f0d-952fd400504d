"""
Logging setup and utilities for the agent system
"""

import logging
import logging.config
import yaml
import os
import sys
from pathlib import Path
from typing import Dict, Any, Optional
from datetime import datetime
import threading
from contextlib import contextmanager
import uuid
from pythonjsonlogger import jsonlogger
from opentelemetry import trace, context

class CorrelationIdFilter(logging.Filter):
    """Filter that injects correlation ID into log records"""
    
    def __init__(self):
        super().__init__()
        self._local = threading.local()
        
    @property
    def correlation_id(self) -> str:
        """Get current correlation ID or generate new one"""
        if not hasattr(self._local, 'correlation_id'):
            self._local.correlation_id = str(uuid.uuid4())
        return self._local.correlation_id
        
    @correlation_id.setter
    def correlation_id(self, value: str):
        """Set correlation ID for current thread"""
        self._local.correlation_id = value
        
    def filter(self, record: logging.LogRecord) -> bool:
        """Add correlation ID to log record"""
        record.correlation_id = self.correlation_id
        return True

class TraceContextFilter(logging.Filter):
    """Filter that injects OpenTelemetry trace context into log records"""
    
    def filter(self, record: logging.LogRecord) -> bool:
        """Add trace context to log record"""
        span_context = trace.get_current_span().get_span_context()
        if span_context and span_context.is_valid:
            record.trace_id = format(span_context.trace_id, '032x')
            record.span_id = format(span_context.span_id, '016x')
        else:
            record.trace_id = None
            record.span_id = None
        return True

class CustomJsonFormatter(jsonlogger.JsonFormatter):
    """Custom JSON formatter with additional fields"""
    
    def add_fields(
        self,
        log_record: Dict,
        record: logging.LogRecord,
        message_dict: Dict
    ) -> None:
        """Add custom fields to log record"""
        super().add_fields(log_record, record, message_dict)
        
        # Add timestamp if not present
        if not log_record.get('timestamp'):
            log_record['timestamp'] = datetime.utcnow().isoformat()
            
        # Add log level
        if log_record.get('level'):
            log_record['level'] = log_record['level'].upper()
        else:
            log_record['level'] = record.levelname
            
        # Add correlation ID and trace context
        log_record['correlation_id'] = getattr(record, 'correlation_id', None)
        log_record['trace_id'] = getattr(record, 'trace_id', None)
        log_record['span_id'] = getattr(record, 'span_id', None)
        
        # Add environment info
        log_record['environment'] = os.getenv('ENVIRONMENT', 'development')
        log_record['service'] = os.getenv('SERVICE_NAME', 'agent-system')
        log_record['version'] = os.getenv('VERSION', 'unknown')

def enrich_log_record(
    record: logging.LogRecord,
    handler: logging.Handler
) -> None:
    """Enrich log record with additional context"""
    # Add thread info
    record.threadName = threading.current_thread().name
    record.thread = threading.get_ident()
    
    # Add process info
    record.processName = record.processName or 'MainProcess'
    
    # Add runtime info
    try:
        frame = sys._getframe(6)
        record.funcName = frame.f_code.co_name
        record.pathname = frame.f_code.co_filename
        record.lineno = frame.f_lineno
    except (AttributeError, ValueError):
        record.funcName = 'unknown'
        record.pathname = 'unknown'
        record.lineno = 0

class LogManager:
    """Manages logging configuration and context"""
    
    def __init__(self):
        self._correlation_filter = CorrelationIdFilter()
        self._trace_filter = TraceContextFilter()
        self.initialized = False
        
    def setup_logging(
        self,
        config_path: Optional[str] = None,
        default_level: int = logging.INFO
    ) -> None:
        """Initialize logging configuration"""
        if self.initialized:
            return
            
        try:
            if config_path and Path(config_path).exists():
                with open(config_path, 'rt') as f:
                    config = yaml.safe_load(f)
                logging.config.dictConfig(config)
            else:
                # Basic configuration if no config file
                logging.basicConfig(
                    level=default_level,
                    format='%(asctime)s [%(levelname)s] %(name)s: %(message)s',
                    handlers=[
                        logging.StreamHandler(sys.stdout)
                    ]
                )
                
            self.initialized = True
            logging.info("Logging system initialized")
            
        except Exception as e:
            print(f"Error setting up logging: {str(e)}")
            logging.basicConfig(level=default_level)
            
    def get_logger(self, name: str) -> logging.Logger:
        """Get a logger instance"""
        if not self.initialized:
            self.setup_logging()
        return logging.getLogger(name)
        
    @contextmanager
    def correlation_id_context(self, correlation_id: str):
        """Context manager for setting correlation ID"""
        previous_id = self._correlation_filter.correlation_id
        try:
            self._correlation_filter.correlation_id = correlation_id
            yield
        finally:
            self._correlation_filter.correlation_id = previous_id

# Global log manager instance
_log_manager: Optional[LogManager] = None

def get_log_manager() -> LogManager:
    """Get global log manager instance"""
    global _log_manager
    if _log_manager is None:
        _log_manager = LogManager()
    return _log_manager

def get_logger(name: str) -> logging.Logger:
    """Convenience function to get a logger"""
    return get_log_manager().get_logger(name)

# Example usage
if __name__ == "__main__":
    # Initialize logging
    log_manager = get_log_manager()
    log_manager.setup_logging("config/logging/logging.yaml")
    
    # Get logger
    logger = get_logger("example")
    
    # Log with correlation ID
    with log_manager.correlation_id_context("test-correlation-id"):
        logger.info("Test log message with correlation ID")
        
        # Nested context
        with log_manager.correlation_id_context("nested-correlation-id"):
            logger.info("Test log message with nested correlation ID")
            
    # Log without correlation ID
    logger.info("Test log message without correlation ID")