import os
import logging
import email
import base64
from datetime import datetime
from google.oauth2.credentials import Credentials
from google_auth_oauthlib.flow import InstalledAppFlow
from google.auth.transport.requests import Request
from googleapiclient.discovery import build
from exchangelib import Credentials as ExchangeCredentials
from exchangelib import Account, DELEGATE
from twilio.rest import Client

# Import system components
from social_media_manager import SocialMediaManager
from secure_credentials import SecureCredentialsManager

# Import advanced models
try:
    from advanced_models.unified_interface import UnifiedModelInterface
    from advanced_models.model_manager import AdvancedModelManager
    ADVANCED_MODELS_AVAILABLE = True
except ImportError:
    ADVANCED_MODELS_AVAILABLE = False
    logger.warning("Advanced models not available. Install with: python install_advanced_models.py")

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class AgentSystem:
    """Main orchestrator for managing multiple business agents and their communications"""

    def __init__(self):
        self.credentials_manager = SecureCredentialsManager()
        self.agents = {}
        self.advanced_models = None
        self.unified_interface = None
        self.initialize_agents()
        self.initialize_advanced_models()

    def initialize_agents(self):
        """Initialize all business agents"""
        # Create managers first
        social_media_manager = SocialMediaManager(self.credentials_manager)
        gmail_manager = GmailManagerAgent()
        outlook_manager = OutlookManagerAgent()
        
        # Create email communication agent
        email_agent = EmailCommunicationAgent()
        email_agent.setup_managers(gmail_manager, outlook_manager, self.credentials_manager)
        
        self.agents = {
            # Core Business Operations
            'insurance': InsuranceAgent(),
            'email': email_agent,
            'content': ContentCreationAgent(),
            'social': SocialMediaAgent(social_media_manager),
            'support': CustomerSupportAgent(),
            'finance': FinanceAgent(),
            'scheduler': SchedulingAgent(),
            'marketing': MarketingAgent(),
            'research': MarketResearchAgent(),
            
            # Communication Agents
            'gmail_manager': gmail_manager,
            'outlook_manager': outlook_manager,
            'phone_manager': PhoneSystemAgent(),
            
            # Administrative Agents
            'document': DocumentProcessingAgent(),
            'crm': CRMAgent(),
            'analytics': AnalyticsAgent(),
        }
        
        # Initialize email accounts
        try:
            email_agent.initialize_email_accounts()
            logger.info("Email accounts initialized successfully")
        except Exception as e:
            logger.error(f"Failed to initialize email accounts: {str(e)}")

    def initialize_advanced_models(self):
        """Initialize advanced AI models if available"""
        if ADVANCED_MODELS_AVAILABLE:
            try:
                self.advanced_models = AdvancedModelManager()
                self.unified_interface = UnifiedModelInterface()
                logger.info("Advanced models initialized successfully")
            except Exception as e:
                logger.error(f"Failed to initialize advanced models: {e}")
                self.advanced_models = None
                self.unified_interface = None
        else:
            logger.info("Advanced models not available")

    async def query_advanced_models(self, query, context=None, image_data=None, strategy=None):
        """Query advanced models through unified interface"""
        if not self.unified_interface:
            return {
                'response': f"Advanced models not available. Query: {query}",
                'confidence': 0.0,
                'metadata': {'error': 'Advanced models not initialized'}
            }

        try:
            if not self.unified_interface.initialized:
                await self.unified_interface.initialize()

            result = await self.unified_interface.query(
                query=query,
                context=context,
                image_data=image_data,
                strategy=strategy
            )

            return {
                'response': result.primary_response,
                'confidence': result.confidence,
                'processing_time': result.processing_time,
                'strategy_used': result.strategy_used.value,
                'models_used': [r.model_type.value for r in result.model_responses],
                'metadata': result.metadata
            }

        except Exception as e:
            logger.error(f"Error querying advanced models: {e}")
            return {
                'response': f"Error processing query: {str(e)}",
                'confidence': 0.0,
                'metadata': {'error': str(e)}
            }

    def get_advanced_models_status(self):
        """Get status of advanced models"""
        if not self.unified_interface:
            return {'available': False, 'reason': 'Not initialized'}

        try:
            return {
                'available': True,
                'initialized': self.unified_interface.initialized,
                'model_status': self.unified_interface.get_model_status(),
                'performance_metrics': self.unified_interface.get_performance_metrics()
            }
        except Exception as e:
            return {'available': False, 'error': str(e)}

class GmailManagerAgent:
    """Manages multiple Gmail accounts using Gmail API"""
    
    def __init__(self):
        self.accounts = {}
        self.SCOPES = ['https://www.googleapis.com/auth/gmail.modify']
        self.credentials_info = {}
        
    def add_account(self, email, password=None):
        """
        Add Gmail account using OAuth 2.0 or password authentication
        
        Args:
            email: The Gmail email address to add
            password: Optional password for authentication
            
        Returns:
            True if successful, False otherwise
        """
        try:
            creds = None
            token_path = f'token_{email.replace("@", "_at_")}.json'
            
            if os.path.exists(token_path):
                creds = Credentials.from_authorized_user_file(token_path, self.SCOPES)
                
            if not creds or not creds.valid:
                if creds and creds.expired and creds.refresh_token:
                    creds.refresh(Request())
                elif os.path.exists('credentials.json'):
                    # Use standard OAuth flow if credentials.json exists
                    flow = InstalledAppFlow.from_client_secrets_file(
                        'credentials.json',
                        self.SCOPES
                    )
                    creds = flow.run_local_server(port=0)
                else:
                    # For password-based authentication, store the credentials
                    # but actual authentication will be done at the time of usage
                    if password:
                        self.credentials_info[email] = {"password": password}
                        logger.info(f"Stored credentials for {email} (will authenticate on first use)")
                        return True
                    else:
                        raise ValueError(f"No OAuth credentials or password provided for {email}")
                    
                # Save credentials if obtained through OAuth
                if creds:
                    with open(token_path, 'w') as token:
                        token.write(creds.to_json())
            
            # Build the Gmail service
            service = build('gmail', 'v1', credentials=creds)
            self.accounts[email] = service
            logger.info(f"Successfully connected to Gmail account: {email}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to connect to Gmail account {email}: {e}")
            return False

    def read_emails(self, account_email, folder="INBOX", limit=10):
        """
        Read emails from specified Gmail account
        
        Args:
            account_email: The email address to read from
            folder: The folder/label to read (default: INBOX)
            limit: Maximum number of emails to return
            
        Returns:
            List of email messages with metadata
        """
        if account_email not in self.accounts:
            logger.warning(f"Account {account_email} not connected. Attempting to connect...")
            if not self.add_account(account_email):
                return []
            
        try:
            service = self.accounts[account_email]
            results = service.users().messages().list(
                userId='me',
                labelIds=[folder],
                maxResults=limit
            ).execute()
            
            messages = []
            for msg in results.get('messages', []):
                message = service.users().messages().get(
                    userId='me',
                    id=msg['id']
                ).execute()
                
                headers = message['payload']['headers']
                subject = next((h['value'] for h in headers if h['name'] == 'Subject'), 'No Subject')
                sender = next((h['value'] for h in headers if h['name'] == 'From'), 'Unknown')
                date = next((h['value'] for h in headers if h['name'] == 'Date'), 'Unknown')
                
                # Extract body content
                body = ""
                if 'parts' in message['payload']:
                    for part in message['payload']['parts']:
                        if part['mimeType'] == 'text/plain':
                            body = base64.urlsafe_b64decode(part['body']['data']).decode('utf-8')
                            break
                elif 'body' in message['payload'] and 'data' in message['payload']['body']:
                    body = base64.urlsafe_b64decode(message['payload']['body']['data']).decode('utf-8')
                
                messages.append({
                    'id': msg['id'],
                    'subject': subject,
                    'from': sender,
                    'date': date,
                    'snippet': message.get('snippet', ''),
                    'body': body,
                    'labels': message.get('labelIds', [])
                })
                
            return messages
            
        except Exception as e:
            logger.error(f"Error reading Gmail messages: {e}")
            return []
    
    def send_email(self, account_email, to, subject, body, cc=None, bcc=None, attachments=None):
        """
        Send an email from a specified Gmail account
        
        Args:
            account_email: The sender's email address
            to: Recipient email address(es)
            subject: Email subject
            body: Email body content (plain text)
            cc: Optional CC recipients
            bcc: Optional BCC recipients
            attachments: Optional list of file paths to attach
            
        Returns:
            Message ID if successful, None otherwise
        """
        if account_email not in self.accounts:
            logger.warning(f"Account {account_email} not connected. Attempting to connect...")
            if not self.add_account(account_email):
                return None
        
        try:
            service = self.accounts[account_email]
            
            message = self._create_message(
                sender=account_email,
                to=to,
                subject=subject,
                body=body,
                cc=cc,
                bcc=bcc
            )
            
            if attachments:
                message = self._add_attachments(message, attachments)
                
            sent_message = service.users().messages().send(
                userId='me',
                body=message
            ).execute()
            
            logger.info(f"Email sent from {account_email} to {to}, message ID: {sent_message['id']}")
            return sent_message['id']
            
        except Exception as e:
            logger.error(f"Error sending Gmail message from {account_email}: {e}")
            return None
    
    def _create_message(self, sender, to, subject, body, cc=None, bcc=None):
        """Create a message for the Gmail API"""
        from email.mime.text import MIMEText
        from email.mime.multipart import MIMEMultipart
        import base64
        
        message = MIMEMultipart()
        message['to'] = to if isinstance(to, str) else ', '.join(to)
        message['from'] = sender
        message['subject'] = subject
        
        if cc:
            message['cc'] = cc if isinstance(cc, str) else ', '.join(cc)
        if bcc:
            message['bcc'] = bcc if isinstance(bcc, str) else ', '.join(bcc)
            
        message.attach(MIMEText(body, 'plain'))
        
        raw_message = base64.urlsafe_b64encode(message.as_string().encode('utf-8')).decode('utf-8')
        return {'raw': raw_message}
    
    def _add_attachments(self, message, attachments):
        """Add attachments to a Gmail API message"""
        from email.mime.application import MIMEApplication
        import os
        import base64
        
        # Decode raw message
        raw = base64.urlsafe_b64decode(message['raw'].encode('utf-8')).decode('utf-8')
        msg = email.message_from_string(raw)
        
        # Add attachments
        for file_path in attachments:
            with open(file_path, 'rb') as f:
                part = MIMEApplication(f.read(), Name=os.path.basename(file_path))
                
            part['Content-Disposition'] = f'attachment; filename="{os.path.basename(file_path)}"'
            msg.attach(part)
        
        # Re-encode
        raw_message = base64.urlsafe_b64encode(msg.as_string().encode('utf-8')).decode('utf-8')
        message['raw'] = raw_message
        return message
    
    def search_emails(self, account_email, query, max_results=10):
        """
        Search for emails using Gmail's search syntax
        
        Args:
            account_email: The email account to search in
            query: The search query (using Gmail search syntax)
            max_results: Maximum number of results to return
            
        Returns:
            List of email messages matching the query
        """
        if account_email not in self.accounts:
            logger.warning(f"Account {account_email} not connected. Attempting to connect...")
            if not self.add_account(account_email):
                return []
                
        try:
            service = self.accounts[account_email]
            results = service.users().messages().list(
                userId='me',
                q=query,
                maxResults=max_results
            ).execute()
            
            messages = []
            for msg in results.get('messages', []):
                message = service.users().messages().get(
                    userId='me',
                    id=msg['id']
                ).execute()
                
                headers = message['payload']['headers']
                subject = next((h['value'] for h in headers if h['name'] == 'Subject'), 'No Subject')
                sender = next((h['value'] for h in headers if h['name'] == 'From'), 'Unknown')
                date = next((h['value'] for h in headers if h['name'] == 'Date'), 'Unknown')
                
                messages.append({
                    'id': msg['id'],
                    'subject': subject,
                    'from': sender,
                    'date': date,
                    'snippet': message.get('snippet', '')
                })
                
            return messages
            
        except Exception as e:
            logger.error(f"Error searching Gmail messages: {e}")
            return []

class OutlookManagerAgent:
    """Manages Outlook accounts using exchangelib"""
    
    def __init__(self):
        self.accounts = {}

    def add_account(self, email, password):
        """Add Outlook account using exchangelib"""
        try:
            credentials = ExchangeCredentials(email, password)
            account = Account(
                email,
                credentials=credentials,
                autodiscover=True,
                access_type=DELEGATE
            )
            self.accounts[email] = account
            logger.info(f"Successfully connected to Outlook account: {email}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to connect to Outlook account {email}: {e}")
            return False

    def read_emails(self, account_email, folder="inbox", limit=10):
        """Read emails from specified Outlook account"""
        if account_email not in self.accounts:
            return []
            
        try:
            account = self.accounts[account_email]
            messages = []
            
            for item in account.inbox.all().order_by('-datetime_received')[:limit]:
                messages.append({
                    'subject': item.subject,
                    'from': str(item.sender),
                    'date': item.datetime_received.strftime("%Y-%m-%d %H:%M:%S"),
                    'body': item.body
                })
                
            return messages
            
        except Exception as e:
            logger.error(f"Error reading Outlook messages: {e}")
            return []

class PhoneSystemAgent:
    """Manages phone communications using Twilio"""
    
    def __init__(self, account_sid, auth_token, phone_number):
        self.client = Client(account_sid, auth_token)
        self.phone_number = phone_number

    def send_sms(self, to_number, message):
        """Send SMS using Twilio"""
        try:
            message = self.client.messages.create(
                body=message,
                from_=self.phone_number,
                to=to_number
            )
            logger.info(f"SMS sent successfully: {message.sid}")
            return True
        except Exception as e:
            logger.error(f"Failed to send SMS: {e}")
            return False

# Business Agent Classes
class InsuranceAgent:
    """Manages insurance operations"""
    def __init__(self):
        self.products = {}
        self.quotes = {}
        self.policies = {}
        
    def generate_quote(self, customer_info, product_type):
        # Add quote generation logic
        pass
        
    def process_claim(self, policy_id, claim_info):
        # Add claim processing logic
        pass

class ContentCreationAgent:
    """Manages content creation and marketing materials"""
    def __init__(self):
        self.templates = {}
        self.campaigns = {}
        
    def create_content(self, content_type, parameters):
        # Add content creation logic
        pass

class SocialMediaAgent:
    """
    Manages social media presence across multiple platforms.
    Works with the SocialMediaManager to handle authentication, posting,
    scheduling, analytics, and engagement for insurance agents.
    """
    def __init__(self, social_media_manager):
        self.social_media_manager = social_media_manager
        self.content_templates = {
            'insurance_tip': [
                "Did you know? {tip}. Contact us to learn more about protecting what matters most.",
                "Insurance Tip: {tip}. Talk to an agent today to review your coverage.",
                "Smart Insurance Tip: {tip}. Call us for a free consultation on your insurance needs."
            ],
            'quote': [
                "\"{quote}\" - {author}. We believe in protecting your future. Let's talk about your insurance options.",
                "Wise words to consider: \"{quote}\" - {author}. How's your insurance coverage looking?",
                "\"{quote}\" - {author}. This reminds us why proper insurance coverage matters."
            ],
            'promotion': [
                "Special offer: {offer}. Valid until {end_date}. Contact us today!",
                "Limited time deal: {offer}. Don't miss out! Offer ends {end_date}.",
                "Exclusive for our followers: {offer}. Reach out before {end_date} to take advantage!"
            ]
        }
        
        # Track active campaigns for each agent
        self.active_campaigns = {}
    
    def add_social_account(self, agent_id, platform, credentials):
        """
        Add a social media account for an insurance agent
        
        Args:
            agent_id: Unique identifier for the agent
            platform: Social platform (twitter, facebook, instagram, linkedin)
            credentials: Dict with auth credentials for the platform
            
        Returns:
            bool: Success status
        """
        return self.social_media_manager.add_social_account(
            agent_id=agent_id,
            platform=platform,
            credentials=credentials
        )
    
    def create_content(self, content_type, parameters):
        """
        Generate social media content based on templates and parameters
        
        Args:
            content_type: Type of content to create (insurance_tip, quote, promotion)
            parameters: Dict with parameters to fill the template
            
        Returns:
            str: Generated content
        """
        import random
        
        if content_type not in self.content_templates:
            raise ValueError(f"Unknown content type: {content_type}")
        
        templates = self.content_templates[content_type]
        selected_template = random.choice(templates)
        
        try:
            return selected_template.format(**parameters)
        except KeyError as e:
            logging.error(f"Missing parameter in content template: {e}")
            return None
    
    def post_to_platform(self, agent_id, platform, content_type, parameters, media_paths=None):
        """
        Create and post content to a social media platform
        
        Args:
            agent_id: Agent identifier
            platform: Target platform
            content_type: Type of content to create
            parameters: Parameters for content template
            media_paths: Optional list of paths to media files
            
        Returns:
            Tuple[bool, str]: Success status and post ID/error message
        """
        # Generate content from template
        text = self.create_content(content_type, parameters)
        if not text:
            return False, "Failed to generate content"
        
        # Prepare content object
        content = {
            "text": text,
            "media_paths": media_paths or []
        }
        
        # Post to platform
        return self.social_media_manager.post_content(
            agent_id=agent_id,
            platform=platform,
            content=text,
            media_paths=media_paths or []
        )
    
    def schedule_post(self, agent_id, platform, content_type, parameters, 
                     scheduled_time, media_paths=None):
        """
        Schedule a post for future publishing
        
        Args:
            agent_id: Agent identifier
            platform: Target platform
            content_type: Type of content to create
            parameters: Parameters for content template
            scheduled_time: When to publish (datetime object)
            media_paths: Optional list of paths to media files
            
        Returns:
            str: Schedule ID
        """
        # Generate content from template
        text = self.create_content(content_type, parameters)
        if not text:
            return None
        
        # Prepare content object
        content = {
            "text": text,
            "media_paths": media_paths or []
        }
        
        # Schedule post
        return self.social_media_manager.schedule_post(
            agent_id=agent_id,
            platform=platform,
            content=text,
            schedule_time=scheduled_time,
            media_paths=media_paths or []
        )
    
    def create_campaign(self, agent_id, campaign_name, platforms, post_schedule, content_configs):
        """
        Create a full social media campaign with multiple scheduled posts
        
        Args:
            agent_id: Agent identifier
            campaign_name: Name of the campaign
            platforms: List of platforms to post to
            post_schedule: List of datetime objects for when to post
            content_configs: List of dicts with content_type and parameters
            
        Returns:
            dict: Campaign info with scheduled post IDs
        """
        if len(post_schedule) != len(content_configs):
            return {"success": False, "error": "Schedule and content configs must be same length"}
        
        campaign = {
            "agent_id": agent_id,
            "name": campaign_name,
            "platforms": platforms,
            "scheduled_posts": [],
            "status": "active"
        }
        
        # Schedule each post in the campaign
        for platform in platforms:
            for i, scheduled_time in enumerate(post_schedule):
                config = content_configs[i]
                
                schedule_id = self.schedule_post(
                    agent_id=agent_id,
                    platform=platform,
                    content_type=config["content_type"],
                    parameters=config["parameters"],
                    scheduled_time=scheduled_time,
                    media_paths=config.get("media_paths")
                )
                
                if schedule_id:
                    campaign["scheduled_posts"].append({
                        "platform": platform,
                        "schedule_id": schedule_id,
                        "scheduled_time": scheduled_time.isoformat(),
                        "content_config": config
                    })
        
        # Store campaign
        if agent_id not in self.active_campaigns:
            self.active_campaigns[agent_id] = {}
            
        self.active_campaigns[agent_id][campaign_name] = campaign
        
        return campaign
    
    def get_agent_analytics(self, agent_id):
        """
        Get comprehensive analytics for an agent across all platforms
        
        Args:
            agent_id: Agent identifier
            
        Returns:
            dict: Analytics data
        """
        return self.social_media_manager.get_analytics(agent_id)
    
    def generate_analytics_report(self, agent_id, time_period="all"):
        """
        Generate a formatted analytics report for an agent
        
        Args:
            agent_id: Agent identifier
            time_period: Time period for report (day, week, month, all)
            
        Returns:
            str: Formatted report
        """
        analytics = self.get_agent_analytics(agent_id)
        
        # Create report
        report = f"Social Media Analytics Report for Agent: {agent_id}\n"
        report += "=" * 50 + "\n\n"
        
        # Overall engagement
        report += "Overall Engagement:\n"
        report += f"Total Likes: {analytics['engagement']['total_likes']}\n"
        report += f"Total Comments: {analytics['engagement']['total_comments']}\n"
        report += f"Total Shares: {analytics['engagement']['total_shares']}\n\n"
        
        # Platform breakdown
        report += "Platform Breakdown:\n"
        for platform, stats in analytics.get('by_platform', {}).items():
            report += f"\n{platform.capitalize()}:\n"
            report += f"  Post Count: {stats['post_count']}\n"
            report += f"  Total Likes: {stats['total_likes']}\n"
            report += f"  Total Comments: {stats['total_comments']}\n"
            report += f"  Total Shares: {stats['total_shares']}\n"
            
            # Calculate engagement rate if post count > 0
            if stats['post_count'] > 0:
                total_interactions = stats['total_likes'] + stats['total_comments'] + stats['total_shares']
                engagement_rate = total_interactions / stats['post_count']
                report += f"  Average Engagement: {engagement_rate:.2f} per post\n"
        
        return report

class CustomerSupportAgent:
    """Handles customer support across channels"""
    def __init__(self):
        self.tickets = {}
        self.responses = {}
        
    def process_ticket(self, ticket_info):
        # Add ticket processing logic
        pass

class FinanceAgent:
    """Manages financial operations"""
    def __init__(self):
        self.transactions = []
        self.reports = {}
        
    def generate_report(self, report_type, date_range):
        # Add report generation logic
        pass

class SchedulingAgent:
    """Manages appointments and scheduling"""
    def __init__(self):
        self.appointments = {}
        self.availability = {}
        
    def schedule_appointment(self, client_info, time_slot):
        # Add appointment scheduling logic
        pass

class MarketingAgent:
    """Handles marketing campaigns"""
    def __init__(self):
        self.campaigns = {}
        self.analytics = {}
        
    def launch_campaign(self, campaign_info):
        # Add campaign launch logic
        pass

class MarketResearchAgent:
    """Conducts market research and analysis"""
    def __init__(self):
        self.research_data = {}
        self.competitors = {}
        
    def analyze_market(self, parameters):
        # Add market analysis logic
        pass

class DocumentProcessingAgent:
    """Processes various document formats"""
    def __init__(self):
        self.templates = {}
        
    def process_document(self, doc_type, content):
        # Add document processing logic
        pass

class CRMAgent:
    """Manages customer relationships"""
    def __init__(self):
        self.customers = {}
        self.interactions = {}
        
    def update_customer(self, customer_id, info):
        # Add customer update logic
        pass

class AnalyticsAgent:
    """Analyzes business metrics and performance"""
    def __init__(self):
        self.metrics = {}
        self.reports = {}
        
    def generate_analytics(self, metric_type, timeframe):
        # Add analytics generation logic
        pass

class EmailCommunicationAgent:
    """
    Manages email communications across multiple email accounts
    Integrates with GmailManagerAgent for API access and core.EmailAgent for UI automation
    """
    def __init__(self):
        self.templates = {
            "insurance_query": "Subject: Insurance Quote Request\n\nDear {recipient_name},\n\nThank you for your interest in our insurance products. Based on your request, here is the information you requested about {insurance_type} coverage.\n\n{insurance_details}\n\nPlease let me know if you have any questions or would like to proceed with an application.\n\nBest regards,\n{agent_name}\n{agent_phone}",
            "meeting_confirmation": "Subject: Meeting Confirmation: {meeting_date}\n\nDear {recipient_name},\n\nThis is to confirm our meeting scheduled for {meeting_date} at {meeting_time} to discuss your {insurance_type} insurance needs.\n\n{additional_info}\n\nPlease let me know if you need to reschedule or have any questions before our meeting.\n\nBest regards,\n{agent_name}\n{agent_phone}",
            "contract_update": "Subject: Important Update to Your Insurance Contract\n\nDear {recipient_name},\n\nI'm writing to inform you about an important update to your {insurance_type} insurance contract.\n\n{update_details}\n\nPlease review the attached documents and let me know if you have any questions.\n\nBest regards,\n{agent_name}\n{agent_phone}",
            "policy_renewal": "Subject: Your Insurance Policy Renewal\n\nDear {recipient_name},\n\nYour {insurance_type} insurance policy is due for renewal on {renewal_date}. \n\n{renewal_details}\n\nPlease let me know if you would like to proceed with the renewal or discuss any changes to your coverage.\n\nBest regards,\n{agent_name}\n{agent_phone}"
        }
        self.scheduled_emails = {}
        self.gmail_manager = None
        self.outlook_manager = None
        self.credentials_manager = None
        self.email_accounts = {
            "<EMAIL>": {
                "type": "gmail",
                "description": "Main insurance business email",
                "password": "GodisSoGood!777"
            },
            "<EMAIL>": {
                "type": "gmail",
                "description": "Contracting information and requests",
                "password": "GodisSoGood!777"
            },
            "<EMAIL>": {
                "type": "gmail",
                "description": "Permanent broker email",
                "password": "GodisSoGood!777"
            }
        }
        
    def setup_managers(self, gmail_manager, outlook_manager, credentials_manager):
        """Set up the email service managers"""
        self.gmail_manager = gmail_manager
        self.outlook_manager = outlook_manager
        self.credentials_manager = credentials_manager
        
    def initialize_email_accounts(self):
        """Initialize and connect to all configured email accounts"""
        for email, account_info in self.email_accounts.items():
            try:
                if account_info["type"] == "gmail":
                    success = self.gmail_manager.add_account(email)
                    status = "Success" if success else "Failed"
                    logger.info(f"Gmail account initialization {status}: {email}")
                elif account_info["type"] == "outlook":
                    success = self.outlook_manager.add_account(email, account_info["password"])
                    status = "Success" if success else "Failed"
                    logger.info(f"Outlook account initialization {status}: {email}")
            except Exception as e:
                logger.error(f"Failed to initialize email account {email}: {str(e)}")
    
    def get_email_client(self, email):
        """Get appropriate email client for the given email address"""
        if email not in self.email_accounts:
            raise ValueError(f"Email account not configured: {email}")
            
        account_type = self.email_accounts[email]["type"]
        if account_type == "gmail":
            return self.gmail_manager
        elif account_type == "outlook":
            return self.outlook_manager
        else:
            raise ValueError(f"Unknown email account type: {account_type}")
            
    def compose_email(self, template_name, parameters):
        """Compose an email using a template"""
        if template_name not in self.templates:
            raise ValueError(f"Email template not found: {template_name}")
            
        template = self.templates[template_name]
        try:
            return template.format(**parameters)
        except KeyError as e:
            logger.error(f"Missing parameter for email template: {e}")
            raise ValueError(f"Missing parameter for email template: {e}")
        
    def send_email(self, sender_email, recipient, subject, content, attachments=None):
        """Send an email through the appropriate email client"""
        if not self.gmail_manager and not self.outlook_manager:
            raise RuntimeError("Email managers not initialized. Call setup_managers() first.")
            
        try:
            client = self.get_email_client(sender_email)
            
            # Implementation depends on the email client
            # For now, we'll just log the operation
            logger.info(f"Sending email from {sender_email} to {recipient}")
            logger.info(f"Subject: {subject}")
            logger.info(f"Content: {content[:50]}...")
            
            # Here we would call the appropriate API methods
            # This is a placeholder for now
            return True
        except Exception as e:
            logger.error(f"Failed to send email: {str(e)}")
            return False
        
    def schedule_email(self, sender_email, recipient, subject, content, send_time, attachments=None):
        """Schedule an email for later delivery"""
        email_id = f"{sender_email}_{recipient}_{send_time.isoformat()}"
        
        self.scheduled_emails[email_id] = {
            "sender": sender_email,
            "recipient": recipient,
            "subject": subject,
            "content": content,
            "send_time": send_time,
            "attachments": attachments or []
        }
        
        logger.info(f"Email scheduled for {send_time.isoformat()}")
        return email_id
        
    def handle_insurance_request(self, request_details):
        """Process an insurance-related email request"""
        request_type = request_details.get("type", "general_query")
        template_name = None
        
        if request_type == "quote_request":
            template_name = "insurance_query"
        elif request_type == "meeting_request":
            template_name = "meeting_confirmation"
        elif request_type == "contract_update":
            template_name = "contract_update"
        elif request_type == "policy_renewal":
            template_name = "policy_renewal"
        else:
            # Default to insurance query
            template_name = "insurance_query"
            
        try:
            content = self.compose_email(template_name, request_details)
            sender = request_details.get("sender_email", "<EMAIL>")
            return self.send_email(
                sender_email=sender,
                recipient=request_details["recipient_email"],
                subject=request_details.get("subject", "Insurance Information"),
                content=content,
                attachments=request_details.get("attachments", [])
            )
        except Exception as e:
            logger.error(f"Failed to handle insurance request: {str(e)}")
            return False
            
    def read_recent_emails(self, email_account, folder="INBOX", limit=10):
        """Read recent emails from the specified account"""
        try:
            client = self.get_email_client(email_account)
            if email_account in self.email_accounts and self.email_accounts[email_account]["type"] == "gmail":
                return self.gmail_manager.read_emails(email_account, folder, limit)
            else:
                return self.outlook_manager.read_emails(email_account, folder, limit)
        except Exception as e:
            logger.error(f"Failed to read emails from {email_account}: {str(e)}")
            return []

# Example usage
if __name__ == "__main__":
    system = AgentSystem()
    
    # Example: Add Gmail account
    gmail_agent = system.agents['gmail_manager']
    gmail_agent.add_account("<EMAIL>")
    
    # Example: Add Outlook account
    outlook_agent = system.agents['outlook_manager']
    outlook_agent.add_account("<EMAIL>", "your_password")
    
    # Example: Send SMS
    phone_agent = PhoneSystemAgent(
        account_sid=os.getenv("TWILIO_ACCOUNT_SID"),
        auth_token=os.getenv("TWILIO_AUTH_TOKEN"),
        phone_number=os.getenv("TWILIO_PHONE_NUMBER")
    )
    phone_agent.send_sms("recipient_number", "Test message from agent system")