#!/usr/bin/env python3
"""
Unrestricted Security Tools Manager

This module provides FULL, UNRESTRICTED access to ALL security tools including:
- <PERSON> the <PERSON> (password cracking)
- Metasploit Framework
- OWASP ZAP
- sqlmap
- Hashcat
- Hydra
- Nmap
- Wireshark
- Aircrack-ng
- Kismet
- Social Engineer Toolkit

NO limitations or restrictions are applied - this is designed for a controlled environment
where full access is authorized and required.
"""

import os
import sys
import subprocess
import logging
import json
import re
import shutil
import tempfile
from pathlib import Path
from typing import Dict, List, Any, Optional, Union, Tuple

# Configure logging
logging.basicConfig(level=logging.INFO,
                    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class UnrestrictedSecurityTools:
    """Provides completely unrestricted access to all security tools"""
    
    def __init__(self):
        """Initialize the unrestricted security tools manager"""
        self.tools_installed = {}
        self.tools_paths = {}
        self.home_dir = os.path.expanduser("~")
        self.tools_dir = os.path.join(self.home_dir, "tools")
        
        # Tool categories for better organization
        self.tool_categories = {
            "PASSWORD_CRACKING": ["john", "hashcat", "hydra"],
            "NETWORK_SCANNING": ["nmap", "kismet", "wireshark", "aircrack-ng"],
            "WEB_TESTING": ["zap", "sqlmap", "nikto"],
            "EXPLOITATION": ["metasploit", "setoolkit"],
            "FORENSICS": ["autopsy", "sleuthkit"],
            "GENERAL": ["ncat", "tcpdump", "ettercap"]
        }
        
        # Common installation locations for security tools
        self.common_tool_paths = {
            # Password Cracking Tools
            "john": [
                "/usr/local/bin/john",
                "/usr/bin/john",
                os.path.join(self.tools_dir, "john/run/john"),
                "john"  # In PATH
            ],
            "hashcat": [
                "/usr/local/bin/hashcat",
                "/usr/bin/hashcat",
                "hashcat"  # In PATH
            ],
            "hydra": [
                "/usr/local/bin/hydra",
                "/usr/bin/hydra",
                "hydra"  # In PATH
            ],
            
            # Network Scanning Tools
            "nmap": [
                "/usr/local/bin/nmap",
                "/usr/bin/nmap",
                "nmap"  # In PATH
            ],
            "kismet": [
                "/usr/local/bin/kismet",
                "/usr/bin/kismet",
                os.path.join(self.tools_dir, "kismet/kismet"),
                "kismet"  # In PATH
            ],
            "wireshark": [
                "/Applications/Wireshark.app/Contents/MacOS/Wireshark",
                "/usr/local/bin/wireshark",
                "wireshark"  # In PATH
            ],
            "aircrack-ng": [
                "/usr/local/bin/aircrack-ng",
                "/usr/bin/aircrack-ng",
                "aircrack-ng"  # In PATH
            ],
            
            # Web Testing Tools
            "zap": [
                "/Applications/OWASP ZAP.app/Contents/MacOS/zap.sh",
                "/usr/local/bin/zap.sh",
                "zap.sh"  # In PATH
            ],
            "sqlmap": [
                "/usr/local/bin/sqlmap",
                "/usr/bin/sqlmap",
                os.path.join(self.tools_dir, "sqlmap/sqlmap.py"),
                "sqlmap"  # In PATH
            ],
            "nikto": [
                "/usr/local/bin/nikto",
                "/usr/bin/nikto",
                "nikto"  # In PATH
            ],
            
            # Exploitation Tools
            "msfconsole": [
                "/usr/local/bin/msfconsole",
                "/usr/bin/msfconsole",
                "/opt/metasploit-framework/bin/msfconsole",
                "msfconsole"  # In PATH
            ],
            "setoolkit": [
                os.path.join(self.tools_dir, "set/setoolkit"),
                "setoolkit"  # In PATH
            ],
            
            # Forensics Tools
            "autopsy": [
                "/Applications/Autopsy.app/Contents/MacOS/autopsy",
                "/usr/local/bin/autopsy",
                "autopsy"  # In PATH
            ],
            
            # General Tools
            "ncat": [
                "/usr/local/bin/ncat",
                "/usr/bin/ncat",
                "ncat"  # In PATH
            ],
            "tcpdump": [
                "/usr/local/bin/tcpdump",
                "/usr/bin/tcpdump",
                "tcpdump"  # In PATH
            ],
            "ettercap": [
                "/usr/local/bin/ettercap",
                "/usr/bin/ettercap",
                "ettercap"  # In PATH
            ]
        }
        
        # Initialize tool detection
        self.detect_installed_tools()
        
    def detect_installed_tools(self):
        """Detect which security tools are installed and their paths"""
        for tool_name, possible_paths in self.common_tool_paths.items():
            found = False
            
            for path in possible_paths:
                if os.path.exists(path) and os.access(path, os.X_OK):
                    self.tools_installed[tool_name] = True
                    self.tools_paths[tool_name] = path
                    found = True
                    logger.info(f"Found {tool_name} at {path}")
                    break
                elif shutil.which(path):
                    # Tool is in PATH
                    self.tools_installed[tool_name] = True
                    self.tools_paths[tool_name] = shutil.which(path)
                    found = True
                    logger.info(f"Found {tool_name} in PATH at {self.tools_paths[tool_name]}")
                    break
                    
            if not found:
                self.tools_installed[tool_name] = False
                logger.warning(f"{tool_name} not found")
                
        # Log summary
        installed_tools = [tool for tool, installed in self.tools_installed.items() if installed]
        logger.info(f"Detected {len(installed_tools)} installed security tools: {', '.join(installed_tools)}")
        
    def get_installed_tools(self):
        """Get a list of all installed tools"""
        return [tool for tool, installed in self.tools_installed.items() if installed]
        
    def get_tools_by_category(self, category):
        """Get installed tools by category"""
        if category not in self.tool_categories:
            raise ValueError(f"Unknown category: {category}")
            
        return [tool for tool in self.tool_categories[category] 
                if self.tools_installed.get(tool, False)]
                
    def run_tool(self, tool_name, args=None, capture_output=True, text=True, cwd=None, timeout=None):
        """
        Run a security tool with specified arguments - UNRESTRICTED
        
        Args:
            tool_name: Name of the tool to run
            args: List of arguments to pass to the tool
            capture_output: Whether to capture command output
            text: Whether to return output as text
            cwd: Working directory to run the command in
            timeout: Maximum execution time in seconds
            
        Returns:
            CompletedProcess object with return code, stdout, and stderr
        """
        if not self.tools_installed.get(tool_name, False):
            if shutil.which(tool_name):
                # Tool is in PATH but not in our predefined list
                self.tools_installed[tool_name] = True
                self.tools_paths[tool_name] = shutil.which(tool_name)
            else:
                raise ValueError(f"{tool_name} is not installed")
            
        tool_path = self.tools_paths[tool_name]
        cmd = [tool_path]
        
        if args:
            if isinstance(args, list):
                cmd.extend(args)
            else:
                cmd.append(args)
                
        logger.info(f"Running command: {' '.join(cmd)}")
        
        try:
            result = subprocess.run(
                cmd,
                capture_output=capture_output,
                text=text,
                cwd=cwd,
                timeout=timeout
            )
            logger.info(f"Command completed with return code: {result.returncode}")
            return result
        except subprocess.TimeoutExpired as e:
            logger.error(f"Command timed out after {timeout} seconds: {e}")
            raise
        except Exception as e:
            logger.error(f"Error running {tool_name}: {e}")
            raise
    
    # ======================== PASSWORD CRACKING TOOLS ========================
    
    def crack_password(self, hash_value, hash_type=None, wordlist=None, rules=None, 
                      tool="john", max_time=300):
        """
        Attempt to crack a password hash using available tools
        
        Args:
            hash_value: The hash to crack (string or file path)
            hash_type: Type of hash (e.g., "md5", "sha1", "ntlm")
            wordlist: Path to wordlist file
            rules: Password mangling rules
            tool: Tool to use ("john" or "hashcat")
            max_time: Maximum time to spend in seconds
            
        Returns:
            Dictionary with cracking results
        """
        if tool not in ["john", "hashcat"]:
            raise ValueError(f"Unsupported tool: {tool}. Use 'john' or 'hashcat'")
            
        if not self.tools_installed.get(tool, False):
            raise ValueError(f"{tool} is not installed")
            
        # Create a temporary file if hash_value is a string
        hash_file = hash_value
        temp_file = None
        if not os.path.isfile(hash_value):
            temp_file = tempfile.NamedTemporaryFile(delete=False, mode='w')
            temp_file.write(hash_value)
            temp_file.close()
            hash_file = temp_file.name
            
        try:
            if tool == "john":
                return self._run_john(hash_file, hash_type, wordlist, rules, max_time)
            elif tool == "hashcat":
                return self._run_hashcat(hash_file, hash_type, wordlist, rules, max_time)
        finally:
            # Clean up temporary file
            if temp_file:
                os.unlink(temp_file.name)
                
    def _run_john(self, hash_file, hash_type=None, wordlist=None, rules=None, max_time=300):
        """Run John the Ripper"""
        args = [hash_file]
        
        if hash_type:
            # Convert hash_type to john format
            john_formats = {
                "md5": "raw-md5",
                "sha1": "raw-sha1",
                "sha256": "raw-sha256",
                "sha512": "raw-sha512",
                "ntlm": "nt",
                "lm": "lm"
            }
            fmt = john_formats.get(hash_type.lower(), hash_type)
            args.extend(["--format", fmt])
            
        if wordlist:
            args.extend(["--wordlist", wordlist])
            
        if rules:
            args.extend(["--rules", rules])
            
        # Add time limit
        args.extend(["--max-run-time", str(max_time)])
        
        # Run John
        try:
            result = self.run_tool("john", args, timeout=max_time+10)
            
            # Get cracked passwords
            show_result = self.run_tool("john", ["--show", hash_file])
            
            return {
                "success": result.returncode == 0,
                "tool": "john",
                "command_output": result.stdout if result.returncode == 0 else result.stderr,
                "cracked_passwords": show_result.stdout if show_result.returncode == 0 else None
            }
        except Exception as e:
            return {
                "success": False,
                "tool": "john",
                "error": str(e)
            }
            
    def _run_hashcat(self, hash_file, hash_type=None, wordlist=None, rules=None, max_time=300):
        """Run Hashcat"""
        args = []
        
        if hash_type:
            # Convert hash_type to hashcat mode
            hashcat_modes = {
                "md5": "0",
                "sha1": "100",
                "sha256": "1400",
                "sha512": "1700",
                "ntlm": "1000",
                "lm": "3000"
            }
            mode = hashcat_modes.get(hash_type.lower(), hash_type)
            args.extend(["-m", mode])
            
        args.append(hash_file)
        
        if wordlist:
            args.append(wordlist)
        else:
            # Use built-in wordlist
            args.append("/usr/share/wordlists/rockyou.txt")
            
        if rules:
            args.extend(["-r", rules])
            
        # Add time limit (in seconds)
        args.extend(["--runtime", str(max_time)])
        
        # Show cracked passwords
        args.append("--show")
        
        # Run Hashcat
        try:
            result = self.run_tool("hashcat", args, timeout=max_time+10)
            
            return {
                "success": result.returncode == 0,
                "tool": "hashcat",
                "command_output": result.stdout if result.returncode == 0 else result.stderr,
                "cracked_passwords": result.stdout if result.returncode == 0 else None
            }
        except Exception as e:
            return {
                "success": False,
                "tool": "hashcat",
                "error": str(e)
            }
    
    # ======================== NETWORK SCANNING TOOLS ========================
    
    def scan_network(self, target, scan_type="basic", ports=None, output_file=None):
        """
        Scan a network target using Nmap
        
        Args:
            target: Target to scan (IP, hostname, network range)
            scan_type: Type of scan ("basic", "comprehensive", "stealth", "vuln")
            ports: Ports to scan (e.g., "22,80,443" or "1-1000")
            output_file: Optional output file path
            
        Returns:
            Dictionary with scan results
        """
        if not self.tools_installed.get("nmap", False):
            raise ValueError("Nmap is not installed")
            
        args = []
        
        # Scan type determines the flags
        if scan_type == "basic":
            args.append("-sV")  # Service/version detection
        elif scan_type == "comprehensive":
            args.extend(["-sS", "-sV", "-sC", "-A", "-O"]) # SYN scan + Service detection + Scripts + OS
        elif scan_type == "stealth":
            args.extend(["-sS", "-T2"]) # SYN scan + Slower timing
        elif scan_type == "vuln":
            args.extend(["-sV", "--script", "vuln"])  # Vulnerability scan
        else:
            # Custom scan type - just pass it directly
            args.extend(scan_type.split())
            
        # Add ports if specified
        if ports:
            args.extend(["-p", ports])
            
        # Add output file if specified
        if output_file:
            args.extend(["-oN", output_file])
            
        # Add target
        args.append(target)
        
        # Run Nmap
        try:
            result = self.run_tool("nmap", args)
            
            # Parse results
            scan_results = {}
            if result.returncode == 0:
                scan_results = self._parse_nmap_output(result.stdout)
                
            return {
                "success": result.returncode == 0,
                "tool": "nmap",
                "command": f"nmap {' '.join(args)}",
                "raw_output": result.stdout,
                "parsed_results": scan_results
            }
        except Exception as e:
            return {
                "success": False,
                "tool": "nmap",
                "error": str(e)
            }
    
    def _parse_nmap_output(self, output):
        """Parse Nmap output into a structured format"""
        results = {
            "hosts": {},
            "ports": {}
        }
        
        current_host = None
        
        for line in output.splitlines():
            # Host information
            if "Nmap scan report for" in line:
                host = line.split("Nmap scan report for ")[1]
                current_host = host
                results["hosts"][current_host] = {
                    "status": "unknown",
                    "ports": {}
                }
            
            # Host status
            elif "Host is" in line and current_host:
                status = line.split("Host is ")[1].split(" ")[0]
                results["hosts"][current_host]["status"] = status
                
            # Port information
            elif current_host and re.match(r"^\d+/\w+\s+\w+\s+.*", line):
                parts = line.split()
                port_proto = parts[0]
                state = parts[1]
                service = parts[2] if len(parts) > 2 else ""
                
                port = port_proto.split("/")[0]
                proto = port_proto.split("/")[1]
                
                results["hosts"][current_host]["ports"][port] = {
                    "protocol": proto,
                    "state": state,
                    "service": service
                }
                
                # Add to global port list
                if port not in results["ports"]:
                    results["ports"][port] = []
                    
                results["ports"][port].append({
                    "host": current_host,
                    "protocol": proto,
                    "state": state,
                    "service": service
                })
                
        return results
    
    # ======================== WEB TESTING TOOLS ========================
    
    def scan_web_application(self, target_url, scan_type="basic", output_file=None):
        """
        Scan a web application for vulnerabilities using OWASP ZAP or sqlmap
        
        Args:
            target_url: URL to scan
            scan_type: Type of scan ("basic", "full", "sql_injection")
            output_file: Optional output file path
            
        Returns:
            Dictionary with scan results
        """
        if scan_type == "sql_injection":
            # Use sqlmap for SQL injection testing
            if not self.tools_installed.get("sqlmap", False):
                raise ValueError("sqlmap is not installed")
                
            args = ["-u", target_url, "--batch"]
            
            if output_file:
                args.extend(["--output-dir", os.path.dirname(output_file)])
                
            try:
                result = self.run_tool("sqlmap", args)
                
                return {
                    "success": result.returncode == 0,
                    "tool": "sqlmap",
                    "command": f"sqlmap {' '.join(args)}",
                    "output": result.stdout
                }
            except Exception as e:
                return {
                    "success": False,
                    "tool": "sqlmap",
                    "error": str(e)
                }
        else:
            # Use OWASP ZAP (headless mode)
            if not self.tools_installed.get("zap", False):
                raise ValueError("OWASP ZAP is not installed")
                
            if scan_type == "basic":
                # Quick scan
                args = ["-cmd", "-quickurl", target_url]
            else:
                # Full scan
                args = ["-cmd", "-autorun", f"zap_scan.conf"]
                
                # Create config file for full scan
                with open("zap_scan.conf", "w") as f:
                    f.write(f"url={target_url}\n")
                    f.write("activeScan=true\n")
                    f.write("spiderFirst=true\n")
                    
            if output_file:
                args.extend(["-report", output_file])
                
            try:
                result = self.run_tool("zap", args, timeout=1800)  # 30 minute timeout
                
                return {
                    "success": result.returncode == 0,
                    "tool": "owasp_zap",
                    "command": f"zap {' '.join(args)}",
                    "output": result.stdout
                }
            except Exception as e:
                return {
                    "success": False,
                    "tool": "owasp_zap",
                    "error": str(e)
                }
    
    # ======================== GENERAL PURPOSE METHODS ========================
    
    def execute_raw_command(self, command, args=None, cwd=None, timeout=None):
        """
        Execute any command directly - COMPLETELY UNRESTRICTED
        
        Args:
            command: The command to run
            args: List of arguments
            cwd: Working directory
            timeout: Timeout in seconds
            
        Returns:
            CompletedProcess object with return code, stdout, and stderr
        """
        cmd = [command]
        
        if args:
            if isinstance(args, list):
                cmd.extend(args)
            else:
                cmd.append(args)
                
        logger.info(f"Executing raw command: {' '.join(cmd)}")
        
        try:
            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                cwd=cwd,
                timeout=timeout
            )
            logger.info(f"Command completed with return code: {result.returncode}")
            return result
        except Exception as e:
            logger.error(f"Error executing command: {e}")
            raise


# Singleton instance for easy access
security_tools = UnrestrictedSecurityTools()

# Example usage
if __name__ == "__main__":
    # Show installed tools
    print("Installed security tools:")
    for tool in security_tools.get_installed_tools():
        print(f"- {tool}")
        
    # Demonstrate a simple network scan if nmap is installed
    if security_tools.tools_installed.get("nmap", False):
        print("\nRunning a basic local scan with Nmap...")
        result = security_tools.scan_network("127.0.0.1", "basic", "22,80,443")
        print(json.dumps(result["parsed_results"], indent=2))