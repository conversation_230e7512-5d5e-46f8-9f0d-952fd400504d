"""
Send <PERSON> Communications - Fixed Version

This script sends actual communications (call, voicemail, text, email)
to <PERSON> using Twilio and Eleven Labs, with proper account configuration.
"""

import os
import requests
import json
import time
from twilio.rest import Client
from email.mime.text import MIMEText
from email.mime.multipart import MI<PERSON>Multipart
import smtplib
import ssl

# Import <PERSON> communication templates
from paul_edwards_communications import Paul<PERSON><PERSON><PERSON>sC<PERSON>munications

# <PERSON> contact information - using the primary number
PAUL_EDWARDS = {
    "first_name": "<PERSON>",
    "last_name": "<PERSON>",
    "email": "<EMAIL>",
    "phone": "+***********",  # Primary contact number
}

# ======================== CONFIGURATION ========================
# Twilio credentials
TWILIO_ACCOUNT_SID = "AC187c871afa232bbbc978caf33f3e25d9"
TWILIO_AUTH_TOKEN = "CqpVewwter1BEMdFIFHrN2XmUyt22wBP"  # Using the API Secret as auth token
TWILIO_PHONE_NUMBER = "+***********"  # Using <PERSON>'s number as the verified number

# Eleven Labs credentials
ELEVEN_LABS_API_KEY = "sk_531ff39d6099a4191b57e4935654c909d6196b59d9339015"
ELEVEN_LABS_VOICE_ID = "21m00Tcm4TlvDq8ikWAM"  # Rachel voice ID

# Email configuration - using Flo Faction email
EMAIL_SENDER = "<EMAIL>"
EMAIL_PASSWORD = "your_app_password_here"  # You'll need to generate an app password
SMTP_SERVER = "smtp.gmail.com"
SMTP_PORT = 587

# Agent information
AGENT_INFO = {
    "name": "Sandra Smith",
    "agency": "Flo Faction Insurance",
    "phone": TWILIO_PHONE_NUMBER,
    "email": EMAIL_SENDER,
    "website": "https://www.flofaction.com",
    "facebook": "https://www.facebook.com/flofaction",
    "instagram": "https://www.instagram.com/flofaction"
}

# Create directories for audio files
os.makedirs("audio", exist_ok=True)

# ======================== ELEVEN LABS TEXT-TO-SPEECH ========================
def generate_speech(text, voice_id=ELEVEN_LABS_VOICE_ID, output_path="audio/output.mp3"):
    """Generate speech from text using Eleven Labs API"""
    url = f"https://api.elevenlabs.io/v1/text-to-speech/{voice_id}"
    
    headers = {
        "Accept": "audio/mpeg",
        "Content-Type": "application/json",
        "xi-api-key": ELEVEN_LABS_API_KEY
    }
    
    data = {
        "text": text,
        "model_id": "eleven_monolingual_v1",
        "voice_settings": {
            "stability": 0.5,
            "similarity_boost": 0.5
        }
    }
    
    response = requests.post(url, json=data, headers=headers)
    
    if response.status_code == 200:
        with open(output_path, "wb") as f:
            f.write(response.content)
        print(f"Audio file saved to {output_path}")
        return output_path
    else:
        print(f"Error generating speech: {response.text}")
        return None

# ======================== TWILIO FUNCTIONS ========================
def initialize_twilio_client():
    """Initialize Twilio client"""
    return Client(TWILIO_ACCOUNT_SID, TWILIO_AUTH_TOKEN)

def make_phone_call(to_number, audio_url=None, twiml=None):
    """Make a phone call using Twilio"""
    client = initialize_twilio_client()
    
    # Format phone number for Twilio
    if not to_number.startswith("+"):
        to_number = "+1" + to_number
    
    try:
        # If we have a TwiML, use that
        if twiml:
            call = client.calls.create(
                twiml=twiml,
                to=to_number,
                from_=TWILIO_PHONE_NUMBER
            )
        # If we have an audio URL, use that
        elif audio_url:
            call = client.calls.create(
                url=audio_url,
                to=to_number,
                from_=TWILIO_PHONE_NUMBER
            )
        else:
            print("Error: Either audio_url or twiml must be provided")
            return None
        
        print(f"Call initiated with SID: {call.sid}")
        return call.sid
    except Exception as e:
        print(f"Error making call: {str(e)}")
        return None

def send_voicemail(to_number, audio_url=None, twiml=None):
    """Send a voicemail using Twilio (calls and hangs up after voicemail)"""
    client = initialize_twilio_client()
    
    # Format phone number for Twilio
    if not to_number.startswith("+"):
        to_number = "+1" + to_number
    
    try:
        # Create TwiML to go straight to voicemail
        if twiml:
            voicemail_twiml = f"""
            <Response>
                <Pause length="2"/>
                {twiml}
                <Hangup/>
            </Response>
            """
        elif audio_url:
            voicemail_twiml = f"""
            <Response>
                <Pause length="2"/>
                <Play>{audio_url}</Play>
                <Hangup/>
            </Response>
            """
        else:
            print("Error: Either audio_url or twiml must be provided")
            return None
        
        # Make the call with the voicemail TwiML
        call = client.calls.create(
            twiml=voicemail_twiml,
            to=to_number,
            from_=TWILIO_PHONE_NUMBER
        )
        
        print(f"Voicemail initiated with SID: {call.sid}")
        return call.sid
    except Exception as e:
        print(f"Error sending voicemail: {str(e)}")
        return None

def send_text_message(to_number, message):
    """Send a text message using Twilio"""
    client = initialize_twilio_client()
    
    # Format phone number for Twilio
    if not to_number.startswith("+"):
        to_number = "+1" + to_number
    
    try:
        message = client.messages.create(
            body=message,
            from_=TWILIO_PHONE_NUMBER,
            to=to_number
        )
        
        print(f"Text message sent with SID: {message.sid}")
        return message.sid
    except Exception as e:
        print(f"Error sending text message: {str(e)}")
        return None

# ======================== EMAIL FUNCTION ========================
def send_email(to_email, subject, body):
    """Send an email using SMTP"""
    try:
        message = MIMEMultipart()
        message["From"] = EMAIL_SENDER
        message["To"] = to_email
        message["Subject"] = subject
        
        # Attach body
        message.attach(MIMEText(body, "plain"))
        
        # Create secure connection and send email
        context = ssl.create_default_context()
        with smtplib.SMTP(SMTP_SERVER, SMTP_PORT) as server:
            server.starttls(context=context)
            server.login(EMAIL_SENDER, EMAIL_PASSWORD)
            server.sendmail(EMAIL_SENDER, to_email, message.as_string())
        
        print(f"Email sent to {to_email}")
        return True
    except Exception as e:
        print(f"Error sending email: {str(e)}")
        return False

# ======================== MAIN FUNCTION ========================
def send_all_communications():
    """Send all communications to Paul Edwards"""
    print("=" * 80)
    print("SENDING COMMUNICATIONS TO PAUL EDWARDS")
    print("=" * 80)
    
    # Update the agent info in the templates
    paul_data = {
        "first_name": PAUL_EDWARDS["first_name"],
        "last_name": PAUL_EDWARDS["last_name"],
        "email": PAUL_EDWARDS["email"],
        "phone": PAUL_EDWARDS["phone"],
        "age": 32,
        "income": 85000,
        "budget": 200,
        "agent_name": AGENT_INFO["name"],
        "agency_name": AGENT_INFO["agency"],
        "agent_phone": AGENT_INFO["phone"],
        "agent_email": AGENT_INFO["email"],
        "website": AGENT_INFO["website"],
        "facebook": AGENT_INFO["facebook"],
        "instagram": AGENT_INFO["instagram"]
    }
    
    # Create custom communication templates with Flo Faction branding
    call_script = f"""
    Hi {PAUL_EDWARDS['first_name']}, this is {AGENT_INFO['name']} with {AGENT_INFO['agency']}. How are you doing today?
    
    I'm reaching out because I help people like you create tax-free retirement income without the risk of the stock market. Based on your profile, I've found that you might qualify for a strategy that could provide around $5,000 per month in tax-free retirement income. Would that be something you'd be interested in learning more about?
    
    Great! I've prepared a personalized analysis for you that shows how an Indexed Universal Life policy could help you build tax-free retirement income while also providing a death benefit of around $720,000 to protect your family.
    
    I'd like to schedule about 30 minutes to walk you through this analysis and answer any questions you might have. Would Tuesday at 2:00 PM or Thursday at 4:00 PM work better for your schedule?
    
    Excellent! I'll send you a calendar invitation with all the details. Before we wrap up, do you have any initial questions about how this strategy works?
    
    I'm looking forward to our conversation. In the meantime, I'll send you a brief overview via email so you can get familiar with the concept. You can also check out our website at {AGENT_INFO['website']} or follow us on social media for more information. Thank you for your time today, {PAUL_EDWARDS['first_name']}, and have a great day!
    """
    
    voicemail_script = f"""
    Hi {PAUL_EDWARDS['first_name']}, this is {AGENT_INFO['name']} with {AGENT_INFO['agency']}. I'm calling because I help people like you create tax-free retirement income without the risk of the stock market.
    
    Based on your profile, I've found that you might qualify for a strategy that could provide significant tax-free retirement income while also protecting your family with a death benefit.
    
    I'd love to share a personalized analysis I've prepared for you. Please give me a call back at {AGENT_INFO['phone']} when you have a moment, or visit our website at {AGENT_INFO['website']} for more information.
    
    Again, this is {AGENT_INFO['name']} at {AGENT_INFO['phone']}. I look forward to speaking with you soon. Thank you!
    """
    
    text_message = f"""
    Hi {PAUL_EDWARDS['first_name']}, this is {AGENT_INFO['name']} with {AGENT_INFO['agency']}. I help people create tax-free retirement income without stock market risk. I've prepared a personalized analysis for you. Would you be interested in seeing this analysis? Call me at {AGENT_INFO['phone']} or visit {AGENT_INFO['website']}. Thanks!
    """
    
    email_subject = "Creating Tax-Free Retirement Income Without Market Risk"
    email_body = f"""
Dear {PAUL_EDWARDS['first_name']},

I hope this email finds you well. My name is {AGENT_INFO['name']} with {AGENT_INFO['agency']}, and I specialize in helping people like you create tax-free retirement income without the risk of the stock market.

Based on the information I have, I've prepared a personalized analysis that shows how you could potentially:

1. Generate approximately $5,000 per month in tax-free retirement income
2. Build cash value that grows without the risk of market losses
3. Provide a death benefit of around $720,000 to protect your family
4. Create a financial strategy that offers both protection and growth potential

Many of my clients are concerned about:
- Market volatility affecting their retirement savings
- Taxes reducing their retirement income
- Having enough money to maintain their lifestyle in retirement
- Protecting their family financially

The strategy I'd like to share with you addresses all these concerns through an Indexed Universal Life policy specifically designed for your situation.

Would you be interested in a brief 30-minute conversation to review this analysis? If so, please call me at {AGENT_INFO['phone']} or reply to this email with a good time to connect.

You can also learn more about our services by visiting our website at {AGENT_INFO['website']} or following us on social media:
- Facebook: {AGENT_INFO['facebook']}
- Instagram: {AGENT_INFO['instagram']}

Thank you for your time, and I look forward to speaking with you soon.

Best regards,
{AGENT_INFO['name']}
{AGENT_INFO['agency']}
{AGENT_INFO['phone']}
{AGENT_INFO['email']}
{AGENT_INFO['website']}
"""
    
    # 1. Generate speech for call and voicemail
    print("\n1. GENERATING SPEECH WITH ELEVEN LABS")
    print("-" * 80)
    
    call_audio_path = generate_speech(call_script, output_path="audio/paul_edwards_call.mp3")
    voicemail_audio_path = generate_speech(voicemail_script, output_path="audio/paul_edwards_voicemail.mp3")
    
    # 2. Make phone call
    print("\n2. MAKING PHONE CALL WITH TWILIO")
    print("-" * 80)
    
    # Since we can't host the audio file at a public URL in this environment,
    # we'll use Twilio's TTS capabilities instead
    call_twiml = f"""
    <Response>
        <Say voice="woman">
            {call_script}
        </Say>
        <Pause length="2"/>
        <Say voice="woman">
            This is the end of the call. Thank you.
        </Say>
    </Response>
    """
    
    call_sid = make_phone_call(PAUL_EDWARDS["phone"], twiml=call_twiml)
    
    # Wait a bit before sending the voicemail to avoid conflicts
    print("Waiting 30 seconds before sending voicemail...")
    time.sleep(30)
    
    # 3. Send voicemail
    print("\n3. SENDING VOICEMAIL WITH TWILIO")
    print("-" * 80)
    
    voicemail_twiml = f"""
    <Response>
        <Say voice="woman">
            {voicemail_script}
        </Say>
    </Response>
    """
    
    voicemail_sid = send_voicemail(PAUL_EDWARDS["phone"], twiml=voicemail_twiml)
    
    # 4. Send text message
    print("\n4. SENDING TEXT MESSAGE WITH TWILIO")
    print("-" * 80)
    
    text_sid = send_text_message(PAUL_EDWARDS["phone"], text_message)
    
    # 5. Send email
    print("\n5. SENDING EMAIL")
    print("-" * 80)
    
    email_success = send_email(
        PAUL_EDWARDS["email"],
        email_subject,
        email_body
    )
    
    # Summary
    print("\n" + "=" * 80)
    print("COMMUNICATION SUMMARY")
    print("=" * 80)
    
    print(f"Call SID: {call_sid}")
    print(f"Voicemail SID: {voicemail_sid}")
    print(f"Text Message SID: {text_sid}")
    print(f"Email Sent: {'Yes' if email_success else 'No'}")
    
    print("\nAll communications have been sent to Paul Edwards.")
    print("=" * 80)

# ======================== WEBSITE INTEGRATION ========================
def integrate_with_wix_website():
    """
    Instructions for integrating with Flo Faction Wix website
    
    This function provides instructions for integrating Twilio and email
    communications with the Flo Faction Wix website.
    """
    print("=" * 80)
    print("INTEGRATING WITH FLO FACTION WIX WEBSITE")
    print("=" * 80)
    
    print("""
To integrate these communications with your Flo Faction Wix website:

1. Twilio Integration:
   - Go to your Wix dashboard and navigate to "Settings" > "API Connections"
   - Add a new API connection for Twilio
   - Enter your Twilio Account SID and Auth Token
   - Create a new Wix Velo function that calls the Twilio API when a form is submitted
   
2. Email Integration:
   - Go to your Wix dashboard and navigate to "Settings" > "Email Settings"
   - Configure your email provider (Gmail)
   - Create email templates that match the templates in this script
   - Set up automated emails for form submissions
   
3. Form Integration:
   - Create a "Contact Us" or "Request a Quote" form on your website
   - Connect the form submission to trigger both Twilio and email communications
   - Store form submissions in your Wix database
   
4. Social Media Integration:
   - Add social media links to your email templates
   - Create social media sharing buttons on your website
   - Set up automated social media posts for new blog content
   
5. Wix Velo Code Example:
   ```javascript
   import {fetch} from 'wix-fetch';
   
   export function sendTwilioMessage(phoneNumber, message) {
     const accountSid = 'AC187c871afa232bbbc978caf33f3e25d9';
     const authToken = 'CqpVewwter1BEMdFIFHrN2XmUyt22wBP';
     const twilioNumber = '+***********';
     
     const url = `https://api.twilio.com/2010-04-01/Accounts/${accountSid}/Messages.json`;
     
     const options = {
       method: 'post',
       headers: {
         'Authorization': 'Basic ' + Buffer.from(`${accountSid}:${authToken}`).toString('base64'),
         'Content-Type': 'application/x-www-form-urlencoded'
       },
       body: `From=${twilioNumber}&To=${phoneNumber}&Body=${message}`
     };
     
     return fetch(url, options)
       .then(response => response.json())
       .then(data => {
         console.log('Message sent with SID:', data.sid);
         return data;
       })
       .catch(error => {
         console.error('Error sending message:', error);
         throw error;
       });
   }
   ```
   
6. Wix Form Submission Handler:
   ```javascript
   import {sendTwilioMessage} from 'backend/twilio';
   import wixData from 'wix-data';
   
   export function contactForm_afterSubmit(event) {
     const formData = event.submissionData;
     const phoneNumber = formData.phone;
     const email = formData.email;
     const name = formData.name;
     
     // Save to database
     wixData.insert('Leads', {
       name: name,
       email: email,
       phone: phoneNumber,
       source: 'Website Contact Form',
       date: new Date()
     });
     
     // Send text message
     const message = `Hi ${name}, thank you for contacting Flo Faction Insurance. One of our agents will reach out to you shortly.`;
     sendTwilioMessage(phoneNumber, message);
     
     // Email is handled automatically by Wix
   }
   ```
    """)
    
    print("=" * 80)

# ======================== SOCIAL MEDIA INTEGRATION ========================
def integrate_with_social_media():
    """
    Instructions for integrating with Flo Faction social media
    
    This function provides instructions for integrating communications
    with Flo Faction social media pages.
    """
    print("=" * 80)
    print("INTEGRATING WITH FLO FACTION SOCIAL MEDIA")
    print("=" * 80)
    
    print("""
To integrate these communications with your Flo Faction social media pages:

1. Facebook Integration:
   - Use the Facebook Graph API to post updates and respond to messages
   - Connect your Facebook page to your Wix website
   - Set up Facebook Messenger integration with Twilio
   
2. Instagram Integration:
   - Connect your Instagram business account to your Facebook page
   - Use the Instagram Graph API for automated posting
   - Create Instagram-specific content templates
   
3. Social Media Automation:
   - Use Zapier or Integromat to connect your CRM with social media
   - Set up automated posting of new blog content
   - Create automated responses for direct messages
   
4. Lead Generation:
   - Create Facebook Lead Ads that integrate with your CRM
   - Set up Instagram Shopping for insurance products
   - Use Facebook Pixel for retargeting website visitors
   
5. Facebook Messenger Integration Code Example:
   ```javascript
   import {fetch} from 'wix-fetch';
   
   export function sendFacebookMessage(userId, message) {
     const pageAccessToken = 'YOUR_PAGE_ACCESS_TOKEN';
     const url = `https://graph.facebook.com/v13.0/me/messages?access_token=${pageAccessToken}`;
     
     const payload = {
       recipient: {
         id: userId
       },
       message: {
         text: message
       }
     };
     
     const options = {
       method: 'post',
       headers: {
         'Content-Type': 'application/json'
       },
       body: JSON.stringify(payload)
     };
     
     return fetch(url, options)
       .then(response => response.json())
       .then(data => {
         console.log('Facebook message sent:', data);
         return data;
       })
       .catch(error => {
         console.error('Error sending Facebook message:', error);
         throw error;
       });
   }
   ```
   
6. Instagram Post Automation:
   ```javascript
   import {fetch} from 'wix-fetch';
   
   export function createInstagramPost(imageUrl, caption) {
     const accessToken = 'YOUR_INSTAGRAM_ACCESS_TOKEN';
     const instagramAccountId = 'YOUR_INSTAGRAM_ACCOUNT_ID';
     
     // First, upload the image to Facebook
     const uploadUrl = `https://graph.facebook.com/v13.0/${instagramAccountId}/media?image_url=${encodeURIComponent(imageUrl)}&caption=${encodeURIComponent(caption)}&access_token=${accessToken}`;
     
     return fetch(uploadUrl, {method: 'post'})
       .then(response => response.json())
       .then(data => {
         const creationId = data.id;
         
         // Then publish the container
         const publishUrl = `https://graph.facebook.com/v13.0/${instagramAccountId}/media_publish?creation_id=${creationId}&access_token=${accessToken}`;
         
         return fetch(publishUrl, {method: 'post'});
       })
       .then(response => response.json())
       .then(data => {
         console.log('Instagram post created:', data);
         return data;
       })
       .catch(error => {
         console.error('Error creating Instagram post:', error);
         throw error;
       });
   }
   ```
    """)
    
    print("=" * 80)

if __name__ == "__main__":
    print("This script will send actual communications to Paul Edwards.")
    print("Make sure you have proper authorization before proceeding.")
    print("You will need valid Twilio and email credentials.")
    
    proceed = input("Do you want to proceed? (yes/no): ")
    
    if proceed.lower() == "yes":
        send_all_communications()
        
        # Provide integration instructions
        print("\nWould you like to see instructions for integrating with the Flo Faction website and social media? (yes/no): ")
        integration_info = input()
        
        if integration_info.lower() == "yes":
            integrate_with_wix_website()
            integrate_with_social_media()
    else:
        print("Operation cancelled.")
