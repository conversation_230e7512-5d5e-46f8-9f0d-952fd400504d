#!/usr/bin/env python3
"""
ProtonVPN Setup and Integration

This script automates the setup of ProtonVPN on macOS and integrates it with
the agent system for secure communications. It handles download, installation,
configuration, and creating persistent startup scripts.

No restrictions are applied - full VPN capabilities are enabled.
"""

import os
import sys
import subprocess
import logging
import json
import time
import requests
from pathlib import Path
from typing import Dict, Any, Optional, Union

# Import unrestricted security tools
try:
    from unrestricted_security_tools import security_tools
except ImportError:
    print("Warning: Unrestricted security tools not found. Some features may be limited.")
    security_tools = None

# Configure logging
logging.basicConfig(level=logging.INFO,
                    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class ProtonVPNManager:
    """Manages ProtonVPN installation and configuration with no restrictions"""
    
    def __init__(self):
        """Initialize the ProtonVPN Manager"""
        self.home_dir = os.path.expanduser("~")
        self.download_dir = os.path.join(self.home_dir, "Downloads")
        self.app_dir = "/Applications/ProtonVPN.app"
        self.script_dir = os.path.join(self.home_dir, "vpn_scripts")
        self.login_items_plist = os.path.join(self.home_dir, "Library/Preferences/com.apple.loginitems.plist")
        self.credentials = {
            "username": "",
            "password": ""
        }
        
        # Create script directory if it doesn't exist
        os.makedirs(self.script_dir, exist_ok=True)
        
    def check_installation(self):
        """Check if ProtonVPN is already installed"""
        return os.path.exists(self.app_dir)
        
    def download_protonvpn(self):
        """Download the latest version of ProtonVPN for macOS"""
        download_url = "https://protonvpn.com/download/ProtonVPN.dmg"
        dmg_path = os.path.join(self.download_dir, "ProtonVPN.dmg")
        
        logger.info(f"Downloading ProtonVPN from {download_url}")
        try:
            # Check if already downloaded
            if os.path.exists(dmg_path):
                logger.info(f"ProtonVPN installer already exists at {dmg_path}")
                return dmg_path
                
            # Download using requests
            response = requests.get(download_url, stream=True)
            response.raise_for_status()
            
            with open(dmg_path, 'wb') as f:
                for chunk in response.iter_content(chunk_size=8192):
                    if chunk:
                        f.write(chunk)
                        
            logger.info(f"Downloaded ProtonVPN installer to {dmg_path}")
            return dmg_path
        except Exception as e:
            logger.error(f"Error downloading ProtonVPN: {e}")
            raise
            
    def install_protonvpn(self, dmg_path=None):
        """Install ProtonVPN from DMG file"""
        if self.check_installation():
            logger.info("ProtonVPN is already installed")
            return True
            
        if not dmg_path:
            dmg_path = os.path.join(self.download_dir, "ProtonVPN.dmg")
            
        if not os.path.exists(dmg_path):
            logger.info("ProtonVPN installer not found, downloading...")
            dmg_path = self.download_protonvpn()
            
        logger.info(f"Installing ProtonVPN from {dmg_path}")
        try:
            # Mount the DMG
            mount_cmd = ["hdiutil", "attach", dmg_path]
            subprocess.run(mount_cmd, check=True)
            
            # Find the volume name
            volumes = os.listdir("/Volumes")
            proton_volume = None
            for vol in volumes:
                if "ProtonVPN" in vol:
                    proton_volume = vol
                    break
                    
            if not proton_volume:
                raise ValueError("Could not find ProtonVPN volume after mounting DMG")
                
            mount_path = f"/Volumes/{proton_volume}"
            
            # Copy the app to Applications
            copy_cmd = ["cp", "-R", f"{mount_path}/ProtonVPN.app", "/Applications/"]
            subprocess.run(copy_cmd, check=True)
            
            # Unmount the DMG
            unmount_cmd = ["hdiutil", "detach", mount_path]
            subprocess.run(unmount_cmd, check=True)
            
            logger.info("ProtonVPN installed successfully")
            return True
        except Exception as e:
            logger.error(f"Error installing ProtonVPN: {e}")
            raise
            
    def set_credentials(self, username, password):
        """Set ProtonVPN credentials"""
        self.credentials["username"] = username
        self.credentials["password"] = password
        logger.info(f"Set credentials for {username}")
        
    def create_startup_script(self, server="random"):
        """
        Create a script to automatically connect to ProtonVPN
        
        Args:
            server: Server to connect to ("random", "fastest", or specific country code)
        """
        script_path = os.path.join(self.script_dir, "start_protonvpn.sh")
        
        # Script content
        script_content = f"""#!/bin/bash
# ProtonVPN Auto-Connect Script
# Created by {os.path.basename(__file__)}

# Wait for network to be available
sleep 10

# Check if ProtonVPN is installed
if [ ! -d "/Applications/ProtonVPN.app" ]; then
    echo "ProtonVPN is not installed"
    exit 1
fi

# Launch ProtonVPN app
open -a ProtonVPN

# Wait for app to initialize
sleep 5

# Use AppleScript to control the UI (since ProtonVPN CLI is paid feature)
osascript <<EOD
tell application "ProtonVPN"
    activate
    delay 3
    # Attempt to connect to server
    tell application "System Events"
        tell process "ProtonVPN"
            click button "Connect" of window 1
            delay 2
        end tell
    end tell
end tell
EOD

# Wait for connection to establish
sleep 5

# Verify connection
curl -s https://api.ipify.org
echo " (Should be a ProtonVPN IP if connected)"
"""
        
        # Write the script
        with open(script_path, 'w') as f:
            f.write(script_content)
            
        # Make executable
        os.chmod(script_path, 0o755)
        
        logger.info(f"Created startup script at {script_path}")
        return script_path
        
    def create_cli_wrapper(self):
        """Create a wrapper for common VPN operations"""
        script_path = os.path.join(self.script_dir, "protonvpn.sh")
        
        # Script content
        script_content = """#!/bin/bash
# ProtonVPN Command Wrapper
# Created by protonvpn_setup.py

function show_help {
    echo "ProtonVPN Command Wrapper"
    echo "Usage: protonvpn.sh [command]"
    echo ""
    echo "Commands:"
    echo "  connect      Connect to ProtonVPN (using UI)"
    echo "  disconnect   Disconnect from ProtonVPN (using UI)"
    echo "  status       Check current connection status"
    echo "  ip           Show current public IP address"
    echo "  help         Show this help message"
    echo ""
}

function connect_vpn {
    open -a ProtonVPN
    sleep 3
    
    osascript <<EOD
tell application "ProtonVPN"
    activate
    delay 2
    tell application "System Events"
        tell process "ProtonVPN"
            click button "Connect" of window 1
        end tell
    end tell
end tell
EOD
    
    echo "Connecting to ProtonVPN..."
    sleep 5
    check_status
}

function disconnect_vpn {
    open -a ProtonVPN
    sleep 3
    
    osascript <<EOD
tell application "ProtonVPN"
    activate
    delay 2
    tell application "System Events"
        tell process "ProtonVPN"
            click button "Disconnect" of window 1
        end tell
    end tell
end tell
EOD
    
    echo "Disconnecting from ProtonVPN..."
    sleep 3
    check_status
}

function check_status {
    # Check if ProtonVPN is running
    if ! pgrep -x "ProtonVPN" > /dev/null; then
        echo "ProtonVPN is not running"
        return
    fi
    
    # Get current IP and check against known ProtonVPN IP ranges
    CURRENT_IP=$(curl -s https://api.ipify.org)
    echo "Current public IP address: $CURRENT_IP"
    
    # This is a simple check - a more comprehensive method would be better
    if ping -c 1 protonvpn.com > /dev/null 2>&1; then
        echo "Internet connection is active"
    else
        echo "Internet connection appears to be down"
    fi
}

function show_ip {
    echo "Current public IP address: $(curl -s https://api.ipify.org)"
}

# Main command processing
case "$1" in
    connect)
        connect_vpn
        ;;
    disconnect)
        disconnect_vpn
        ;;
    status)
        check_status
        ;;
    ip)
        show_ip
        ;;
    help|--help|-h)
        show_help
        ;;
    *)
        show_help
        ;;
esac
"""
        
        # Write the script
        with open(script_path, 'w') as f:
            f.write(script_content)
            
        # Make executable
        os.chmod(script_path, 0o755)
        
        # Create symbolic link in /usr/local/bin if possible
        try:
            link_path = "/usr/local/bin/protonvpn"
            if os.path.exists(link_path):
                os.remove(link_path)
            os.symlink(script_path, link_path)
            os.chmod(link_path, 0o755)
            logger.info(f"Created symbolic link at {link_path}")
        except Exception as e:
            logger.warning(f"Could not create symbolic link: {e}")
            
        logger.info(f"Created CLI wrapper at {script_path}")
        return script_path
        
    def setup_login_item(self, script_path):
        """Set up the VPN script to run at login"""
        # Using osascript to add login item
        apple_script = f"""
tell application "System Events"
    make login item at end with properties {{path:"{script_path}", hidden:false}}
end tell
"""
        
        try:
            subprocess.run(["osascript", "-e", apple_script], check=True)
            logger.info(f"Added {script_path} as login item")
            return True
        except Exception as e:
            logger.error(f"Error setting up login item: {e}")
            return False
            
    def verify_connection(self):
        """Verify VPN connection is working"""
        try:
            # Get current IP address
            ip_cmd = ["curl", "-s", "https://api.ipify.org"]
            result = subprocess.run(ip_cmd, capture_output=True, text=True, check=True)
            current_ip = result.stdout.strip()
            
            logger.info(f"Current public IP address: {current_ip}")
            
            # We can't programmatically check if this is a ProtonVPN IP without a database
            # But we can report it so the user can verify
            
            return {
                "ip_address": current_ip,
                "connection_active": True
            }
        except Exception as e:
            logger.error(f"Error verifying connection: {e}")
            return {
                "error": str(e),
                "connection_active": False
            }
            
    def run_full_setup(self, username=None, password=None, auto_connect=True):
        """
        Run the complete ProtonVPN setup process
        
        Args:
            username: ProtonVPN username (email)
            password: ProtonVPN password
            auto_connect: Whether to set up auto-connect on login
        
        Returns:
            Dict with setup results
        """
        results = {
            "installation": False,
            "script_created": False,
            "login_item": False,
            "connection": None
        }
        
        try:
            # Step 1: Check and install if needed
            if not self.check_installation():
                dmg_path = self.download_protonvpn()
                self.install_protonvpn(dmg_path)
            results["installation"] = True
            
            # Step 2: Set credentials if provided
            if username and password:
                self.set_credentials(username, password)
                
            # Step 3: Create startup script
            startup_script = self.create_startup_script()
            cli_script = self.create_cli_wrapper()
            results["script_created"] = True
            
            # Step 4: Set up login item if requested
            if auto_connect:
                self.setup_login_item(startup_script)
                results["login_item"] = True
                
            # Step 5: Launch ProtonVPN
            subprocess.run(["open", "-a", "ProtonVPN"], check=True)
            
            # Wait for user to log in manually (we can't automate this part easily)
            logger.info("Please log in to ProtonVPN manually with your credentials")
            logger.info(f"Username: {username or 'your ProtonVPN username'}")
            
            # Step 6: Verify connection
            # Wait for potential connection
            time.sleep(10)
            results["connection"] = self.verify_connection()
            
            logger.info("ProtonVPN setup completed successfully")
            return results
        except Exception as e:
            logger.error(f"Error during ProtonVPN setup: {e}")
            results["error"] = str(e)
            return results


def integrate_with_agents():
    """Integrate ProtonVPN with the agent system for secure communications"""
    # Add code here to integrate with your agent system
    # This is a placeholder that would connect to your agent_system.py
    pass


# Main execution
if __name__ == "__main__":
    print("ProtonVPN Setup and Integration")
    print("===============================")
    
    proton = ProtonVPNManager()
    
    if len(sys.argv) > 1 and sys.argv[1] == "--auto":
        # Fully automatic mode
        if len(sys.argv) >= 4:
            username = sys.argv[2]
            password = sys.argv[3]
            print(f"Running automatic setup for user {username}")
            results = proton.run_full_setup(username, password)
        else:
            print("Running automatic setup (you'll need to log in manually)")
            results = proton.run_full_setup()
            
        print("\nSetup Results:")
        for key, value in results.items():
            if key != "connection":
                print(f"- {key}: {value}")
                
        if results.get("connection"):
            conn = results["connection"]
            print(f"\nConnection Status:")
            print(f"- IP Address: {conn.get('ip_address', 'Unknown')}")
            print(f"- Connection Active: {conn.get('connection_active', False)}")
    else:
        # Interactive mode
        print("\nThis script will set up ProtonVPN on your Mac with the following steps:")
        print("1. Download and install ProtonVPN if not already installed")
        print("2. Create scripts for easy connection management")
        print("3. Set up auto-connect on login (optional)")
        print("4. Verify connection")
        
        proceed = input("\nDo you want to proceed? (y/n): ").strip().lower()
        if proceed != 'y':
            print("Setup cancelled")
            sys.exit(0)
            
        # Check installation
        if proton.check_installation():
            print("\nProtonVPN is already installed")
        else:
            print("\nProtonVPN is not installed. Downloading and installing...")
            dmg_path = proton.download_protonvpn()
            proton.install_protonvpn(dmg_path)
            print("ProtonVPN installed successfully")
            
        # Get credentials
        username = input("\nEnter your ProtonVPN username (email): ").strip()
        password = input("Enter your ProtonVPN password: ").strip()
        
        if username and password:
            proton.set_credentials(username, password)
            
        # Create scripts
        print("\nCreating startup and management scripts...")
        startup_script = proton.create_startup_script()
        cli_script = proton.create_cli_wrapper()
        print(f"Startup script created at: {startup_script}")
        print(f"CLI wrapper script created at: {cli_script}")
        print("You can use the CLI wrapper by running: ./protonvpn.sh COMMAND")
        
        # Ask about auto-connect
        auto_connect = input("\nDo you want to set up auto-connect on login? (y/n): ").strip().lower() == 'y'
        if auto_connect:
            proton.setup_login_item(startup_script)
            print("Auto-connect on login set up successfully")
            
        # Launch ProtonVPN
        print("\nLaunching ProtonVPN...")
        subprocess.run(["open", "-a", "ProtonVPN"], check=True)
        
        print("\nPlease log in to ProtonVPN manually with your credentials")
        print(f"Username: {username}")
        
        # Ask to verify connection
        verify = input("\nDo you want to verify your connection? (y/n): ").strip().lower() == 'y'
        if verify:
            print("Verifying connection...")
            # Wait for potential connection
            time.sleep(5)
            connection = proton.verify_connection()
            
            if connection.get("connection_active", False):
                print(f"Connection active")
                print(f"Current public IP address: {connection.get('ip_address', 'Unknown')}")
            else:
                print("Connection verification failed")
                if connection.get("error"):
                    print(f"Error: {connection['error']}")
                    
        print("\nProtonVPN setup completed")
        print("To connect: ./protonvpn.sh connect")
        print("To disconnect: ./protonvpn.sh disconnect")
        print("To check status: ./protonvpn.sh status")