#!/usr/bin/env python3
"""
Integration MCP Server

This module provides MCP server functionality for API integration and service connection.
"""

import os
import sys
import json
import logging
import asyncio
import argparse
import aiohttp
from pathlib import Path
from typing import Dict, List, Optional, Union, Any
from aiohttp import web

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s [%(levelname)s] %(name)s: %(message)s",
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler("integration_server.log")
    ]
)
logger = logging.getLogger("integration-server")

class IntegrationServer:
    """MCP Server implementation for API integration"""
    
    def __init__(self, host: str = "localhost", port: int = 8089):
        self.host = host
        self.port = port
        self.app = web.Application()
        self.register_routes()
        self.integrations = {}
        self.connections = {}
        
        # Load integrations from file if exists
        self._load_integrations()
        
    def register_routes(self):
        """Register API routes"""
        self.app.router.add_get("/", self.handle_root)
        self.app.router.add_get("/health", self.handle_health)
        self.app.router.add_get("/capabilities", self.handle_capabilities)
        self.app.router.add_post("/integration/register", self.handle_register_integration)
        self.app.router.add_post("/integration/call", self.handle_call_integration)
        self.app.router.add_post("/connection/create", self.handle_create_connection)
        self.app.router.add_get("/connection/status", self.handle_connection_status)

    async def handle_root(self, request):
        """Handle root endpoint"""
        return web.json_response({
            "name": "Integration MCP Server",
            "version": "1.0.0",
            "status": "running"
        })
        
    async def handle_health(self, request):
        """Handle health check endpoint"""
        return web.json_response({
            "status": "healthy",
            "timestamp": asyncio.get_event_loop().time()
        })
        
    async def handle_capabilities(self, request):
        """Handle capabilities endpoint"""
        return web.json_response({
            "capabilities": [
                "api-integration",
                "service-connection",
                "webhook-handling",
                "data-synchronization"
            ]
        })
        
    async def handle_register_integration(self, request):
        """Handle integration registration endpoint"""
        try:
            data = await request.json()
            integration_id = data.get("id", f"integration_{len(self.integrations) + 1}")
            name = data.get("name", "Unnamed Integration")
            api_url = data.get("api_url", "")
            auth_type = data.get("auth_type", "none")
            auth_config = data.get("auth_config", {})
            
            # Register the integration
            self.integrations[integration_id] = {
                "id": integration_id,
                "name": name,
                "api_url": api_url,
                "auth_type": auth_type,
                "auth_config": auth_config,
                "created_at": asyncio.get_event_loop().time(),
                "status": "registered"
            }
            
            # Save integrations to file
            self._save_integrations()
            
            logger.info(f"Registered integration: {name} ({integration_id})")
            
            return web.json_response({
                "status": "success",
                "integration_id": integration_id,
                "message": f"Integration '{name}' registered successfully"
            })
            
        except Exception as e:
            logger.error(f"Error registering integration: {e}")
            return web.json_response({
                "status": "error",
                "message": str(e)
            }, status=500)
            
    async def handle_call_integration(self, request):
        """Handle integration call endpoint"""
        try:
            data = await request.json()
            integration_id = data.get("integration_id", "")
            endpoint = data.get("endpoint", "")
            method = data.get("method", "GET")
            payload = data.get("payload", {})
            
            if integration_id not in self.integrations:
                return web.json_response({
                    "status": "error",
                    "message": f"Integration with ID '{integration_id}' not found"
                }, status=404)
                
            integration = self.integrations[integration_id]
            
            # Build the request URL
            url = f"{integration['api_url'].rstrip('/')}/{endpoint.lstrip('/')}"
            
            # Set up authentication
            headers = {}
            if integration["auth_type"] == "api_key":
                headers[integration["auth_config"].get("header_name", "X-API-Key")] = integration["auth_config"].get("api_key", "")
            elif integration["auth_type"] == "bearer":
                headers["Authorization"] = f"Bearer {integration['auth_config'].get('token', '')}"
                
            # Make the API call
            async with aiohttp.ClientSession() as session:
                if method.upper() == "GET":
                    async with session.get(url, headers=headers, params=payload) as response:
                        response_data = await response.json()
                elif method.upper() == "POST":
                    async with session.post(url, headers=headers, json=payload) as response:
                        response_data = await response.json()
                elif method.upper() == "PUT":
                    async with session.put(url, headers=headers, json=payload) as response:
                        response_data = await response.json()
                elif method.upper() == "DELETE":
                    async with session.delete(url, headers=headers) as response:
                        response_data = await response.json()
                else:
                    return web.json_response({
                        "status": "error",
                        "message": f"Unsupported method: {method}"
                    }, status=400)
                    
            logger.info(f"Called integration: {integration['name']} ({integration_id}) - {method} {endpoint}")
            
            return web.json_response({
                "status": "success",
                "integration_id": integration_id,
                "response": response_data
            })
            
        except Exception as e:
            logger.error(f"Error calling integration: {e}")
            return web.json_response({
                "status": "error",
                "message": str(e)
            }, status=500)
            
    async def handle_create_connection(self, request):
        """Handle connection creation endpoint"""
        try:
            data = await request.json()
            connection_id = data.get("id", f"connection_{len(self.connections) + 1}")
            name = data.get("name", "Unnamed Connection")
            service_type = data.get("service_type", "")
            config = data.get("config", {})
            
            # Create the connection
            self.connections[connection_id] = {
                "id": connection_id,
                "name": name,
                "service_type": service_type,
                "config": config,
                "created_at": asyncio.get_event_loop().time(),
                "status": "created"
            }
            
            logger.info(f"Created connection: {name} ({connection_id})")
            
            return web.json_response({
                "status": "success",
                "connection_id": connection_id,
                "message": f"Connection '{name}' created successfully"
            })
            
        except Exception as e:
            logger.error(f"Error creating connection: {e}")
            return web.json_response({
                "status": "error",
                "message": str(e)
            }, status=500)
            
    async def handle_connection_status(self, request):
        """Handle connection status endpoint"""
        try:
            connection_id = request.query.get("connection_id", "")
            
            if connection_id and connection_id not in self.connections:
                return web.json_response({
                    "status": "error",
                    "message": f"Connection with ID '{connection_id}' not found"
                }, status=404)
                
            if connection_id:
                # Return status for a specific connection
                connection = self.connections[connection_id]
                return web.json_response({
                    "status": "success",
                    "connection": {
                        "id": connection["id"],
                        "name": connection["name"],
                        "service_type": connection["service_type"],
                        "status": connection["status"]
                    }
                })
            else:
                # Return status for all connections
                connection_statuses = [{
                    "id": conn["id"],
                    "name": conn["name"],
                    "service_type": conn["service_type"],
                    "status": conn["status"]
                } for conn in self.connections.values()]
                
                return web.json_response({
                    "status": "success",
                    "connections": connection_statuses
                })
            
        except Exception as e:
            logger.error(f"Error getting connection status: {e}")
            return web.json_response({
                "status": "error",
                "message": str(e)
            }, status=500)
            
    def _load_integrations(self):
        """Load integrations from file"""
        integrations_file = Path("integrations.json")
        if integrations_file.exists():
            try:
                with open(integrations_file, "r") as f:
                    self.integrations = json.load(f)
                logger.info(f"Loaded {len(self.integrations)} integrations from file")
            except Exception as e:
                logger.error(f"Error loading integrations: {e}")
                
    def _save_integrations(self):
        """Save integrations to file"""
        try:
            with open("integrations.json", "w") as f:
                json.dump(self.integrations, f, indent=2)
            logger.info(f"Saved {len(self.integrations)} integrations to file")
        except Exception as e:
            logger.error(f"Error saving integrations: {e}")
            
    async def start(self):
        """Start the server"""
        runner = web.AppRunner(self.app)
        await runner.setup()
        site = web.TCPSite(runner, self.host, self.port)
        
        logger.info(f"Starting Integration MCP Server on {self.host}:{self.port}")
        await site.start()
        
        # Start connection status monitoring
        asyncio.create_task(self._monitor_connections())
        
        return site
        
    async def _monitor_connections(self):
        """Background task to monitor connection status"""
        while True:
            try:
                for connection_id, connection in self.connections.items():
                    if connection["status"] == "created":
                        # Attempt to connect
                        try:
                            # Placeholder for actual connection logic
                            # In a real implementation, this would attempt to connect to the service
                            connection["status"] = "connected"
                            logger.info(f"Connected to {connection['name']} ({connection_id})")
                        except Exception as e:
                            connection["status"] = "error"
                            connection["error"] = str(e)
                            logger.error(f"Error connecting to {connection['name']} ({connection_id}): {e}")
            except Exception as e:
                logger.error(f"Error monitoring connections: {e}")
                
            # Sleep for a while before checking again
            await asyncio.sleep(60)  # Check every minute

def main():
    """Main entry point"""
    parser = argparse.ArgumentParser(description="Integration MCP Server")
    parser.add_argument("--host", default="localhost", help="Host to bind to")
    parser.add_argument("--port", type=int, default=8089, help="Port to bind to")
    args = parser.parse_args()
    
    server = IntegrationServer(host=args.host, port=args.port)
    
    loop = asyncio.get_event_loop()
    try:
        loop.run_until_complete(server.start())
        loop.run_forever()
    except KeyboardInterrupt:
        logger.info("Server stopped by keyboard interrupt")
    finally:
        loop.close()

if __name__ == "__main__":
    main()
