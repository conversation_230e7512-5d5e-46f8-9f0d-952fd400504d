"""
<PERSON> Templates

This module provides test communication templates for <PERSON>,
including call scripts, voicemail scripts, text messages, and emails.
"""

import datetime
from typing import Dict

# <PERSON> client data
PAUL_EDWARDS = {
    "first_name": "<PERSON>",
    "last_name": "<PERSON>",
    "age": 32,
    "gender": "Male",
    "email": "<EMAIL>",
    "phone": "**********",
    "address": "2426 SE Whitehorse Street",
    "city": "Port Saint Lucie",
    "state": "FL",
    "zip": "34983",
    "income": 85000,
    "budget": 200,
    "has_mortgage": True,
    "mortgage_amount": 250000,
    "retirement_focus": True,
    "health_status": "good",
    "tobacco_use": False,
    "product_type": "indexed_universal_life"
}

# Agent information
AGENT_INFO = {
    "name": "<PERSON>",
    "agency": "Elite Insurance Solutions",
    "phone": "************",
    "email": "<EMAIL>"
}

# Communication templates
class PaulEdwardsCommunications:
    """Communication templates for <PERSON>"""
    
    @staticmethod
    def get_call_script() -> str:
        """Get personalized call script for <PERSON>"""
        
        script = f"""
CALL SCRIPT FOR PAUL EDWARDS

Introduction:
"<PERSON> <PERSON>, this is {AGENT_INFO['name']} with {AGENT_INFO['agency']}. How are you doing today? [PAUSE FOR RESPONSE]

I'm reaching out because I help people like you create tax-free retirement income without the risk of the stock market. Based on your profile, I've found that you might qualify for a strategy that could provide around $5,000 per month in tax-free retirement income. Would that be something you'd be interested in learning more about? [PAUSE FOR RESPONSE]

Great! I've prepared a personalized analysis for you that shows how an Indexed Universal Life policy could help you build tax-free retirement income while also providing a death benefit of around $720,000 to protect your family.

I'd like to schedule about 30 minutes to walk you through this analysis and answer any questions you might have. Would Tuesday at 2:00 PM or Thursday at 4:00 PM work better for your schedule? [PAUSE FOR RESPONSE]

Excellent! I'll send you a calendar invitation with all the details. Before we wrap up, do you have any initial questions about how this strategy works? [PAUSE FOR RESPONSE]

[ADDRESS ANY QUESTIONS]

I'm looking forward to our conversation on [CONFIRMED DATE/TIME]. In the meantime, I'll send you a brief overview via email so you can get familiar with the concept. Thank you for your time today, Paul, and have a great day!"

Key Talking Points:
- Tax-free retirement income potential
- Protection from market downturns
- Death benefit protection for family
- Cash value growth potential
- Flexibility of the policy

Objection Handling:
- "I need to think about it" → "I understand completely. This is an important decision. The meeting is just to provide information so you can make an informed decision. There's no obligation, and I won't be asking you to make any decisions during our call."
- "I already have retirement plans" → "That's great! Many of my clients use this strategy to complement their existing retirement plans, especially to create a tax-free income stream. Would it be worth 30 minutes to see if this could enhance what you're already doing?"
- "Is this whole life insurance?" → "No, this is Indexed Universal Life, which is quite different. It provides both death benefit protection and cash value growth potential linked to market indexes, but without the risk of market losses. I'd be happy to explain the differences in our meeting."
"""
        
        return script
    
    @staticmethod
    def get_voicemail_script() -> str:
        """Get personalized voicemail script for Paul Edwards"""
        
        script = f"""
VOICEMAIL SCRIPT FOR PAUL EDWARDS

"Hi Paul, this is {AGENT_INFO['name']} with {AGENT_INFO['agency']}. I'm calling because I help people like you create tax-free retirement income without the risk of the stock market.

Based on your profile, I've found that you might qualify for a strategy that could provide significant tax-free retirement income while also protecting your family with a death benefit.

I'd love to share a personalized analysis I've prepared for you. Please give me a call back at {AGENT_INFO['phone']} when you have a moment.

Again, this is {AGENT_INFO['name']} at {AGENT_INFO['phone']}. I look forward to speaking with you soon. Thank you!"
"""
        
        return script
    
    @staticmethod
    def get_text_message() -> str:
        """Get personalized text message for Paul Edwards"""
        
        message = f"""
TEXT MESSAGE FOR PAUL EDWARDS

"Hi Paul, this is {AGENT_INFO['name']} with {AGENT_INFO['agency']}. I help people create tax-free retirement income without stock market risk. I've prepared a personalized analysis for you that shows how you could potentially generate $5,000/month in tax-free retirement income. Would you be interested in seeing this analysis? I'm available at {AGENT_INFO['phone']}."
"""
        
        return message
    
    @staticmethod
    def get_email() -> Dict[str, str]:
        """Get personalized email for Paul Edwards"""
        
        email = {
            "to": PAUL_EDWARDS["email"],
            "from": AGENT_INFO["email"],
            "subject": "Creating Tax-Free Retirement Income Without Market Risk",
            "body": f"""
Dear Paul,

I hope this email finds you well. My name is {AGENT_INFO['name']} with {AGENT_INFO['agency']}, and I specialize in helping people like you create tax-free retirement income without the risk of the stock market.

Based on the information I have, I've prepared a personalized analysis that shows how you could potentially:

1. Generate approximately $5,000 per month in tax-free retirement income
2. Build cash value that grows without the risk of market losses
3. Provide a death benefit of around $720,000 to protect your family
4. Create a financial strategy that offers both protection and growth potential

Many of my clients are concerned about:
- Market volatility affecting their retirement savings
- Taxes reducing their retirement income
- Having enough money to maintain their lifestyle in retirement
- Protecting their family financially

The strategy I'd like to share with you addresses all these concerns through an Indexed Universal Life policy specifically designed for your situation.

Would you be interested in a brief 30-minute conversation to review this analysis? If so, please call me at {AGENT_INFO['phone']} or reply to this email with a good time to connect.

I've attached a brief overview of how Indexed Universal Life works for your reference.

Thank you for your time, and I look forward to speaking with you soon.

Best regards,
{AGENT_INFO['name']}
{AGENT_INFO['agency']}
{AGENT_INFO['phone']}
{AGENT_INFO['email']}
"""
        }
        
        return email
    
    @staticmethod
    def get_follow_up_email() -> Dict[str, str]:
        """Get personalized follow-up email for Paul Edwards"""
        
        email = {
            "to": PAUL_EDWARDS["email"],
            "from": AGENT_INFO["email"],
            "subject": "Following Up - Tax-Free Retirement Strategy",
            "body": f"""
Dear Paul,

I hope this email finds you well. I'm following up on my previous message about creating tax-free retirement income without the risk of the stock market.

I wanted to check if you've had a chance to review the information I shared and see if you have any questions I can answer.

Additionally, I came across some information that I thought would be particularly relevant to your situation:

- A recent study showed that people who incorporate tax-free strategies into their retirement planning often end up with 20-30% more spendable income compared to traditional retirement accounts.
- For someone in your age group (32), starting an IUL policy now could potentially result in over $1 million in tax-free retirement income over a 20-year retirement period.

I'm available to discuss this further at your convenience. Feel free to call me at {AGENT_INFO['phone']} or reply to this email with a good time to connect.

Thank you for your time, and I look forward to speaking with you soon.

Best regards,
{AGENT_INFO['name']}
{AGENT_INFO['agency']}
{AGENT_INFO['phone']}
{AGENT_INFO['email']}
"""
        }
        
        return email
    
    @staticmethod
    def get_appointment_confirmation_text() -> str:
        """Get personalized appointment confirmation text for Paul Edwards"""
        
        # Set appointment date to next Tuesday at 2:00 PM
        today = datetime.datetime.now()
        days_until_tuesday = (1 - today.weekday()) % 7 + 7  # Next Tuesday
        appointment_date = today + datetime.timedelta(days=days_until_tuesday)
        formatted_date = appointment_date.strftime("%A, %B %d")
        
        message = f"""
APPOINTMENT CONFIRMATION TEXT FOR PAUL EDWARDS

"Hi Paul, this is {AGENT_INFO['name']} confirming our appointment on {formatted_date} at 2:00 PM to discuss your personalized tax-free retirement income strategy. Please let me know if you need to reschedule. Looking forward to our conversation!"
"""
        
        return message
    
    @staticmethod
    def run_test_communications():
        """Print all test communications for Paul Edwards"""
        
        print("=" * 80)
        print("TEST COMMUNICATIONS FOR PAUL EDWARDS")
        print("=" * 80)
        
        print("\n1. CALL SCRIPT:")
        print("-" * 80)
        print(PaulEdwardsCommunications.get_call_script())
        
        print("\n2. VOICEMAIL SCRIPT:")
        print("-" * 80)
        print(PaulEdwardsCommunications.get_voicemail_script())
        
        print("\n3. TEXT MESSAGE:")
        print("-" * 80)
        print(PaulEdwardsCommunications.get_text_message())
        
        print("\n4. EMAIL:")
        print("-" * 80)
        email = PaulEdwardsCommunications.get_email()
        print(f"To: {email['to']}")
        print(f"From: {email['from']}")
        print(f"Subject: {email['subject']}")
        print(f"Body:\n{email['body']}")
        
        print("\n5. FOLLOW-UP EMAIL:")
        print("-" * 80)
        follow_up = PaulEdwardsCommunications.get_follow_up_email()
        print(f"To: {follow_up['to']}")
        print(f"From: {follow_up['from']}")
        print(f"Subject: {follow_up['subject']}")
        print(f"Body:\n{follow_up['body']}")
        
        print("\n6. APPOINTMENT CONFIRMATION TEXT:")
        print("-" * 80)
        print(PaulEdwardsCommunications.get_appointment_confirmation_text())
        
        print("\n" + "=" * 80)
        print("END OF TEST COMMUNICATIONS")
        print("=" * 80)

# Run test communications if executed directly
if __name__ == "__main__":
    PaulEdwardsCommunications.run_test_communications()
