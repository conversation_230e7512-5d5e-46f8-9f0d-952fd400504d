#!/bin/bash

# Colors for better readability
GREEN='\033[0;32m'
BLUE='\033[0;34m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color
BOLD='\033[1m'

echo -e "${BLUE}${BOLD}=== Paul Edwards AI Project Transfer Script ===${NC}"
echo -e "${YELLOW}This script will set up your new computer with all the necessary tools and code.${NC}\n"

# Create project directory
echo -e "${BLUE}Creating project directory...${NC}"
mkdir -p ~/AIAgentProjects
cd ~/AIAgentProjects

# Clone repository (assuming it's on GitHub, adjust the URL as needed)
echo -e "${BLUE}Cloning repository...${NC}"
read -p "Enter your repository URL (or press Enter to clone manually later): " REPO_URL

if [ -n "$REPO_URL" ]; then
  git clone "$REPO_URL" PaulEdwardsAI
  cd PaulEdwardsAI
else
  # Create the directory and prepare for manual copy
  mkdir -p PaulEdwardsAI
  cd PaulEdwardsAI
  echo -e "${YELLOW}You'll need to manually copy your project files to $(pwd)${NC}"
fi

# Check for Python
echo -e "${BLUE}Setting up Python environment...${NC}"
if ! command -v python3 &> /dev/null; then
  echo -e "${RED}Python not found. Installing Python...${NC}"
  if [[ "$OSTYPE" == "darwin"* ]]; then
    # macOS
    brew install python
  elif [[ "$OSTYPE" == "linux-gnu"* ]]; then
    # Linux
    sudo apt update && sudo apt install -y python3 python3-pip python3-venv
  else
    echo -e "${RED}Unsupported OS. Please install Python manually.${NC}"
  fi
else
  echo -e "${GREEN}Python found!${NC}"
fi

# Create virtual environment
echo -e "${BLUE}Creating Python virtual environment...${NC}"
python3 -m venv venv
source venv/bin/activate

# Install dependencies
echo -e "${BLUE}Installing Python dependencies...${NC}"
if [ -f "requirements.txt" ]; then
  pip install -r requirements.txt
else
  echo -e "${YELLOW}No requirements.txt found. Creating one with basic dependencies...${NC}"
  cat > requirements.txt << 'REQS'
requests
python-dotenv
cryptography
pydantic
elevenlabs
openai
langchain
REQS
  pip install -r requirements.txt
fi

# Install security tools based on OS
echo -e "${BLUE}Installing security tools...${NC}"
if [[ "$OSTYPE" == "darwin"* ]]; then
  # macOS
  echo -e "${YELLOW}Running macOS security tools installer...${NC}"
  if [ -f "install_security_tools_macos.sh" ]; then
    bash install_security_tools_macos.sh
  else
    echo -e "${RED}install_security_tools_macos.sh not found. Skipping.${NC}"
  fi
elif [[ "$OSTYPE" == "linux-gnu"* ]]; then
  # Linux
  echo -e "${YELLOW}Running Linux security tools installer...${NC}"
  if [ -f "install_security_tools.sh" ]; then
    bash install_security_tools.sh
  else
    echo -e "${RED}install_security_tools.sh not found. Skipping.${NC}"
  fi
else
  echo -e "${RED}Unsupported OS. Please install security tools manually.${NC}"
fi

# Set up Docker if needed
echo -e "${BLUE}Setting up Docker...${NC}"
if [ -f "docker-compose.yml" ]; then
  if ! command -v docker &> /dev/null; then
    echo -e "${YELLOW}Docker not found. Please install Docker manually.${NC}"
    echo -e "Visit https://docs.docker.com/get-docker/ for installation instructions."
  else
    echo -e "${GREEN}Docker found! Building containers...${NC}"
    docker-compose build
  fi
fi

# Set up MCP servers
echo -e "${BLUE}Setting up MCP servers...${NC}"
if [ -f "start_mcp_servers.py" ]; then
  python start_mcp_servers.py
else
  echo -e "${YELLOW}MCP server startup script not found. Skipping.${NC}"
fi

# Ask for API keys
echo -e "${BLUE}${BOLD}=== Configuration ===${NC}"
echo -e "${YELLOW}Setting up environment variables...${NC}"

# Create .env file if it doesn't exist
if [ ! -f ".env" ]; then
  cat > .env << 'ENVFILE'
# OpenAI Configuration
OPENAI_API_KEY=

# ElevenLabs Configuration
ELEVENLABS_API_KEY=

# Email Configuration
EMAIL_USERNAME=
EMAIL_PASSWORD=
EMAIL_SERVER=
EMAIL_PORT=

# Phone/SMS Configuration
VONAGE_API_KEY=
VONAGE_API_SECRET=

# Security Configuration
ENCRYPTION_KEY=
ENVFILE

  echo -e "${YELLOW}Created .env file. Please fill in your API keys and credentials.${NC}"
  echo -e "${YELLOW}You can edit this file later with 'nano .env' or any text editor.${NC}"
fi

echo -e "\n${GREEN}${BOLD}Setup complete!${NC}"
echo -e "${YELLOW}Your project is now set up at: $(pwd)${NC}"
echo -e "${YELLOW}To activate the virtual environment: source venv/bin/activate${NC}"
echo -e "${YELLOW}To start the system: python main.py${NC}"
