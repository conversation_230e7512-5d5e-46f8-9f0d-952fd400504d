"""
Unified Agent Dashboard

This module provides a comprehensive dashboard that combines product recommendations
with sales strategies, client management, and performance tracking.
"""

import datetime
import logging
from typing import Dict, List, Optional, Tuple

# Import from other modules
from enhanced_iul_calculator import EnhancedIULCalculator
from enhanced_underwriting_criteria import evaluate_client_health_rating
from carrier_knockout_guides import find_best_product_type_for_client, get_best_product_matches
from cody_askins_strategies import CodyAskinsStrategies
from cody_askins_implementation import CodyAskinsImplementation

logger = logging.getLogger(__name__)

class UnifiedAgentDashboard:
    """Unified dashboard for insurance agents combining all enhanced systems"""
    
    def __init__(self):
        """Initialize the dashboard with all components"""
        self.iul_calculator = EnhancedIULCalculator()
        self.askins_strategies = CodyAskinsStrategies()
        self.askins_implementation = CodyAskinsImplementation()
        
        # Dashboard sections
        self.sections = [
            "client_recommendations",
            "sales_strategies",
            "lead_generation",
            "performance_metrics",
            "communication_templates"
        ]
        
        # Communication templates
        self.communication_templates = {
            "initial_contact": {
                "call_script": {
                    "iul": "Hi [CLIENT_NAME], this is [AGENT_NAME] with [AGENCY]. I'm reaching out because I help people like you create tax-free retirement income without the risk of the stock market. Would an extra [RETIREMENT_AMOUNT] per month in tax-free retirement income be helpful? [PAUSE] Great! I'd like to schedule 20 minutes to show you how this might work for your specific situation. Would [DAY] at [TIME] or [DAY] at [TIME] work better for you?",
                    "medicare": "Hello [CLIENT_NAME], this is [AGENT_NAME] with [AGENCY]. I specialize in helping people navigate their Medicare options to find the best coverage while minimizing out-of-pocket costs. Many people I work with save hundreds of dollars a month while improving their coverage. I'd like to offer you a free Medicare review to see if we can do the same for you. Would [DAY] at [TIME] or [DAY] at [TIME] work better for you?",
                    "final_expense": "Hi [CLIENT_NAME], this is [AGENT_NAME] with [AGENCY]. I'm calling because I help people ensure their final expenses are taken care of so their families don't face financial burden during a difficult time. I'd like to share some options that many of my clients have found very affordable. Would [DAY] at [TIME] or [DAY] at [TIME] work better for you?"
                },
                "voicemail": {
                    "iul": "Hi [CLIENT_NAME], this is [AGENT_NAME] with [AGENCY]. I'm reaching out because I help people like you create tax-free retirement income without the risk of the stock market. I'd love to share some strategies that might benefit you. Please give me a call back at [PHONE_NUMBER]. Again, this is [AGENT_NAME] at [PHONE_NUMBER]. I look forward to speaking with you!",
                    "medicare": "Hello [CLIENT_NAME], this is [AGENT_NAME] with [AGENCY]. I'm calling about your Medicare options. Many people I work with save hundreds of dollars a month while improving their coverage. I'd like to offer you a free Medicare review. Please call me back at [PHONE_NUMBER]. Again, this is [AGENT_NAME] at [PHONE_NUMBER]. Thank you!",
                    "final_expense": "Hi [CLIENT_NAME], this is [AGENT_NAME] with [AGENCY]. I'm calling about affordable options to cover final expenses so your family doesn't face financial burden during a difficult time. Please call me back at [PHONE_NUMBER]. Again, this is [AGENT_NAME] at [PHONE_NUMBER]. Thank you!"
                },
                "text_message": {
                    "iul": "Hi [CLIENT_NAME], this is [AGENT_NAME] with [AGENCY]. I help people create tax-free retirement income without stock market risk. Would you be interested in learning more? I'm available at [PHONE_NUMBER].",
                    "medicare": "Hello [CLIENT_NAME], this is [AGENT_NAME] with [AGENCY]. I'd like to offer you a free Medicare review that could save you money while improving coverage. Please call me at [PHONE_NUMBER] if interested.",
                    "final_expense": "Hi [CLIENT_NAME], this is [AGENT_NAME] with [AGENCY]. I help families protect themselves from the high costs of final expenses with affordable coverage options. Call me at [PHONE_NUMBER] to learn more."
                },
                "email": {
                    "iul": {
                        "subject": "Creating Tax-Free Retirement Income Without Market Risk",
                        "body": """
Dear [CLIENT_NAME],

I hope this email finds you well. My name is [AGENT_NAME] with [AGENCY], and I specialize in helping people like you create tax-free retirement income without the risk of the stock market.

Many of my clients are concerned about:
- Market volatility affecting their retirement savings
- Taxes reducing their retirement income
- Having enough money to maintain their lifestyle in retirement

I have strategies that address all these concerns while providing protection for your loved ones.

Would you be interested in a brief 20-minute conversation to see if these strategies might benefit you? If so, please call me at [PHONE_NUMBER] or reply to this email with a good time to connect.

Thank you for your time, and I look forward to speaking with you soon.

Best regards,
[AGENT_NAME]
[AGENCY]
[PHONE_NUMBER]
[EMAIL]
                        """
                    },
                    "medicare": {
                        "subject": "Your Medicare Options - Free Review Available",
                        "body": """
Dear [CLIENT_NAME],

I hope this email finds you well. My name is [AGENT_NAME] with [AGENCY], and I specialize in helping people navigate their Medicare options.

Many people I work with are concerned about:
- Rising healthcare costs
- Gaps in Medicare coverage
- Finding the right plan for their specific health needs
- Overpaying for coverage they don't need

I offer a free, no-obligation Medicare review to help you understand your options and potentially save money while improving your coverage.

Would you be interested in scheduling a brief conversation? If so, please call me at [PHONE_NUMBER] or reply to this email with a good time to connect.

Thank you for your time, and I look forward to helping you navigate your Medicare options.

Best regards,
[AGENT_NAME]
[AGENCY]
[PHONE_NUMBER]
[EMAIL]
                        """
                    },
                    "final_expense": {
                        "subject": "Protecting Your Family From Final Expenses",
                        "body": """
Dear [CLIENT_NAME],

I hope this email finds you well. My name is [AGENT_NAME] with [AGENCY], and I specialize in helping people ensure their final expenses are taken care of.

Many families face unexpected financial burdens during an already difficult time. The average funeral now costs between $7,000 and $12,000, and that doesn't include other end-of-life expenses.

I help people find affordable coverage options that:
- Cover funeral and burial costs
- Pay off remaining medical bills
- Provide a financial cushion for loved ones
- Give peace of mind knowing these expenses are taken care of

Would you be interested in learning about these affordable options? If so, please call me at [PHONE_NUMBER] or reply to this email with a good time to connect.

Thank you for your time, and I look forward to speaking with you soon.

Best regards,
[AGENT_NAME]
[AGENCY]
[PHONE_NUMBER]
[EMAIL]
                        """
                    }
                }
            },
            "follow_up": {
                "call_script": {
                    "general": "Hi [CLIENT_NAME], this is [AGENT_NAME] with [AGENCY] following up on our conversation about [TOPIC]. I wanted to check if you've had a chance to review the information I sent and see if you have any questions I can answer. [PAUSE] Also, I came across some additional information that I thought might be helpful for your situation. Would you like me to share that with you?",
                },
                "voicemail": {
                    "general": "Hi [CLIENT_NAME], this is [AGENT_NAME] with [AGENCY] following up on our conversation about [TOPIC]. I wanted to check if you've had a chance to review the information I sent and see if you have any questions. I also have some additional information that might be helpful for your situation. Please give me a call back at [PHONE_NUMBER] when you have a moment. Again, this is [AGENT_NAME] at [PHONE_NUMBER]. Thank you!",
                },
                "text_message": {
                    "general": "Hi [CLIENT_NAME], this is [AGENT_NAME] with [AGENCY] following up on [TOPIC]. Have you had a chance to review the information? I have some additional insights that might help. Call me at [PHONE_NUMBER] when convenient.",
                },
                "email": {
                    "general": {
                        "subject": "Following Up - [TOPIC]",
                        "body": """
Dear [CLIENT_NAME],

I hope this email finds you well. I'm following up on our recent conversation about [TOPIC].

I wanted to check if you've had a chance to review the information I shared and see if you have any questions I can answer.

Additionally, I came across some information that I thought would be particularly relevant to your situation:

[ADDITIONAL_INFO]

I'm available to discuss this further at your convenience. Feel free to call me at [PHONE_NUMBER] or reply to this email with a good time to connect.

Thank you for your time, and I look forward to speaking with you again soon.

Best regards,
[AGENT_NAME]
[AGENCY]
[PHONE_NUMBER]
[EMAIL]
                        """
                    }
                }
            },
            "appointment_confirmation": {
                "text_message": {
                    "general": "Hi [CLIENT_NAME], this is [AGENT_NAME] confirming our appointment on [DATE] at [TIME]. Please let me know if you need to reschedule. Looking forward to our conversation!",
                },
                "email": {
                    "general": {
                        "subject": "Appointment Confirmation - [DATE]",
                        "body": """
Dear [CLIENT_NAME],

This is a friendly reminder of our appointment scheduled for:

Date: [DATE]
Time: [TIME]
Topic: [TOPIC]

[ADDITIONAL_INSTRUCTIONS]

If you need to reschedule, please call me at [PHONE_NUMBER] or reply to this email.

I look forward to our conversation!

Best regards,
[AGENT_NAME]
[AGENCY]
[PHONE_NUMBER]
[EMAIL]
                        """
                    }
                }
            }
        }
    
    def get_client_recommendations(self, client_data: Dict) -> Dict:
        """
        Get comprehensive product recommendations for a client
        
        Args:
            client_data: Dictionary with client information
            
        Returns:
            Dictionary with detailed recommendations
        """
        # Find best product type
        product_type = find_best_product_type_for_client(client_data)
        
        # Get specific product matches
        product_matches = get_best_product_matches(client_data, product_type)
        
        # Get health rating for top carriers
        health_ratings = {}
        for match in product_matches[:3]:  # Top 3 matches
            carrier = match["carrier"]
            health_rating = evaluate_client_health_rating(client_data, carrier)
            health_ratings[carrier] = health_rating
        
        # If IUL is recommended, get detailed quote
        iul_quote = None
        if product_type == "indexed_universal_life" and product_matches:
            # Add recommended carrier/product to client data
            client_data_with_rec = client_data.copy()
            client_data_with_rec["recommended_carrier"] = product_matches[0]["carrier"]
            client_data_with_rec["recommended_product"] = product_matches[0]["product"]
            
            # Generate detailed quote
            iul_quote = self.iul_calculator.calculate_iul_quote(client_data_with_rec)
        
        # Get sales strategy recommendations
        sales_strategy = self.askins_strategies.get_recommended_strategy(product_type, client_data.get("budget", 500))
        
        # Get sales script
        if product_type == "indexed_universal_life":
            sales_script = self.askins_strategies.get_sales_script("iul", client_data)
        else:
            sales_script = self.askins_strategies.get_sales_script("default", client_data)
        
        # Compile recommendations
        recommendations = {
            "client_name": f"{client_data.get('first_name', '')} {client_data.get('last_name', '')}",
            "client_age": client_data.get("age", 0),
            "recommended_product_type": product_type,
            "product_matches": product_matches[:5],  # Top 5 matches
            "health_ratings": health_ratings,
            "iul_quote": iul_quote,
            "sales_strategy": sales_strategy,
            "sales_script": sales_script,
            "next_steps": [
                "Schedule initial appointment",
                "Prepare personalized presentation",
                "Set up follow-up sequence",
                "Prepare application materials"
            ]
        }
        
        return recommendations
    
    def get_communication_templates(self, client_data: Dict, template_type: str, product_type: str) -> Dict:
        """
        Get communication templates for a specific client and product
        
        Args:
            client_data: Dictionary with client information
            template_type: Type of template (initial_contact, follow_up, etc.)
            product_type: Type of product (iul, medicare, etc.)
            
        Returns:
            Dictionary with personalized templates
        """
        if template_type not in self.communication_templates:
            return {"error": f"Template type {template_type} not found"}
        
        templates = self.communication_templates[template_type]
        personalized = {}
        
        # Prepare replacement values
        replacements = {
            "[CLIENT_NAME]": f"{client_data.get('first_name', '')} {client_data.get('last_name', '')}",
            "[AGENT_NAME]": client_data.get("agent_name", "Your Agent"),
            "[AGENCY]": client_data.get("agency_name", "Insurance Agency"),
            "[PHONE_NUMBER]": client_data.get("agent_phone", "************"),
            "[EMAIL]": client_data.get("agent_email", "<EMAIL>"),
            "[TOPIC]": product_type.upper(),
            "[DATE]": (datetime.datetime.now() + datetime.timedelta(days=2)).strftime("%A, %B %d"),
            "[TIME]": "2:00 PM",
            "[DAY]": (datetime.datetime.now() + datetime.timedelta(days=2)).strftime("%A"),
            "[ADDITIONAL_INFO]": "Some additional information relevant to your situation...",
            "[ADDITIONAL_INSTRUCTIONS]": "Please have your current policy information available if possible."
        }
        
        # Add product-specific replacements
        if product_type == "iul":
            age = client_data.get("age", 40)
            income = client_data.get("income", 75000)
            monthly_retirement = round((income * 0.7) / 12, -2)  # 70% income replacement rounded to nearest 100
            replacements["[RETIREMENT_AMOUNT]"] = f"${monthly_retirement:,}"
        
        # Personalize each template type
        for comm_type, templates_by_type in templates.items():
            if comm_type not in personalized:
                personalized[comm_type] = {}
            
            # Get the right template for this product type
            if product_type in templates_by_type:
                template = templates_by_type[product_type]
            elif "general" in templates_by_type:
                template = templates_by_type["general"]
            else:
                continue
            
            # Handle different template formats
            if isinstance(template, str):
                # Simple string template
                personalized_text = template
                for key, value in replacements.items():
                    personalized_text = personalized_text.replace(key, str(value))
                personalized[comm_type] = personalized_text
            elif isinstance(template, dict) and "subject" in template and "body" in template:
                # Email template with subject and body
                personalized_subject = template["subject"]
                personalized_body = template["body"]
                
                for key, value in replacements.items():
                    personalized_subject = personalized_subject.replace(key, str(value))
                    personalized_body = personalized_body.replace(key, str(value))
                
                personalized[comm_type] = {
                    "subject": personalized_subject,
                    "body": personalized_body
                }
        
        return personalized
    
    def get_performance_dashboard(self, agent_id: str, date_range: Tuple[datetime.date, datetime.date] = None) -> Dict:
        """
        Get performance metrics dashboard for an agent
        
        Args:
            agent_id: Agent identifier
            date_range: Optional date range (start_date, end_date)
            
        Returns:
            Dictionary with performance metrics
        """
        # This would normally pull from a database - using mock data for example
        if not date_range:
            end_date = datetime.date.today()
            start_date = end_date - datetime.timedelta(days=30)
        else:
            start_date, end_date = date_range
        
        # Mock performance data
        performance = {
            "agent_id": agent_id,
            "date_range": {
                "start": start_date.strftime("%Y-%m-%d"),
                "end": end_date.strftime("%Y-%m-%d"),
                "days": (end_date - start_date).days
            },
            "lead_metrics": {
                "new_leads": 120,
                "contacted_leads": 108,
                "contact_rate": 0.90,
                "appointments_set": 32,
                "appointment_rate": 0.30,
                "shows": 28,
                "show_rate": 0.88
            },
            "sales_metrics": {
                "applications": 18,
                "application_rate": 0.64,
                "issued_policies": 15,
                "issue_rate": 0.83,
                "total_premium": 36000,
                "average_premium": 2400
            },
            "product_breakdown": {
                "iul": {
                    "applications": 8,
                    "issued": 7,
                    "premium": 22400,
                    "average_premium": 3200
                },
                "term": {
                    "applications": 6,
                    "issued": 5,
                    "premium": 6000,
                    "average_premium": 1200
                },
                "final_expense": {
                    "applications": 4,
                    "issued": 3,
                    "premium": 7600,
                    "average_premium": 2533
                }
            },
            "key_ratios": {
                "lead_to_sale": 0.125,
                "cost_per_lead": 12.50,
                "cost_per_sale": 100.00,
                "roi": 24.00  # 24x return on lead spend
            },
            "comparison_to_goals": {
                "leads": {
                    "goal": 150,
                    "actual": 120,
                    "percentage": 0.80
                },
                "appointments": {
                    "goal": 45,
                    "actual": 32,
                    "percentage": 0.71
                },
                "sales": {
                    "goal": 20,
                    "actual": 15,
                    "percentage": 0.75
                },
                "premium": {
                    "goal": 40000,
                    "actual": 36000,
                    "percentage": 0.90
                }
            },
            "improvement_opportunities": [
                {
                    "metric": "Contact rate",
                    "current": 0.90,
                    "target": 0.95,
                    "impact": "+2 sales per month",
                    "action_items": [
                        "Implement SMS pre-call notification",
                        "Adjust calling times to evening hours",
                        "Use multiple contact methods simultaneously"
                    ]
                },
                {
                    "metric": "Appointment rate",
                    "current": 0.30,
                    "target": 0.40,
                    "impact": "+3 sales per month",
                    "action_items": [
                        "Refine value proposition in initial call",
                        "Use social proof more effectively",
                        "Implement Cody Askins' 8% framework"
                    ]
                }
            ]
        }
        
        return performance

# Example usage
if __name__ == "__main__":
    dashboard = UnifiedAgentDashboard()
    
    # Test client
    test_client = {
        "first_name": "Paul",
        "last_name": "Edwards",
        "age": 32,
        "gender": "Male",
        "email": "<EMAIL>",
        "phone": "**********",
        "health_class": "Preferred",
        "income": 85000,
        "budget": 200,
        "tobacco_use": False,
        "bmi": 24.5,
        "has_mortgage": True,
        "mortgage_amount": 250000,
        "retirement_focus": True,
        "risk_tolerance": "moderate",
        "health_status": "good",
        "agent_name": "Sandra Smith",
        "agency_name": "Elite Insurance Solutions",
        "agent_phone": "************",
        "agent_email": "<EMAIL>"
    }
    
    # Get client recommendations
    recommendations = dashboard.get_client_recommendations(test_client)
    
    print(f"Recommendations for {recommendations['client_name']} (Age {recommendations['client_age']}):")
    print(f"Recommended Product Type: {recommendations['recommended_product_type']}")
    
    print("\nTop Product Matches:")
    for i, match in enumerate(recommendations['product_matches'][:3], 1):
        print(f"{i}. {match['carrier'].title()} - {match['product'].replace('_', ' ').title()} (Match Score: {match['match_score']:.2f})")
    
    # Get communication templates
    product_type = "iul"  # Based on recommendation
    templates = dashboard.get_communication_templates(test_client, "initial_contact", product_type)
    
    print("\nPersonalized Call Script:")
    print(templates.get("call_script", "No template available"))
    
    print("\nPersonalized Voicemail Script:")
    print(templates.get("voicemail", "No template available"))
    
    print("\nPersonalized Text Message:")
    print(templates.get("text_message", "No template available"))
    
    if "email" in templates:
        print("\nPersonalized Email:")
        print(f"Subject: {templates['email'].get('subject', '')}")
        print(f"Body: {templates['email'].get('body', '')[:100]}...")
