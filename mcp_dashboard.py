#!/usr/bin/env python3
"""
MCP Dashboard - Manage and Monitor MCP Servers

This dashboard provides a user-friendly interface to:
1. View the status of all MCP servers
2. Configure new MCP servers
3. Test connections to MCP servers
4. Manage API keys for authenticated servers

Run this script to manage your MCP server connections.
"""

import os
import sys
import json
import asyncio
import argparse
import requests
import webbrowser
from datetime import datetime
import logging
from typing import Dict, List, Any, Optional

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger("MCP-Dashboard")

# Configuration file path
CONFIG_FILE = os.path.join(os.path.dirname(os.path.abspath(__file__)), "mcp_config.json")

# Default MCP servers to use when no config exists
from start_mcp_servers import MCP_SERVERS

class MCPDashboard:
    """Interactive dashboard for managing MCP servers"""
    
    def __init__(self):
        self.servers = self._load_config()
        self.active_servers = []
        
    def _load_config(self) -> List[Dict]:
        """Load MCP server configuration"""
        try:
            if os.path.exists(CONFIG_FILE):
                with open(CONFIG_FILE, 'r') as f:
                    config = json.load(f)
                    if 'servers' in config and isinstance(config['servers'], list):
                        return config['servers']
            
            # No config or invalid, use defaults
            return MCP_SERVERS
        except Exception as e:
            logger.error(f"Error loading configuration: {str(e)}")
            return MCP_SERVERS
    
    def _save_config(self) -> bool:
        """Save current server configuration"""
        try:
            config = {
                'servers': self.servers,
                'last_updated': datetime.now().isoformat()
            }
            with open(CONFIG_FILE, 'w') as f:
                json.dump(config, f, indent=4)
            return True
        except Exception as e:
            logger.error(f"Error saving configuration: {str(e)}")
            return False
    
    async def test_server_connection(self, server: Dict) -> bool:
        """Test connection to a server"""
        try:
            server_type = server.get('type', 'unknown')
            
            if server_type == 'public':
                # For public servers, just check if domain is reachable
                base_url = server['url'].split('://')[1].split('/')[0]
                url = f"https://{base_url}"
                
                # If authentication is required, check if we have the key
                if server.get('auth_required', False):
                    env_var = server.get('auth_env_var')
                    if env_var and not os.environ.get(env_var):
                        print(f"⚠️  Warning: {server['name']} requires authentication via {env_var} environment variable")
            else:
                # For local servers, check the health endpoint
                url = f"{server['url']}/health"
                
            print(f"Testing connection to {server['name']} ({url})...")
            response = requests.get(url, timeout=5)
            
            if response.status_code < 500:  # Any non-server error is good enough for a test
                print(f"✓ Connection successful to {server['name']}")
                return True
            else:
                print(f"✗ Failed to connect to {server['name']}: Status code {response.status_code}")
                return False
                
        except requests.exceptions.RequestException as e:
            print(f"✗ Failed to connect to {server['name']}: {str(e)}")
            return False
    
    async def check_all_servers(self) -> List[Dict]:
        """Check health of all configured servers"""
        active_servers = []
        
        print("\nChecking connection to all configured MCP servers...\n")
        
        for server in self.servers:
            is_active = await self.test_server_connection(server)
            if is_active:
                active_servers.append(server)
                try:
                    # Register with MCP registry if available
                    from mcp_server_registry import registry
                    await registry.initialize()
                    registry.register_server(
                        server['server_id'],
                        server['name'], 
                        server['url'],
                        server.get('capabilities', [])
                    )
                except ImportError:
                    pass
        
        self.active_servers = active_servers
        return active_servers
    
    def show_server_status(self):
        """Display status of all servers"""
        print("\n" + "="*60)
        print("MCP SERVER STATUS")
        print("="*60)
        
        for i, server in enumerate(self.servers, 1):
            is_active = server in self.active_servers
            status = "✓ ACTIVE" if is_active else "✗ INACTIVE"
            server_type = server.get('type', 'unknown').upper()
            
            print(f"[{i}] {status} - [{server_type}] {server['name']}")
            print(f"    URL: {server['url']}")
            print(f"    Capabilities: {', '.join(server.get('capabilities', []))}")
            
            # Print auth info for public servers
            if server.get('type') == 'public' and server.get('auth_required'):
                env_var = server.get('auth_env_var', '')
                env_set = "✓" if os.environ.get(env_var) else "✗"
                print(f"    Auth: {server.get('auth_type', 'required')} ({env_var} {env_set})")
            
            print()
            
        print(f"Summary: {len(self.active_servers)}/{len(self.servers)} servers active")
        print("="*60)
    
    def add_server(self):
        """Add a new MCP server to the configuration"""
        print("\n" + "="*60)
        print("ADD NEW MCP SERVER")
        print("="*60)
        
        # Get server details
        server_id = input("Server ID (unique identifier): ").strip()
        if not server_id:
            print("Server ID cannot be empty")
            return False
        
        # Check if server ID already exists
        if any(s['server_id'] == server_id for s in self.servers):
            print(f"Server ID '{server_id}' already exists")
            return False
        
        name = input("Server Name (human-readable): ").strip()
        if not name:
            name = server_id
        
        url = input("Server URL (e.g. http://localhost:8080): ").strip()
        if not url:
            print("Server URL cannot be empty")
            return False
        
        # Get server type
        print("\nServer Type:")
        print("1. Public Server (remote API)")
        print("2. Docker Container")
        print("3. Local Process")
        
        server_type_choice = input("Enter choice [1-3]: ").strip()
        if server_type_choice == '1':
            server_type = 'public'
        elif server_type_choice == '2':
            server_type = 'docker'
        elif server_type_choice == '3':
            server_type = 'local'
        else:
            server_type = 'unknown'
        
        # Additional details based on type
        server_details = {
            "server_id": server_id,
            "name": name,
            "url": url,
            "type": server_type,
            "capabilities": []
        }
        
        # Handle Docker-specific settings
        if server_type == 'docker':
            port = input("Port (e.g. 8080): ").strip()
            if port:
                server_details['port'] = int(port)
                
            container_name = input("Docker Container Name: ").strip()
            if container_name:
                start_command = f"docker run -d -p {port}:{port} --name {container_name} {container_name}:latest"
                server_details['start_command'] = start_command
        
        # Handle API authentication for public servers
        if server_type == 'public':
            auth_required = input("Does this server require authentication? (y/n): ").lower().startswith('y')
            if auth_required:
                server_details['auth_required'] = True
                server_details['auth_type'] = input("Auth Type [api_key, oauth, other]: ").strip() or 'api_key'
                server_details['auth_env_var'] = input("Environment Variable Name for Auth: ").strip()
        
        # Get server capabilities
        capabilities_input = input("Server Capabilities (comma-separated, e.g. text-completion,code-generation): ").strip()
        if capabilities_input:
            server_details['capabilities'] = [cap.strip() for cap in capabilities_input.split(',')]
        
        # Add server to the configuration
        self.servers.append(server_details)
        self._save_config()
        
        print(f"\nServer '{name}' has been added to the configuration")
        return True
    
    def remove_server(self):
        """Remove a server from the configuration"""
        self.show_server_status()
        
        try:
            choice = int(input("\nEnter server number to remove (0 to cancel): "))
            if choice == 0:
                return False
            
            if 1 <= choice <= len(self.servers):
                server = self.servers[choice-1]
                
                confirm = input(f"Are you sure you want to remove '{server['name']}'? (y/n): ")
                if confirm.lower().startswith('y'):
                    self.servers.pop(choice-1)
                    self._save_config()
                    print(f"\nServer '{server['name']}' has been removed")
                    return True
            else:
                print("Invalid selection")
                
        except ValueError:
            print("Please enter a valid number")
            
        return False
    
    def configure_auth(self):
        """Configure authentication for servers that require it"""
        auth_servers = [s for s in self.servers if s.get('type') == 'public' and s.get('auth_required')]
        
        if not auth_servers:
            print("\nNo servers require authentication")
            return False
        
        print("\n" + "="*60)
        print("CONFIGURE AUTHENTICATION")
        print("="*60)
        
        for i, server in enumerate(auth_servers, 1):
            env_var = server.get('auth_env_var', '')
            current_value = os.environ.get(env_var, '')
            masked_value = '*' * min(len(current_value), 8) if current_value else 'not set'
            
            print(f"[{i}] {server['name']}")
            print(f"    Auth Type: {server.get('auth_type', 'unknown')}")
            print(f"    Environment Variable: {env_var}")
            print(f"    Current Value: {masked_value}")
            print()
        
        try:
            choice = int(input("\nEnter server number to configure (0 to cancel): "))
            if choice == 0:
                return False
            
            if 1 <= choice <= len(auth_servers):
                server = auth_servers[choice-1]
                env_var = server.get('auth_env_var', '')
                
                if not env_var:
                    print("No environment variable specified for this server")
                    return False
                
                current_value = os.environ.get(env_var, '')
                print(f"\nConfiguring authentication for {server['name']}")
                print(f"Environment Variable: {env_var}")
                print(f"Current Value: {'*' * min(len(current_value), 8) if current_value else 'not set'}")
                
                # Get new value
                new_value = input("Enter new value (leave empty to keep current): ").strip()
                if new_value:
                    # Set environment variable
                    os.environ[env_var] = new_value
                    print(f"\nSet {env_var} environment variable")
                    
                    # Suggest adding to shell profile
                    shell_profile = os.path.expanduser("~/.zshrc" if os.path.exists(os.path.expanduser("~/.zshrc")) else "~/.bash_profile")
                    print(f"\nTo make this permanent, add the following to your {shell_profile}:")
                    print(f"export {env_var}='{new_value}'")
                    
                return True
            else:
                print("Invalid selection")
                
        except ValueError:
            print("Please enter a valid number")
            
        return False
    
    def discover_public_servers(self):
        """Attempt to discover additional public MCP servers"""
        print("\nDiscovering public MCP servers...")
        
        registry_api = next((s for s in self.active_servers if s['server_id'] == 'mcp-registry-api'), None)
        if not registry_api:
            print("MCP Registry API is not available")
            return False
        
        try:
            url = f"{registry_api['url']}/servers"
            print(f"Querying {url}...")
            
            response = requests.get(url, timeout=10)
            if response.status_code == 200:
                discovered = response.json().get('servers', [])
                print(f"Discovered {len(discovered)} servers")
                
                # Filter out servers we already have
                existing_ids = {s['server_id'] for s in self.servers}
                new_servers = [s for s in discovered if s['server_id'] not in existing_ids]
                
                if not new_servers:
                    print("All discovered servers are already in your configuration")
                    return False
                
                print(f"\nFound {len(new_servers)} new servers:")
                for i, server in enumerate(new_servers, 1):
                    print(f"[{i}] {server['name']} - {server['url']}")
                    print(f"    Capabilities: {', '.join(server.get('capabilities', []))}")
                    print()
                
                # Ask which servers to add
                choices = input("Enter server numbers to add (comma-separated, or 'all'): ").strip()
                if choices.lower() == 'all':
                    self.servers.extend(new_servers)
                    self._save_config()
                    print(f"Added {len(new_servers)} new servers")
                    return True
                
                try:
                    selected_indices = [int(c.strip()) - 1 for c in choices.split(',')]
                    servers_to_add = [new_servers[i] for i in selected_indices if 0 <= i < len(new_servers)]
                    
                    if servers_to_add:
                        self.servers.extend(servers_to_add)
                        self._save_config()
                        print(f"Added {len(servers_to_add)} new servers")
                        return True
                    
                except (ValueError, IndexError):
                    print("Invalid selection")
            else:
                print(f"Failed to query registry: {response.status_code}")
                
        except Exception as e:
            print(f"Error discovering servers: {str(e)}")
            
        return False
    
    async def register_mcp_client(self):
        """Register the MCP client with active servers"""
        from mcp_client import register_with_servers
        
        print("\nRegistering client with active MCP servers...")
        result = await register_with_servers(self.active_servers)
        
        if result:
            print("✓ Client registered with active servers")
            return True
        else:
            print("✗ Failed to register client")
            return False
    
    def show_help(self):
        """Show help information"""
        print("\n" + "="*60)
        print("MCP DASHBOARD HELP")
        print("="*60)
        print("This tool helps you manage Model Context Protocol (MCP) servers that")
        print("your agent system can use to integrate AI capabilities.")
        print("\nCommands:")
        print("  status     - Check connection to all MCP servers")
        print("  add        - Add a new MCP server")
        print("  remove     - Remove an MCP server")
        print("  auth       - Configure authentication for servers")
        print("  discover   - Discover new public MCP servers")
        print("  register   - Register client with active servers")
        print("  help       - Show this help information")
        print("  quit       - Exit the dashboard")
        print("\nWhat is MCP?")
        print("MCP (Model Context Protocol) is a protocol for communicating with")
        print("AI models and services. It provides a standardized way to access")
        print("capabilities like text generation, embedding, image generation, etc.")
        print("\nTips:")
        print("- Public servers require authentication for many operations")
        print("- Docker-based servers require Docker to be running")
        print("- Use 'discover' to find new public MCP servers")
        print("- After adding new servers, use 'status' to check connections")
        print("="*60)
    
    def interactive_menu(self):
        """Run the interactive dashboard menu"""
        menu_options = {
            "status": ("Check server status", self.status_command),
            "add": ("Add a new server", self.add_command),
            "remove": ("Remove a server", self.remove_command),
            "auth": ("Configure authentication", self.auth_command),
            "discover": ("Discover public servers", self.discover_command),
            "register": ("Register with active servers", self.register_command),
            "help": ("Show help", self.help_command),
            "quit": ("Exit the dashboard", None)
        }
        
        print("\nMCP Dashboard - Manage your Model Context Protocol servers")
        self.help_command()
        
        while True:
            print("\nAvailable commands:")
            for cmd, (desc, _) in menu_options.items():
                print(f"  {cmd:<10} - {desc}")
                
            choice = input("\nEnter command: ").strip().lower()
            
            if choice == "quit":
                print("Exiting MCP Dashboard")
                break
                
            if choice in menu_options:
                handler = menu_options[choice][1]
                if handler:
                    asyncio.run(handler())
            else:
                print(f"Unknown command: {choice}")
                print("Type 'help' for available commands")
    
    async def status_command(self):
        """Command handler for status"""
        await self.check_all_servers()
        self.show_server_status()
    
    async def add_command(self):
        """Command handler for add"""
        self.add_server()
    
    async def remove_command(self):
        """Command handler for remove"""
        self.remove_server()
    
    async def auth_command(self):
        """Command handler for auth"""
        self.configure_auth()
    
    async def discover_command(self):
        """Command handler for discover"""
        # First check servers
        if not self.active_servers:
            await self.check_all_servers()
        
        if not any(s['server_id'] == 'mcp-registry-api' for s in self.active_servers):
            print("MCP Registry API is not available. Cannot discover new servers.")
            return
            
        self.discover_public_servers()
    
    async def register_command(self):
        """Command handler for register"""
        # First check servers
        if not self.active_servers:
            await self.check_all_servers()
            
        if not self.active_servers:
            print("No active servers available for registration")
            return
            
        await self.register_mcp_client()
    
    async def help_command(self):
        """Command handler for help"""
        self.show_help()
        
def parse_args():
    """Parse command-line arguments"""
    parser = argparse.ArgumentParser(description="MCP Server Dashboard")
    
    subparsers = parser.add_subparsers(dest="command", help="Command to run")
    
    # Status command
    subparsers.add_parser("status", help="Check status of all MCP servers")
    
    # Add command
    subparsers.add_parser("add", help="Add a new MCP server")
    
    # Remove command
    subparsers.add_parser("remove", help="Remove an MCP server")
    
    # Auth command
    subparsers.add_parser("auth", help="Configure authentication for servers")
    
    # Discover command
    subparsers.add_parser("discover", help="Discover public MCP servers")
    
    return parser.parse_args()

async def main():
    """Main entry point"""
    args = parse_args()
    dashboard = MCPDashboard()
    
    if args.command == "status":
        await dashboard.check_all_servers()
        dashboard.show_server_status()
    elif args.command == "add":
        dashboard.add_server()
    elif args.command == "remove":
        dashboard.remove_server()
    elif args.command == "auth":
        dashboard.configure_auth()
    elif args.command == "discover":
        await dashboard.check_all_servers()
        dashboard.discover_public_servers()
    else:
        # No command or unknown command, run interactive menu
        dashboard.interactive_menu()
    
    return 0

if __name__ == "__main__":
    sys.exit(asyncio.run(main()))