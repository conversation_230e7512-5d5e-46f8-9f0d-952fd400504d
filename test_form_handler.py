import logging
from quote_form_handler import QuoteFormHandler

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def test_quote_forms():
    """Test quote form filling with improved handler"""
    
    # Medicare client data
    medicare_client = {
        "name": "Test Medicare Client",
        "dob": "1955-06-15",
        "height": "5'10\"",
        "weight": "175",
        "gender": "M",
        "phone": "************",
        "email": "<EMAIL>",
        "address": "123 Senior Way, Port St. Lucie, FL 34952",
        "tobacco_use": False,
        "product_type": "Medicare Supplement",
        "plan_type": "Plan G"
    }
    
    # Life insurance client data
    life_client = {
        "name": "Test Life Client",
        "dob": "1980-08-20",
        "height": "5'11\"",
        "weight": "180",
        "gender": "M",
        "phone": "************",
        "email": "<EMAIL>",
        "address": "456 Insurance Blvd, Port St. Lucie, FL 34952",
        "tobacco_use": False,
        "product_type": "Term Life",
        "desired_coverage": "250000",
        "term_length": "20"
    }
    
    handler = QuoteFormHandler(headless=False)  # Set to True for production
    
    try:
        # Test Medicare Supplement quote (MOO)
        logger.info("\n=== Testing Medicare Supplement Quote Form ===")
        logger.info("Opening Mutual of Omaha quote form...")
        
        success = handler.fill_moo_quote_form(medicare_client)
        if success:
            logger.info("Successfully filled MOO Medicare form")
            input("\nPress Enter after reviewing the Medicare quote form...")
        else:
            logger.error("Failed to fill MOO Medicare form")
            
        # Test Term Life quote (FFL)
        logger.info("\n=== Testing Term Life Quote Form ===")
        logger.info("Opening FFL Trident Life quote form...")
        
        success = handler.fill_ffl_quote_form(life_client)
        if success:
            logger.info("Successfully filled FFL Life form")
            input("\nPress Enter after reviewing the Life quote form...")
        else:
            logger.error("Failed to fill FFL Life form")
            
    except Exception as e:
        logger.error(f"Error during testing: {str(e)}")
        
    finally:
        handler.close()
        
    logger.info("\nForm testing completed!")

if __name__ == "__main__":
    test_quote_forms()