"""
MCP Coordinator <PERSON><PERSON><PERSON> - <PERSON>les coordinating tasks between agents and MCP servers
"""

import logging
import asyncio
from typing import Dict, List, Optional, Any, Union
from datetime import datetime
import json

from mcp_server_registry import registry as mcp_registry
from mcp_client import MCPClient, get_client_for_server_id, get_client_for_capability

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class MCPCoordinator:
    """Manages coordination between MCP servers and agent system"""
    
    def __init__(self):
        self.active_tasks = {}
        self.registered_emails = set()
        
    async def initialize(self) -> bool:
        """Initialize the MCP coordinator"""
        try:
            # Initialize MCP registry
            await mcp_registry.initialize()
            
            # Register <EMAIL> for email sending
            self.registered_emails.add("<EMAIL>")
            
            return True
            
        except Exception as e:
            logger.error(f"Failed to initialize MCP coordinator: {str(e)}")
            return False
            
    async def process_task(self, task_type: str, data: Dict) -> Dict:
        """Process a task with appropriate MCP server"""
        try:
            task_id = f"{task_type}_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            logger.info(f"Processing task: {task_id}")
            
            self.active_tasks[task_id] = {
                "type": task_type,
                "status": "processing",
                "start_time": datetime.now(),
                "data": data
            }
            
            result = await self._execute_task(task_type, data)
            
            # Update task status
            self.active_tasks[task_id]["status"] = "completed"
            self.active_tasks[task_id]["end_time"] = datetime.now()
            self.active_tasks[task_id]["result"] = result
            
            return result
            
        except Exception as e:
            error_msg = f"Error processing MCP task {task_type}: {str(e)}"
            logger.error(error_msg)
            
            if task_id in self.active_tasks:
                self.active_tasks[task_id]["status"] = "failed"
                self.active_tasks[task_id]["error"] = error_msg
                self.active_tasks[task_id]["end_time"] = datetime.now()
            
            return {"success": False, "error": error_msg}
            
    async def _execute_task(self, task_type: str, data: Dict) -> Dict:
        """Execute a specific task with an MCP server"""
        if task_type == "send_email":
            return await self._handle_email_task(data)
        elif task_type == "generate_audio":
            return await self._handle_audio_task(data)
        elif task_type == "execute_code":
            return await self._handle_code_execution_task(data)
        elif task_type == "analyze_data":
            return await self._handle_data_analysis_task(data)
        elif task_type == "chat_completion":
            return await self._handle_chat_completion_task(data)
        elif task_type == "search_knowledge":
            return await self._handle_knowledge_search_task(data)
        else:
            # For unknown task types, find a server with general agent-tools capability
            client = await get_client_for_capability("agent-tools")
            if not client:
                return {"success": False, "error": f"No server available for task type: {task_type}"}
            
            try:
                # Try to execute as a tool
                result = await client.execute_tool(task_type, data)
                return {"success": True, "result": result}
            except Exception as e:
                return {"success": False, "error": str(e)}
    
    async def _handle_email_task(self, data: Dict) -> Dict:
        """Handle sending an email task"""
        try:
            # Get a server with email capability
            client = await get_client_for_capability("email")
            if not client:
                return {"success": False, "error": "No server available with email capability"}
            
            # Extract email data
            to = data.get("to")
            if not to:
                return {"success": False, "error": "No recipient specified"}
                
            subject = data.get("subject", "No Subject")
            body = data.get("body", "")
            cc = data.get("cc")
            attachments = data.get("attachments")
            
            # Send the email
            success = await client.send_email(
                to=to,
                subject=subject,
                body=body,
                cc=cc,
                attachments=attachments
            )
            
            if success:
                # Register the recipient email for future reference
                if isinstance(to, str):
                    self.registered_emails.add(to)
                else:
                    for email in to:
                        self.registered_emails.add(email)
                        
                return {
                    "success": True,
                    "message": f"Email sent successfully to {to}",
                    "subject": subject
                }
            else:
                return {"success": False, "error": "Failed to send email"}
                
        except Exception as e:
            return {"success": False, "error": f"Error in email task: {str(e)}"}
    
    async def _handle_audio_task(self, data: Dict) -> Dict:
        """Handle audio generation task"""
        try:
            # Get a server with audio capability
            client = await get_client_for_capability("audio")
            if not client:
                return {"success": False, "error": "No server available with audio capability"}
            
            # Extract audio data
            text = data.get("text")
            if not text:
                return {"success": False, "error": "No text provided for audio generation"}
                
            voice = data.get("voice")
            
            # Generate the audio
            audio_path = await client.generate_audio(text, voice)
            
            if audio_path:
                return {
                    "success": True,
                    "audio_path": audio_path
                }
            else:
                return {"success": False, "error": "Failed to generate audio"}
                
        except Exception as e:
            return {"success": False, "error": f"Error in audio task: {str(e)}"}
    
    async def _handle_code_execution_task(self, data: Dict) -> Dict:
        """Handle code execution task"""
        try:
            # Get a server with code execution capability
            client = await get_client_for_capability("code-execution")
            if not client:
                return {"success": False, "error": "No server available with code execution capability"}
            
            # Extract code data
            code = data.get("code")
            if not code:
                return {"success": False, "error": "No code provided for execution"}
                
            language = data.get("language", "python")
            
            # Execute the code
            result = await client.execute_code(code, language)
            return result
                
        except Exception as e:
            return {"success": False, "error": f"Error in code execution task: {str(e)}"}
    
    async def _handle_data_analysis_task(self, data: Dict) -> Dict:
        """Handle data analysis task"""
        try:
            # Get a server with data analysis capability
            client = await get_client_for_capability("data-analysis")
            if not client:
                return {"success": False, "error": "No server available with data analysis capability"}
            
            # Execute the data analysis tool
            result = await client.execute_tool("analyze_data", data)
            return result
                
        except Exception as e:
            return {"success": False, "error": f"Error in data analysis task: {str(e)}"}
    
    async def _handle_chat_completion_task(self, data: Dict) -> Dict:
        """Handle chat completion task"""
        try:
            # Get a server with chat capability
            client = await get_client_for_capability("chat")
            if not client:
                return {"success": False, "error": "No server available with chat capability"}
            
            # Extract chat data
            messages = data.get("messages", [])
            if not messages:
                return {"success": False, "error": "No messages provided for chat completion"}
                
            model = data.get("model")
            tools = data.get("tools")
            
            # Execute chat completion
            response = await client.invoke_chat(messages, model, tools)
            return response
                
        except Exception as e:
            return {"success": False, "error": f"Error in chat completion task: {str(e)}"}
    
    async def _handle_knowledge_search_task(self, data: Dict) -> Dict:
        """Handle knowledge search task"""
        try:
            # Get a server with knowledge search capability
            client = await get_client_for_capability("knowledge-search")
            if not client:
                return {"success": False, "error": "No server available with knowledge search capability"}
            
            # Extract search data
            query = data.get("query")
            if not query:
                return {"success": False, "error": "No query provided for knowledge search"}
                
            top_k = data.get("top_k", 5)
            
            # Execute knowledge search
            results = await client.search_knowledge_base(query, top_k)
            return {
                "success": True,
                "results": results,
                "count": len(results)
            }
                
        except Exception as e:
            return {"success": False, "error": f"Error in knowledge search task: {str(e)}"}
            
    async def send_email_to_paul(self, subject: str, body: str, 
                              attachments: Optional[List[str]] = None) -> bool:
        """Convenience method to send email to Paul Edwards"""
        try:
            result = await self.process_task("send_email", {
                "to": "<EMAIL>",
                "subject": subject,
                "body": body,
                "attachments": attachments
            })
            
            return result.get("success", False)
                
        except Exception as e:
            logger.error(f"Error sending email to Paul: {str(e)}")
            return False

# Create a singleton instance
coordinator = MCPCoordinator()

async def main():
    """Test the MCP coordinator"""
    await coordinator.initialize()
    
    # Test sending an email
    email_result = await coordinator.send_email_to_paul(
        "Test from MCP Coordinator",
        "This is a test email sent from the MCP coordinator.\n\n"
        "The system is now properly configured to work with MCP servers."
    )
    
    print(f"Email sent: {email_result}")
    
    # Test getting active MCP servers
    active_servers = mcp_registry.get_active_servers()
    print(f"Active MCP servers: {len(active_servers)}")
    for server in active_servers:
        print(f"  - {server.name} ({server.url}): {', '.join(server.capabilities)}")

if __name__ == "__main__":
    asyncio.run(main())