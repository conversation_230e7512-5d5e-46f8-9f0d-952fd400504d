"""
Intelligent Request Router - AI-powered request analysis and routing system
Analyzes user requests and routes them to the most appropriate agents
"""

import re
import asyncio
import logging
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
from enum import Enum
import json
from datetime import datetime

from utils.logging_setup import get_logger

class AgentType(Enum):
    COMMUNICATION = "communication"
    INSURANCE = "insurance"
    SECURITY = "security"
    TRADING = "trading"
    UI_AUTOMATION = "ui_automation"
    CONTENT_CREATION = "content_creation"
    LEAD_GENERATION = "lead_generation"
    GENERAL = "general"

@dataclass
class RoutingDecision:
    primary_agent: AgentType
    secondary_agents: List[AgentType]
    confidence: float
    reasoning: str
    parameters: Dict[str, Any]
    estimated_duration: int  # in seconds

@dataclass
class RequestContext:
    user_id: str
    session_id: str
    previous_requests: List[str]
    user_preferences: Dict[str, Any]
    current_time: datetime

class IntelligentRequestRouter:
    """AI-powered request router that analyzes and routes user requests"""
    
    def __init__(self):
        self.logger = get_logger("request_router")
        
        # Request patterns for different agent types
        self.patterns = {
            AgentType.COMMUNICATION: [
                r'\b(call|phone|text|sms|email|message|contact|reach out|communicate)\b',
                r'\b(voicemail|voice message|leave message)\b',
                r'\b(send|deliver|transmit)\b.*\b(message|email|text)\b',
                r'\b(paul edwards|alyssa|client|lead|prospect)\b.*\b(call|text|email)\b'
            ],
            AgentType.INSURANCE: [
                r'\b(insurance|policy|quote|premium|coverage|claim)\b',
                r'\b(iul|indexed universal life|life insurance|term life)\b',
                r'\b(medicare|health insurance|final expense)\b',
                r'\b(underwriting|application|carrier|broker)\b',
                r'\b(quote|pricing|rate|cost)\b.*\b(insurance|policy)\b'
            ],
            AgentType.SECURITY: [
                r'\b(security|penetration|pentest|vulnerability|audit)\b',
                r'\b(scan|hack|exploit|breach|attack)\b',
                r'\b(nmap|john|hashcat|metasploit)\b',
                r'\b(password|credential|authentication)\b.*\b(crack|break|recover)\b'
            ],
            AgentType.TRADING: [
                r'\b(trade|trading|stock|market|investment|portfolio)\b',
                r'\b(buy|sell|analyze|chart|technical analysis)\b',
                r'\b(crypto|cryptocurrency|bitcoin|ethereum)\b',
                r'\b(profit|loss|risk|return|yield)\b'
            ],
            AgentType.UI_AUTOMATION: [
                r'\b(click|type|fill|form|website|browser|automate)\b',
                r'\b(screenshot|capture|scroll|navigate)\b',
                r'\b(ui|interface|button|field|element)\b',
                r'\b(automation|bot|script|macro)\b'
            ],
            AgentType.CONTENT_CREATION: [
                r'\b(create|write|generate|compose|draft)\b',
                r'\b(content|article|post|blog|copy|text)\b',
                r'\b(social media|facebook|twitter|linkedin|instagram)\b',
                r'\b(marketing|advertisement|campaign|promotion)\b'
            ],
            AgentType.LEAD_GENERATION: [
                r'\b(lead|prospect|client|customer|contact)\b',
                r'\b(generate|find|search|discover|identify)\b.*\b(lead|prospect)\b',
                r'\b(campaign|outreach|follow up|drip)\b',
                r'\b(conversion|qualification|scoring)\b'
            ]
        }
        
        # Agent capabilities and specializations
        self.agent_capabilities = {
            AgentType.COMMUNICATION: {
                'primary': ['call', 'text', 'email', 'voicemail'],
                'secondary': ['contact_management', 'message_templates', 'scheduling'],
                'integrations': ['twilio', 'gmail', 'elevenlabs']
            },
            AgentType.INSURANCE: {
                'primary': ['quote_generation', 'application_processing', 'underwriting'],
                'secondary': ['carrier_integration', 'policy_management', 'claims'],
                'integrations': ['carrier_portals', 'quote_engines', 'crm']
            },
            AgentType.SECURITY: {
                'primary': ['vulnerability_scanning', 'penetration_testing', 'audit'],
                'secondary': ['credential_recovery', 'network_analysis', 'reporting'],
                'integrations': ['nmap', 'john', 'hashcat', 'metasploit']
            },
            AgentType.TRADING: {
                'primary': ['market_analysis', 'trade_execution', 'portfolio_management'],
                'secondary': ['risk_assessment', 'technical_analysis', 'reporting'],
                'integrations': ['trading_apis', 'market_data', 'charting_tools']
            },
            AgentType.UI_AUTOMATION: {
                'primary': ['web_automation', 'form_filling', 'data_extraction'],
                'secondary': ['screenshot', 'monitoring', 'testing'],
                'integrations': ['selenium', 'playwright', 'ui_tars']
            },
            AgentType.CONTENT_CREATION: {
                'primary': ['content_writing', 'social_media', 'marketing_copy'],
                'secondary': ['seo_optimization', 'brand_voice', 'scheduling'],
                'integrations': ['social_apis', 'cms', 'analytics']
            },
            AgentType.LEAD_GENERATION: {
                'primary': ['lead_discovery', 'qualification', 'outreach'],
                'secondary': ['scoring', 'nurturing', 'conversion'],
                'integrations': ['crm', 'email_marketing', 'social_media']
            }
        }
        
        # Context-aware routing rules
        self.context_rules = {
            'time_sensitive': [AgentType.COMMUNICATION, AgentType.TRADING],
            'high_value': [AgentType.INSURANCE, AgentType.TRADING],
            'security_critical': [AgentType.SECURITY],
            'customer_facing': [AgentType.COMMUNICATION, AgentType.CONTENT_CREATION]
        }
    
    async def analyze_and_route(self, request_text: str, context: RequestContext) -> RoutingDecision:
        """Analyze request and determine optimal routing"""
        try:
            self.logger.info(f"Analyzing request: {request_text[:100]}...")
            
            # Step 1: Pattern matching analysis
            pattern_scores = self._analyze_patterns(request_text)
            
            # Step 2: Context analysis
            context_scores = self._analyze_context(request_text, context)
            
            # Step 3: Combine scores
            combined_scores = self._combine_scores(pattern_scores, context_scores)
            
            # Step 4: Determine primary and secondary agents
            primary_agent, confidence = self._select_primary_agent(combined_scores)
            secondary_agents = self._select_secondary_agents(combined_scores, primary_agent)
            
            # Step 5: Extract parameters
            parameters = self._extract_parameters(request_text, primary_agent)
            
            # Step 6: Estimate duration
            estimated_duration = self._estimate_duration(primary_agent, parameters)
            
            # Step 7: Generate reasoning
            reasoning = self._generate_reasoning(request_text, primary_agent, secondary_agents, confidence)
            
            decision = RoutingDecision(
                primary_agent=primary_agent,
                secondary_agents=secondary_agents,
                confidence=confidence,
                reasoning=reasoning,
                parameters=parameters,
                estimated_duration=estimated_duration
            )
            
            self.logger.info(f"Routing decision: {primary_agent.value} (confidence: {confidence:.2f})")
            return decision
            
        except Exception as e:
            self.logger.error(f"Error in request analysis: {str(e)}")
            # Fallback to general agent
            return RoutingDecision(
                primary_agent=AgentType.GENERAL,
                secondary_agents=[],
                confidence=0.5,
                reasoning=f"Fallback routing due to analysis error: {str(e)}",
                parameters={},
                estimated_duration=60
            )
    
    def _analyze_patterns(self, request_text: str) -> Dict[AgentType, float]:
        """Analyze request text against known patterns"""
        scores = {}
        request_lower = request_text.lower()
        
        for agent_type, patterns in self.patterns.items():
            score = 0.0
            matches = 0
            
            for pattern in patterns:
                if re.search(pattern, request_lower):
                    matches += 1
                    # Weight based on pattern specificity
                    score += 1.0 / len(patterns)
            
            # Boost score for multiple pattern matches
            if matches > 1:
                score *= 1.5
            
            scores[agent_type] = min(score, 1.0)
        
        return scores
    
    def _analyze_context(self, request_text: str, context: RequestContext) -> Dict[AgentType, float]:
        """Analyze request context for additional routing hints"""
        scores = {}
        
        # Initialize all agent types with base score
        for agent_type in AgentType:
            scores[agent_type] = 0.0
        
        # Time-based context
        current_hour = context.current_time.hour
        if 9 <= current_hour <= 17:  # Business hours
            scores[AgentType.COMMUNICATION] += 0.2
            scores[AgentType.INSURANCE] += 0.2
        
        # User preference context
        if context.user_preferences:
            preferred_agents = context.user_preferences.get('preferred_agents', [])
            for agent_name in preferred_agents:
                try:
                    agent_type = AgentType(agent_name)
                    scores[agent_type] += 0.3
                except ValueError:
                    pass
        
        # Previous request context
        if context.previous_requests:
            last_request = context.previous_requests[-1].lower()
            # If previous request was about insurance, boost insurance agent
            if any(word in last_request for word in ['insurance', 'quote', 'policy']):
                scores[AgentType.INSURANCE] += 0.2
            # If previous was about communication, boost communication
            elif any(word in last_request for word in ['call', 'text', 'email']):
                scores[AgentType.COMMUNICATION] += 0.2
        
        return scores
    
    def _combine_scores(self, pattern_scores: Dict[AgentType, float], 
                       context_scores: Dict[AgentType, float]) -> Dict[AgentType, float]:
        """Combine pattern and context scores"""
        combined = {}
        
        for agent_type in AgentType:
            pattern_score = pattern_scores.get(agent_type, 0.0)
            context_score = context_scores.get(agent_type, 0.0)
            
            # Weight pattern matching more heavily than context
            combined[agent_type] = (pattern_score * 0.7) + (context_score * 0.3)
        
        return combined
    
    def _select_primary_agent(self, scores: Dict[AgentType, float]) -> Tuple[AgentType, float]:
        """Select the primary agent based on scores"""
        if not scores:
            return AgentType.GENERAL, 0.5
        
        # Find the highest scoring agent
        primary_agent = max(scores.keys(), key=lambda k: scores[k])
        confidence = scores[primary_agent]
        
        # If confidence is too low, default to general agent
        if confidence < 0.3:
            return AgentType.GENERAL, 0.5
        
        return primary_agent, confidence
    
    def _select_secondary_agents(self, scores: Dict[AgentType, float], 
                                primary_agent: AgentType) -> List[AgentType]:
        """Select secondary agents that might assist"""
        secondary = []
        
        # Sort agents by score, excluding primary
        sorted_agents = sorted(
            [(agent, score) for agent, score in scores.items() if agent != primary_agent],
            key=lambda x: x[1],
            reverse=True
        )
        
        # Add agents with significant scores as secondary
        for agent, score in sorted_agents[:2]:  # Max 2 secondary agents
            if score > 0.2:
                secondary.append(agent)
        
        return secondary
    
    def _extract_parameters(self, request_text: str, primary_agent: AgentType) -> Dict[str, Any]:
        """Extract relevant parameters based on the primary agent"""
        parameters = {}
        request_lower = request_text.lower()
        
        if primary_agent == AgentType.COMMUNICATION:
            # Extract contact information
            phone_match = re.search(r'\b(\d{3}[-.]?\d{3}[-.]?\d{4})\b', request_text)
            if phone_match:
                parameters['phone'] = phone_match.group(1)
            
            email_match = re.search(r'\b([a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,})\b', request_text)
            if email_match:
                parameters['email'] = email_match.group(1)
            
            # Extract names
            if 'paul edwards' in request_lower:
                parameters['contact_name'] = 'Paul Edwards'
            elif 'alyssa' in request_lower:
                parameters['contact_name'] = 'Alyssa C.'
            
            # Extract communication method
            if 'call' in request_lower:
                parameters['method'] = 'call'
            elif 'text' in request_lower or 'sms' in request_lower:
                parameters['method'] = 'text'
            elif 'email' in request_lower:
                parameters['method'] = 'email'
            elif 'voicemail' in request_lower:
                parameters['method'] = 'voicemail'
        
        elif primary_agent == AgentType.INSURANCE:
            # Extract insurance type
            if 'iul' in request_lower or 'indexed universal life' in request_lower:
                parameters['insurance_type'] = 'iul'
            elif 'medicare' in request_lower:
                parameters['insurance_type'] = 'medicare'
            elif 'final expense' in request_lower:
                parameters['insurance_type'] = 'final_expense'
            elif 'term' in request_lower:
                parameters['insurance_type'] = 'term'
            
            # Extract age if mentioned
            age_match = re.search(r'\b(\d{1,2})\s*(?:years?\s*old|yo)\b', request_lower)
            if age_match:
                parameters['age'] = int(age_match.group(1))
        
        elif primary_agent == AgentType.SECURITY:
            # Extract target information
            ip_match = re.search(r'\b(\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3})\b', request_text)
            if ip_match:
                parameters['target_ip'] = ip_match.group(1)
            
            domain_match = re.search(r'\b([a-zA-Z0-9.-]+\.[a-zA-Z]{2,})\b', request_text)
            if domain_match and not email_match:  # Avoid matching email domains
                parameters['target_domain'] = domain_match.group(1)
        
        return parameters
    
    def _estimate_duration(self, primary_agent: AgentType, parameters: Dict[str, Any]) -> int:
        """Estimate task duration in seconds"""
        base_durations = {
            AgentType.COMMUNICATION: 30,
            AgentType.INSURANCE: 120,
            AgentType.SECURITY: 300,
            AgentType.TRADING: 60,
            AgentType.UI_AUTOMATION: 45,
            AgentType.CONTENT_CREATION: 90,
            AgentType.LEAD_GENERATION: 180,
            AgentType.GENERAL: 60
        }
        
        base_duration = base_durations.get(primary_agent, 60)
        
        # Adjust based on parameters
        if primary_agent == AgentType.COMMUNICATION:
            if parameters.get('method') == 'call':
                base_duration += 60  # Calls take longer
        elif primary_agent == AgentType.SECURITY:
            if parameters.get('target_ip') or parameters.get('target_domain'):
                base_duration += 120  # Targeted scans take longer
        
        return base_duration
    
    def _generate_reasoning(self, request_text: str, primary_agent: AgentType, 
                          secondary_agents: List[AgentType], confidence: float) -> str:
        """Generate human-readable reasoning for the routing decision"""
        reasoning_parts = []
        
        # Primary agent reasoning
        reasoning_parts.append(f"Selected {primary_agent.value} as primary agent")
        
        if confidence > 0.8:
            reasoning_parts.append("with high confidence based on clear pattern matches")
        elif confidence > 0.6:
            reasoning_parts.append("with moderate confidence based on pattern analysis")
        else:
            reasoning_parts.append("with low confidence, may need user confirmation")
        
        # Secondary agents reasoning
        if secondary_agents:
            agent_names = [agent.value for agent in secondary_agents]
            reasoning_parts.append(f"Secondary agents ({', '.join(agent_names)}) may assist")
        
        # Specific reasoning based on detected patterns
        request_lower = request_text.lower()
        if 'paul edwards' in request_lower or 'alyssa' in request_lower:
            reasoning_parts.append("Detected specific client names requiring communication")
        
        if any(word in request_lower for word in ['urgent', 'asap', 'immediately']):
            reasoning_parts.append("Detected urgency indicators")
        
        return ". ".join(reasoning_parts) + "."
    
    def get_agent_info(self, agent_type: AgentType) -> Dict[str, Any]:
        """Get detailed information about an agent type"""
        return {
            'type': agent_type.value,
            'capabilities': self.agent_capabilities.get(agent_type, {}),
            'description': self._get_agent_description(agent_type)
        }
    
    def _get_agent_description(self, agent_type: AgentType) -> str:
        """Get human-readable description of agent capabilities"""
        descriptions = {
            AgentType.COMMUNICATION: "Handles all communication tasks including calls, texts, emails, and voicemails",
            AgentType.INSURANCE: "Manages insurance-related tasks like quotes, applications, and policy management",
            AgentType.SECURITY: "Performs security assessments, penetration testing, and vulnerability analysis",
            AgentType.TRADING: "Executes trading operations, market analysis, and portfolio management",
            AgentType.UI_AUTOMATION: "Automates web interactions, form filling, and UI testing",
            AgentType.CONTENT_CREATION: "Creates marketing content, social media posts, and written materials",
            AgentType.LEAD_GENERATION: "Identifies, qualifies, and nurtures potential clients and leads",
            AgentType.GENERAL: "Handles general tasks and coordinates with other specialized agents"
        }
        return descriptions.get(agent_type, "General purpose agent")

# Global instance
_request_router: Optional[IntelligentRequestRouter] = None

def get_request_router() -> IntelligentRequestRouter:
    """Get global request router instance"""
    global _request_router
    if _request_router is None:
        _request_router = IntelligentRequestRouter()
    return _request_router
