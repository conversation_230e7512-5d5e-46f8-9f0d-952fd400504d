import os
from cryptography.fernet import Fernet

# Load encryption key from environment variable or generate a new one
ENCRYPTION_KEY = os.getenv("ENCRYPTION_KEY", Fernet.generate_key().decode())
cipher = Fernet(ENCRYPTION_KEY.encode())

def encrypt_credential(credential: str) -> str:
    """Encrypt a credential."""
    return cipher.encrypt(credential.encode()).decode()

def decrypt_credential(encrypted_credential: str) -> str:
    """Decrypt a credential."""
    return cipher.decrypt(encrypted_credential.encode()).decode()

# Example usage
if __name__ == "__main__":
    # Replace with actual credentials
    example_credential = "example_password"
    encrypted = encrypt_credential(example_credential)
    print(f"Encrypted: {encrypted}")
    decrypted = decrypt_credential(encrypted)
    print(f"Decrypted: {decrypted}")