"""
Unified Command Center - Central interface for all agent interactions
Provides a single point of access for users to interact with the entire AI agentic system
"""

import asyncio
import json
import logging
from datetime import datetime
from typing import Dict, List, Any, Optional, Callable
from dataclasses import dataclass, asdict
from enum import Enum
import aiohttp
from aiohttp import web, WSMsgType
import aiohttp_cors
from pathlib import Path

from core.coordinator import get_coordinator
from core.config_manager import get_config_manager
from core.state_manager import get_state_manager
from core.event_system import get_event_system
from utils.logging_setup import get_logger

class RequestType(Enum):
    COMMUNICATION = "communication"
    INSURANCE = "insurance"
    SECURITY = "security"
    TRADING = "trading"
    UI_AUTOMATION = "ui_automation"
    GENERAL = "general"

class RequestPriority(Enum):
    LOW = 1
    NORMAL = 2
    HIGH = 3
    URGENT = 4

@dataclass
class UserRequest:
    id: str
    user_id: str
    request_text: str
    request_type: RequestType
    priority: RequestPriority
    timestamp: datetime
    context: Dict[str, Any]
    status: str = "pending"
    assigned_agents: List[str] = None
    results: Dict[str, Any] = None
    
    def __post_init__(self):
        if self.assigned_agents is None:
            self.assigned_agents = []
        if self.results is None:
            self.results = {}

class UnifiedCommandCenter:
    """Central command center for managing all agent interactions"""
    
    def __init__(self, host: str = "localhost", port: int = 8080):
        self.logger = get_logger("command_center")
        self.host = host
        self.port = port
        
        # Core components
        self.coordinator = get_coordinator()
        self.config_manager = get_config_manager()
        self.state_manager = get_state_manager()
        self.event_system = get_event_system()
        
        # Request management
        self.active_requests: Dict[str, UserRequest] = {}
        self.request_handlers: Dict[RequestType, Callable] = {}
        
        # WebSocket connections
        self.websocket_connections: List[web.WebSocketResponse] = []
        
        # Web application
        self.app = web.Application()
        self.setup_routes()
        
        # Agent status tracking
        self.agent_status: Dict[str, Dict] = {}
        
    def setup_routes(self):
        """Setup HTTP routes and WebSocket endpoints"""
        # API routes
        self.app.router.add_post('/api/request', self.handle_user_request)
        self.app.router.add_get('/api/requests', self.get_requests)
        self.app.router.add_get('/api/agents/status', self.get_agent_status)
        self.app.router.add_post('/api/agents/{agent_id}/action', self.agent_action)
        self.app.router.add_get('/api/system/health', self.system_health)
        
        # WebSocket for real-time updates
        self.app.router.add_get('/ws', self.websocket_handler)
        
        # Static files for dashboard
        self.app.router.add_static('/', path='dashboard/dist', name='static')
        
        # CORS setup
        cors = aiohttp_cors.setup(self.app, defaults={
            "*": aiohttp_cors.ResourceOptions(
                allow_credentials=True,
                expose_headers="*",
                allow_headers="*",
                allow_methods="*"
            )
        })
        
        # Add CORS to all routes
        for route in list(self.app.router.routes()):
            cors.add(route)
    
    async def handle_user_request(self, request: web.Request) -> web.Response:
        """Handle incoming user requests"""
        try:
            data = await request.json()
            
            # Create user request object
            user_request = UserRequest(
                id=f"req_{datetime.now().strftime('%Y%m%d_%H%M%S_%f')}",
                user_id=data.get('user_id', 'default_user'),
                request_text=data['request_text'],
                request_type=RequestType(data.get('request_type', 'general')),
                priority=RequestPriority(data.get('priority', 2)),
                timestamp=datetime.now(),
                context=data.get('context', {})
            )
            
            # Store request
            self.active_requests[user_request.id] = user_request
            
            # Process request asynchronously
            asyncio.create_task(self.process_request(user_request))
            
            # Broadcast to WebSocket clients
            await self.broadcast_update({
                'type': 'new_request',
                'request': asdict(user_request)
            })
            
            return web.json_response({
                'status': 'accepted',
                'request_id': user_request.id,
                'message': 'Request received and processing started'
            })
            
        except Exception as e:
            self.logger.error(f"Error handling user request: {str(e)}")
            return web.json_response({
                'status': 'error',
                'message': str(e)
            }, status=400)
    
    async def process_request(self, user_request: UserRequest):
        """Process a user request by routing to appropriate agents"""
        try:
            self.logger.info(f"Processing request {user_request.id}: {user_request.request_text}")
            
            # Update status
            user_request.status = "processing"
            await self.broadcast_update({
                'type': 'request_update',
                'request_id': user_request.id,
                'status': 'processing'
            })
            
            # Route to appropriate handler
            handler = self.request_handlers.get(user_request.request_type)
            if handler:
                results = await handler(user_request)
                user_request.results = results
                user_request.status = "completed"
            else:
                # Default handling
                results = await self.default_request_handler(user_request)
                user_request.results = results
                user_request.status = "completed"
            
            # Broadcast completion
            await self.broadcast_update({
                'type': 'request_completed',
                'request_id': user_request.id,
                'results': user_request.results
            })
            
        except Exception as e:
            self.logger.error(f"Error processing request {user_request.id}: {str(e)}")
            user_request.status = "error"
            user_request.results = {'error': str(e)}
            
            await self.broadcast_update({
                'type': 'request_error',
                'request_id': user_request.id,
                'error': str(e)
            })
    
    async def default_request_handler(self, user_request: UserRequest) -> Dict[str, Any]:
        """Default handler for general requests"""
        # This would integrate with your intelligent request router
        return {
            'message': f"Processed request: {user_request.request_text}",
            'timestamp': datetime.now().isoformat(),
            'agents_used': ['general_agent']
        }
    
    async def get_requests(self, request: web.Request) -> web.Response:
        """Get all active requests"""
        try:
            requests_data = []
            for req in self.active_requests.values():
                req_dict = asdict(req)
                req_dict['timestamp'] = req.timestamp.isoformat()
                requests_data.append(req_dict)
            
            return web.json_response({
                'requests': requests_data,
                'total': len(requests_data)
            })
            
        except Exception as e:
            return web.json_response({
                'error': str(e)
            }, status=500)
    
    async def get_agent_status(self, request: web.Request) -> web.Response:
        """Get status of all agents"""
        try:
            # Get component status from coordinator
            component_status = self.coordinator.get_component_status()
            
            # Add agent-specific information
            agent_info = {}
            for name, status in component_status.items():
                agent_info[name] = {
                    'status': status['state'],
                    'error': status.get('error'),
                    'last_activity': datetime.now().isoformat(),
                    'capabilities': self.get_agent_capabilities(name)
                }
            
            return web.json_response({
                'agents': agent_info,
                'system_status': 'healthy' if all(
                    s['state'] == 'running' for s in component_status.values()
                ) else 'degraded'
            })
            
        except Exception as e:
            return web.json_response({
                'error': str(e)
            }, status=500)
    
    def get_agent_capabilities(self, agent_name: str) -> List[str]:
        """Get capabilities for a specific agent"""
        capabilities_map = {
            'communication': ['call', 'text', 'email', 'voicemail'],
            'insurance': ['quote', 'application', 'underwriting', 'claims'],
            'security': ['scan', 'audit', 'penetration_test', 'vulnerability_assessment'],
            'trading': ['analysis', 'execution', 'portfolio_management', 'risk_assessment'],
            'ui_automation': ['click', 'type', 'scroll', 'screenshot', 'form_fill']
        }
        return capabilities_map.get(agent_name, ['general'])
    
    async def agent_action(self, request: web.Request) -> web.Response:
        """Perform action on specific agent"""
        try:
            agent_id = request.match_info['agent_id']
            data = await request.json()
            action = data.get('action')
            
            # Route action to appropriate agent
            result = await self.execute_agent_action(agent_id, action, data.get('params', {}))
            
            return web.json_response({
                'status': 'success',
                'result': result
            })
            
        except Exception as e:
            return web.json_response({
                'status': 'error',
                'message': str(e)
            }, status=400)
    
    async def execute_agent_action(self, agent_id: str, action: str, params: Dict) -> Any:
        """Execute action on specific agent"""
        # This would integrate with your agent system
        self.logger.info(f"Executing action {action} on agent {agent_id} with params {params}")
        return f"Action {action} executed on {agent_id}"
    
    async def system_health(self, request: web.Request) -> web.Response:
        """Get overall system health"""
        try:
            component_status = self.coordinator.get_component_status()
            
            health_data = {
                'status': 'healthy',
                'timestamp': datetime.now().isoformat(),
                'components': component_status,
                'active_requests': len(self.active_requests),
                'connected_clients': len(self.websocket_connections)
            }
            
            # Check if any components are in error state
            if any(s['state'] == 'error' for s in component_status.values()):
                health_data['status'] = 'degraded'
            
            return web.json_response(health_data)
            
        except Exception as e:
            return web.json_response({
                'status': 'error',
                'message': str(e)
            }, status=500)
    
    async def websocket_handler(self, request: web.Request) -> web.WebSocketResponse:
        """Handle WebSocket connections for real-time updates"""
        ws = web.WebSocketResponse()
        await ws.prepare(request)
        
        self.websocket_connections.append(ws)
        self.logger.info(f"New WebSocket connection. Total: {len(self.websocket_connections)}")
        
        try:
            async for msg in ws:
                if msg.type == WSMsgType.TEXT:
                    try:
                        data = json.loads(msg.data)
                        await self.handle_websocket_message(ws, data)
                    except json.JSONDecodeError:
                        await ws.send_str(json.dumps({
                            'type': 'error',
                            'message': 'Invalid JSON'
                        }))
                elif msg.type == WSMsgType.ERROR:
                    self.logger.error(f'WebSocket error: {ws.exception()}')
                    
        except Exception as e:
            self.logger.error(f"WebSocket error: {str(e)}")
        finally:
            if ws in self.websocket_connections:
                self.websocket_connections.remove(ws)
            self.logger.info(f"WebSocket disconnected. Total: {len(self.websocket_connections)}")
        
        return ws
    
    async def handle_websocket_message(self, ws: web.WebSocketResponse, data: Dict):
        """Handle incoming WebSocket messages"""
        message_type = data.get('type')
        
        if message_type == 'ping':
            await ws.send_str(json.dumps({'type': 'pong'}))
        elif message_type == 'subscribe':
            # Handle subscription to specific events
            await ws.send_str(json.dumps({
                'type': 'subscribed',
                'events': data.get('events', [])
            }))
    
    async def broadcast_update(self, update: Dict):
        """Broadcast update to all connected WebSocket clients"""
        if not self.websocket_connections:
            return
        
        message = json.dumps(update)
        disconnected = []
        
        for ws in self.websocket_connections:
            try:
                await ws.send_str(message)
            except Exception as e:
                self.logger.warning(f"Failed to send to WebSocket: {str(e)}")
                disconnected.append(ws)
        
        # Remove disconnected clients
        for ws in disconnected:
            if ws in self.websocket_connections:
                self.websocket_connections.remove(ws)
    
    def register_request_handler(self, request_type: RequestType, handler: Callable):
        """Register a handler for specific request types"""
        self.request_handlers[request_type] = handler
        self.logger.info(f"Registered handler for {request_type.value}")
    
    async def start(self):
        """Start the command center server"""
        try:
            self.logger.info(f"Starting Unified Command Center on {self.host}:{self.port}")
            
            # Start the web server
            runner = web.AppRunner(self.app)
            await runner.setup()
            
            site = web.TCPSite(runner, self.host, self.port)
            await site.start()
            
            self.logger.info(f"Command Center started at http://{self.host}:{self.port}")
            
        except Exception as e:
            self.logger.error(f"Failed to start Command Center: {str(e)}")
            raise
    
    async def stop(self):
        """Stop the command center server"""
        try:
            # Close all WebSocket connections
            for ws in self.websocket_connections:
                await ws.close()
            
            self.logger.info("Unified Command Center stopped")
            
        except Exception as e:
            self.logger.error(f"Error stopping Command Center: {str(e)}")

# Global instance
_command_center: Optional[UnifiedCommandCenter] = None

def get_command_center() -> UnifiedCommandCenter:
    """Get global command center instance"""
    global _command_center
    if _command_center is None:
        _command_center = UnifiedCommandCenter()
    return _command_center
