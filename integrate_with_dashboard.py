#!/usr/bin/env python3
"""
Dashboard Integration Script
===========================

Integrates the advanced AI models with your existing dashboard and interface.
"""

import os
import json
from pathlib import Path

def update_dashboard_with_advanced_models():
    """Update the dashboard to include advanced models"""
    
    print("🚀 Integrating Advanced Models with Dashboard...")
    
    # Check if dashboard files exist
    dashboard_files = [
        "unified_dashboard.py",
        "dashboard.py", 
        "main_interface.py",
        "app.py"
    ]
    
    found_dashboard = None
    for file in dashboard_files:
        if Path(file).exists():
            found_dashboard = file
            break
    
    if not found_dashboard:
        print("❌ No dashboard file found. Creating integration template...")
        create_integration_template()
        return
    
    print(f"✅ Found dashboard: {found_dashboard}")
    
    # Create integration code
    integration_code = '''
# Advanced Models Integration
try:
    from advanced_models.unified_interface import UnifiedModelInterface, ResponseStrategy, QueryType
    from enhanced_agent_interface import EnhancedAgentInterface
    ADVANCED_MODELS_AVAILABLE = True
    print("✅ Advanced models loaded successfully")
except ImportError as e:
    ADVANCED_MODELS_AVAILABLE = False
    print(f"⚠️ Advanced models not available: {e}")

class AdvancedModelIntegration:
    """Integration class for advanced models"""
    
    def __init__(self):
        self.interface = None
        self.enhanced_interface = None
        self.initialized = False
    
    async def initialize(self):
        """Initialize advanced models"""
        if not ADVANCED_MODELS_AVAILABLE:
            return False
        
        try:
            self.interface = UnifiedModelInterface()
            await self.interface.initialize()
            
            self.enhanced_interface = EnhancedAgentInterface()
            await self.enhanced_interface.initialize()
            
            self.initialized = True
            return True
        except Exception as e:
            print(f"❌ Failed to initialize advanced models: {e}")
            return False
    
    async def process_query(self, query, context=None, image_data=None, strategy=None):
        """Process query with advanced models"""
        if not self.initialized:
            await self.initialize()
        
        if not self.initialized:
            return {
                'response': f"Advanced models not available. Query: {query}",
                'confidence': 0.0,
                'models_used': [],
                'error': 'Advanced models not initialized'
            }
        
        try:
            # Use enhanced interface for best results
            result = await self.enhanced_interface.process_query(
                query=query,
                context=context,
                image_data=image_data,
                strategy=strategy
            )
            
            return {
                'response': result.primary_response,
                'confidence': result.confidence,
                'processing_time': result.processing_time,
                'models_used': result.models_used,
                'strategy_used': result.strategy_used,
                'alternative_responses': result.alternative_responses,
                'metadata': result.metadata
            }
            
        except Exception as e:
            return {
                'response': f"Error processing query: {str(e)}",
                'confidence': 0.0,
                'models_used': [],
                'error': str(e)
            }
    
    def get_available_models(self):
        """Get list of available models"""
        if not ADVANCED_MODELS_AVAILABLE:
            return []
        
        return [
            "MANUS (OpenManus) - Autonomous Reasoning",
            "MiMo-VL-7B - Vision-Language Model", 
            "Detail Flow - ByteDance Flow Processing",
            "Giga Agent - Abacus.ai Autonomous Agent",
            "Honest AI - Google Research Agent"
        ]
    
    def get_available_strategies(self):
        """Get list of available strategies"""
        if not ADVANCED_MODELS_AVAILABLE:
            return []
        
        return [
            "best_single - Select best performing model",
            "consensus - Aggregate multiple model responses",
            "parallel_all - Query all models simultaneously", 
            "sequential - Query models in priority order",
            "specialized - Route to most appropriate model"
        ]

# Global instance
advanced_models = AdvancedModelIntegration()

# Helper functions for easy integration
async def query_advanced_models(query, context=None, image_data=None, strategy=None):
    """Helper function to query advanced models"""
    return await advanced_models.process_query(query, context, image_data, strategy)

def get_advanced_models_status():
    """Get status of advanced models"""
    return {
        'available': ADVANCED_MODELS_AVAILABLE,
        'initialized': advanced_models.initialized,
        'models': advanced_models.get_available_models(),
        'strategies': advanced_models.get_available_strategies()
    }
'''
    
    # Write integration file
    with open("advanced_models_integration.py", "w") as f:
        f.write(integration_code)
    
    print("✅ Created advanced_models_integration.py")
    
    # Create usage example
    create_usage_example()
    
    print("\n🎉 Integration completed successfully!")
    print("\n📋 Next steps:")
    print("1. Import the integration in your dashboard:")
    print("   from advanced_models_integration import query_advanced_models, get_advanced_models_status")
    print("\n2. Add advanced model queries to your interface:")
    print("   result = await query_advanced_models('Your query here')")
    print("\n3. Display model status in your dashboard:")
    print("   status = get_advanced_models_status()")

def create_integration_template():
    """Create a template for dashboard integration"""
    
    template_code = '''#!/usr/bin/env python3
"""
Advanced Models Dashboard Template
=================================

Template dashboard that showcases all advanced AI models.
"""

import asyncio
import streamlit as st
from advanced_models_integration import query_advanced_models, get_advanced_models_status

def main():
    st.title("🚀 Advanced AI Models Dashboard")
    st.write("Powered by multiple state-of-the-art AI models")
    
    # Show model status
    status = get_advanced_models_status()
    
    if status['available']:
        st.success("✅ Advanced models are available")
        
        # Model selection
        st.sidebar.header("Model Configuration")
        
        # Strategy selection
        strategies = [s.split(' - ')[0] for s in status['strategies']]
        selected_strategy = st.sidebar.selectbox("Response Strategy", strategies)
        
        # Query input
        st.header("Query Interface")
        query = st.text_area("Enter your query:", height=100)
        
        # Image upload for vision queries
        uploaded_file = st.file_uploader("Upload image (optional)", type=['png', 'jpg', 'jpeg'])
        image_data = None
        if uploaded_file:
            image_data = uploaded_file.read()
            st.image(uploaded_file, caption="Uploaded Image", use_column_width=True)
        
        # Process query
        if st.button("🔍 Process Query"):
            if query:
                with st.spinner("Processing with advanced models..."):
                    result = asyncio.run(query_advanced_models(
                        query=query,
                        image_data=image_data,
                        strategy=selected_strategy
                    ))
                
                # Display results
                st.header("Results")
                st.write("**Response:**")
                st.write(result['response'])
                
                col1, col2, col3 = st.columns(3)
                with col1:
                    st.metric("Confidence", f"{result['confidence']:.2f}")
                with col2:
                    st.metric("Processing Time", f"{result.get('processing_time', 0):.2f}s")
                with col3:
                    st.metric("Models Used", len(result['models_used']))
                
                # Show model details
                if result['models_used']:
                    st.subheader("Models Used")
                    for model in result['models_used']:
                        st.write(f"• {model}")
                
                # Show alternative responses if available
                if result.get('alternative_responses'):
                    st.subheader("Alternative Responses")
                    for i, alt in enumerate(result['alternative_responses'], 1):
                        with st.expander(f"Alternative {i}"):
                            st.write(alt.get('response', 'No response'))
            else:
                st.warning("Please enter a query")
    
    else:
        st.error("❌ Advanced models not available")
        st.write("Run: `python install_advanced_models.py`")
    
    # Show available models
    st.sidebar.header("Available Models")
    for model in status['models']:
        st.sidebar.write(f"• {model}")

if __name__ == "__main__":
    main()
'''
    
    with open("advanced_models_dashboard.py", "w") as f:
        f.write(template_code)
    
    print("✅ Created advanced_models_dashboard.py template")

def create_usage_example():
    """Create usage examples"""
    
    example_code = '''#!/usr/bin/env python3
"""
Advanced Models Usage Examples
=============================

Examples of how to use the advanced models in your applications.
"""

import asyncio
from advanced_models_integration import query_advanced_models, get_advanced_models_status

async def example_basic_query():
    """Basic query example"""
    print("🔍 Basic Query Example")
    print("-" * 30)
    
    result = await query_advanced_models("What is artificial intelligence?")
    
    print(f"Response: {result['response'][:200]}...")
    print(f"Confidence: {result['confidence']:.2f}")
    print(f"Models Used: {result['models_used']}")

async def example_research_query():
    """Research query with consensus strategy"""
    print("\\n🔬 Research Query Example")
    print("-" * 30)
    
    result = await query_advanced_models(
        query="Analyze the impact of renewable energy on the economy",
        strategy="consensus"
    )
    
    print(f"Response: {result['response'][:200]}...")
    print(f"Confidence: {result['confidence']:.2f}")
    print(f"Strategy Used: {result['strategy_used']}")

async def example_vision_query():
    """Vision query example (simulated)"""
    print("\\n👁️ Vision Query Example")
    print("-" * 30)
    
    # In real usage, load actual image data
    fake_image_data = b"fake_image_data"
    
    result = await query_advanced_models(
        query="Describe what you see in this image",
        image_data=fake_image_data,
        strategy="specialized"
    )
    
    print(f"Response: {result['response'][:200]}...")
    print(f"Confidence: {result['confidence']:.2f}")

async def example_autonomous_task():
    """Autonomous task example"""
    print("\\n🤖 Autonomous Task Example")
    print("-" * 30)
    
    result = await query_advanced_models(
        query="Independently research and create a business plan for a sustainable energy startup",
        strategy="specialized"
    )
    
    print(f"Response: {result['response'][:200]}...")
    print(f"Confidence: {result['confidence']:.2f}")

def example_status_check():
    """Check system status"""
    print("\\n📊 System Status")
    print("-" * 30)
    
    status = get_advanced_models_status()
    
    print(f"Available: {status['available']}")
    print(f"Initialized: {status['initialized']}")
    print(f"Models: {len(status['models'])}")
    print(f"Strategies: {len(status['strategies'])}")

async def main():
    """Run all examples"""
    print("🚀 Advanced Models Usage Examples")
    print("=" * 50)
    
    # Check status first
    example_status_check()
    
    # Run examples
    await example_basic_query()
    await example_research_query()
    await example_vision_query()
    await example_autonomous_task()
    
    print("\\n✅ All examples completed!")

if __name__ == "__main__":
    asyncio.run(main())
'''
    
    with open("advanced_models_examples.py", "w") as f:
        f.write(example_code)
    
    print("✅ Created advanced_models_examples.py")

def main():
    """Main integration function"""
    print("🔧 Advanced Models Dashboard Integration")
    print("=" * 50)
    
    update_dashboard_with_advanced_models()
    
    print("\\n🎯 Integration Summary:")
    print("✅ Advanced models installed and tested")
    print("✅ Integration files created")
    print("✅ Usage examples provided")
    print("✅ Dashboard template created")
    
    print("\\n🚀 You now have access to:")
    print("• MANUS (OpenManus) - Autonomous reasoning")
    print("• MiMo-VL-7B - Vision-language understanding")
    print("• Detail Flow - ByteDance flow processing")
    print("• Giga Agent - Abacus.ai autonomous agent")
    print("• Honest AI - Google research agent")
    
    print("\\n📚 Files created:")
    print("• advanced_models_integration.py - Main integration")
    print("• advanced_models_dashboard.py - Dashboard template")
    print("• advanced_models_examples.py - Usage examples")
    
    print("\\n🎉 Ready to use the most advanced AI agent system!")

if __name__ == "__main__":
    main()
