import asyncio
from core.base_agent import BaseAgent

class LocalCapabilitiesTestAgent(BaseAgent):
    """Test agent demonstrating local tools and LLM integration."""
    
    async def run_local_llm_test(self, prompt: str):
        """Test local LLM capabilities."""
        try:
            responses = await self.process_with_local_llm(
                prompt,
                max_length=200,
                temperature=0.7
            )
            print(f"Local LLM Response: {responses[0]}")
        except ValueError as e:
            print(f"Local LLM test failed: {str(e)}")
            
    async def run_screen_tools_test(self):
        """Test screen interaction capabilities."""
        try:
            # Capture screen
            screen = await self.perform_screen_action("capture")
            print("Screen capture successful")
            
            # Type some text
            await self.perform_screen_action(
                "type",
                text="Hello from local tools!"
            )
            print("Text typing successful")
            
            # Perform a mouse click
            await self.perform_screen_action(
                "click",
                x=100,
                y=100
            )
            print("Mouse click successful")
            
        except Exception as e:
            print(f"Screen tools test failed: {str(e)}")
            
async def main():
    # Create and initialize the test agent
    agent = LocalCapabilitiesTestAgent("TestAgent")
    await agent.initialize()
    
    # Test local LLM
    await agent.run_local_llm_test(
        "What are the main benefits of using local language models?"
    )
    
    # Test screen tools
    await agent.run_screen_tools_test()
    
    # Clean up
    agent.cleanup()

if __name__ == "__main__":
    asyncio.run(main())