#!/usr/bin/env python3
"""
UI TRAS 1.5 Setup Script

This script sets up the UI TRAS 1.5 web evaluation agent with the proper
VLLM configuration and dependencies to work with MidScene for Chrome automation.
"""

import os
import sys
import subprocess
import json
import time
import shutil
from pathlib import Path
import logging

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s [%(levelname)s] %(message)s",
    handlers=[logging.StreamHandler()]
)
logger = logging.getLogger("ui-tras-setup")

# Set the Operative API key
OPERATIVE_API_KEY = "op-sMxrcBrpW8uyzqeL8mg9N9SiDNY_ijY6kB8QUbiFj1I"
os.environ["OPERATIVE_API_KEY"] = OPERATIVE_API_KEY

class UITrasSetup:
    """Setup class for UI TRAS 1.5 Web Evaluation Agent"""
    
    def __init__(self):
        self.script_dir = Path(os.path.dirname(os.path.abspath(__file__)))
        self.agent_dir = self.script_dir / "web-eval-agent"
        self.vllm_config_file = self.script_dir / "vllm_config.json"
        self.operative_key = OPERATIVE_API_KEY
        
    def check_dependencies(self):
        """Check and install required dependencies"""
        logger.info("Checking dependencies...")
        
        # Check uv installation
        try:
            result = subprocess.run(["uv", "--version"], capture_output=True, text=True)
            logger.info(f"Found uv: {result.stdout.strip()}")
        except FileNotFoundError:
            logger.info("Installing uv...")
            subprocess.run(["curl", "-LsSf", "https://astral.sh/uv/install.sh", "-o", "uv_install.sh"], check=True)
            subprocess.run(["sh", "uv_install.sh"], check=True)
            os.remove("uv_install.sh")
            logger.info("uv installed successfully")
            
        # Check if playwright is installed
        try:
            result = subprocess.run(["playwright", "--version"], capture_output=True, text=True)
            logger.info(f"Found playwright: {result.stdout.strip()}")
        except FileNotFoundError:
            logger.info("Installing playwright...")
            subprocess.run(["npm", "install", "-g", "chromium", "playwright"], check=True)
            subprocess.run(["uvx", "--with", "playwright", "playwright", "install", "--with-deps"], check=True)
            logger.info("playwright installed successfully")
    
    def setup_web_eval_agent(self):
        """Install or update web-eval-agent"""
        logger.info("Setting up web-eval-agent...")
        
        cmd = [
            "uvx", 
            "--refresh-package", 
            "webEvalAgent", 
            "--from", 
            "git+https://github.com/Operative-Sh/web-eval-agent.git", 
            "webEvalAgent"
        ]
        
        env = os.environ.copy()
        if self.operative_key:
            env["OPERATIVE_API_KEY"] = self.operative_key
        else:
            logger.warning("OPERATIVE_API_KEY environment variable is not set")
            logger.warning("Please get your API key at operative.sh")
            self.operative_key = input("Enter your Operative API key: ").strip()
            if not self.operative_key:
                logger.error("No API key provided. Exiting.")
                return False
            env["OPERATIVE_API_KEY"] = self.operative_key
            # Save the key to the environment
            os.environ["OPERATIVE_API_KEY"] = self.operative_key
            
        try:
            result = subprocess.run(cmd, env=env, capture_output=True, text=True)
            if result.returncode == 0:
                logger.info("web-eval-agent installed successfully")
                return True
            else:
                logger.error(f"Error installing web-eval-agent: {result.stderr}")
                return False
        except Exception as e:
            logger.error(f"Error setting up web-eval-agent: {e}")
            return False
    
    def configure_vllm(self):
        """Configure VLLM settings for optimal MidScene integration"""
        logger.info("Configuring VLLM settings...")
        
        vllm_config = {
            "model_settings": {
                "default": {
                    "model_path": "local",
                    "tensor_parallel_size": 1,
                    "max_model_len": 8192,
                    "trust_remote_code": True,
                    "gpu_memory_utilization": 0.9,
                    "quantization": "awq"
                }
            },
            "midscene_settings": {
                "enabled": True,
                "chrome_path": "/Applications/Google Chrome.app/Contents/MacOS/Google Chrome",
                "headless": False,
                "timeout_ms": 30000,
                "viewport": {
                    "width": 1280,
                    "height": 800
                }
            },
            "server": {
                "host": "localhost",
                "port": 8086,
                "cors_origin": "*"
            },
            "logging": {
                "level": "info"
            }
        }
        
        # Check if Chrome exists in the default location for macOS
        if not os.path.exists(vllm_config["midscene_settings"]["chrome_path"]):
            # Try to find Chrome in common locations
            possible_paths = [
                "/Applications/Google Chrome.app/Contents/MacOS/Google Chrome",
                "/Applications/Chrome.app/Contents/MacOS/Chrome",
                "/usr/bin/google-chrome",
                "/usr/bin/chromium-browser"
            ]
            
            chrome_found = False
            for path in possible_paths:
                if os.path.exists(path):
                    vllm_config["midscene_settings"]["chrome_path"] = path
                    logger.info(f"Found Chrome at {path}")
                    chrome_found = True
                    break
            
            if not chrome_found:
                logger.warning("Could not find Chrome. Please install Google Chrome.")
                chrome_path = input("Enter path to Chrome executable (or press Enter to search later): ").strip()
                if chrome_path:
                    vllm_config["midscene_settings"]["chrome_path"] = chrome_path
        
        # Save the configuration
        with open(self.vllm_config_file, "w") as f:
            json.dump(vllm_config, f, indent=4)
        
        logger.info(f"VLLM configuration saved to {self.vllm_config_file}")
        return True
    
    def update_mcp_registry(self):
        """Update MCP registry with web-eval-agent configuration"""
        logger.info("Updating MCP registry...")
        
        registry_file = self.script_dir / "mcp_registry.json"
        if not registry_file.exists():
            # Create a new registry file
            registry_data = {
                "servers": {},
                "capabilities": {},
                "active_servers": []
            }
        else:
            # Load existing registry
            try:
                with open(registry_file) as f:
                    registry_data = json.load(f)
            except Exception as e:
                logger.error(f"Error loading registry file: {e}")
                registry_data = {
                    "servers": {},
                    "capabilities": {},
                    "active_servers": []
                }
        
        # Add web-eval-agent to registry
        registry_data["servers"]["web-eval-agent"] = {
            "id": "web-eval-agent",
            "name": "UI TRAS 1.5 Web Evaluation Agent",
            "url": "http://localhost:8086",
            "registered_at": time.strftime("%Y-%m-%dT%H:%M:%S.%fZ")
        }
        
        registry_data["capabilities"]["web-eval-agent"] = [
            "web-evaluation", "browser-automation", "midscene", "ui-tras"
        ]
        
        if "web-eval-agent" not in registry_data.get("active_servers", []):
            registry_data["active_servers"] = list(set(registry_data.get("active_servers", [])) | {"web-eval-agent"})
        
        # Save the updated registry
        with open(registry_file, "w") as f:
            json.dump(registry_data, f, indent=2)
        
        logger.info("MCP registry updated successfully")
        return True
    
    def run(self):
        """Run the setup process"""
        logger.info("Starting UI TRAS 1.5 setup...")
        
        # Step 1: Check and install dependencies
        self.check_dependencies()
        
        # Step 2: Configure VLLM
        self.configure_vllm()
        
        # Step 3: Set up web-eval-agent
        if not self.setup_web_eval_agent():
            logger.error("Failed to set up web-eval-agent")
            return False
        
        # Step 4: Update MCP registry
        self.update_mcp_registry()
        
        logger.info("UI TRAS 1.5 setup completed successfully")
        logger.info("")
        logger.info("To use UI TRAS 1.5:")
        logger.info("1. Run the MCP server: python start_mcp_servers.py")
        logger.info("2. Make sure OPERATIVE_API_KEY is set in your environment")
        logger.info("3. Your UI TRAS 1.5 should now connect properly with MidScene")
        logger.info("")
        
        return True

if __name__ == "__main__":
    setup = UITrasSetup()
    if not setup.run()e
        sys.exit(1)