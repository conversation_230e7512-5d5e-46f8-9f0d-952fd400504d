import os
from content_creation_agent import ContentCreationAgent
import logging

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def load_test_config():
    """Load test configuration from environment variables."""
    return {
        "heygen_api_key": os.getenv("HEYGEN_API_KEY"),
        "facebook_access_token": os.getenv("FACEBOOK_ACCESS_TOKEN"),
        "facebook_app_secret": os.getenv("FACEBOOK_APP_SECRET"),
        "facebook_app_id": os.getenv("FACEBOOK_APP_ID"),
        "instagram_username": os.getenv("INSTAGRAM_USERNAME"),
        "instagram_password": os.getenv("INSTAGRAM_PASSWORD"),
        "elevenlabs_api_key": os.getenv("ELEVENLABS_API_KEY"),
        "stability_api_key": os.getenv("STABILITY_API_KEY"),
        "openai_api_key": os.getenv("OPENAI_API_KEY")
    }

def test_voice_generation(agent):
    """Test ElevenLabs voice generation."""
    logger.info("Testing voice generation...")
    result = agent.execute_task(
        "generate_voice",
        text="Hello! This is a test of the voice generation system.",
        voice_id="default"
    )
    assert result is not None, "Voice generation failed"
    logger.info("Voice generation test: PASSED")
    return result

def test_image_generation(agent):
    """Test Stability AI image generation."""
    logger.info("Testing image generation...")
    result = agent.execute_task(
        "generate_image",
        prompt="A beautiful sunset over a mountain landscape, digital art style"
    )
    assert result is not None, "Image generation failed"
    logger.info("Image generation test: PASSED")
    return result

def test_video_generation(agent):
    """Test HeyGen video generation."""
    logger.info("Testing video generation...")
    result = agent.execute_task(
        "generate_video",
        script_text="Welcome to our test video. This is an AI-generated presentation."
    )
    assert result is not None, "Video generation failed"
    logger.info("Video generation test: PASSED")
    return result

def test_multimedia_content(agent):
    """Test combined multimedia content creation."""
    logger.info("Testing multimedia content creation...")
    result = agent.execute_task(
        "create_multimedia",
        script="This is a test of our multimedia content creation system.",
        voice_settings={"voice_id": "default"},
        image_prompt="Futuristic technology background"
    )
    assert result is not None, "Multimedia content creation failed"
    logger.info("Multimedia content test: PASSED")
    return result

def test_social_media_posting(agent, content_path):
    """Test social media posting."""
    # Test Facebook posting
    logger.info("Testing Facebook posting...")
    fb_result = agent.execute_task(
        "post_to_facebook",
        content_path=content_path,
        caption="Testing our AI content creation system! #AIContent #Test",
        page_id=os.getenv("FACEBOOK_PAGE_ID")
    )
    assert fb_result is not None, "Facebook posting failed"
    logger.info("Facebook posting test: PASSED")

    # Test Instagram posting
    logger.info("Testing Instagram posting...")
    ig_result = agent.execute_task(
        "post_to_instagram",
        media_path=content_path,
        caption="AI-generated content test! 🤖 #AIContent #Testing #Innovation"
    )
    assert ig_result is not None, "Instagram posting failed"
    logger.info("Instagram posting test: PASSED")

def main():
    """Run all tests."""
    try:
        # Initialize agent with test configuration
        config = load_test_config()
        agent = ContentCreationAgent("Test Agent", config=config)

        # Run individual component tests
        voice_file = test_voice_generation(agent)
        image_file = test_image_generation(agent)
        video_file = test_video_generation(agent)

        # Test combined multimedia content
        content_file = test_multimedia_content(agent)

        # Test social media posting with the generated content
        if content_file:
            test_social_media_posting(agent, content_file)

        logger.info("All tests completed successfully!")
        
    except AssertionError as e:
        logger.error(f"Test failed: {str(e)}")
    except Exception as e:
        logger.error(f"Unexpected error during testing: {str(e)}")

if __name__ == "__main__":
    main()