"""
Try All Twilio Credentials

This script tries multiple sets of Twilio credentials to find the working one.
"""

import os
import requests
import base64
from dotenv import load_dotenv
import json

# Load environment variables from .env file
load_dotenv()

# Twilio credentials sets
TWILIO_CREDENTIALS = [
    {
        "name": "From .env file",
        "account_sid": os.getenv("TWILIO_ACCOUNT_SID"),
        "auth_token": os.getenv("TWILIO_AUTH_TOKEN"),
    },
    {
        "name": "From instructions",
        "account_sid": "**********************************",
        "auth_token": "CqpVewwter1BEMdFIFHrN2XmUyt22wBP",
    },
    {
        "name": "API Key from instructions",
        "account_sid": "**********************************",
        "auth_token": "GERDY2CPR8KCH3G51HVHP3ED",
    },
    {
        "name": "Alternative Auth Token",
        "account_sid": "**********************************",
        "auth_token": "0458bc22d41d6756fc8e62d3e2938382",
    }
]

def test_credentials(credentials):
    """Test a set of Twilio credentials"""
    print("=" * 80)
    print(f"TESTING CREDENTIALS: {credentials['name']}")
    print("=" * 80)
    
    print(f"Account SID: {credentials['account_sid']}")
    print(f"Auth Token: {credentials['auth_token'][:5]}{'*' * (len(credentials['auth_token']) - 5)}")
    
    url = f"https://api.twilio.com/2010-04-01/Accounts/{credentials['account_sid']}.json"
    
    # Create the authentication header
    auth = base64.b64encode(f"{credentials['account_sid']}:{credentials['auth_token']}".encode()).decode()
    headers = {
        "Authorization": f"Basic {auth}",
        "Content-Type": "application/json"
    }
    
    try:
        response = requests.get(url, headers=headers)
        
        print(f"Status Code: {response.status_code}")
        print(f"Response: {response.text}")
        
        if response.status_code >= 200 and response.status_code < 300:
            print("Authentication successful!")
            return True
        else:
            print("Authentication failed.")
            return False
    except Exception as e:
        print(f"Error: {str(e)}")
        return False

def test_send_sms(credentials):
    """Test sending an SMS with a set of Twilio credentials"""
    print("=" * 80)
    print(f"TESTING SMS SENDING WITH: {credentials['name']}")
    print("=" * 80)
    
    url = f"https://api.twilio.com/2010-04-01/Accounts/{credentials['account_sid']}/Messages.json"
    
    # Create the authentication header
    auth = base64.b64encode(f"{credentials['account_sid']}:{credentials['auth_token']}".encode()).decode()
    headers = {
        "Authorization": f"Basic {auth}",
        "Content-Type": "application/x-www-form-urlencoded"
    }
    
    # Create the message data
    data = {
        "To": "+***********",  # Paul Edwards' primary number
        "From": "+***********",  # Your verified Twilio number
        "Body": "This is a test message from Flo Faction Insurance. Please ignore."
    }
    
    try:
        response = requests.post(url, headers=headers, data=data)
        
        print(f"Status Code: {response.status_code}")
        print(f"Response: {response.text}")
        
        if response.status_code >= 200 and response.status_code < 300:
            print("SMS sent successfully!")
            return True
        else:
            print("Failed to send SMS.")
            return False
    except Exception as e:
        print(f"Error: {str(e)}")
        return False

def main():
    """Try all sets of Twilio credentials"""
    print("=" * 80)
    print("TRYING ALL TWILIO CREDENTIALS")
    print("=" * 80)
    
    working_credentials = []
    
    # Test all credential sets
    for credentials in TWILIO_CREDENTIALS:
        if test_credentials(credentials):
            working_credentials.append(credentials)
    
    # Print summary
    print("=" * 80)
    print("CREDENTIALS SUMMARY")
    print("=" * 80)
    
    if working_credentials:
        print(f"Found {len(working_credentials)} working credential sets:")
        for credentials in working_credentials:
            print(f"- {credentials['name']}: {credentials['account_sid']}")
        
        # Try sending an SMS with the first working credentials
        print("\nTrying to send an SMS with the first working credentials...")
        test_send_sms(working_credentials[0])
    else:
        print("No working credentials found.")
        print("Please check your Twilio account status and credentials.")

if __name__ == "__main__":
    main()
