"""
Send Email to <PERSON> using mailx

This script sends an email to <PERSON> using the mailx command-line utility.
"""

import os
import subprocess
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()

# <PERSON> contact information
PAUL_EDWARDS = {
    "first_name": os.getenv("PAUL_FIRST_NAME", "<PERSON>"),
    "last_name": os.getenv("PAUL_LAST_NAME", "<PERSON>"),
    "email": os.getenv("PAUL_EMAIL", "<EMAIL>"),
    "primary_phone": os.getenv("PAUL_PRIMARY_PHONE", "+17722089646"),
    "secondary_phone": os.getenv("PAUL_SECONDARY_PHONE", "+17725395908")
}

# Agent information
AGENT_INFO = {
    "name": os.getenv("AGENT_NAME", "<PERSON>"),
    "agency": os.getenv("AGENT_AGENCY", "Flo Faction Insurance"),
    "email": os.getenv("AGENT_EMAIL", "<EMAIL>"),
    "website": os.getenv("AGENT_WEBSITE", "https://www.flofaction.com/insurance"),
}

def create_email_content():
    """Create email subject and body"""
    subject = "Creating Tax-Free Retirement Income Without Market Risk"
    body = f"""
Dear {PAUL_EDWARDS['first_name']},

I hope this email finds you well. My name is {AGENT_INFO['name']} with {AGENT_INFO['agency']}, and I specialize in helping people like you create tax-free retirement income without the risk of the stock market.

Based on the information I have, I've prepared a personalized analysis that shows how you could potentially:

1. Generate approximately $5,000 per month in tax-free retirement income
2. Build cash value that grows without the risk of market losses
3. Provide a death benefit of around $720,000 to protect your family
4. Create a financial strategy that offers both protection and growth potential

Many of my clients are concerned about:
- Market volatility affecting their retirement savings
- Taxes reducing their retirement income
- Having enough money to maintain their lifestyle in retirement
- Protecting their family financially

The strategy I'd like to share with you addresses all these concerns through an Indexed Universal Life policy specifically designed for your situation.

Would you be interested in a brief 30-minute conversation to review this analysis? If so, please call me or reply to this email with a good time to connect.

You can also learn more about our services by visiting our website at {AGENT_INFO['website']}.

Thank you for your time, and I look forward to speaking with you soon.

Best regards,
{AGENT_INFO['name']}
{AGENT_INFO['agency']}
{AGENT_INFO['website']}
    """
    return subject, body

def send_email_with_mailx():
    """Send email using mailx command-line utility"""
    print("=" * 80)
    print("SENDING EMAIL TO PAUL EDWARDS USING MAILX")
    print("=" * 80)
    
    subject, body = create_email_content()
    
    try:
        # Create a temporary file with the email body
        with open("temp_email.txt", "w") as f:
            f.write(body)
        
        # Use the mail command
        cmd = f"cat temp_email.txt | mail -s '{subject}' {PAUL_EDWARDS['email']}"
        result = subprocess.run(cmd, shell=True, capture_output=True, text=True)
        
        # Remove the temporary file
        os.remove("temp_email.txt")
        
        if result.returncode == 0:
            print(f"Email sent to {PAUL_EDWARDS['email']} using mail command")
            print("Command output:")
            print(result.stdout)
            return True
        else:
            print(f"Error sending email with mail command: {result.stderr}")
            return False
    except Exception as e:
        print(f"Error sending email with mailx: {str(e)}")
        return False

def main():
    """Send email to Paul Edwards"""
    print("This script will send an email to Paul Edwards using mailx.")
    print(f"Recipient: {PAUL_EDWARDS['email']}")
    
    proceed = input("Do you want to proceed? (yes/no): ")
    
    if proceed.lower() == "yes":
        send_email_with_mailx()
    else:
        print("Operation cancelled.")

if __name__ == "__main__":
    main()
