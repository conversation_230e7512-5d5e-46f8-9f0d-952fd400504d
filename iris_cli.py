#!/usr/bin/env python3
"""
IRIS Interactive CLI
===================

Interactive command-line interface for the IRIS system.
Provides easy access to all IRIS capabilities through a conversational interface.
"""

import asyncio
import sys
import os
from pathlib import Path
import base64
from typing import Optional

# Add current directory to path for imports
sys.path.append(str(Path(__file__).parent))

try:
    from iris_dashboard import IRISDashboard
    CLI_AVAILABLE = True
except ImportError as e:
    CLI_AVAILABLE = False
    print(f"❌ IRIS CLI not available: {e}")

class IRISCLI:
    """Interactive CLI for IRIS"""
    
    def __init__(self):
        self.dashboard = None
        self.running = False
        
    async def start(self):
        """Start the interactive CLI"""
        if not CLI_AVAILABLE:
            print("❌ IRIS CLI components not available")
            return
        
        print("🚀 IRIS INTERACTIVE CLI")
        print("=" * 60)
        print("Initializing IRIS system...")
        
        # Initialize dashboard
        self.dashboard = IRISDashboard()
        success = await self.dashboard.initialize()
        
        if not success:
            print("❌ Failed to initialize IRIS")
            return
        
        print("\n✅ IRIS is ready!")
        self._show_help()
        
        self.running = True
        
        try:
            while self.running:
                await self._handle_input()
        except KeyboardInterrupt:
            print("\n👋 Goodbye!")
        finally:
            if self.dashboard:
                await self.dashboard.cleanup()
    
    def _show_help(self):
        """Show help information"""
        print("\n📖 IRIS CLI COMMANDS")
        print("-" * 40)
        print("💬 Just type your question or request")
        print("🖼️  /image <path> <query> - Analyze an image")
        print("🌐 /web <url> <task> - Web automation task")
        print("📊 /status - Show system status")
        print("📋 /history - Show query history")
        print("❓ /help - Show this help")
        print("🚪 /quit or /exit - Exit IRIS")
        print("-" * 40)
        print("\n💡 Examples:")
        print("  What is term life insurance?")
        print("  /image document.png Extract the policy number")
        print("  /web https://example.com Fill out the contact form")
        print("  Research the latest insurance regulations")
        print()
    
    async def _handle_input(self):
        """Handle user input"""
        try:
            user_input = input("🤖 IRIS> ").strip()
            
            if not user_input:
                return
            
            # Handle commands
            if user_input.startswith('/'):
                await self._handle_command(user_input)
            else:
                # Regular query
                await self._process_query(user_input)
                
        except EOFError:
            self.running = False
        except Exception as e:
            print(f"❌ Error: {e}")
    
    async def _handle_command(self, command: str):
        """Handle CLI commands"""
        parts = command.split(' ', 2)
        cmd = parts[0].lower()
        
        if cmd in ['/quit', '/exit']:
            self.running = False
            print("👋 Exiting IRIS...")
            
        elif cmd == '/help':
            self._show_help()
            
        elif cmd == '/status':
            await self._show_status()
            
        elif cmd == '/history':
            self._show_history()
            
        elif cmd == '/image':
            if len(parts) >= 3:
                image_path = parts[1]
                query = parts[2]
                await self._process_image_query(image_path, query)
            else:
                print("❌ Usage: /image <path> <query>")
                
        elif cmd == '/web':
            if len(parts) >= 3:
                url = parts[1]
                task = parts[2]
                await self._process_web_query(url, task)
            else:
                print("❌ Usage: /web <url> <task>")
                
        else:
            print(f"❌ Unknown command: {cmd}")
            print("Type /help for available commands")
    
    async def _process_query(self, query: str):
        """Process a regular text query"""
        print(f"\n🔍 Processing: {query}")
        
        result = await self.dashboard.process_query(query)
        
        # Result is already displayed by the dashboard
        # Just add a separator
        print()
    
    async def _process_image_query(self, image_path: str, query: str):
        """Process an image query"""
        try:
            # Load image file
            if not os.path.exists(image_path):
                print(f"❌ Image file not found: {image_path}")
                return
            
            with open(image_path, 'rb') as f:
                image_data = f.read()
            
            print(f"\n🖼️ Analyzing image: {image_path}")
            print(f"Query: {query}")
            
            result = await self.dashboard.process_query(
                query=query,
                query_type="vision",
                image_data=image_data
            )
            
            print()
            
        except Exception as e:
            print(f"❌ Image processing error: {e}")
    
    async def _process_web_query(self, url: str, task: str):
        """Process a web automation query"""
        print(f"\n🌐 Web task: {task}")
        print(f"URL: {url}")
        
        # Combine URL and task into query
        full_query = f"Navigate to {url} and {task}"
        
        result = await self.dashboard.process_query(
            query=full_query,
            query_type="web_automation"
        )
        
        print()
    
    async def _show_status(self):
        """Show system status"""
        if not self.dashboard:
            print("❌ Dashboard not initialized")
            return
        
        info = self.dashboard.get_system_info()
        
        print("\n📊 IRIS SYSTEM STATUS")
        print("-" * 40)
        print(f"Session ID: {info['session_id']}")
        print(f"Initialized: {'✅' if info['initialized'] else '❌'}")
        print(f"Queries Processed: {info['query_history_count']}")
        
        print("\n🔧 Component Status:")
        for component, status in info['system_status'].items():
            icon = "✅" if status else "❌"
            name = component.replace('_', ' ').title()
            print(f"  {icon} {name}")
        
        print("\n🎯 Available Capabilities:")
        for capability in info['capabilities']:
            print(f"  • {capability}")
        
        print()
    
    def _show_history(self):
        """Show query history"""
        if not self.dashboard or not self.dashboard.query_history:
            print("📋 No query history available")
            return
        
        print("\n📋 QUERY HISTORY")
        print("-" * 40)
        
        # Show last 10 queries
        recent_queries = self.dashboard.query_history[-10:]
        
        for i, entry in enumerate(recent_queries, 1):
            timestamp = entry['timestamp'][:19]  # Remove microseconds
            query = entry['query'][:50] + "..." if len(entry['query']) > 50 else entry['query']
            success = "✅" if entry['result'].get('success', False) else "❌"
            
            print(f"{i:2d}. {timestamp} {success} {query}")
        
        if len(self.dashboard.query_history) > 10:
            print(f"... and {len(self.dashboard.query_history) - 10} more")
        
        print()

async def main():
    """Main CLI function"""
    if len(sys.argv) > 1 and sys.argv[1] == '--help':
        print("IRIS Interactive CLI")
        print("===================")
        print()
        print("Usage:")
        print("  python iris_cli.py              # Start interactive mode")
        print("  python iris_cli.py --help       # Show this help")
        print()
        print("Interactive Commands:")
        print("  💬 Type any question or request")
        print("  🖼️  /image <path> <query>       # Analyze an image")
        print("  🌐 /web <url> <task>           # Web automation")
        print("  📊 /status                     # System status")
        print("  📋 /history                    # Query history")
        print("  ❓ /help                       # Show help")
        print("  🚪 /quit                       # Exit")
        print()
        print("Examples:")
        print("  What is life insurance?")
        print("  /image policy.pdf Extract the policy details")
        print("  /web https://example.com Navigate to contact page")
        print("  Research insurance market trends")
        return
    
    # Start interactive CLI
    cli = IRISCLI()
    await cli.start()

if __name__ == "__main__":
    asyncio.run(main())
