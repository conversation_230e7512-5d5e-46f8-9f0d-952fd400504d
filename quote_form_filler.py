import logging
import time
from typing import Dict
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException
from carrier_urls import CARRIER_URLS

logger = logging.getLogger(__name__)

class QuoteFormFiller:
    """Automates filling quote forms on carrier websites"""
    
    def __init__(self):
        self.driver = None
        
    def _initialize_driver(self):
        """Initialize Chrome WebDriver"""
        options = webdriver.ChromeOptions()
        options.add_argument('--start-maximized')
        options.add_argument('--disable-extensions')
        self.driver = webdriver.Chrome(options=options)
        
    def close(self):
        """Close browser"""
        if self.driver:
            self.driver.quit()
            
    def fill_moo_quote_form(self, client_data: Dict):
        """Fill Mutual of Omaha quote form"""
        try:
            if not self.driver:
                self._initialize_driver()
                
            # Navigate to MOO quote page
            url = CARRIER_URLS['mutual_of_omaha']['quote_url']
            logger.info(f"Navigating to: {url}")
            self.driver.get(url)
            
            # Wait for form to load
            WebDriverWait(self.driver, 10).until(
                EC.presence_of_element_located((By.ID, "quoteForm"))
            )
            
            # Fill basic information
            self._fill_field("applicantName", client_data["name"])
            self._fill_field("dateOfBirth", client_data["dob"])
            self._fill_field("zip", client_data["address"].split()[-1])
            
            # Handle tobacco use
            tobacco = "Yes" if client_data.get("tobacco_use") else "No"
            self._select_option("tobaccoUse", tobacco)
            
            # Fill height and weight
            if "height" in client_data:
                self._fill_field("height", client_data["height"])
            if "weight" in client_data:
                self._fill_field("weight", client_data["weight"])
                
            # Select product type
            product = client_data.get("product_type", "Medicare Supplement")
            self._select_option("productType", product)
            
            logger.info("Successfully filled MOO quote form")
            return True
            
        except Exception as e:
            logger.error(f"Error filling MOO form: {str(e)}")
            return False
            
    def fill_ffl_quote_form(self, client_data: Dict):
        """Fill FFL Trident quote form"""
        try:
            if not self.driver:
                self._initialize_driver()
                
            # Navigate to FFL quote page
            url = CARRIER_URLS['ffl_trident']['quote_url']
            logger.info(f"Navigating to: {url}")
            self.driver.get(url)
            
            # Wait for form to load
            WebDriverWait(self.driver, 10).until(
                EC.presence_of_element_located((By.ID, "quoteForm"))
            )
            
            # Fill basic information
            self._fill_field("fullName", client_data["name"])
            self._fill_field("dateOfBirth", client_data["dob"])
            self._fill_field("zipCode", client_data["address"].split()[-1])
            
            # Select coverage amount
            coverage = client_data.get("desired_coverage", "250000")
            self._select_option("coverageAmount", coverage)
            
            # Select term length (default to 20 years)
            self._select_option("termLength", "20")
            
            # Handle health class
            health_class = "Preferred" if not client_data.get("tobacco_use") else "Standard"
            self._select_option("healthClass", health_class)
            
            logger.info("Successfully filled FFL quote form")
            return True
            
        except Exception as e:
            logger.error(f"Error filling FFL form: {str(e)}")
            return False
            
    def _fill_field(self, field_id: str, value: str):
        """Fill form field by ID"""
        try:
            field = WebDriverWait(self.driver, 5).until(
                EC.presence_of_element_located((By.ID, field_id))
            )
            field.clear()
            field.send_keys(value)
            time.sleep(0.5)  # Small delay for stability
        except TimeoutException:
            logger.warning(f"Field not found: {field_id}")
            
    def _select_option(self, select_id: str, value: str):
        """Select option from dropdown by ID"""
        try:
            select = WebDriverWait(self.driver, 5).until(
                EC.presence_of_element_located((By.ID, select_id))
            )
            for option in select.find_elements(By.TAG_NAME, "option"):
                if option.text.strip().lower() == value.lower():
                    option.click()
                    time.sleep(0.5)  # Small delay for stability
                    break
        except TimeoutException:
            logger.warning(f"Select not found: {select_id}")

def main():
    """Test form filling"""
    logging.basicConfig(level=logging.INFO)
    
    # Test client data
    test_client = {
        "name": "John Medicare Test",
        "dob": "1955-06-15",
        "height": "5'10\"",
        "weight": "175",
        "address": "123 Senior Way, Port St. Lucie, FL 34952",
        "tobacco_use": False,
        "product_type": "Medicare Supplement"
    }
    
    filler = QuoteFormFiller()
    
    try:
        # Test MOO form
        logger.info("\nTesting MOO quote form...")
        success = filler.fill_moo_quote_form(test_client)
        
        # Wait for user to verify
        input("Press Enter after verifying MOO form...")
        
        # Update for life insurance quote
        test_client["product_type"] = "Term Life"
        test_client["desired_coverage"] = "250000"
        
        # Test FFL form
        logger.info("\nTesting FFL quote form...")
        success = filler.fill_ffl_quote_form(test_client)
        
        # Wait for user to verify
        input("Press Enter after verifying FFL form...")
        
    finally:
        filler.close()

if __name__ == "__main__":
    main()