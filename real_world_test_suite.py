"""
Comprehensive test suite for evaluating all agents in real-world scenarios
"""

import logging
import asyncio
from datetime import datetime
from typing import Dict, List
import json
from pathlib import Path

# Import all agents and modules
from agent_coordinator import AgentCoordinator
from content_creation_agent import ContentAgent
from agent_learning_modules import LearningModule
from knowledge_management import KnowledgeManager
from client_template import ClientManager
from insurance_carriers import CarrierManager
from trading_agent import TradingAgent
from secure_credentials import SecureCredentialsManager

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class RealWorldTestSuite:
    """Test suite for real-world scenario evaluation"""
    
    def __init__(self):
        self.coordinator = AgentCoordinator()
        self.knowledge_manager = KnowledgeManager()
        self.client_manager = ClientManager()
        self.carrier_manager = CarrierManager()
        self.creds_manager = SecureCredentialsManager()
        self.test_results = {}
        
    async def run_full_test_suite(self):
        """Run all real-world scenario tests"""
        logger.info("Starting comprehensive real-world testing...")
        
        # Test scenarios
        test_scenarios = [
            self.test_medicare_enrollment,
            self.test_policy_changes,
            self.test_content_creation,
            self.test_client_communication,
            self.test_market_analysis,
            self.test_agent_learning,
            self.test_multiagent_coordination
        ]
        
        for test in test_scenarios:
            try:
                scenario_name = test.__name__.replace('test_', '')
                logger.info(f"\nRunning scenario: {scenario_name}")
                result = await test()
                self.test_results[scenario_name] = result
            except Exception as e:
                logger.error(f"Error in {test.__name__}: {str(e)}")
                self.test_results[test.__name__] = {
                    "status": "failed",
                    "error": str(e)
                }
                
        await self._save_test_results()
        
    async def test_medicare_enrollment(self):
        """Test complete Medicare enrollment process"""
        try:
            # Create test client
            client_data = {
                "name": "Test Medicare Client",
                "dob": "1955-06-15",
                "phone": "************",
                "email": "<EMAIL>",
                "address": "123 Test St, Port St. Lucie, FL 34952",
                "plan_type": "Medicare Supplement",
                "preferred_carrier": "Mutual of Omaha",
                "health_conditions": ["Diabetes Type 2", "High Blood Pressure"],
                "medications": [
                    {"name": "Metformin", "dosage": "500mg"},
                    {"name": "Lisinopril", "dosage": "10mg"}
                ]
            }
            
            # Test sequence
            steps = []
            
            # 1. Create client record
            client = self.client_manager.create_client(client_data)
            steps.append(("client_creation", "success"))
            
            # 2. Get carrier quotes
            quotes = await self.carrier_manager.get_quotes(client, "sandra")
            steps.append(("quote_generation", "success" if quotes else "failed"))
            
            # 3. Generate enrollment content
            content_task = {
                "type": "medicare_enrollment",
                "client": client,
                "quotes": quotes
            }
            content = await self.coordinator.process_task(content_task)
            steps.append(("content_generation", "success" if content else "failed"))
            
            # 4. Submit application
            application = await self.submit_test_application(client, quotes)
            steps.append(("application_submission", "success" if application else "failed"))
            
            return {
                "status": "success",
                "steps": steps,
                "notes": "Full Medicare enrollment process completed"
            }
            
        except Exception as e:
            return {
                "status": "failed",
                "error": str(e)
            }
            
    async def test_policy_changes(self):
        """Test handling policy changes and updates"""
        try:
            # Test policy update scenarios
            scenarios = [
                {
                    "type": "address_change",
                    "data": {
                        "new_address": "456 New St, Port St. Lucie, FL 34952"
                    }
                },
                {
                    "type": "plan_change",
                    "data": {
                        "new_plan": "Plan N",
                        "reason": "Lower premium needed"
                    }
                },
                {
                    "type": "add_coverage",
                    "data": {
                        "coverage_type": "dental",
                        "carrier": "Humana"
                    }
                }
            ]
            
            results = []
            for scenario in scenarios:
                result = await self._process_policy_change(scenario)
                results.append({
                    "scenario": scenario["type"],
                    "status": result["status"]
                })
                
            return {
                "status": "success",
                "results": results
            }
            
        except Exception as e:
            return {
                "status": "failed",
                "error": str(e)
            }
            
    async def test_content_creation(self):
        """Test content creation for various scenarios"""
        try:
            content_types = [
                {
                    "type": "email",
                    "purpose": "welcome",
                    "client_type": "medicare"
                },
                {
                    "type": "document",
                    "purpose": "policy_summary",
                    "product": "medicare_supplement"
                },
                {
                    "type": "sms",
                    "purpose": "appointment_reminder",
                    "urgency": "high"
                }
            ]
            
            results = []
            for content_type in content_types:
                content = await self.coordinator.process_content_task(content_type)
                results.append({
                    "type": content_type["type"],
                    "status": "success" if content else "failed"
                })
                
            return {
                "status": "success",
                "results": results
            }
            
        except Exception as e:
            return {
                "status": "failed",
                "error": str(e)
            }
            
    async def test_client_communication(self):
        """Test client communication workflows"""
        try:
            # Test different communication scenarios
            scenarios = [
                {
                    "type": "initial_contact",
                    "method": "email",
                    "urgency": "normal"
                },
                {
                    "type": "follow_up",
                    "method": "phone",
                    "urgency": "high"
                },
                {
                    "type": "policy_update",
                    "method": "sms",
                    "urgency": "medium"
                }
            ]
            
            results = []
            for scenario in scenarios:
                result = await self._test_communication(scenario)
                results.append({
                    "scenario": scenario["type"],
                    "status": result["status"]
                })
                
            return {
                "status": "success",
                "results": results
            }
            
        except Exception as e:
            return {
                "status": "failed",
                "error": str(e)
            }
            
    async def test_agent_learning(self):
        """Test agent learning and adaptation"""
        try:
            # Test learning scenarios
            scenarios = [
                {
                    "type": "policy_changes",
                    "data": "New Medicare rules for 2025"
                },
                {
                    "type": "market_trends",
                    "data": "Healthcare cost analysis Q1 2025"
                },
                {
                    "type": "client_preferences",
                    "data": "Analysis of Plan G vs Plan N selection factors"
                }
            ]
            
            results = []
            for scenario in scenarios:
                result = await self._test_learning(scenario)
                results.append({
                    "scenario": scenario["type"],
                    "status": result["status"]
                })
                
            return {
                "status": "success",
                "results": results
            }
            
        except Exception as e:
            return {
                "status": "failed",
                "error": str(e)
            }
            
    async def test_multiagent_coordination(self):
        """Test coordination between multiple agents"""
        try:
            # Test complex multi-agent scenarios
            scenarios = [
                {
                    "type": "enrollment_workflow",
                    "agents": ["content", "insurance", "communication"]
                },
                {
                    "type": "policy_service",
                    "agents": ["insurance", "learning", "communication"]
                },
                {
                    "type": "market_analysis",
                    "agents": ["trading", "learning", "content"]
                }
            ]
            
            results = []
            for scenario in scenarios:
                result = await self._test_coordination(scenario)
                results.append({
                    "scenario": scenario["type"],
                    "status": result["status"]
                })
                
            return {
                "status": "success",
                "results": results
            }
            
        except Exception as e:
            return {
                "status": "failed",
                "error": str(e)
            }
            
    async def _save_test_results(self):
        """Save test results to file"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"test_results/real_world_tests_{timestamp}.json"
        
        Path("test_results").mkdir(exist_ok=True)
        
        with open(filename, 'w') as f:
            json.dump(self.test_results, f, indent=2)
            
        logger.info(f"Test results saved to {filename}")

async def main():
    """Run the test suite"""
    test_suite = RealWorldTestSuite()
    await test_suite.run_full_test_suite()

if __name__ == "__main__":
    asyncio.run(main())